package test;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.dxhy.EimsOrderApplication;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.entity.AdditionElemrntQueryRes;
import com.dxhy.order.modules.entity.RedInvoiceConfirmSldhRes;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.modules.service.SalerWarningService;
import com.dxhy.order.pojo.InvoiceInfoRes;
import com.dxhy.order.utils.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.Inet4Address;
import java.nio.charset.Charset;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = EimsOrderApplication.class)
public class InvoiceRecordTest {


    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;
    @Autowired
    private SalerWarningService salerWarningService;
    @Resource
    private RedisTemplate redisTemplate;

    // ofd - png
    @Test
    public void test1() throws IOException, InvocationTargetException, IllegalAccessException {


//        String url = "https://fpsjb.sichuan.chinatax.gov.cn:7012/api?action=getDoc&code=051001800311_72379571_20200416_I1C1F7C1&type=3";
//        byte[] ofd_byte = HttpsUtils.doGet(url);
//        System.out.println("1 -----:" + ofd_byte);
//
//        System.out.println("2----" + ofd_byte + "-----");
//        System.out.println("3--------------------------------------------------------------");
//        System.out.println(new String (ofd_byte,"utf-8"));
//        System.out.println("4-------------------------------------------------------------");
//        FileOutputStream fos = new FileOutputStream("D:/bing.ofd");
//        fos.write(ofd_byte);
//
//        fos.close();
//        System.out.println("6--------------------------------------------------------------");
//        System.out.println("done!");
//        byte[] bytes1 = "hello world".getBytes();        //Verify original content
//        System.out.println( new String(bytes1,"utf-8") );
//
//        //使用航信
//        OfdPreResponse ofdPreResponse = AisinoClient.invokeConvert(Base64.decodeBase64(ofd_byte), "210", "http://10.151.34.23:18081");
//        System.out.println( "5--------------------------------------------------------------");
//
//        String res = JsonUtils.getInstance().toJsonString(ofdPreResponse.getPreviews());
//        System.out.println( "6--------------------------"+res+"------------------------------------");
//


    }

    @Test
    public void test3() {
        String key = "kp:sm:slzt:91441900MA519W5D1B";
        String redisValue = (String) redisTemplate.opsForValue().get(key);
        System.out.println(redisValue);
        //redisTemplate.opsForValue().set(key, key, 1, TimeUnit.SECONDS);
    }

    public static void main(String[] args) throws IOException, ParseException, IntrospectionException, InvocationTargetException, IllegalAccessException {

        String pdfUrl = "http://fp.baiwang.com/format/d?d=324CE5E6CFE6686BD211BB31296101CE46A372B0FBAC2046204FF6506323E0C14890B54D622032EF455C8A64D51BA643\n";
        byte[] resp1 = HttpUtils.doGet(pdfUrl);
        System.out.println(Base64Encoding.encodeToString(resp1));
//        String bc = "-0.5";
//        String b11 = bc.replace("-", "");
//        BigDecimal bigDecimal = new BigDecimal(b11);
//        DecimalFormat decimalFormat = new DecimalFormat("0.00");
//        System.out.println(decimalFormat.format(bigDecimal));
//


//        String si = "{\"hzqrxxztDm\":\"01\",\"hzfpxxqrdbh\":\"44191122111004403413\",\"xsfnsrsbh\":\"91441900MA519W5D1B\",\"zzsytDm\":\"03\",\"lzkprq\":\"2022-11-24 11:21:19\",\"ykjhzfpbz\":\"Y\",\"hzcxse\":0.0,\"gmfmc\":\"深圳标普云科技有限公司\",\"chyyDm\":\"04\",\"uuid\":\"0fe2cbf2abfd4df580dfdfb23bfc0f07\",\"xfsytDm\":\"00\",\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"lzfphm\":\"22442000000003463876\",\"lzfppzDm\":\"02\",\"lzhjse\":0.0,\"xsfmc\":\"广东标普人力资源有限公司\",\"lrfsf\":\"0\",\"hzqrxxmxList\":[{\"ggxh\":\"HJJK-1\",\"fpspdj\":\"5.00\",\"spfwjc\":\"矿产品\",\"xh\":1,\"se\":0.0,\"dw\":\"吨\",\"sphfwssflhbbm\":\"1020600000000000000\",\"xmmc\":\"其他矿产品\",\"hwhyslwfwmc\":\"*矿产品*其他矿产品\",\"sl1\":0.0,\"je\":-5.0,\"lzmxxh\":1}],\"lzhjje\":5.0,\"hzcxje\":-5.0,\"fprzztDm\":\"00\"}";
//
//        QueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().parseObject(si, QueryHzqrxxRes.class);
//        System.out.println(queryHzqrxxRes);

        // 循环该类的get方法
//        Test2 specialInvoiceReversalEntity = new Test2();
//        specialInvoiceReversalEntity.setName("123");
//        Class clazz = specialInvoiceReversalEntity.getClass();//获得实体类名
//        System.out.println(" ---clazz---:" + clazz);
//        Field[] fields = specialInvoiceReversalEntity.getClass().getDeclaredFields();//获得属性
//        //获得Object对象中的所有方法
//        for (Field field : fields) {
////            System.out.println(" ---field---:" + field);
////            PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
////            Method getMethod = pd.getReadMethod();
////            System.out.println(" ---pd---:" + getMethod);
////            getMethod.invoke(specialInvoiceReversalEntity);
//
//            Annotation[] annotations = field.getAnnotations();
//            for (Annotation annotation : annotations) {
//                System.out.println(" ---pd---:" + annotation.annotationType());
//            }

//            Method getMethod = pd.getReadMethod();//获得get方法
//            getMethod.invoke(specialInvoiceReversalEntity);//此处为执行该Object对象的get方法
//            Method setMethod = pd.getWriteMethod();//获得set方法
//            setMethod.invoke(specialInvoiceReversalEntity, "参数");//此处为执行该Object对象的set方法
//        }

        // 获取前一天日期
//        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minus(1, ChronoUnit.DAYS);
//        String startTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
//        String endTime = sdf1.format(new Date());
//
//        System.out.println(startTime);
//        System.out.println(endTime);
        String s = "111";


//        NumberFormat num = NumberFormat.getPercentInstance();
//        num.setMaximumIntegerDigits(3);
//        num.setMaximumFractionDigits(2);
//        System.out.println(num.format(Double.valueOf(s)));

        if ("test".equals(s)) {
            String datt = "{\"hzqrxxztDm\":\"01\",\"xsfmc\":\"广东标普人力资源有限公司\",\"hzfpxxqrdbh\":\"44191122111004702640\",\"kdsj\":\"2022-11-23 18:21:10\",\"xsfnsrsbh\":\"91441900MA519W5D1B\",\"qdfphm\":\"22442000000003252696\",\"gmfmc\":\"深圳标普云科技有限公司\",\"jshj\":-11.0,\"clzt\":\"处理成功\",\"uuid\":\"90e0c210d82f44628b493cf6178f6164\",\"gmfnsrsbh\":\"91440300MA5EF4Q15E\"}";
            RedInvoiceConfirmSldhRes redInvoiceConfirmSldhRes = JsonUtils.getInstance().fromJson(datt, RedInvoiceConfirmSldhRes.class);

            System.out.println(redInvoiceConfirmSldhRes);
            System.out.println(RandomUtil.randomNumbers(ConfigurerInfo.INT_7));


//            Date begin = DateUtil.beginOfQuarter(new Date());
//            Date end = DateUtil.endOfQuarter(new Date());
//            System.out.println( "--------------------------"+begin+ "--" + end + "----------------------------------");
//
//
//            String str = "[\"1\",\"2\"]";
//            System.out.println( "--------------------------"+str+"------------------------------------");
//            List<String> list = JsonUtils.getInstance().parseObject(str, ArrayList.class);
//            System.out.println( "--------------------------"+list+"------------------------------------");


            // 日期格式处理
//            Date currentTime = new Date();
//            System.out.println( "--------------------------"+currentTime+"------------------------------------");
//            Calendar calendar = Calendar.getInstance();
//            calendar.set(Calendar.DAY_OF_MONTH, 1);
//            System.out.println( "--------------------------"+calendar.getTime()+"------------------------------------");
//            //DateTime parse = DateUtil.parse(endTime, "yyyy-MM-dd");
//            calendar.add(Calendar.DAY_OF_MONTH, 1);
//            System.out.println( "--------------------------"+calendar.getTime()+"------------------------------------");
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//            String formDate = sdf.format(calendar.getTime());
//            System.out.println( "--------------------------"+formDate+"------------------------------------");

        } else if ("test1".equals(s)) {
            // 获取当前时间
            Calendar calendarToDay = Calendar.getInstance();
            System.out.println("1--------------------------month:" + calendarToDay + "------------------------------------");
            // 获取当前月份的第一天
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);

            // 循环当前时间前的日期，对比list中是否有数据
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formDate = sdf.format(calendar.getTime());
            System.out.println("2--------------------------formDate:" + formDate + "------------------------------------");

            int i = 0;
            // 从第一天递增，直到与当前日期为同一天跳出循环
            while (!calendarToDay.getTime().before(calendar.getTime())) {
                i++;
                System.out.println("3--------------------------" + sdf.format(calendar.getTime()) + "------------------------------------");
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                System.out.println("3--------------------------" + sdf.format(calendar.getTime()) + "------------------------------------");
                if (i > 30) {
                    break;
                }
            }

        } else if ("test2".equals(s)) {


            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, 0);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            String formDate = sdf.format(calendar.getTime());
            System.out.println("1------------" + formDate + "-------------");
            calendar.add(Calendar.MONTH, 1);
            formDate = sdf.format(calendar.getTime());
            System.out.println("2------------" + formDate + "-------------");
        } else if ("test4".equals(s)) {

            // Date tmp = DateUtil.parse("2022-05-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
            Date tmp = DateUtil.parse("2022-05-01", "yyyy年MM月dd日");
            System.out.println("2------------" + tmp + "-------------");
            Date begin = DateUtil.beginOfQuarter(tmp);
            System.out.println("2------------" + begin + "-------------");
            Date end = DateUtil.endOfQuarter(tmp);
            System.out.println("2------------" + end + "-------------");


        } else if ("test5".equals(s)) {
            String a = "0.06";
            BigDecimal a1 = new BigDecimal("1");
            BigDecimal b1 = new BigDecimal("0.06");
            BigDecimal b = new BigDecimal("100");
            BigDecimal d = b.divide(a1.add(b1), 2, RoundingMode.HALF_UP);

            System.out.println("2------------" + b + "-------------");
        } else if ("test6".equals(s)) {

            // 金额逗号标记
            int a = 123123131;
            String t = String.format("%,d", a);
            System.out.println(t);
            // 截取整数部分
            BigDecimal b1 = new BigDecimal("1880.06");
            b1 = b1.setScale(0, BigDecimal.ROUND_DOWN);
            System.out.println(b1);

            DecimalFormat slFormat = new DecimalFormat("######0.00");
            String value = slFormat.format(Double.parseDouble("1010.3383"));
            System.out.println(value);


        } else if ("A".equals(s)) {
            String url = "https://fpsjb.sichuan.chinatax.gov.cn:7012/api?action=getDoc&code=051001800311_72379571_20200416_I1C1F7C1&type=3";
            byte[] ofd_byte = HttpsUtils.doGet(url);
            System.out.println("1 -----:" + ofd_byte);
        } else if ("B".equals(s)) {


            String month = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M).format(new Date());
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.DAY_OF_MONTH, 1);


            String calendarToDay = String.valueOf(calendar.getMaximum(Calendar.DAY_OF_MONTH));
            System.out.println("calendarToDay-----:" + month + "-" + calendarToDay);

            SimpleDateFormat sdf = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_YMD);
            Date maxDate = sdf.parse(month + "-" + calendarToDay);
            System.out.println("maxDate-----:" + maxDate);
            System.out.println("calendar.getTime()-----:" + calendar.getTime());


            // 下月第一天
            Calendar nextMonthFirst = Calendar.getInstance();
            nextMonthFirst.add(Calendar.MONTH, 1);
            System.out.println("nextMonthFirst-----:" + nextMonthFirst.getTime());


            // 当月最后一天
            Calendar lastDateMonth = Calendar.getInstance();
            final int lastDay = lastDateMonth.getActualMaximum(Calendar.DAY_OF_MONTH);
            lastDateMonth.set(Calendar.DAY_OF_MONTH, lastDay);
            System.out.println("lastDateMonth-----:" + lastDateMonth.getTime());

            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, Integer.parseInt("2022"));
            cal.set(Calendar.MONTH, Integer.parseInt("07") - 1);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DATE));
            System.out.println("lastDateMonth2-----:" + new SimpleDateFormat("yyyy-MM-dd").format(Calendar.getInstance().getTime()));


            Calendar nextMonthFirst1 = Calendar.getInstance();
            nextMonthFirst1.add(Calendar.MONTH, 1);
            nextMonthFirst1.set(Calendar.DAY_OF_MONTH, 0);
            //nextMonthFirst.add(Calendar.MONTH, 0);
            System.out.println("nextMonthFirst1-----:" + lastDateMonth.getTime());

            // 手机正则校验
            String phone = "13111111111";
            String regex = "^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(17[013678])|(18[0,5-9]))\\d{8}$";
            if (phone.length() != 11) {
                System.out.println("手机号应为11位数");
            } else {
                Pattern p = Pattern.compile(regex);
                Matcher m = p.matcher(phone);
                boolean isMatch = m.matches();
                if (isMatch) {
                    System.out.println("1 -----:手机号格式正确");
                } else {
                    System.out.println("1 -----:手机号格式错误");
                }
            }
        } else if ("C".equals(s)) {
            String auth = "0c7874d4b177:MzAwMjQ0MzAzODQwMzUxNTExNTA4MTQ2MjI3MDcyODYwMTG";
            byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("ISO-8859-1")));
            String authHeader = "Basic " + new String(encodedAuth);
            System.out.println("---------" + authHeader);
        } else if ("D".equals(s)) {

            String n = "0.015";
            System.out.println("---------" + n.length());
            String slStr = new BigDecimal(n).multiply(new BigDecimal("100")).setScale((n.length() - 4) > 0 ? n.length() - 4 : 0, BigDecimal.ROUND_DOWN).toString() + "%";
            System.out.println("---------" + slStr);

            String n1 = "0.01";
            String slStr1 = new BigDecimal(n1).multiply(new BigDecimal("100")).setScale((n1.length() - 4) > 0 ? n1.length() - 4 : 0, BigDecimal.ROUND_DOWN).toString() + "%";
            System.out.println("---------" + slStr1);
            System.out.println("---------" + n1.length());
        } else if ("E".equals(s)) {
            Date begin = DateUtil.parse("2022-07", OrderInfoContentEnum.DATE_FORMAT_DATE);
            System.out.println("---------" + begin);
        } else if ("F".equals(s)) {
            Map map = new HashMap<>();
            map.put("s", "[\"<EMAIL>\",\"<EMAIL>\"]");
            ArrayList arrayList = JsonUtils.getInstance().parseObject(map.get("s").toString(), ArrayList.class);
            String b = JsonUtils.getInstance().toJsonString(arrayList);
            System.out.println("---------" + b);

        } else if ("G".equals(s)) {

            // 日期转14位数字
            long time = new Date().getTime();
            System.out.println("" + time);
            Date dates = new Date(time);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");
            String times = sdf.format(dates);
            System.out.println(times);

            System.out.println("当前时间：" + (new SimpleDateFormat("yyyyMMddhhmmss").format(new Date())));
            // 本机ip
            String hostAddress = Inet4Address.getLocalHost().getHostAddress();
            System.out.println("本机ip：" + hostAddress);

            String.valueOf(org.apache.commons.lang3.RandomUtils.nextLong(0, 31));
            System.out.println("32:：" + hostAddress);

        } else if ("H".equals(s)) {
            String qdfphm = "22442000000921350956";
            String ofdUrl = "https://dppt99.guangdong.chinatax.gov.cn:8443/v/2_22442000000921350956_202208181525234618ED847";
            String content = "【标普云】尊敬的客户您好，发票号码：" + qdfphm + "已经开票成功，下载地址：" + ofdUrl;
            System.out.println("----手机短信----");
            System.out.println(content);
        } else if ("i".equals(s)) {
            Map req = new HashMap();
            req.put("taxNo", "91440184769514916M");
            req.put("flag", "false");
            try {
                String result = HttpClientUtil.doGet("http://*************:8501/user-base/user/getAuthNameByTaxno", req);
                System.out.println(result);
            } catch (Exception e) {
                System.out.println("1");
            }

        } else if ("J".equals(s)) {

            String templateName = "email_fpts.ftl";

            Map model = new HashMap();
            model.put("fphm", "123");
            model.put("fpdm", "123");
            model.put("ghfmc", "123");
            model.put("xhfmc", "123");
            model.put("kprq", "123");
            model.put("ofdurl", "123");
            model.put("fpje", "123");
            FreeMarkerUtil freeMarkerUtil = new FreeMarkerUtil();

            String content = freeMarkerUtil.processTemplate(templateName, model);
            System.out.println("content:" + content);

        } else if ("20221009".equals(s)) {

            String str1 = "";

            List list = new ArrayList();
            list.add("1");
            list.add("2");
            list.forEach(st -> {
                if (StringUtils.isNotEmpty(st.toString())) {
                    System.out.println("11");
                } else {
                    System.out.println("22");
                }
            });

            list.forEach(st -> {
                System.out.println(StringUtils.isNotEmpty(st.toString()) ? "33" : "44");
            });
        } else if ("20221018".equals(s)) {
            int i = 41;
            int j = i / 10; // 商
            int k = i % 10; // 余数
            System.out.println("i: " + j);
            System.out.println("k: " + k);

        } else if ("20221020".equals(s)) {
            String resp = "{\"total\":\"2\",\"current\":\"1\",\"pages\":\"1\",\"size\":\"10\",\"optimizeCountSql\":true,\"records\":[{\"xgrmc\":\"邓敏\",\"yyzt\":\"0\",\"nsrsbh\":\"91441900797773851T\",\"lrrmc\":\"邓敏\",\"xgrq\":\"2022-10-21 16:59:17\",\"nsrmc\":\"东莞康视达自动化科技有限公司\",\"lrrq\":\"2022-10-21 16:59:17\",\"sjlx1\":\"string\",\"fjysxmmc\":\"20221021Fj\",\"uuid\":\"58ec8db3d35541e8a7eacb40455f916a\",\"yxbz\":\"Y\"},{\"xgrmc\":\"邓敏\",\"yyzt\":\"1\",\"nsrsbh\":\"91441900797773851T\",\"lrrmc\":\"邓敏\",\"xgrq\":\"2022-09-29 14:44:56\",\"nsrmc\":\"东莞康视达自动化科技有限公司\",\"lrrq\":\"2022-09-29 14:44:56\",\"sjlx1\":\"date\",\"fjysxmmc\":\"附加\",\"uuid\":\"a317a27c1cab483dadf59b82992da8bb\",\"yxbz\":\"Y\"}],\"searchCount\":true,\"orders\":[]}";

            AdditionElemrntQueryRes additionElemrntQueryRes = JsonUtils.getInstance().parseObject(resp, AdditionElemrntQueryRes.class);
            System.out.println(additionElemrntQueryRes);
        } else if ("20221026".equals(s)) {
            // 其他
            String resp = " {\"current\":1,\"records\":[{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001954176\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 14:04:54\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001954176\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001948191\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 12:18:55\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001948191\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001950216\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 12:07:36\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001950216\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001943343\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 11:09:11\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001943343\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001942354\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":1.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 11:00:12\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001942354\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001942445\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 10:59:02\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001942445\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001941164\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 10:45:34\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001941164\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001940706\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":-2.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-09 10:33:43\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001940706\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001920333\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":-1.0,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-08 17:24:05\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001920333\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001782352\",\"gmfmc\":\"深圳刷宝科技有限公司\",\"fplyDm\":\"2\",\"hjje\":-0.8,\"gmfnsrsbh\":\"914403000789964262\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-04 20:09:16\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001782352\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001767969\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":-1.2,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-04 15:42:11\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001767969\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001767580\",\"gmfmc\":\"深圳标普云科技有限公司\",\"fplyDm\":\"2\",\"hjje\":-1.1,\"gmfnsrsbh\":\"91440300MA5EF4Q15E\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-04 15:41:44\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001767580\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440605MA4UX1R653\",\"fpdm\":\"\",\"hjse\":0.0,\"qdfphm\":\"22442000000001764763\",\"gmfmc\":\"李帛润\",\"fplyDm\":\"2\",\"hjje\":-1.3,\"gmfnsrsbh\":\"\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市云潮信息科技有限公司\",\"fplxDm\":\"82\",\"kprq\":\"2022-11-04 15:16:58\",\"fpztDm\":\"01\",\"fphm\":\"22442000000001764763\"}],\"pages\":1,\"size\":50}\n";

            // HQ
            String resp2 = "{\"total\":\"7\",\"current\":\"1\",\"pages\":\"1\",\"size\":\"50\",\"optimizeCountSql\":true,\"records\":[{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001659586\",\"sflzfp\":\"Y\",\"gmfmc\":\"佛山市顺德区罗林记美食店\",\"fplyDm\":\"2\",\"hjje\":\"1000\",\"gmfnsrsbh\":\"92440606MAC2TLER6R\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-11-01 16:24:56\",\"fpztDm\":\"01\",\"fphm\":\"\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001660050\",\"sflzfp\":\"Y\",\"gmfmc\":\"广东青桔新能源汽车有限公司\",\"fplyDm\":\"2\",\"hjje\":\"1200\",\"gmfnsrsbh\":\"91440604MA53Y49P77\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-11-01 16:19:07\",\"fpztDm\":\"01\",\"fphm\":\"\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001660325\",\"sflzfp\":\"Y\",\"gmfmc\":\"佛山市豪美鑫物业科技有限公司\",\"fplyDm\":\"2\",\"hjje\":\"300\",\"gmfnsrsbh\":\"91440605MA7HTT7073\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-11-01 16:16:21\",\"fpztDm\":\"01\",\"fphm\":\"\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001659170\",\"sflzfp\":\"Y\",\"gmfmc\":\"佛山市山湖拓展培训有限公司\",\"fplyDm\":\"2\",\"hjje\":\"3000\",\"gmfnsrsbh\":\"91440605345557576U\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-11-01 16:09:16\",\"fpztDm\":\"01\",\"fphm\":\"\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001603252\",\"sflzfp\":\"Y\",\"gmfmc\":\"佛山市新供销裕丰农科技有限公司\",\"fplyDm\":\"2\",\"hjje\":\"900\",\"gmfnsrsbh\":\"91440604MA7H64762H\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-10-31 16:14:37\",\"fpztDm\":\"01\",\"fphm\":\"\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001602099\",\"sflzfp\":\"Y\",\"gmfmc\":\"佛山市顺龙美文化传媒有限公司\",\"fplyDm\":\"2\",\"hjje\":\"900\",\"gmfnsrsbh\":\"91440604MA7JB0QX26\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-10-31 16:11:54\",\"fpztDm\":\"01\",\"fphm\":\"\"},{\"gjbq\":\"1\",\"xsfnsrsbh\":\"91440604MA54FE456U\",\"hjse\":\"0\",\"qdfphm\":\"22442000000001602428\",\"sflzfp\":\"Y\",\"gmfmc\":\"佛山市兴辉达科技有限公司\",\"fplyDm\":\"2\",\"hjje\":\"3300\",\"gmfnsrsbh\":\"91440604MA4WKWTX47\",\"fppzDm\":\"02\",\"xsfmc\":\"佛山市宏昇企业服务有限公司\",\"fplxDm\":\"82\",\"kpfnsrsbh\":\"91440604MA54FE456U\",\"kprq\":\"2022-10-31 16:08:06\",\"fpztDm\":\"01\",\"fphm\":\"\"}],\"searchCount\":true,\"orders\":[]}";

            InvoiceInfoRes invoiceInfoRes = new InvoiceInfoRes();
            invoiceInfoRes = JsonUtils.getInstance().parseObject(resp, InvoiceInfoRes.class);
            System.out.println(invoiceInfoRes);

            InvoiceInfoRes invoiceInfoRes2 = new InvoiceInfoRes();
            invoiceInfoRes2 = JsonUtils.getInstance().parseObject(resp2, InvoiceInfoRes.class);
            System.out.println(invoiceInfoRes2);
        } else if ("20221027".equals(s)) {
//            Instant begin1 = Instant.now();
//            System.out.println(Thread.currentThread().getName() + "总开始：" + begin1);
//            ExecutorService executor = new ScheduledThreadPoolExecutor(10);
//            for ( int i = 0; i < 10;i++ ) {
//                executor.execute(new Thread(new Runnable() {
//
//                    @Override
//                    public void run() {
//                        Instant begin = Instant.now();
//                        System.out.println(Thread.currentThread().getName() + "开始：" + begin);
//                        threadTest();
//                        Instant end = Instant.now();
//                        System.out.println(Thread.currentThread().getName() + "结束：" + end);
//                        long millis = Duration.between(begin, end).toMillis();
//                        System.out.println(Thread.currentThread().getName() + "耗时" + millis);
//                    }
//                }));
//            }

            Instant begin = Instant.now();
            System.out.println(Thread.currentThread().getName() + "开始：" + begin);
            for (int i = 0; i < 10; i++) {
                threadTest();
            }
            Instant end = Instant.now();
            System.out.println(Thread.currentThread().getName() + "结束：" + end);
            long millis = Duration.between(begin, end).toMillis();
            System.out.println(Thread.currentThread().getName() + "耗时" + millis);
        }


    }

    public static void threadTest() {
        //System.out.println(Instant.now() + "--" + Thread.currentThread().getName());
        for (int i = 0; i < 5000; i++) {
            for (int j = 0; j < 10000; j++) {
                List<String> s1 = new ArrayList<>();
                List<String> s2 = new ArrayList<>();
                List<String> s3 = new ArrayList<>();
                List<String> s4 = new ArrayList<>();
                s1.add("1");
                s2.add("1");
                s3.add("1");
                s4.add("1");
            }

        }

    }


}
