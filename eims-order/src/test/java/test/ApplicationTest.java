package test;


import com.dxhy.EimsOrderApplication;
import com.dxhy.order.modules.service.RedInvoiceConfirmService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = EimsOrderApplication.class)
public class ApplicationTest {

    @Resource
    private RedInvoiceConfirmService redInvoiceConfirmService;


    @Test
    public void exc() {
//        String param = "{\"flag\":\"1\",\"nsrsbh\":\"91441900MA519W5D1B\",\"beginTime\":\"2022-08-01\",\"endTime\":\"2022-11-30\"}";
//        redInvoiceConfirmService.queryHzqrxxOutResult(param);

        BigDecimal bigDecimal = new BigDecimal(0);
        BigDecimal bigDecimal1 = new BigDecimal("980.00");
        System.out.println(bigDecimal1.add(bigDecimal).toPlainString());


    }



    @Test
    public void test() {
//        DateTime begin = now();
//        for (int i = 0; i < 6; i++) {
//            for (int j = 0; j < 6; j++) {
//                exc();
//            }
//            int activeCount = executor.getActiveCount();
//            while (true) {
//                if (activeCount == 0) {
//                    break;
//                }
//                System.out.println("执行中.........");
//            }
//            System.out.println("开始执行下一个循环");
//        }
//        System.out.println("耗时:"+new Duration(begin, now()).getMillis());




//        String param ="{\"com\":\"zhongtong\",\"num\":\"751697621107367\"}";
//        String customer ="5B7E61736FBD1837ABFFF1C996953E94";
//        String key = "dJltvKtQ4024";
//        String sign = StringUtils.MD51(param+key+customer);
//        HashMap params = new HashMap();
//        params.put("param",param);
//        params.put("sign",sign.toUpperCase());
//        params.put("customer",customer);
//        String resp;
//        try {
////            new HttpRequest().postData("", params, "utf-8").toString();
//            resp = HttpClientUtil.sendHttpPost("http://poll.kuaidi100.com/poll/query.do",params);
//            System.out.println(resp);
//        } catch (Exception e) {
//            // TODO Auto-generated catch block
//            e.printStackTrace();
//        }



//        try {
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
//            final Date parse = simpleDateFormat.parse("2004-02");
//
//
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(parse);
//            System.out.println(calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        String begin ="2019-06-01 10:00:00";
//
//        String end ="2019-07-30 23:00:00";
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        try {
//            Date begin1 = sdf.parse(begin);
//            Date end1 = sdf.parse(end);
//            final long time = (begin1.getTime()-end1.getTime())/ (1000*3600*24);
//            System.out.println("+===================="+time);
////            if (begin.equals(sdf.format(begin1))) {
////                System.out.println("In SimpleDateFormat checking , " + begin + " is legal time string.");
////            } else {
////                System.out.println("In SimpleDateFormat checking , " + begin + " is not legal time string.");
////            }
//            // System.out.println(realDate.toString());
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }




//        String path = Thread.currentThread().getContextClassLoader().getResource("download/aa.xls").getPath();
//        System.out.println(path);


//        String pdf = "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";
//        byte[] decode = Base64.getDecoder().decode(pdf);
//        File  file = new File("C:\\Users\\<USER>\\Desktop\\fapiao.pdf");
//
//
//
//        try {
//            FileOutputStream  fileOutputStream = new FileOutputStream(file);
//            for (int i = 0; i < decode.length; i++) {
//                fileOutputStream.write(decode[i]);
//            }
//            fileOutputStream.flush();
//            fileOutputStream.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        LocalDate start = LocalDate.of(2019, 6, 3);
//        LocalDate end = LocalDate.of(2019, 5, 30);
//        Period period = Period.between(end,start);
//        System.out.println(period.getDays());


//        Instant begin = Instant.now();
//        try {
//            Thread.sleep(3000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        long used = ChronoUnit.MILLIS.between(begin, Instant.now())/1000;
//        System.out.println("===================="+used);

//        boolean status = false;
//        try {
//            status = JdStoreUtils.uploadFile("testpdf", "evcds123", "C:\\Users\\<USER>\\Downloads\\20190419.pdf");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        System.out.println(status);


//        for (int i = 0; i < 137; i++) {
//            System.out.println("private static final String  ITEM_NAME_"+i+"= "+"\""+i+"\""+";");
//        }

//        String s = "2019-04-04 19:04:50".replaceAll("-","");
//        System.out.println(s.substring(0,4));
//        System.out.println(s.substring(4,6));
//        System.out.println(s.substring(6,8));



        //获取当前时间
//        int y,m,d;
//        Calendar cal= Calendar.getInstance();
//        y=cal.get(Calendar.YEAR);
//        m=cal.get(Calendar.MONTH)+1;
//        d=cal.get(Calendar.DATE)-1;
//        if(m<10){
////            m=
//        }
//        String beginTime = y+"-"+m+"-"+d+" 00:00:00";
//        String endTime = y+"-"+m+"-"+d+" 23:59:59";
//        System.out.println(beginTime+"    "+endTime);

//        List<String> list = new ArrayList<>();
//        list.add("1");
//        list.add("2");
//        list.add("4");
//        list.add("4");
//        list.add("4");
//        list.add("5");
//
//        System.out.println("================="+list.size());
//        Iterator<String> iterator1 = list.iterator();
//        while(iterator1.hasNext()){
//            System.out.println(iterator1.next());
//            if(iterator1.next().equals("4")){
//                Iterator<String> iterator = list.iterator();
//                while(iterator.hasNext()){
//                    if(iterator.next().equals("4")){
////                        iterator.remove();
//                        list.removeIf(x1->x1.equals("4"));
//                        System.out.println("-------------"+list.size());
//                    }
//                }
//
//                list.add("222");
//            }
//
//        }
//        System.out.println("`````````````````"+list.size());
//        list.forEach(x->{
//            if (x.equals("4")){
//                System.out.println(x);
//                list.removeIf(x1->x1.equals("4"));
//                System.out.println("========="+list.size());
//            }
//        });

    }


    @Test
    public  void insertDateToDb(){
        //批量往数据库插入数据
//        INSERT INTO `data_pump_test`.`invoice_info`(`id`, `serial_num`, `jqbh`, `fpdm`, `fphm`, `fp_zldm`, `hjje`, `hjse`, `kprq`, `jym`, `nsrsbh`, `cardno`, `fp_mw`, `kplx`, `ghf_nsrsbh`, `ghf_nsrmc`, `ghf_dzdh`, `ghf_yhzh`, `kpxm`, `kpr`, `skr`, `fhr`, `yfpdm`, `yfphm`, `hptzdbh`, `qdbz`, `bz`, `xsf_nsrsbh`, `xsf_nsrmc`, `xsf_dzdh`, `xsf_yhzh`, `jshjje`, `sl`, `bsqs`, `syh`, `dd_hjje`, `dd_jshj`, `dd_hjse`, `slv`, `sxh`, `zfbz`, `zfsj`, `repair_flag`, `bmbbh`, `insert_time`, `bl_field1`, `bl_field2`, `YYSBZ`, `dkbz`, `ydbs`, `dkbdbs`, `dknsrsbh`, `dkqymc`)
//        VALUES (5, '150001193702062285BAIDU2019040800000004', '661616316001', '150003528888', '72080404', '51', 400.00, 64.00, '2019-04-08 11:29:27', '77188642822621120038', '150001193702062285', '0', 'PDY3OD4rMy08Pj43OSotKzk0PjwxPC00PD4wNS0wLT45OTE+OTA3Nzg8OCozKjA1Nzg4MDU4OSorLzU2OSs8NyorKzY8NS04Nzk5NT42OTw5NTw3NC85Mi0+MSowPDg0MzU+PDg4Ny82KjUr', '0', '914403005615217119', '压力测试百度国际科技（深圳）有限公司', '北京海淀1', '华夏银行1 3332478615679871', '*谷物*001111', '金开二', '金收二', '金复二', NULL, NULL, NULL, 'N', '批量导入', '150001193702062285', '150001193702062285-小企业1', '北京市海淀区中关村南大街18号1 01012345671', '中国建设银行1 6332478615679871', 464, '0.16', '1', '00000709', 400.00, 464.00, 64.00, NULL, '4', '0', NULL, '0', '31.0', '2019-04-08 11:22:54', '0', NULL, '0000000000', NULL, NULL, NULL, NULL, NULL);

    }

}
