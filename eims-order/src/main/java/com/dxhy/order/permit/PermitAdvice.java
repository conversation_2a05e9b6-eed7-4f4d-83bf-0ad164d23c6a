package com.dxhy.order.permit;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * AOP权限校验
 * @author: zhangjinjing
 * @Date: 2022/7/4 14:42
 * @Version 1.0
 */
@Aspect
@Component
@Slf4j
public class PermitAdvice {

    @Value("${sso.getTenantIdUrl}")
    private String getTenantIdUrl;

    @Resource
    private TenantRdsService tenantRdsService;

    /**
     * 定义一个切点：所有被PostMapping注解修饰的方法会织入advice
     * 也可以针对路径定义切点
     * @Pointcut("execution(* com.mutest.controller..*.*(..))")
     *
     **/
    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    private void permitAdvicePointcutPost() {}

    @Around("permitAdvicePointcutPost()")
    public Object aroundPermitAdvicePointcutPost(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();


        if (StringUtils.isNotBlank(request.getParameter("baseNsrsbh"))) {
            String baseNsrsbh = request.getParameter("baseNsrsbh");
            String changeTenantResult = tenantRdsService.switchRdsBytaxNo(baseNsrsbh);
            if(StringUtils.isNotEmpty(changeTenantResult)){
                log.debug("纳税人识别号:{}租户身份认证失败: {}", baseNsrsbh,changeTenantResult);
                return R.error("租户身份认证失败: " + changeTenantResult);
            }
            return joinPoint.proceed();
        }

        // 1 校验身份/查询租户编码/令牌续期
        String auth = request.getHeader("Authorization");
        log.debug("get Authorization: {}", auth);
        if(StringUtils.isEmpty(auth)){
            return R.error("身份校验未通过");
        }
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", auth);
        String checkPermitResponseBodyStr = "";
        String tenantId = "";
        try{
            ResponseEntity<String> checkPermitResponseEntity = restTemplate.exchange(
                    this.getTenantIdUrl,
                    HttpMethod.POST,
                    new HttpEntity<>(null, httpHeaders),
                    String.class);
            checkPermitResponseBodyStr = checkPermitResponseEntity.getBody();
            JSONObject checkPermitResponseBody = JSONObject.parseObject(checkPermitResponseBodyStr);
            String code = checkPermitResponseBody.getString("code");
            if(StringUtils.isEmpty(code) || !"0".equals(code)){
                log.debug("身份校验失败: {}", checkPermitResponseBodyStr);
                return R.error("身份校验失败");
            }
            tenantId = checkPermitResponseBody.getString("data");
        }catch (Exception e){
            log.error("auth error,: {}", checkPermitResponseBodyStr, e);
            return R.error("身份校验异常");
        }
        // 2 切换租户
        //  tenantId  改为税号,2023-01-04
        if(StringUtils.isEmpty(tenantId)){
            log.debug("身份认证失败: {}", checkPermitResponseBodyStr);
            return R.error("身份认证失败");
        }
        log.info("token对应的tenantId为： {}", tenantId);
        String changeTenantResult = tenantRdsService.switchRds(tenantId);
        if(StringUtils.isNotEmpty(changeTenantResult)){
            log.debug("租户身份认证失败: {}", changeTenantResult);
            return R.error("租户身份认证失败: " + changeTenantResult);
        }
        // 3 进入controller
        return joinPoint.proceed();
    }

}
