package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.RedInvoiceConfirmEntity;
import com.dxhy.order.modules.entity.RedInvoiceConfirmSldh;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.modules.service.RedInvoiceConfirmService;
import com.dxhy.order.pojo.RedConfirmHandle;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;


/**
 * 红字申请单
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 11:39:24
 */
@RestController
@RequestMapping("redInvoiceConfirm")
@Slf4j
@Api(tags = "红字申请单")
public class RedInvoiceConfirmController {
    @Autowired
    private RedInvoiceConfirmService redInvoiceConfirmService;
    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;

    /**
     * 列表
     */
    @ApiOperation("红字发票确认信息查询")
    @PostMapping("/list")
    public R list(@RequestBody RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        PageUtils page = redInvoiceConfirmService.queryPage(redInvoiceConfirmEntity);
        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") String id) {
        RedInvoiceConfirmEntity additionElement = redInvoiceConfirmService.getById(id);
        return R.ok().put("additionElement", additionElement);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        redInvoiceConfirmService.save(redInvoiceConfirmEntity);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        redInvoiceConfirmService.updateById(redInvoiceConfirmEntity);//全部更新
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody String[] ids) {
        redInvoiceConfirmService.removeBatchByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 红字发票确认信息-撤销
     */
    @ApiOperation("红字发票确认信息-撤销")
    @PostMapping("/redInvoiceConfirmBackOut")
    public R redInvoiceConfirmBackOut(@RequestBody IdDTO idDTO) {
        log.info("红字发票确认信息-撤销:{}", idDTO);
        return redInvoiceConfirmService.redInvoiceConfirmBackOut(idDTO.getId());
    }

    /**
     * 红字发票记录
     */
    @ApiOperation("红字发票记录")
    @PostMapping("/selectRedInvoiceRecord")
    public R selectRedInvoiceRecord(@RequestBody RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        PageUtils page = redInvoiceConfirmService.selectRedInvoiceRecord(redInvoiceConfirmEntity);
        return R.ok().put("page", page);
    }

    /**
     * 红字发票记录-查看
     */
    @ApiOperation("红字发票记录-查看")
    @PostMapping("/selectRedInvoiceRecordView")
    public R selectRedInvoiceRecordView(@RequestBody IdDTO idDTO) {
        return redInvoiceConfirmService.selectRedInvoiceRecordView(idDTO.getId());
    }

    /**
     * 新增红字发票确认信息-查看确认单详情
     */
    @ApiOperation("新增红字发票确认信息-查看确认单详情")
    @PostMapping("/redInvoiceConfirmDetail")
    public R redInvoiceConfirmDetail(@RequestBody IdDTO idDTO) {
        log.info("新增红字发票确认信息-查看确认单详情:{}", idDTO);
        return redInvoiceConfirmService.redInvoiceConfirmDetail(idDTO.getId());
    }

    /**
     * 新增红字发票确认信息-查看确认单详情-撤销
     */
    @ApiOperation("新增红字发票确认信息-查看确认单详情-撤销")
    @PostMapping("/redInvoiceConfirmCancell")
    public R redInvoiceConfirmCancell(@RequestBody IdDTO idDTO) {
        log.info("新增红字发票确认信息-查看确认单详情:{}", idDTO.getId());
        return R.ok();
    }

    /**
     * 新增红字发票确认信息-去开红字发票/去开票
     */
    @ApiOperation("新增红字发票确认信息-去开红字发票-查询")
    @PostMapping("/redInvoiceConfirmIssueQuery")
    public R redInvoiceConfirmIssueQuery(@RequestBody IdDTO idDTO) {
        log.info("新增红字发票确认信息-去开红字发票-查询:{}", idDTO);
        return redInvoiceConfirmService.redInvoiceConfirmIssueQuery(idDTO.getId());
    }

    /**
     * 新增红字发票确认信息-去开红字发票-开具发票
     */
    @ApiOperation("新增红字发票确认信息-去开红字发票-开具发票")
    @PostMapping("/redInvoiceConfirmIssue")
    public R redInvoiceConfirmIssue(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return redInvoiceConfirmService.redInvoiceConfirmIssue(orderInvoiceInfoEntity);
    }

    /**
     * 红字发票-统计
     */
    @ApiOperation("红字发票-统计")
    @PostMapping("/redInvoiceConfirmStatistics")
    public R redInvoiceConfirmStatistics() {
        return redInvoiceConfirmService.redInvoiceConfirmStatistics();
    }

    /**
     * 选择票据-抽取发票信息
     */
    /*@ApiOperation("选择票据-抽取发票信息")
    @PostMapping("/getInvoiceInfosAndItems")
    public R getInvoiceInfosAndItems(){
        return redInvoiceConfirmService.getAllInvoiceInfosAndItems();
    }*/

    /**
     * 选择票据-条件查询
     */
    @ApiOperation("选择票据-条件查询")
    @PostMapping("/selectRedInvoiceConfirm")
    public R selectRedInvoiceConfirm(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        PageUtils page = redInvoiceConfirmService.selectRedInvoiceConfirm(orderInvoiceInfoEntity);
        return R.ok().put("page", page);
    }

    /**
     * 选择票据-发票预览
     * 蓝票信息
     */
    @ApiOperation("选择票据-发票预览")
    @PostMapping("/redInvoiceConfirmPreview")
    public R redInvoiceConfirmPreview(@RequestBody IdDTO idDTO) {
        log.info("红字发票确认信息-发票预览:{}", idDTO);
        return orderInvoiceInfoService.orderInvoiceDetail(idDTO);
    }

    /**
     * 选择票据-选择
     */
    @ApiOperation("选择票据-选择")
    @PostMapping("/redInvoiceConfirmChoose")
    public R redInvoiceConfirmChoose(@RequestBody IdDTO idDTO) {
        log.info("新增红字发票确认信息-选择:{}", idDTO);
        return redInvoiceConfirmService.redInvoiceConfirmChoose(idDTO);
    }

    /**
     * 红字发票确认单提交
     */
    @ApiOperation("信息确认-提交")
    @PostMapping("/redInvoiceConfirmSubmit")
    public R redInvoiceConfirmSubmit(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        try {
            return redInvoiceConfirmService.redInvoiceConfirmSubmit(orderInvoiceInfoEntity);
        }catch (Exception e){
            return R.error(e.getMessage());
        }

    }

    /**
     * 定时拉取红字确认单信息
     */
    /*@ApiOperation("定时拉取红字确认单信息")
    @PostMapping("/pullRedInvoiceConfirmSldh")
    public R pullRedInvoiceConfirmSldh(){
        return redInvoiceConfirmService.pullRedInvoiceConfirmSldh();
    }*/

    /**
     * 开具红票
     */
    @ApiOperation("开具红票")
    @PostMapping("/redInvoiceIssue")
    public R redInvoiceIssue(@RequestBody RedInvoiceConfirmSldh redInvoiceConfirmSldh) {
        return redInvoiceConfirmService.redInvoiceIssue(redInvoiceConfirmSldh);
    }

    /**
     * 定时拉取红票结果信息
     */
    /*@ApiOperation("定时拉取红票结果信息")
    @PostMapping("/pullRedInvoiceResultInfo")
    public R pullRedInvoiceResultInfo(){
        return redInvoiceConfirmService.pullRedInvoiceResultInfo();
    }*/

    /**
     * 红字发票确认信息-查看
     */
    @ApiOperation("红字发票确认信息-查看")
    @PostMapping("/redInvoiceConfirmView")
    public R redInvoiceConfirmView(@RequestBody IdDTO idDTO) {
        log.info("红字发票确认信息-查看:{}", idDTO);
        return redInvoiceConfirmService.redInvoiceConfirmView(idDTO.getId());
    }

    /**
     * 我发起的确认单查询
     */
    @ApiOperation("我发起的确认单查询")
    @PostMapping("/listRedInvoiceConfirm")
    public R listRedInvoiceConfirm(@RequestBody RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        PageUtils page = redInvoiceConfirmService.listRedInvoiceConfirm(redInvoiceConfirmEntity);
        return R.ok().put("page", page);
    }

    /**
     * 定时查询 我收到的确认单
     */
    /*@ApiOperation("定时查询 我收到的确认单")
    @PostMapping("/queryHzqrxx")
    public R queryHzqrxx(){
        return redInvoiceConfirmService.queryHzqrxxInfo();
    }*/

    /**
     * 我收到的确认单查询
     */
    @ApiOperation("我收到的确认单查询")
    @PostMapping("/getListRedInvoiceConfirm")
    public R getListRedInvoiceConfirm(@RequestBody RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        PageUtils page = redInvoiceConfirmService.getListRedInvoiceConfirm(redInvoiceConfirmEntity);
        return R.ok().put("page", page);
    }

    /**
     * 红字发票确认信息处理
     */
    @ApiOperation("红字发票确认信息处理")
    @PostMapping("/hzqrxxUpdate")
    public R hzqrxxUpdate(@RequestBody RedConfirmHandle redConfirmHandle) {
        return redInvoiceConfirmService.hzqrxxUpdate(redConfirmHandle);
    }

    /**
     * 定时查询 确认单状态 02 03的
     */
    /*@ApiOperation("定时查询 02销方录入待购方确认 03购方录入待销方确认")
    @PostMapping("/queryHzqrxxStatus")
    public R queryHzqrxxStatus(){
        return redInvoiceConfirmService.queryHzqrxxStatus();
    }*/

    /**
     * 我收到的红字确认信息-查看
     */
    @ApiOperation("我收到的红字确认信息-查看")
    @PostMapping("/getRedInvoiceConfirmInfo")
    public R getRedInvoiceConfirmInfo(@RequestBody IdDTO idDTO) {
        log.info("红字发票确认信息-查看:{}", idDTO);
        return redInvoiceConfirmService.getRedInvoiceConfirmInfo(idDTO.getId());
    }

    /**
     * 红票记录-我开具的
     */
    @ApiOperation("红票记录-我开具的")
    @PostMapping("/getHadInvoiceInfos")
    public R getHadInvoiceInfos(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return redInvoiceConfirmService.getHadInvoiceInfos(orderInvoiceInfoEntity);
    }

    /**
     * 红票记录-我开具的-详情
     */
    @ApiOperation("红票记录-我开具的")
    @PostMapping("/getHadInvoiceDetail")
    public R getHadInvoiceDetail(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return redInvoiceConfirmService.getHadInvoiceDetail(orderInvoiceInfoEntity);
    }

    /**
     * 红票记录-我收到的
     */
    @ApiOperation("红票记录-我收到的")
    @PostMapping("/getReceiveInvoiceInfos")
    public R getReceiveInvoiceInfos(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return redInvoiceConfirmService.getReceiveInvoiceInfos(orderInvoiceInfoEntity);
    }

    /**
     * 红票记录-我收到的-详情
     */
    @ApiOperation("红票记录-我收到的-详情")
    @PostMapping("/getReceiveInvoiceDetail")
    public R getReceiveInvoiceDetail(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return redInvoiceConfirmService.getReceiveInvoiceDetail(orderInvoiceInfoEntity);
    }

    /**
     * 拉取发票基本信息-一年之内的数据
     */
    @ApiOperation("拉取发票信息-一年之内的数据")
    @GetMapping("/getOneYearInvoiceInfosAndItems")
    public R getOneYearInvoiceInfosAndItems(@RequestParam("nsrsbh") String nsrsbh, @RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        return redInvoiceConfirmService.getOneYearInvoiceInfosAndItems(nsrsbh, startTime, endTime);
    }

    /**
     * 确认单状态为：申请失败-重试功能
     */
    @ApiOperation("确认单状态为：申请失败-重试功能")
    @PostMapping("/retryRedInvoiceConfirmSldh")
    public R retryRedInvoiceConfirmSldh(@RequestBody IdDTO idDTO) {
        return redInvoiceConfirmService.retryRedInvoiceConfirmSldh(idDTO.getId());
    }
}
