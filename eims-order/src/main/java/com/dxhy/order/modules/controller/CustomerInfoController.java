package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.entity.CustomerInfoEntity;
import com.dxhy.order.modules.pojo.dto.*;
import com.dxhy.order.modules.service.CustomerInfoQrcodeService;
import com.dxhy.order.modules.service.CustomerInfoService;
import com.dxhy.order.utils.QrCodeUtil;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

/**
 * 客户信息模块 controller
 * <AUTHOR>
 * @Date 2022/6/27 12:26
 * @Version 1.0
 **/
@RestController
@Api(tags = "客户信息模块")
@Slf4j
@RequestMapping("/customerInfo")
public class CustomerInfoController {
    private final String modelName = "客户信息模块";

    @Value("${url.einvoiceMobileUrl}")
    private String einvoiceMobileUrl;
    @Value("${order.invoice.customerPath}")
    private String customerPath;

    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private CustomerInfoQrcodeService customerInfoQrcodeService;

    /**
     * 获取客户分类树
     */
    @PostMapping("/getGroupTree")
    @ApiOperation(value = "获取客户分类树")
    public R getGroupTree(@RequestBody @Valid BaseNsrsbhDTO baseNsrsbhDTO){
        log.info("{} - getGroupTree - {}", this.modelName, baseNsrsbhDTO);
        return customerInfoService.getGroupTree(baseNsrsbhDTO.getBaseNsrsbh());
    }

    /**
     * 保存客户分类（新增 修改）
     */
    @PostMapping("/saveCustomerGroup")
    @ApiOperation(value = "保存客户分类（新增 修改）")
    public R saveCustomerGroup(@RequestBody CustomerGroupSaveDTO customerGroupSaveDTO){
        log.info("{} - saveGroup - {}", this.modelName, customerGroupSaveDTO);
        return customerInfoService.saveCustomerGroup(customerGroupSaveDTO);
    }

    /**
     * 删除客户分类
     */
    @PostMapping("/deleteCustomerGroup")
    @ApiOperation(value = "删除客户息分类")
    public R deleteCustomerGroup(@RequestBody IdDTO idDTO){
        log.info("{} - deleteCustomerInfoGroup - {}", this.modelName, idDTO);
        return customerInfoService.deleteCustomerGroup(idDTO.getId());
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表")
    public R list(@RequestBody CustomerInfoListDTO customerInfoListDTO){
        log.info("{} - list - {}", this.modelName, customerInfoListDTO);
        return customerInfoService.queryPage(customerInfoListDTO);
    }

    /**
     * 列表 - 不选择客户分类
     */
    @PostMapping("/listWithoutId")
    @ApiOperation(value = "列表 - 不选择客户分类")
    public R listWithoutId(@RequestBody CustomerInfoListWithoutIdDTO customerInfoListWithoutIdDTO){
        log.info("{} - listWithoutId - {}", this.modelName, customerInfoListWithoutIdDTO);
        return customerInfoService.listWithoutId(customerInfoListWithoutIdDTO);
    }

    /**
     * 根据名称查询列表 - 不分页
     */
    @PostMapping("/listByNameWithoutPage")
    @ApiOperation(value = "根据名称查询列表 - 不分页")
    public R listByNameWithoutPage(@RequestBody CustomerInfoListByNameWithoutPageDTO customerInfoListByNameWithoutPageDTO){
        log.info("{} - listByNameWithoutPage - {}", this.modelName, customerInfoListByNameWithoutPageDTO);
        return customerInfoService.listByNameWithoutPage(customerInfoListByNameWithoutPageDTO);
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    @ApiOperation(value = "信息")
    public R info(@RequestBody IdDTO idDTO){
        log.info("{} - info - {}", this.modelName, idDTO);
        CustomerInfoEntity customerInfoEntity = customerInfoService.getById(idDTO.getId());
        return R.ok().put("data", customerInfoEntity);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增")
    public R save(@RequestBody CustomerInfoSaveDTO customerInfoSaveDTO){
        log.info("{} - save - {}", this.modelName, customerInfoSaveDTO);
        return customerInfoService.saveData(customerInfoSaveDTO);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody CustomerInfoSaveDTO customerInfoSaveDTO){
        log.info("{} - update - {}", this.modelName, customerInfoSaveDTO);
        return customerInfoService.saveData(customerInfoSaveDTO);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public R delete(@RequestBody IdDTO idDTO){
        log.info("{} - delete - {}", this.modelName, idDTO);
        return customerInfoService.deleteData(idDTO.getId());
    }

    /**
     * 导出
     */
    @PostMapping("/exportData")
    @ApiOperation(value = "导出")
    public void exportData(@RequestBody IdsDTO idsDTO, HttpServletResponse response){
        log.info("{} - exportData - {}", this.modelName, idsDTO);
        if(idsDTO != null && CollectionUtils.isNotEmpty(idsDTO.getIds())){
            customerInfoService.exportData(idsDTO.getIds(), response);
        }
    }

    /**
     * 新增扫码客户
     */
    @RequestMapping(path = "/saveQrcodeCustomer", method = RequestMethod.POST)
    @ApiOperation(value = "新增扫码客户")
    public R saveQrcodeCustomer(@RequestBody CustomerInfoQrcodeSaveDTO customerInfoQrcodeSaveDTO){
        log.info("{} - saveQrcodeCustomer - {}", this.modelName, customerInfoQrcodeSaveDTO);
        return customerInfoQrcodeService.saveQrcodeCustomer(customerInfoQrcodeSaveDTO);
    }

    /**
     * 扫码客户列表
     */
    @PostMapping("/listQrcodeCustomer")
    @ApiOperation(value = "扫码客户列表")
    public R listQrcodeCustomer(@RequestBody BaseNsrsbhWithPageDTO baseNsrsbhWithPageDTO){
        log.info("{} - listQrcodeCustomer - {}", this.modelName, baseNsrsbhWithPageDTO);
        return customerInfoQrcodeService.listQrcodeCustomer(baseNsrsbhWithPageDTO);
    }

    /**
     * 获取扫码地址
     */
    @PostMapping("/genQrcodeUrl")
    @ApiOperation(value = "获取扫码地址")
    public R genQrcodeUrl(@RequestBody BaseNsrsbhDTO baseNsrsbhDTO){
        log.info("{} - genQrcodeUrl - {}", this.modelName, baseNsrsbhDTO);
        String url = einvoiceMobileUrl + "?baseNsrsbh=" + baseNsrsbhDTO.getBaseNsrsbh();
        String base64 = QrCodeUtil.drawLogoQrCode(null, url, null, null);
        return R.ok().put("data", base64);
    }

    /**
     * 下载客户信息导入模板
     */
    @ApiOperation("下载客户信息导入模板")
    @PostMapping("downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("{} - downloadTemplate", this.modelName);
        try {
            byte[] bytes = FileUtils.readFileToByteArray(new File(customerPath));
            if (response != null) {
                response.setContentType("application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition", "attachment;filename=\"" + new String(("客户信息导入模板" + ".xls").getBytes("gb2312"), "ISO8859-1"));
                OutputStream out = response.getOutputStream();
                out.write(bytes);
                out.close();
            }
        } catch (IOException e) {
            log.error("下载客户信息导入模板异常:{}", e);
        }
    }

    /**
     * 上传客户信息
     */
    @ApiOperation("上传客户信息")
    @PostMapping("uploadCustomerExcel")
    public R uploadCustomerExcel(@RequestParam("file") MultipartFile file) {
        log.info("{} - uploadCustomerExcel", this.modelName);
        return customerInfoService.uploadCustomerExcel(file);
    }

    /**
     * 保存Excel上传的客户信息
     */
    @PostMapping("/saveDataFromExcel")
    @ApiOperation(value = "保存Excel上传的客户信息")
    public R saveDataFromExcel(@RequestBody CustomerInfoExcelSaveDTO customerInfoExcelSaveDTO){
        log.info("{} - saveDataFromExcel - {}", this.modelName, customerInfoExcelSaveDTO);
        return customerInfoService.saveDataFromExcel(customerInfoExcelSaveDTO);
    }

    /**
     * 将Excel错误的数据导出
     */
    @PostMapping("/genExcelForErrorData")
    @ApiOperation(value = "将Excel错误的数据导出")
    public void genExcelForErrorData(@RequestBody CustomerInfoExcelSaveDTO customerInfoExcelSaveDTO, HttpServletResponse response){
        log.info("{} - genExcelForErrorData - {}", this.modelName, customerInfoExcelSaveDTO);
        customerInfoService.genExcelForErrorData(customerInfoExcelSaveDTO, response);
    }

}
