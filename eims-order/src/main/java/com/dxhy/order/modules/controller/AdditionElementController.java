package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.entity.AdditionElementSaveDTO;
import com.dxhy.order.modules.pojo.dto.AdditionElementListDTO;
import com.dxhy.order.modules.pojo.dto.BaseNsrsbhDTO;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.vo.AdditionElementVO;
import com.dxhy.order.modules.service.AdditionElementService;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 附加信息模块 controller
 * <AUTHOR>
 * @Date 2022/6/27 12:26
 * @Version 1.0
 **/
@RestController
@RequestMapping("/additionElement")
@Slf4j
@Api(tags = "附加信息模块")
public class AdditionElementController {
    private final String modelName = "附加信息模块";
    @Autowired
    private AdditionElementService additionElementService;

    /**
     * 列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表")
    public R list(@RequestBody AdditionElementListDTO additionElementListDTO){
        log.info("{} - list - {}", this.modelName, additionElementListDTO);
        if(additionElementListDTO == null || StringUtils.isEmpty(additionElementListDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        PageUtils page = additionElementService.queryPage(additionElementListDTO);
        return R.ok().put("data", page);
    }

    /**
     * 列表 - 全部
     * ;;;
     */
    @PostMapping("/listAll")
    @ApiOperation(value = "列表 - 全部")
    public R listAll(@RequestBody BaseNsrsbhDTO baseNsrsbhDTO){
        log.info("{} - listAll - {}", this.modelName, baseNsrsbhDTO);
        if(baseNsrsbhDTO == null || StringUtils.isEmpty(baseNsrsbhDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        List<AdditionElementVO> voList = additionElementService.listAll(baseNsrsbhDTO.getBaseNsrsbh());
        return R.ok().put("data", voList);
    }

    /**
     * 根据场景模板ID查询附加信息列表
     */
    @PostMapping("/listAdditionElementBySceneTemplateId")
    @ApiOperation(value = "根据场景模板ID查询附加信息列表")
    public R listAdditionElementBySceneTemplateId(@RequestBody IdDTO idDTO){
        log.info("{} - listAdditionElementBySceneTemplateId - {}", this.modelName, idDTO);
        return additionElementService.listAdditionElementBySceneTemplateId(idDTO.getId());
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    @ApiOperation(value = "信息")
    public R info(@RequestBody IdDTO idDTO){
        log.info("{} - info - {}", this.modelName, idDTO);
        return additionElementService.queryDataById(idDTO.getId());
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增")
    public R save(@RequestBody AdditionElementSaveDTO additionElementSaveDTO){
        log.info("{} - save - {}", this.modelName, additionElementSaveDTO);
        return additionElementService.saveData(additionElementSaveDTO);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody AdditionElementSaveDTO additionElementSaveDTO){
        log.info("{} - update - {}", this.modelName, additionElementSaveDTO);
        return additionElementService.saveData(additionElementSaveDTO);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public R delete(@RequestBody IdsDTO idsDTO){
        log.info("{} - delete - {}", this.modelName, idsDTO);
        return additionElementService.deleteData(idsDTO);
    }

}
