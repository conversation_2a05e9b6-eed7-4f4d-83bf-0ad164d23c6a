package com.dxhy.order.modules.controller;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.order.modules.service.TaxpayerInfoService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.pojo.DeptInfo;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 用户中心模块 controller
 * 对接各用户中心接口用，全电系统后端调用各系统的用户接口，为前端提供统一的服务调用接口
 * <AUTHOR>
 * @Date 2022/6/27 12:26
 * @Version 1.0
 **/
@RestController
@Api(tags = "用户中心模块")
@Slf4j
@RequestMapping("/user")
public class UserController {
    private final String modelName = "用户中心模块";

    @Resource
    private SsoUtil ssoUtil;

    @Resource
    private TaxpayerInfoService taxpayerInfoService;

    @PostMapping("/getDeptInfo")
    @ApiOperation(value = "获取权限数据")
    public R getDeptInfo(@RequestBody Map<String, String> params){
        log.info("{} - getDeptInfo", this.modelName);
        String baseNsrsbh = params.get("baseNsrsbh");
        JSONObject deptInfo = ssoUtil.getDeptInfo(baseNsrsbh);
        if(deptInfo == null){
            return R.error("权限数据获取失败");
        }
        return R.ok().put("data", deptInfo);
    }

    @PostMapping("/testApi")
    @ApiOperation(value = "测试功能")
    public String testApi(){
        String userName = ssoUtil.getUserName();
        return userName;
    }

    @RequestMapping("/addDept")
    @ApiOperation(value = "门户同步企业信息")
    public R addDept(@RequestBody DeptInfo deptInfo){
        try {
            log.info("门户同步企业信息,请求参数为:{}", JsonUtils.getInstance().toJsonString(deptInfo));
            //切换到主数据源
            String dataSourceKey = DynamicDataSource.getDataSourceKey();
            log.info("当前选择的数据库为:{}",dataSourceKey);
            DynamicDataSource.setDataSourceDefault();
            return taxpayerInfoService.insertDept(deptInfo);
        }catch ( Exception e){
            log.info("门户同步企业信息,请求异常", e);
            return R.error("9999","请求异常");
        }

    }

}
