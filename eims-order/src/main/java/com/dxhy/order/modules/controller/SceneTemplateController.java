package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.dto.SceneTemplateListDTO;
import com.dxhy.order.modules.pojo.dto.SceneTemplateSaveDTO;
import com.dxhy.order.modules.service.SceneTemplateService;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 场景模板模块 controller
 * <AUTHOR>
 * @Date 2022/6/27 12:26
 * @Version 1.0
 **/
@RestController
@Api(tags = "场景模板模块")
@Slf4j
@RequestMapping("/sceneTemplate")
public class SceneTemplateController {
    private final String modelName = "场景模板模块";
    @Autowired
    private SceneTemplateService sceneTemplateService;

    /**
     * 列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表")
    public R list(@RequestBody SceneTemplateListDTO sceneTemplateListDTO){
        log.info("{} - list - {}", this.modelName, sceneTemplateListDTO);
        return sceneTemplateService.queryPage(sceneTemplateListDTO);
    }

    /**
     * 列表 - 不分页
     */
    @PostMapping("/listWithoutPage")
    @ApiOperation(value = "列表 - 不分页")
    public R listWithoutPage(@RequestBody SceneTemplateListDTO sceneTemplateListDTO){
        log.info("{} - listWithoutPage - {}", this.modelName, sceneTemplateListDTO);
        return sceneTemplateService.listWithoutPage(sceneTemplateListDTO);
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    @ApiOperation(value = "信息")
    public R info(@RequestBody IdDTO idDTO){
        log.info("{} - info - {}", this.modelName, idDTO);
        return sceneTemplateService.queryDataById(idDTO.getId());
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增")
    public R save(@RequestBody SceneTemplateSaveDTO sceneTemplateSaveDTO){
        log.info("{} - save - {}", this.modelName, sceneTemplateSaveDTO);
        return sceneTemplateService.saveData(sceneTemplateSaveDTO);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody SceneTemplateSaveDTO sceneTemplateSaveDTO){
        log.info("{} - update - {}", this.modelName, sceneTemplateSaveDTO);
        return sceneTemplateService.saveData(sceneTemplateSaveDTO);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public R delete(@RequestBody IdsDTO idsDTO){
        log.info("{} - delete - {}", this.modelName, idsDTO);
        return sceneTemplateService.deleteData(idsDTO);
    }

}
