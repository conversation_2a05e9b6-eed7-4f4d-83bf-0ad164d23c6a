package com.dxhy.order.modules.controller;


import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.constant.RedisConstant;
import com.dxhy.order.modules.entity.ColumnQueryReq;
import com.dxhy.order.modules.service.RedisService;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 列配置
 * @Date 2022/9/27
 **/
@RestController
@Api(tags = "列配置模块")
@Slf4j
@RequestMapping("/columnConfig")
public class ColumnConfigController {

    private final String logger= "列配置模块";

    @Resource
    RedisService redisService;


    /**
     * 查询列配置信息
     */
    @PostMapping("/getConfig")
    @ApiOperation(value = "获取列配置信息")
    public R getConfig(HttpServletRequest request, @RequestBody ColumnQueryReq columnQueryReq){
        log.info("{},查询列配置信息，入参：{}", logger, JsonUtils.getInstance().toJsonString(columnQueryReq));
        //String userId = columnQueryReq.getUserId();
        String userId = String.valueOf(request.getAttribute("userId"));
        log.info("{},新增列配置信息，userId:{}", logger, userId);
        String key = String.format(RedisConstant.COLUMNCONFIG, userId, columnQueryReq.getPageMark());
        log.info("{},查询列配置信息，key值：{}", logger, key);

        String scKey = String.format(RedisConstant.COLUMNCONFIG, userId, columnQueryReq.getPageMarkInfo());
        log.info("{},查询筛选条件配置信息，key值：{}", logger, scKey);

        String value = redisService.get(key);
        String scValue = redisService.get(scKey);
        R r = R.ok();
        if (StringUtils.isNotBlank(scValue)) {
            log.info("{},查询筛选条件配置信息，key存在返回：{}", logger, scValue);
            r.put("scValue", scValue);
        }else {
            r.put("scValue","");
        }
        if (StringUtils.isNotBlank(value)) {
            log.info("{},查询列配置信息，key存在返回：{}", logger, value);
            r.put(OrderManagementConstant.DATA, value);
            return r;
        }
        log.info("{},查询列配置信息，key不存在", logger);
        return R.error();
    }


    /**
     * 新增列配置信息
     */
    @PostMapping("/addConfig")
    @ApiOperation(value = "新增列配置信息")
    public R addConfig(HttpServletRequest request, @RequestBody ColumnQueryReq columnQueryReq){
        log.info("{},新增列配置信息，入参：{}", logger, JsonUtils.getInstance().toJsonString(columnQueryReq));
        //String userId = columnQueryReq.getUserId();
        String userId = String.valueOf(request.getAttribute("userId"));
        log.info("{},新增列配置信息，userId:{}", logger, userId);
        String key = String.format(RedisConstant.COLUMNCONFIG, userId, columnQueryReq.getPageMark());
        log.info("{},新增列配置信息，key值：{}", logger, key);
        String scKey = String.format(RedisConstant.COLUMNCONFIG, userId, columnQueryReq.getPageMarkInfo());
        log.info("{},新增筛选条件配置信息，key值：{}", logger, scKey);
        // 列配置信息
        boolean b = redisService.set(key, columnQueryReq.getConfigInfo());
        //筛选条件配置信息
        boolean sc = redisService.set(scKey, columnQueryReq.getScreenConditionInfo());
        if (sc) {
            log.info("{},新增筛选条件配置信息，新增完成：{}", logger, scKey + ":" +  scKey);
        }
        if (b) {
            log.info("{},新增列配置信息，新增完成：{}", logger, key + ":" +  columnQueryReq.getConfigInfo());
            return R.ok();
        }
        log.info("{},新增列配置信息，新增失败", logger);
        return R.error();
    }
}
