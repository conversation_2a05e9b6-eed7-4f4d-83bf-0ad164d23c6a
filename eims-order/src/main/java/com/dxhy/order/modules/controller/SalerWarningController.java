package com.dxhy.order.modules.controller;

import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.entity.InvoiceConfigInfo;
import com.dxhy.order.modules.service.InvoiceConfigService;
import com.dxhy.order.modules.service.SalerWarningService;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 基础配置（发票预警）
 *
 * <AUTHOR>
 * @email
 * @date 2022-07-21
 */
@Api(tags = "配置（基础配置-开票配置）")
@RestController
@RequestMapping("saler")
public class SalerWarningController {

    @Autowired
    private SalerWarningService salerWarningService;

    @Autowired
    private InvoiceConfigService invoiceConfigService;

    /**
     * 发票预警配置 - 查询
     * 没有数据则为0，否则则返回数据
     */
    @ApiOperation("发票预警配置--查询")
    @PostMapping("/queryWarnInfo")
    public R queryWarnInfo(@RequestBody Map map) {
        Map mapResp = salerWarningService.queryWarnInfo(map);
        return R.ok().put(OrderManagementConstant.DATA, mapResp);
    }

    /**
     * 发票预警配置 - 修改
     */
    @ApiOperation("发票预警配置--修改")
    @PostMapping("/updateWarnInfo")
    public R updateWarnInfo(@RequestBody Map map) {
        R r = salerWarningService.updateWarnInfo(map);
        return r;
    }

    /**
     * 开票配置 - 查询
     */
    @ApiOperation("开票配置--查询")
    @PostMapping("/queryInvoiceConfig")
    public R queryInvoiceCconfig(@RequestBody InvoiceConfigInfo invoiceConfigInfo) {
        R r = invoiceConfigService.queryList(invoiceConfigInfo);
        return r;
    }

    /**
     * 开票配置 - 开票人列表查询
     */
    @ApiOperation("开票配置--查询")
    @PostMapping("/queryPersons")
    public R queryPersons(@RequestBody InvoiceConfigInfo invoiceConfigInfo) {
        R r = invoiceConfigService.queryPersons(invoiceConfigInfo);
        return r;
    }

    /**
     * 开票配置 - 新增
     */
    @ApiOperation("开票配置--新增")
    @PostMapping("/addInvoiceConfig")
    public R addInvoiceCconfig(@RequestBody InvoiceConfigInfo invoiceConfigInfo) {
        R r = invoiceConfigService.insertInvoiceConfig(invoiceConfigInfo);
        return r;
    }

    /**
     * 开票配置 - 编辑
     */
    @ApiOperation("开票配置--编辑")
    @PostMapping("/updateInvoiceConfig")
    public R updateInvoiceConfig(@RequestBody InvoiceConfigInfo invoiceConfigInfo) {
        R r = invoiceConfigService.updateInvoiceConfig(invoiceConfigInfo);
        return r;
    }

    /**
     * 开票配置 - 删除
     */
    @ApiOperation("开票配置--删除")
    @PostMapping("/delInvoiceConfig")
    public R delInvoiceConfig(@RequestBody InvoiceConfigInfo invoiceConfigInfo) {
        R r = invoiceConfigService.delInvoiceConfig(invoiceConfigInfo);
        return r;
    }

    /**
     * 开票配置 - 批量删除
     */
    @ApiOperation("开票配置--删除")
    @PostMapping("/delMoreInvoiceConfig")
    public R delMoreInvoiceConfig(@RequestBody Map map) {
        R r = invoiceConfigService.delMoreInvoiceConfig(map);
        return r;
    }

    /**
     * 开票配置 - 默认账户勾选校验
     */
    @ApiOperation("开票配置--检查默认账户")
    @PostMapping("/checkHadConfig")
    public R checkHadConfig(@RequestBody InvoiceConfigInfo invoiceConfigInfo) {
        R r = invoiceConfigService.checkHadConfig(invoiceConfigInfo);
        return r;
    }

//    /**
//     * 定时任务对已开发票额度进行预警计算
//     */
//    @ApiOperation("定时任务预警提醒")
//    @Scheduled(cron = "${order.invoice.warningTask}")
//    public void warningTask(){
//        salerWarningService.warningTask();
//    }


}
