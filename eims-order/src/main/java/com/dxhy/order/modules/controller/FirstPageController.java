package com.dxhy.order.modules.controller;

import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.entity.FirstPageChartsEntity;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


/**
 * 全电概览
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-23 18:53:24
 */
@Api(tags = "全电概览")
@RestController
@RequestMapping("firstPage")
public class FirstPageController {

    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;

    /**
     *  全电概览 - 本月数据概览
     */
    @ApiOperation("全电概览--本月数据概览")
    @PostMapping("/queryMonthData")
    public R queryMonthData(@RequestParam String baseNsrsbh){
        FirstPageChartsEntity firstPageChartsEntity = orderInvoiceInfoService.queryMonthData(baseNsrsbh);
        return R.ok().put(OrderManagementConstant.DATA, firstPageChartsEntity);
    }

    /**
     *  全电概览 - 累计数据概览
     */
    @ApiOperation("全电概览--累计数据")
    @PostMapping("/queryYearData")
    public R queryYearsData(@RequestParam String baseNsrsbh){
        FirstPageChartsEntity firstPageChartsEntity = orderInvoiceInfoService.queryYearsData(baseNsrsbh);
        return R.ok().put(OrderManagementConstant.DATA, firstPageChartsEntity);
    }

    /**
     *  全电概览 - 待开票清单 - 删除
     */
    @ApiOperation("全电概览--待开票清单--删除")
    @PostMapping("/updateReadyInvoiceStatus")
    public R updateReadyInvoiceStatus(@RequestParam String ID){
        return orderInvoiceInfoService.updateReadyInvoiceStatus(ID);
    }

    /**
     *  全电概览 - 开具中清单 - 撤销
     */
    @ApiOperation("全电概览--待开票清单--撤销")
    @PostMapping("/updateInvoicingStatus")
    public R updateInvoicingStatus(@RequestBody Map map){
        return orderInvoiceInfoService.updateInvoicingStatus(map.get("ID").toString());
    }

    /**
     *  全电概览 - 已开发票清单 - 查询
     */
    @ApiOperation("全电概览--本月已开发票清单")
    @PostMapping("/queryInvoicedList")
    public R queryInvoicedList(@RequestBody Map map){
        String baseNsrsbh = map.get("baseNsrsbh").toString();
        String kpzt = map.get("kpzt").toString();
        int currPage = Integer.parseInt(map.get("currPage").toString());
        int pageSize = Integer.parseInt(map.get("pageSize").toString());
        OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
        orderInvoiceInfoEntity.setXhfNsrsbh(baseNsrsbh);
        orderInvoiceInfoEntity.setKpzt(kpzt);
        orderInvoiceInfoEntity.setCurrPage(currPage);
        orderInvoiceInfoEntity.setPageSize(pageSize);
        PageUtils pageUtils = orderInvoiceInfoService.queryInvoicedList(orderInvoiceInfoEntity);
        return R.ok().put(OrderManagementConstant.DATA, pageUtils);
    }
}
