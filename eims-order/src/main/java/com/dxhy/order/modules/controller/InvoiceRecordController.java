package com.dxhy.order.modules.controller;


import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.dto.OrderRecordHmListDTO;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 开票记录 - 信息查询
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-23 11:30:24
 */
@Api(tags = "开票记录")
@Slf4j
@RestController
@RequestMapping("invoiceRecord")
public class InvoiceRecordController {

    private final String Logger = "开票记录";

    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;
    @Autowired
    private FileOperateService fileOperateService;
    @Autowired
    private OpenApiService openApiService;
    @Autowired
    private NsrsbhTenantRelationService nsrsbhTenantRelationService;
    @Autowired
    private TenantRdsService tenantRdsService;

    @Resource
    private ETaxAccountService eTaxAccountService;
    @Value("${url.einvoiceMobileUrl}")
    private String einvoiceMobileEwmjfUrl;

    /**
     * 开票记录 - 列表查询
     */
    @ApiOperation("开票记录--列表查询")
    @PostMapping("/queryList")
    public R queryInvoiceRecordPage(@RequestBody InvoiceRecordQueryList invoiceRecordQueryList) {
        PageUtils page = orderInvoiceInfoService.queryInvoiceRecordPage(invoiceRecordQueryList);
        return R.ok().put(OrderManagementConstant.DATA, page);
    }

    /**
     * 开票记录 - 购方信息查询
     */
    @ApiOperation("开票记录--购方信息查询")
    @PostMapping("/queryGhfList")
    public R queryGhfList(@RequestBody CustomerGroupEntity customerGroupEntity) {
        List<String> list = orderInvoiceInfoService.queryGhfList(customerGroupEntity.getBaseNsrsbh());
        return R.ok().put(OrderManagementConstant.DATA, list);
    }

    /**
     * 开票记录 - PDF下载
     */
    @ApiOperation("开票记录--OFD下载")
    @PostMapping("/downLoadOFD")
    public void downLoadOFD(@RequestBody InvoiceRecordQueryList invoiceRecordQueryList, HttpServletRequest request, HttpServletResponse response) {
        fileOperateService.downLoadOFD(invoiceRecordQueryList.getQdfphm(), request, response);
    }

    /**
     * 开票记录 - Excel导出
     */
    @ApiOperation("开票记录--Excel导出")
    @PostMapping("/exportExcel")
    public void exportExcel(HttpServletRequest request, HttpServletResponse response, @RequestBody InvoiceRecordQueryList list) {
        fileOperateService.exportExcel(list, request, response);
    }


    /**
     * 开票记录 - 批量导出压缩包
     */
    @ApiOperation("开票记录--批量导出压缩包")
    @PostMapping("/exportZip")
    public void exportZip(HttpServletRequest request, HttpServletResponse response,
                          @RequestBody InvoiceRecordZip invoiceRecordZip) {

        fileOperateService.exportZip(request, response, invoiceRecordZip);
    }


    /**
     * 开票记录 - 批量推送
     */
    @ApiOperation("开票记录--推送")
    @PostMapping("/pushManyInvoiceInfo")
    public R pushManyInvoiceInfo(@RequestBody Map map) {
        String email = map.get("email").toString();
        String phone = map.get("phone").toString();
        String qdfphmStr = map.get("qdfphm").toString().replace("[", "").replace("]", "");
        String[] qdfphms = qdfphmStr.split(",");
        log.info("{发票推送}，qdfphms:{}", qdfphms);
        for (String qdfphm : qdfphms) {
            log.info("{发票推送}，qdfphm:{}", qdfphm.trim());
            if (!StringUtils.isEmpty(email)) {
                openApiService.sendEmail(email, qdfphm.trim());
                orderInvoiceInfoService.updateEmailByqdhm(email, qdfphm.trim());
            }
            if (!StringUtils.isEmpty(phone)) {
                openApiService.sendShortMessage(phone, qdfphm.trim());
                orderInvoiceInfoService.updatePhoneByqdhm(phone, qdfphm.trim());
            }
        }
        return R.ok();
    }


    /**
     * 获取扫码地址
     */
    @RequestMapping("/genQrcodeUrl")
    @ApiOperation(value = "获取扫码地址")
    public R genQrcodeUrl(@RequestBody InvoiceRecordInfoEntity InvoiceRecordInfoEntity) {
        log.info("{}，获取扫码地址：{}", Logger, JsonUtils.getInstance().toJsonString(InvoiceRecordInfoEntity));
        String url = einvoiceMobileEwmjfUrl + "?baseNsrsbh=" + InvoiceRecordInfoEntity.getBaseNsrsbh() + "&id=" + InvoiceRecordInfoEntity.getID() + "&type=2";
        String base64 = QrCodeUtil.drawLogoQrCode(null, url, null, null);
        return R.ok().put("data", base64);
    }


    /**
     * 获取实名认证二维码 --自研rpa
     */
    @RequestMapping("/getQrcode/{nsrsbh}")
    @ApiOperation(value = "获取实名认证二维码")
    public R getQrcode(@PathVariable("nsrsbh") String nsrsbh) {
        log.info("{}，获取实名认证二维码，请求参数：{}", Logger, nsrsbh);

        return orderInvoiceInfoService.getQrcode(nsrsbh);
    }

    /**
     * 获取实名认证二维码状态  --自研rpa
     */
    @RequestMapping("/getQrcodeStatus/{nsrsbh}/{rzid}")
    @ApiOperation(value = "获取实名认证二维码")
    public R getQrcodeStatus(@PathVariable("nsrsbh") String nsrsbh, @PathVariable("rzid") String rzid) {
        log.info("{}，获取实名认证二维码状态，请求参数：{}，{}", Logger, nsrsbh, rzid);

        return orderInvoiceInfoService.getQrcodeStatus(nsrsbh, rzid);
    }
    /**
     * 保存电子税务局登录账号信息--dxrpa使用
     */
    @PostMapping("/saveEtaxAccount")
    @ApiOperation(value = "保存电子税务局登录账号信息")
    public R saveEtaxAccount(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        log.info("{}，保存电子税务局登录账号信息，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));
        eTaxAccountEntity.setId(DistributedKeyMaker.generateShotKey());
        eTaxAccountEntity.setStatus("0");
        eTaxAccountEntity.setCreateTime(new Date());
        if (eTaxAccountService.save(eTaxAccountEntity)) {
            return R.ok();
        }
        return R.error("账号信息保存失败");
    }
    /**
     * 查询单个电子税务局登录账号信息--dxrpa使用
     */
    @PostMapping("/selectById")
    @ApiOperation(value = "查询电子税务局登录账号信息")
    public R selectById(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        log.info("{}，查询电子税务局登录账号信息，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));
        final ETaxAccountEntity byId = eTaxAccountService.getById(eTaxAccountEntity.getId());
        return R.ok().put("data",byId);
    }
    /**
     * 根据税号查询单个电子税务局登录账号信息--dxrpa使用
     */
    @PostMapping("/selectByNsrsbh")
    @ApiOperation(value = "查询电子税务局登录账号信息")
    public R selectByNsrsbh(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        log.info("{}，根据税号查询电子税务局登录账号信息，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

        return  eTaxAccountService.selectByNsrsbh(eTaxAccountEntity);
    }
    /**
     * 编辑电子税务局登录账号信息--dxrpa使用
     */
    @PostMapping("/updateEtaxAccount")
    @ApiOperation(value = "编辑电子税务局登录账号信息")
    public R updateEtaxAccount(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        log.info("{}，编辑电子税务局登录账号信息，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

        if (eTaxAccountService.updateById(eTaxAccountEntity)) {
            return R.ok();
        }
        return R.error("账号信息更新失败");
    }
    /**
     * 激活电子税务局登录账号信息--dxrpa使用
     */
    @PostMapping("/activeEtaxAccount")
    @ApiOperation(value = "激活电子税务局登录账号信息")
    public R activeEtaxAccount(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        try {
            log.info("{}，激活电子税务局登录账号信息，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

            return eTaxAccountService.activeEtaxAccount(eTaxAccountEntity);
        }catch (Exception e){
            log.error("激活电子税务局登录账号信息异常,{} ",e);
            return R.error("激活电子税务局登录账号异常");
        }


    }
    /**
     * 登录电子税务局--dxrpa使用
     */
    @PostMapping("/login")
    @ApiOperation(value = "登录电子税务局")
    public R login(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        try {
            log.info("{}，登录电子税务局，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

            return eTaxAccountService.login(eTaxAccountEntity);
        }catch (Exception e){
            log.error("登录电子税务局异常,{} ",e);
            return R.error("登录电子税务局异常");
        }


    }

    /**
     * 设置登录验证码
     */
    @PostMapping("/setSms")
    @ApiOperation(value = "登录电子税务局设置登录验证码")
    public R setSms(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        try {
            log.info("{}，设置登录验证码，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

            return eTaxAccountService.setSms(eTaxAccountEntity);
        }catch (Exception e){
            log.error("设置登录验证码,{} ",e);
            return R.error("设置登录验证码");
        }


    }

    /**
     * 获取实名认证二维码--dxrpa使用
     */
    @PostMapping("/getAuthQrcode")
    @ApiOperation(value = "获取实名认证二维码")
    public R getAuthQrcode(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        try {
            log.info("{}，登录电子税务局，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

            return eTaxAccountService.getAuthQrcode(eTaxAccountEntity);
        }catch (Exception e){
            log.error("获取实名认证二维码异常,{} ",e);
            return R.error("获取实名认证二维码异常");
        }


    }

    /**
     * 获取实名认证状态--dxrpa使用
     */
    @PostMapping("/getAuthStatus")
    @ApiOperation(value = "获取实名认证状态")
    public R getAuthStatus(@RequestBody ETaxAccountEntity  eTaxAccountEntity) {
        try {
            log.info("{}，获取实名认证状态，请求参数：{}", Logger, JsonUtils.getInstance().toJsonString(eTaxAccountEntity));

            return eTaxAccountService.getAuthStatus(eTaxAccountEntity);
        }catch (Exception e){
            log.error("获取实名认证状态异常,{} ",e);
            return R.error("获取实名认证状态异常");
        }


    }


    /**
     * 获取扫码界面数据
     */
    @RequestMapping("/getEwmInfo")
    @ApiOperation(value = "获取扫码信息")
    public R getEwmInfo(@RequestBody InvoiceRecordInfoEntity invoiceRecordInfoEntity) {
        // 先切换回主库
        DynamicDataSource.setDataSourceDefault();
        NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity = nsrsbhTenantRelationService.getById(invoiceRecordInfoEntity.getBaseNsrsbh());
        if (nsrsbhTenantRelationEntity == null || StringUtils.isEmpty(nsrsbhTenantRelationEntity.getTenantCode())) {
            return R.error("租户数据有误");
        }
        String changeTenantResult = tenantRdsService.switchRds(nsrsbhTenantRelationEntity.getTenantCode());
        if (!StringUtils.isEmpty(changeTenantResult)) {
            return R.error(changeTenantResult);
        }
        return R.ok().put("data", orderInvoiceInfoService.getEwmInfo(invoiceRecordInfoEntity));
    }

    /**
     * 开票记录四种查询（全部，开票中，开票失败，开票完成）
     */
    @PostMapping("/getOrderInvoiceInfoByKPZT")
    @ApiOperation(value = "开票记录查询")
    public R getOrderInvoiceInfoByKPZT(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return orderInvoiceInfoService.getOrderInvoiceInfoByKPZT(orderInvoiceInfoEntity);
    }


    /**
     * 开票记录-计算开票完成，开票中，开票失败的总量
     */
    @ApiOperation("开票记录-计算开票完成，开票中，开票失败的总量")
    @PostMapping("/countSL")
    public R countSL(@RequestBody IdsDTO ids) {
        return orderInvoiceInfoService.countSL(ids.getBaseNsrsbh());
    }


    /**
     * 开票记录--批量交付
     */
    @ApiOperation("开票记录--批量交付")
    @PostMapping("/batchDelivery")
    public R batchDelivery(@RequestBody OrderRecordHmListDTO orderRecordHmListDTO) {
        return orderInvoiceInfoService.batchDelivery(orderRecordHmListDTO.getQdfphm());
    }

    /**
     * 手工标记为开票完成设为待处理
     */
    @ApiOperation("开票记录--手工标记为开票完成设为待处理")
    @PostMapping("/updateToWait")
    public R updateToWait(@RequestBody IdDTO id) {
        return orderInvoiceInfoService.updateToWait(id);
    }

    /**
     * 开票记录查看详情-订单信息详情
     */
    @ApiOperation("开票记录查看详情-订单信息详情")
    @PostMapping("/orderDetail")
    public R getOrderDetail(@RequestBody IdDTO id) {
        return orderInvoiceInfoService.getOrderDetail(id);
    }

    /**
     * 开票记录查看详情-商品信息详情
     */
    @ApiOperation("开票记录查看详情-商品信息详情")
    @PostMapping("/getOrderItemDetail")
    public R getOrderItemDetail(@RequestBody IdDTO id) {
        return orderInvoiceInfoService.getOrderItemDetail(id);
    }

    /**
     * 开票记录查看详情-特定业务详情
     */
    @ApiOperation("开票记录查看详情-特定业务详情")
    @PostMapping("/getOrderTdywDetail")
    public R getOrderTdywDetail(@RequestBody IdDTO id) {
        return orderInvoiceInfoService.getOrderTdywDetail(id);
    }

}
