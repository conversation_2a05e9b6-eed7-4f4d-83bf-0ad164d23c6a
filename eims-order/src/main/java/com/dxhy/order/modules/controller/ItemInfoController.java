package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.entity.ItemInfoEntity;
import com.dxhy.order.modules.pojo.bo.GroupTreeBO;
import com.dxhy.order.modules.pojo.dto.*;
import com.dxhy.order.modules.service.ItemInfoService;
import com.dxhy.order.utils.R;
import com.dxhy.order.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

/**
 * 开票项目模块（商品信息） controller
 * <AUTHOR>
 * @Date 2022/6/27 12:26
 * @Version 1.0
 **/
@RestController
@Api(tags = "开票项目模块（商品信息）")
@Slf4j
@RequestMapping("/itemInfo")
public class ItemInfoController {
    private final String modelName = "开票项目模块";

    @Autowired
    private ItemInfoService itemInfoService;
    @Value("${order.invoice.itemInfoPath}")
    private String itemInfoPath;

    /**
     * 获取项目分类树
     */
    @PostMapping("/getGroupTree")
    @ApiOperation(value = "获取项目分类树")
    public R getGroupTree(@RequestBody BaseNsrsbhDTO baseNsrsbhDTO){
        log.info("{} - getGroupTree - {}", this.modelName, baseNsrsbhDTO);
        GroupTreeBO groupTree = itemInfoService.getGroupTree(baseNsrsbhDTO.getBaseNsrsbh());
        return R.ok().put("data", groupTree);
    }

    /**
     * 保存项目分类（新增 修改）
     */
    @PostMapping("/saveItemGroup")
    @ApiOperation(value = "保存客户项目分类（新增 修改）")
    public R saveItemGroup(@RequestBody ItemGroupSaveDTO itemGroupSaveDTO){
        log.info("{} - saveGroup - {}", this.modelName, itemGroupSaveDTO);
        return itemInfoService.saveItemGroup(itemGroupSaveDTO);
    }

    /**
     * 删除项目分类
     */
    @PostMapping("/deleteItemGroup")
    @ApiOperation(value = "删除客户项目分类")
    public R deleteItemGroup(@RequestBody IdDTO idDTO){
        log.info("{} - deleteCustomerInfoGroup - {}", this.modelName, idDTO);
        return itemInfoService.deleteItemGroup(idDTO.getId());
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表")
    public R list(@RequestBody ItemInfoListDTO itemInfoListDTO){
        log.info("{} - list - {}", this.modelName, itemInfoListDTO);
        return itemInfoService.queryPage(itemInfoListDTO);
    }

    /**
     * 列表
     * 20220908 增加不区分nsrsbh的查询接口
     */
    @PostMapping("/listWithoutNsrsbh")
    @ApiOperation(value = "列表")
    public R listWithoutNsrsbh(@RequestBody ItemInfoListDTO itemInfoListDTO){
        log.info("{} - listWithoutNsrsbh - {}", this.modelName, itemInfoListDTO);
        return itemInfoService.queryPageWithoutNsrsbh(itemInfoListDTO);
    }

    /**
     * 根据名称查询列表 - 不分页
     */
    @PostMapping("/listByNameWithoutPage")
    @ApiOperation(value = "根据名称查询列表 - 不分页")
    public R listByNameWithoutPage(@RequestBody ItemInfoListByNameWithoutPageDTO itemInfoListByNameWithoutPageDTO){
        log.info("{} - listByNameWithoutPage - {}", this.modelName, itemInfoListByNameWithoutPageDTO);
        return itemInfoService.listByNameWithoutPage(itemInfoListByNameWithoutPageDTO);
    }

    /**
     * 信息
     */
    @PostMapping("/info")
    @ApiOperation(value = "信息")
    public R info(@RequestBody IdDTO idDTO){
        log.info("{} - info - {}", this.modelName, idDTO);
        ItemInfoEntity itemInfoEntity = itemInfoService.getById(idDTO.getId());
        itemInfoEntity.setDj(StringUtil.numberFormat2(itemInfoEntity.getDj()));
        return R.ok().put("data", itemInfoEntity);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增")
    public R save(@RequestBody ItemInfoSaveDTO itemInfoSaveDTO){
        log.info("{} - save - {}", this.modelName, itemInfoSaveDTO);
        return itemInfoService.saveData(itemInfoSaveDTO);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody ItemInfoSaveDTO itemInfoSaveDTO){
        log.info("{} - update - {}", this.modelName, itemInfoSaveDTO);
        return itemInfoService.saveData(itemInfoSaveDTO);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public R delete(@RequestBody IdDTO idDTO){
        log.info("{} - delete - {}", this.modelName, idDTO.getId());
        return itemInfoService.deleteData(idDTO.getId());
    }

    /**
     * 导出
     */
    @PostMapping("/exportData")
    @ApiOperation(value = "导出")
    public void exportData(@RequestBody IdsDTO idsDTO, HttpServletResponse response){
        log.info("{} - exportData - {}", this.modelName, idsDTO);
        if(idsDTO != null && CollectionUtils.isNotEmpty(idsDTO.getIds())){
            itemInfoService.exportData(idsDTO.getIds(), response);
        }
    }

    /**
     * 下载项目信息导入模板
     */
    @ApiOperation("下载项目信息导入模板")
    @PostMapping("downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("{} - downloadTemplate", this.modelName);
        try {
            byte[] bytes = FileUtils.readFileToByteArray(new File(itemInfoPath));
            if (response != null) {
                response.setContentType("application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition", "attachment;filename=\"" + new String(("项目信息导入模板" + ".xls").getBytes("gb2312"), "ISO8859-1"));
                OutputStream out = response.getOutputStream();
                out.write(bytes);
                out.close();
            }
        } catch (IOException e) {
            log.error("下载项目信息导入模板异常:{}", e);
        }
    }

    /**
     * 上传项目信息
     */
    @ApiOperation("上传项目信息")
    @PostMapping("uploadItemInfoExcel")
    public R uploadItemInfoExcel(@RequestParam("file") MultipartFile file) {
        log.info("{} - uploadItemInfoExcel", this.modelName);
        return itemInfoService.uploadItemInfoExcel(file);
    }

    /**
     * 保存Excel上传的项目信息
     */
    @PostMapping("/saveDataFromExcel")
    @ApiOperation(value = "保存Excel上传的项目信息")
    public R saveDataFromExcel(@RequestBody ItemInfoExcelSaveDTO itemInfoExcelSaveDTO){
        log.info("{} - saveDataFromExcel - {}", this.modelName, itemInfoExcelSaveDTO);
        return itemInfoService.saveDataFromExcel(itemInfoExcelSaveDTO);
    }

    /**
     * 将Excel错误的数据导出
     */
    @PostMapping("/genExcelForErrorData")
    @ApiOperation(value = "将Excel错误的数据导出")
    public void genExcelForErrorData(@RequestBody ItemInfoExcelSaveDTO itemInfoExcelSaveDTO, HttpServletResponse response){
        log.info("{} - genExcelForErrorData - {}", this.modelName, itemInfoExcelSaveDTO);
        itemInfoService.genExcelForErrorData(itemInfoExcelSaveDTO, response);
    }

}
