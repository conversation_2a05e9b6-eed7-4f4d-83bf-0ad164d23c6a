package com.dxhy.order.modules.controller;

import com.alibaba.fastjson.JSON;
import com.dxhy.order.modules.entity.OrderInvoiceHistoryEntity;
import com.dxhy.order.modules.service.OrderInvoiceHistoryService;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 历史发票下载、发票归集（进销项）
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
@Api(tags = "历史发票下载管理")
@RestController
@RequestMapping("/orderInvoiceHistory")
@Slf4j
public class OrderInvoiceHistoryController {

    @Autowired
    private OrderInvoiceHistoryService orderInvoiceHistoryService;


    /**
     * 列表
     */
    @PostMapping("/list")
    @ApiOperation("查询历史发票下载列表")
    public R list(@RequestBody OrderInvoiceHistoryEntity orderInvoiceHistoryEntity) {
        log.info("发票列表请求参数为:{}", JSON.toJSONString(orderInvoiceHistoryEntity));
        return orderInvoiceHistoryService.queryPage(orderInvoiceHistoryEntity);
    }

    /**
     * 触发归集，传id时重新归集某一条任务，不传id时根据条件归集全部发票
     */
    @PostMapping("/download")
    @ApiOperation(value = "触发历史发票下载", notes = "传id时重新归集某条任务，不传id时根据传的条件归集")
    public R delete(@RequestBody OrderInvoiceHistoryEntity orderInvoiceHistoryEntity) {
        orderInvoiceHistoryService.downLoad(orderInvoiceHistoryEntity);
        return R.ok();
    }
}
