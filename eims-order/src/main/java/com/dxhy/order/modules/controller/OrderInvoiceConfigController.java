package com.dxhy.order.modules.controller;


import com.dxhy.order.modules.entity.OrderInvoiceConfigEntity;
import com.dxhy.order.modules.service.OrderInvoiceConfigService;
import com.dxhy.order.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 开票配置表
 *
 * <AUTHOR>
 * @email
 * @date 2022-12-06 20:00:18
 */
@RestController
@RequestMapping("orderinvoiceconfig")
public class OrderInvoiceConfigController {
    @Autowired
    private OrderInvoiceConfigService orderInvoiceConfigService;


    /**
     * 保存
     */
    @PostMapping("/save")
    public R save(@RequestBody OrderInvoiceConfigEntity orderInvoiceConfig) {
        return orderInvoiceConfigService.saveOrderInvoiceConfigEntity(orderInvoiceConfig);
    }

    /**
     * 查询
     */
    @PostMapping("/list")
    public R selectOrderInvoiceConfigEntity(@RequestBody OrderInvoiceConfigEntity orderInvoiceConfig) {
        return orderInvoiceConfigService.selectOrderInvoiceConfigEntity(orderInvoiceConfig);
    }

    @PostMapping("/taxpayerNsrsbh")
    public R taxpayerNsrsbh(@RequestBody OrderInvoiceConfigEntity orderInvoiceConfig) {
        return orderInvoiceConfigService.taxpayerNsrsbh(orderInvoiceConfig);
    }

    /**
     * 保存 - 拆合规则
     */
    @PostMapping("/saveChgz")
    public R saveChgz(@RequestBody OrderInvoiceConfigEntity orderInvoiceConfig) {
        return orderInvoiceConfigService.saveChgz(orderInvoiceConfig);
    }
}
