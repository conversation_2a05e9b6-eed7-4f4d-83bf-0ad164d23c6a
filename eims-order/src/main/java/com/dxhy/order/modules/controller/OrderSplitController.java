package com.dxhy.order.modules.controller;

import com.alibaba.fastjson.JSON;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.constant.OrderSplitEnum;
import com.dxhy.order.exception.OrderSplitException;
import com.dxhy.order.model.OrderInfoContentEnum;
import com.dxhy.order.model.OrderSplitConfig;
import com.dxhy.order.model.page.PageSplit;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.pojo.dto.OrderSplitSaveDTO;
import com.dxhy.order.modules.service.OrderSplitService;
import com.dxhy.order.pojo.SplitInvoiceInfo;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.NsrsbhUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description ：订单拆分控制层
 * @date ：2025年5月21日 上午10:11:56
 */
@Api(value = "订单拆分", tags = {"订单拆分模块"})
@RestController
@RequestMapping("/orderSplit")
@Slf4j
public class OrderSplitController {

    private static final String LOGGER_MSG = "(订单拆分接口)";

    @Resource
    private OrderSplitService orderSplitService;


    /**
     * 按明细拆分
     */
    @ApiOperation("按明细拆分")
    @PostMapping("/splitByInvoiceItem")
    public R splitByInvoiceItem(@RequestBody SplitInvoiceInfo splitInvoiceInfo) {
        try {
            return orderSplitService.splitByInvoiceItem(splitInvoiceInfo);
        } catch (Exception e) {
            log.error("按明细拆分异常", e);
            return R.error("9999", "拆分异常,请联系管理员");
        }
    }

    /**
     * 按明细拆分,退回
     */
    @ApiOperation("拆分还原")
    @PostMapping("/splitBack")
    public R splitBack(@RequestBody SplitInvoiceInfo splitInvoiceInfo) {
        try {
            return orderSplitService.splitBack(splitInvoiceInfo.getOrderInvoiceId());
        } catch (Exception e) {
            log.error("按明细拆分,退回到原订单或原应收单异常", e);
            return R.error("9999", "按明细拆分,退回到原订单或原应收单异常");
        }
    }

    /**
     * 金额拆分订单,对应前端-单据管理-单据详情-金额拆分功能
     *
     * @param @param  orderId
     * @param @param  orderItemId
     * @param @return
     * @param @throws Exception
     * @return R
     * @throws
     * @Title :
     * @Description ：根据金额拆分的金额 规则：用户如果输入一个金额，拆分成两个订单，一次类推，最多输入三个金额，保数量 按含税金额拆分
     */
    @ApiOperation(value = "根据金额拆分订单接口", notes = "订单拆分-根据金额拆分订单信息")
    @PostMapping("/priceSplit")
    public R splitByDj(@RequestBody PageSplit pageSplit) {

        String xhfNsrsbh = pageSplit.getXhfNsrsbh();
        String jeArray = pageSplit.getJeArray();
        String orderId = pageSplit.getOrderId();
        String hsbz = pageSplit.getHsbz();
        String isEquivalent = pageSplit.getIsEquivalent();

        log.info("{}拆分的接口，原始金额数组字符串:{}", LOGGER_MSG, jeArray);
        
        // 手动解析金额数组，避免精度问题
        List<String> parseJeArray = new ArrayList<>();
        
        try {
            // 检查jeArray格式并手动解析
            if (jeArray != null && jeArray.startsWith("[") && jeArray.endsWith("]")) {
                // 去除首尾的[]
                String content = jeArray.substring(1, jeArray.length() - 1);
                // 处理可能存在的空字符串情况
                if (!content.trim().isEmpty()) {
                    // 根据逗号分割
                    String[] amounts = content.split(",");
                    for (String amount : amounts) {
                        // 去除额外的引号和空格
                        String cleanAmount = amount.trim();
                        if (cleanAmount.startsWith("\"") && cleanAmount.endsWith("\"")) {
                            cleanAmount = cleanAmount.substring(1, cleanAmount.length() - 1);
                        }
                        // 确保金额格式正确
                        log.info("{}手动解析金额值:{}", LOGGER_MSG, cleanAmount);
                        parseJeArray.add(cleanAmount);
                    }
                }
            } else {
                // 如果不是预期的格式，尝试使用原始解析方式
                parseJeArray = JSON.parseArray(jeArray, String.class);
            }
        } catch (Exception e) {
            log.error("{}解析金额数组异常，尝试使用原始方式解析", LOGGER_MSG, e);
            // 出错时回退到原始解析方式
            parseJeArray = JSON.parseArray(jeArray, String.class);
        }
        
        // 打印最终解析结果
        for (String amount : parseJeArray) {
            log.info("{}最终解析拆分金额:{}", LOGGER_MSG, amount);
        }
        
        R vo = new R();
        log.info("{}拆分的接口，前端传入的参数orderId:{},拆分金额:{}", LOGGER_MSG, orderId, parseJeArray);
        try {
            if (StringUtils.isBlank(xhfNsrsbh)) {
                log.error("{},请求税号为空!", LOGGER_MSG);
                return R.error(OrderInfoContentEnum.TAXCODE_ISNULL.getKey(), OrderInfoContentEnum.TAXCODE_ISNULL.getMessage());
            }

            //订单拆分前校验
            List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
            List<String> orderIds = new ArrayList<>();
            orderIds.add(orderId);
            R r = orderSplitService.checkPreSpilt(orderIds, shList);
            if (!ConfigureConstant.STRING_0000.equals(r.get(OrderManagementConstant.CODE))) {
                return r;
            }

            /**
             * 拆分订单
             */
            OrderSplitConfig orderSplitConfig = new OrderSplitConfig();
            orderSplitConfig.setSplitType(OrderSplitEnum.ORDER_SPLIT_TYPE_2.getKey());
            orderSplitConfig.setJeList(parseJeArray);
            orderSplitConfig.setHsbz(hsbz);
            orderSplitConfig.setSplitRule(OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_JE.getKey());
            orderSplitConfig.setIsEquivalent(isEquivalent);
            List<OrderInvoiceInfoEntity> splitOrder = orderSplitService.splitOrder(orderId, shList, orderSplitConfig);
            
            // 确保精确的金额被正确应用
            for (int i = 0; i < splitOrder.size() && i < parseJeArray.size(); i++) {
                OrderInvoiceInfoEntity entity = splitOrder.get(i);
                if ("EXACT_AMOUNT".equals(entity.getByzd1())) {
                    String exactAmount = parseJeArray.get(i);
                    // 确保金额格式符合标准（带有2位小数）
                    if (!exactAmount.contains(".")) {
                        // 如果金额没有小数点，添加.00
                        exactAmount = exactAmount + ".00";
                    } else {
                        // 如果有小数点但小数位不足2位，补足
                        String[] parts = exactAmount.split("\\.");
                        if (parts.length > 1 && parts[1].length() < 2) {
                            // 小数位不足2位
                            exactAmount = parts[0] + "." + String.format("%-2s", parts[1]).replace(' ', '0');
                        }
                    }
                    // 直接设置精确的价税合计
                    entity.setJshj(exactAmount);
                    log.info("{}手动设置订单拆分金额，确保精确值：{}", LOGGER_MSG, exactAmount);
                }
            }
            
            vo.put(OrderManagementConstant.DATA, splitOrder);
        } catch (OrderSplitException e) {
            log.error("{}订单多金额拆分异常", LOGGER_MSG, e);
            vo.put(OrderManagementConstant.CODE, e.getCode()).put(OrderManagementConstant.MESSAGE, e.getMessage());
            return vo;
        } catch (Exception e) {
            log.error("{}订单拆分异常", LOGGER_MSG, e);
            vo.put(OrderManagementConstant.CODE, OrderInfoContentEnum.RECEIVE_FAILD.getKey());
            vo.put(OrderManagementConstant.MESSAGE, OrderInfoContentEnum.RECEIVE_FAILD.getMessage());
            return vo;
        }
        return vo;
    }

    /**
     * 数量拆分订单,,对应前端-单据管理-单据详情-数量拆分功能
     *
     * @param @param  orderId
     * @param @param  orderItemId
     * @param @return
     * @param @throws Exception
     * @return R
     * @throws
     * @Title : splitBySL
     * @Description ：根据数量拆分 输入一个数量 拆分成两个订单，以此类推，最多输入三个数量
     */
    @ApiOperation(value = "根据数量拆分订单接口", notes = "订单拆分-根据数量拆分订单信息")
    @PostMapping("/amountSplit")
    public R splitBySl(@RequestBody PageSplit pageSplit) {
        String xhfNsrsbh = pageSplit.getXhfNsrsbh();
        String slArray = pageSplit.getSlArray();
        String orderId = pageSplit.getOrderId();
        String isEquivalent = pageSplit.getIsEquivalent();
        log.info("{}按数量拆分订单rest接口，前端传入的参数orderId:{},orderItemId:{}", LOGGER_MSG, orderId, JsonUtils.getInstance().toJsonString(slArray));
        List<String> parseSlArray = JSON.parseArray(slArray, String.class);
        R vo = new R();
        try {
            if (StringUtils.isBlank(xhfNsrsbh)) {
                log.error("{},请求税号为空!", LOGGER_MSG);
                return R.error(OrderInfoContentEnum.TAXCODE_ISNULL.getKey(), OrderInfoContentEnum.TAXCODE_ISNULL.getMessage());
            }
            //订单拆分前校验
            List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
            List<String> orderIds = new ArrayList<>();
            orderIds.add(orderId);
            R r = orderSplitService.checkPreSpilt(orderIds, shList);
            if (!ConfigureConstant.STRING_0000.equals(r.get(OrderManagementConstant.CODE))) {
                return r;
            }

            OrderSplitConfig config = new OrderSplitConfig();
            config.setSplitType(OrderSplitEnum.ORDER_SPLIT_TYPE_3.getKey());
            config.setSlList(parseSlArray);
            config.setSplitRule(OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_SL.getKey());
            config.setIsEquivalent(isEquivalent);
            List<OrderInvoiceInfoEntity> splitOrder = orderSplitService.splitOrder(orderId, shList, config);
            vo.put(OrderManagementConstant.DATA, splitOrder);
        } catch (OrderSplitException e) {
            log.error("{}订单按数量拆分异常", LOGGER_MSG, e);
            vo.put(OrderManagementConstant.CODE, e.getCode());
            vo.put(OrderManagementConstant.MESSAGE, e.getMessage());
            return vo;
        } catch (Exception e) {
            log.error("{}订单拆分异常", LOGGER_MSG, e);
            vo.put(OrderManagementConstant.CODE, OrderInfoContentEnum.RECEIVE_FAILD.getKey());
            vo.put(OrderManagementConstant.MESSAGE, OrderInfoContentEnum.RECEIVE_FAILD.getMessage());
            return vo;
        }
        return vo;
    }

    /**
     * 拆分后数据保存功能,对应前端
     *
     * @param @param  orderId
     * @param @param  orderItemId
     * @param @return
     * @param @throws Exception
     * @return R
     * @throws
     * @Description ：订单拆分后数据的保存接口
     */
    @ApiOperation(value = "订单拆分后的数据保存接口", notes = "订单拆分-订单拆分后的数据保存接口")
    @PostMapping("/saveOrderInfo")
    public R saveOrderInfo(
            @ApiParam(name = "orderInfo", value = "拆分订单的全信息") @RequestBody OrderSplitSaveDTO orderSplitSaveDTO) {
        try {
            List<OrderInvoiceInfoEntity> commonList = orderSplitSaveDTO.getOrderInfo();
            log.info("{}保存拆分订单rest接口，前端传入的参数commonList:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(commonList));
            orderSplitService.saveOrderSplitInfo(commonList);
            return R.ok();
        } catch (Exception e) {
            log.error("{}订单保存异常", LOGGER_MSG, e);
            return R.error(OrderInfoContentEnum.RECEIVE_FAILD.getKey(), OrderInfoContentEnum.RECEIVE_FAILD.getMessage());

        }
    }


}
