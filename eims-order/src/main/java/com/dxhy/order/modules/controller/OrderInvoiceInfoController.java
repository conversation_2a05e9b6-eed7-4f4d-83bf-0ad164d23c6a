package com.dxhy.order.modules.controller;

import com.alibaba.fastjson.JSON;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.dto.NoOrderInvoiceInfoListDTO;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.pojo.QdInvalidRequest;
import com.dxhy.order.utils.JacksonUtils;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * 订单和发票关系表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
@Api(tags = "发票管理")
@RestController
@RequestMapping("orderInvoiceInfo")
@Slf4j
public class OrderInvoiceInfoController {

    @Value("${order.invoice.temPath}")
    private String orderInvoicePath;

    @Value("${order.invoice.temBatchPath}")
    private String orderInvoiceBatchPath;
    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;

    /**
     * 数电纸票作废
     */
    @PostMapping("/invalid")
    @ApiOperation("数电纸票作废")
    public R invalid(@RequestBody QdInvalidRequest qdInvalidRequest) {
        log.info("数电纸票作废请求参数为:{}", JSON.toJSONString(qdInvalidRequest));
        return orderInvoiceInfoService.invalid(qdInvalidRequest);
    }

    /**
     * 获取数电纸票打印xml
     */
    @PostMapping("/getXml")
    @ApiOperation("获取数电纸票打印xml")
    public R getXml(@RequestBody QdInvalidRequest qdInvalidRequest) {
        log.info("获取数电纸票打印xml请求参数为:{}", JSON.toJSONString(qdInvalidRequest));
        return orderInvoiceInfoService.getXml(qdInvalidRequest);
    }

    /**
     * 列表
     */
    @PostMapping("/list")
    @ApiOperation("查询发票列表")
    public R list(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("发票列表请求参数为:{}", JSON.toJSONString(orderInvoiceInfoEntity));
        return orderInvoiceInfoService.queryPage(orderInvoiceInfoEntity);
    }

    /**
     * 查询进项发票列表
     */
    @PostMapping("/queryJxfpPage")
    @ApiOperation("查询进项发票列表")
    public R queryJxfpPage(@RequestBody Map<String, Object> params) {
        return orderInvoiceInfoService.queryJxfpPage(params);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") String id) {
        OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoService.getById(id);
        return R.ok().put("orderInvoiceInfo", orderInvoiceInfo);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfo) {
        orderInvoiceInfoService.save(orderInvoiceInfo);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfo) {
        orderInvoiceInfoService.updateById(orderInvoiceInfo);//全部更新
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody String[] ids) {
        orderInvoiceInfoService.removeBatchByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 发票暂存接口
     */
    @ApiOperation("发票暂存")
    @PostMapping("orderInvoiceTemporary")
    public R orderInvoiceTemporary(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("发票暂存请求参数为:{}", JacksonUtils.toJsonPrettyString(orderInvoiceInfoEntity));
        return orderInvoiceInfoService.orderInvoiceTemporary(orderInvoiceInfoEntity);
    }

    /**
     * 发票填开-发票开具接口
     */
    @ApiOperation("发票填开-发票开具")
    @PostMapping("invoiceInputIssue")
    public R invoiceInputIssue(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("发票填开-发票开具接口请求参数为:{}", JacksonUtils.toJsonPrettyString(orderInvoiceInfoEntity));
        return orderInvoiceInfoService.invoiceInputIssue(orderInvoiceInfoEntity);
    }

    /**
     * 订单开票-发票开具接口
     */
    @ApiOperation("订单开票-发票开具")
    @PostMapping("orderInvoiceIssue")
    public R orderInvoiceIssue(@RequestBody String ids) {
        log.info("订单开票-发票开具请求参数为:{}", ids);
        List<String> msg = new ArrayList<>();
        if (StringUtils.isEmpty(ids)) {
            msg.add("勾选订单不能为空");
            return R.setCodeAndMsg(OrderInfoContentEnum.ORDER_ISSUE, JsonUtils.getInstance().toJsonString(msg));
        }
        String idList = JSON.parseObject(ids).get("ids").toString();
        String[] split = idList.split(",");
        List<String> strings = Arrays.asList(split);
        return orderInvoiceInfoService.orderInvoiceIssue(strings);
    }

    /**
     * 发票删除接口
     */
    @ApiOperation("发票删除接口")
    @PostMapping("deleteByInvoiceId")
    public R deleteByInvoiceId(@RequestBody String ids) {
        log.info("发票删除接口入参:{}", ids);
        if (StringUtils.isEmpty(ids)) {
            return R.error("勾选不能为空");
        }
        String idList = JSON.parseObject(ids).get("ids").toString();
        String baseNsrsbh = JSON.parseObject(ids).get("baseNsrsbh").toString();
        String[] split = idList.split(",");
        List<String> strings = Arrays.asList(split);
        return orderInvoiceInfoService.deleteByInvoiceId(strings, baseNsrsbh);
    }

    /**
     * 发票信息批量导入
     */
    @ApiOperation("发票信息批量导入")
    @PostMapping("uploadOrderInvoiceInfo")
    public R uploadInvoiceItemInfo(@RequestParam("file") MultipartFile file) {
        log.info("发票信息批量导入 uploadOrderInvoiceInfo入参: file: {}", file.getOriginalFilename());
        return orderInvoiceInfoService.uploadOrderInvoiceInfo(file);
    }

    /**
     * 发票信息保存
     */
    @ApiOperation("发票信息保存")
    @PostMapping("saveOrderInvoice")
    public R saveOrderInvoice(@RequestBody OrderInvoiceImportReqEntity orderInvoiceImportReqEntity) {
        log.info("发票信息保存入参:{}", JsonUtils.getInstance().toJsonString(orderInvoiceImportReqEntity));
        return orderInvoiceInfoService.saveOrderInvoice(orderInvoiceImportReqEntity.getOrderInvoiceImportExcelEntities(), orderInvoiceImportReqEntity.getXhfMc(), orderInvoiceImportReqEntity.getXhfNsrsbh()
                , orderInvoiceImportReqEntity.getXhfDz(), orderInvoiceImportReqEntity.getXhfDh(), orderInvoiceImportReqEntity.getXhfYh(), orderInvoiceImportReqEntity.getXhfZh());
    }

    /**
     * 合并订单
     */
    @ApiOperation("合并订单")
    @PostMapping("mergeOrderInvoice")
    public R mergeOrderInvoice(@RequestBody Map<String, String> map) {
        log.info("合并订单接口入参:{}", JsonUtils.getInstance().toJsonString(map));
        if (StringUtils.isEmpty(map.get("ids"))) {
            return R.error("勾选信息不能为空");
        }
        String[] split = map.get("ids").split(",");
        List<String> strings = Arrays.asList(split);
        return orderInvoiceInfoService.mergeOrderInvoice(strings, map.get("baseNsrsbh"));
    }

    /**
     * 订单退回
     */
    @ApiOperation("订单退回")
    @PostMapping("orderInvoiceBack")
    public R orderInvoiceBack(@RequestBody String ids) {
        log.info("订单退回接口入参:{}", ids);
        if (StringUtils.isEmpty(ids)) {
            return R.error("勾选信息不能为空");
        }
        String idList = JSON.parseObject(ids).get("ids").toString();
        String baseNsrsbh = JSON.parseObject(ids).get("baseNsrsbh").toString();
        String[] split = idList.split(",");
        List<String> strings = Arrays.asList(split);
        return orderInvoiceInfoService.orderInvoiceBack(strings, baseNsrsbh);
    }

    /**
     * 发票预览
     */
    @ApiOperation("发票预览")
    @PostMapping("orderInvoiceDetail")
    public R orderInvoiceDetail(@RequestBody IdDTO id) {
        log.info("发票预览接口入参:{}", id);
        return orderInvoiceInfoService.orderInvoiceDetail(id);
    }

    /**
     * 发票详情
     */
    @ApiOperation("发票详情")
    @PostMapping("orderInvoiceDetailInfo")
    public R orderInvoiceDetailInfo(@RequestBody IdDTO id) {
        log.info("发票详情接口入参:{}", id);
        return orderInvoiceInfoService.orderInvoiceDetailInfo(id);
    }

    /**
     * 根据原订单号或者原应收单号 穿透查询
     */
    @ApiOperation("根据原订单号或者原应收单号穿透查询")
    @PostMapping("orderInvoiceYddhOrYysdh")
    public R orderInvoiceYddhOrYysdh(@RequestBody DjbhReq djbhReq) {
        return orderInvoiceInfoService.orderInvoiceYddhOrYysdh(djbhReq);
    }

    /**
     * 预览发票图片
     */
    @PostMapping(value = "previewInvoicePng")
    @ApiOperation(value = "预览发票图片", notes = "发票详情-预览发票图片")
    public R previewInvoicePng(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("预览发票图片入参:{}", orderInvoiceInfoEntity);
        if (ObjectUtils.isEmpty(orderInvoiceInfoEntity)) {
            log.error("{},请求参数为空!");
            return R.ok();
        }
        List<String> pngBase64List = new ArrayList<>();
        pngBase64List = orderInvoiceInfoService.getInvoicePng(orderInvoiceInfoEntity);
        if (ObjectUtils.isEmpty(pngBase64List) || ObjectUtils.isEmpty(pngBase64List.get(0))) {
            return R.ok();
        }
        return R.ok().put("data", pngBase64List);
    }

    /**
     * 订单导入模板下载
     */
    @ApiOperation("订单导入模板下载")
    @PostMapping("downLoadInvoiceTemplate")
    public void downLoadInvoiceTemplate(HttpServletResponse response) {
        try {
            byte[] bytes = FileUtils.readFileToByteArray(new File(orderInvoicePath));
            if (response != null) {
                response.setContentType("application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=\"" + new String(("订单开票导入模板" + ".xls").getBytes("gb2312"), "ISO8859-1"));
                OutputStream out = response.getOutputStream();
                out.write(bytes);
                out.close();
            }
        } catch (IOException e) {
            log.error("订单导入模板下载异常:{}", e);
        }
    }

    /**
     * 下载错误明细
     */
    @ApiOperation("下载错误明细")
    @PostMapping("downLoadInvoiceItemDetail")
    public void downLoadInvoiceItemDetail(@RequestBody OrderInvoiceImportReqEntity orderInvoiceImportReqEntity, HttpServletResponse response) {
        orderInvoiceInfoService.downLoadInvoiceItemDetail(orderInvoiceImportReqEntity.getOrderInvoiceImportExcelEntities(), response);
    }

    /**
     * 发票信息批量导入
     */
    @ApiOperation("发票信息批量导入2.0")
    @PostMapping("uploadBatchOrderInvoiceInfo")
    public R uploadBatchOrderInvoiceInfo(@RequestParam("file") MultipartFile file, @RequestParam("baseNsrsbh") String baseNsrsbh) {
        log.info("发票信息批量导入 uploadBatchOrderInvoiceInfo入参: file: {},baseNsrsbh: {}", file.getOriginalFilename(), baseNsrsbh);
        return orderInvoiceInfoService.uploadBatchOrderInvoiceInfo(file, baseNsrsbh);
    }

    /**
     * 下载错误明细2.0
     */
    @ApiOperation("下载错误明细2.0")
    @PostMapping("downLoadInvoiceFailDetail")
    public void downLoadInvoiceFailDetail(@RequestBody InvoiceImportInfoResEntity orderInvoiceImportReqEntity, HttpServletResponse response) throws Exception {
        orderInvoiceInfoService.downLoadInvoiceFailDetail(orderInvoiceImportReqEntity, response);
    }

    /**
     * 订单批量导入模板下载
     */
    @ApiOperation("订单批量导入模板下载2.0")
    @PostMapping("downLoadBatchInvoiceTemplate")
    public void downLoadBatchInvoiceTemplate(HttpServletResponse response) {
        try {
            byte[] bytes = FileUtils.readFileToByteArray(new File(orderInvoiceBatchPath));
            if (response != null) {
                response.setContentType("application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=\"" + new String(("批量导入开票模板.xlsx").getBytes("gb2312"), "ISO8859-1"));
                OutputStream out = response.getOutputStream();
                out.write(bytes);
                out.close();
            }
        } catch (IOException e) {
            log.error("订单导入模板下载异常:{}", e);
        }
    }

    /**
     * 发票批量信息保存
     */
    @ApiOperation("发票批量信息保存2.0")
    @PostMapping("saveBatchOrderInvoice")
    public R saveBatchOrderInvoice(@RequestBody InvoiceImportInfoResEntity invoiceImportInfoResEntity) {
        return orderInvoiceInfoService.saveBatchOrderInvoice(invoiceImportInfoResEntity);
    }

    /**
     * 新增开票项目时，商品和税收分类编码框支持输入税收分类编码
     */
    @ApiOperation("新增开票项目时，商品和税收分类编码框支持输入税收分类编码")
    @PostMapping("getTaxClassCodeSpjc")
    public R getTaxClassCodeSpjc(@RequestBody TaxClassCodeEntity taxClassCodeEntity) {
        return orderInvoiceInfoService.getTaxClassCodeSpjc(taxClassCodeEntity);
    }

    /**
     * 开票-购方名称模糊查询
     */
    @ApiOperation("购方名称模糊查询")
    @PostMapping("getGhfmcByLike")
    public R getGhfmcByLike(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return orderInvoiceInfoService.getGhfmcByLike(orderInvoiceInfoEntity);
    }

    /**
     * 开票-购方精确查询
     */
    @ApiOperation("购方名称模糊查询")
    @PostMapping("getGhfmc")
    public R getGhfmc(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return orderInvoiceInfoService.getGhfmc(orderInvoiceInfoEntity);
    }

    /**
     * 开票-智能赋码推荐的商品编码
     */
    @ApiOperation("智能赋码推荐的商品编码")
    @PostMapping("getSsbmByLike")
    public R getSsbmByLike(@RequestBody OrderInvoiceItemEntity orderInvoiceItemEntity) {
        return orderInvoiceInfoService.getSsbmByLike(orderInvoiceItemEntity);
    }

    /**
     * 待处理页签的数据可以标记为无需开票
     */
    @ApiOperation("待处理页签的数据可以标记为无需开票")
    @PostMapping("noNeedOrderInvoiceInfo")
    public R noNeedOrderInvoiceInfo(@RequestBody IdsDTO idsDTO) {
        return orderInvoiceInfoService.noNeedOrderInvoiceInfo(idsDTO);
    }

    /**
     * 已手工开票下拉框
     */
    @ApiOperation("已手工开票下拉框")
    @PostMapping("manualInvoiceInfo")
    public R manualInvoiceInfo() {
        return orderInvoiceInfoService.manualInvoiceInfo();
    }

    /**
     * 已手工开票确定
     */
    @ApiOperation("已手工开票确定")
    @PostMapping("manualInvoiceInfoDeter")
    public R manualInvoiceInfoDeter(@RequestBody ManualInvoiceReq manualInvoiceReq) {
        return orderInvoiceInfoService.manualInvoiceInfoDeter(manualInvoiceReq);
    }

    /**
     * 发票开具、发票暂存 编辑订单金额 大于本身应收单的金额做比较给提示
     */
    @ApiOperation("发票开具、发票暂存 编辑订单金额 大于本身应收单的金额做比较给提示")
    @PostMapping("editCompareJeInvoiceInfo")
    public R editCompareJeInvoiceInfo(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return orderInvoiceInfoService.editCompareJeInvoiceInfo(orderInvoiceInfoEntity);
    }

    /**
     * 无需开票页签的数据可以标记为待处理
     */
    @ApiOperation("无需开票页签的数据可以标记为待处理")
    @PostMapping("/noNeedToWaitInvoiceInfo")
    public R noNeedToWaitInvoiceInfo(@RequestBody IdsDTO idsDTO) {
        return orderInvoiceInfoService.noNeedToWaitInvoice(idsDTO);
    }

    /**
     * 无需开票页签查询无需开票数据
     */
    @ApiOperation("无需开票页签查询无需开票数据")
    @PostMapping("/SelectnoNeedInvoiceInfo")
    public R SelectnoNeedInvoiceInfo(@RequestBody NoOrderInvoiceInfoListDTO noOrderInvoiceInfoListDTO) {
        return orderInvoiceInfoService.SelectnoNeedInvoiceInfo(noOrderInvoiceInfoListDTO);
    }

    /**
     * 批量开票无需开票-查看详情
     */
    @ApiOperation("计算不含税金额，税额，价税合计")
    @PostMapping("/NoNeedDetails")
    public R NoNeedDetails(@RequestBody IdsDTO idsDTO) {
        return orderInvoiceInfoService.NoNeedDetails(idsDTO);
    }

    /**
     * 批量开票-无需开票-编辑备注
     */
    @ApiOperation("批量开票-无需开票-编辑备注")
    @PostMapping("updateNoNeedBz")
    public R updateNoNeedBz(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        return orderInvoiceInfoService.updateNoNeedBzInfo(orderInvoiceInfoEntity);
    }

    /**
     * 开票记录界面单独使用的重试接口
     */
    @ApiOperation("开票记录界面-重试接口")
    @PostMapping("orderInvoiceIssueForRecord")
    public R orderInvoiceIssueForRecord(@RequestBody String ids) {
        log.info("开票记录界面-重试接口请求参数为:{}", ids);
        List<String> msg = new ArrayList<>();
        if (StringUtils.isEmpty(ids)) {
            msg.add("勾选订单不能为空");
            return R.setCodeAndMsg(OrderInfoContentEnum.ORDER_ISSUE, JsonUtils.getInstance().toJsonString(msg));
        }
        String idList = JSON.parseObject(ids).get("ids").toString();
        String[] split = idList.split(",");
        List<String> strings = Arrays.asList(split);
        return orderInvoiceInfoService.orderInvoiceIssue(strings);
    }

    /**
     * 开票记录界面单独使用的发票暂存接口
     */
    @ApiOperation("开票记录页面-发票暂存")
    @PostMapping("orderInvoiceTemporaryForRecord")
    public R orderInvoiceTemporaryForRecord(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("开票记录页面-发票暂存请求参数为:{}", JSON.toJSONString(orderInvoiceInfoEntity));
        return orderInvoiceInfoService.orderInvoiceTemporary(orderInvoiceInfoEntity);
    }

    /**
     * 开票记录界面开票失败时编辑发票填开-发票开具接口
     */
    @ApiOperation("开票记录界面-发票填开-发票开具")
    @PostMapping("invoiceInputIssueForRecord")
    public R invoiceInputIssueForRecord(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("开票记录界面-发票填开-发票开具接口请求参数为:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
        return orderInvoiceInfoService.invoiceInputIssue(orderInvoiceInfoEntity);
    }
}
