package com.dxhy.order.modules.controller;


import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.entity.DataStaticsQueryResultList;
import com.dxhy.order.modules.entity.DataStatisticQueryList;
import com.dxhy.order.modules.service.FileOperateService;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 数据统计
 *
 * <AUTHOR>
 * @email
 * @date 2028-06-23 15:07:24
 */
@Api(tags = "数据统计")
@RestController
@RequestMapping("dataStaistic")
public class DataStatisticsController {

    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;

    @Autowired
    private FileOperateService fileOperateService;


    /**
     *  数据统计 - 按项目统计
     */
    @ApiOperation("数据统计--按项目统计")
    @PostMapping("/queryXMStaisticList")
    public R queryXMStaisticList(@RequestBody DataStatisticQueryList dataStatisticQueryList){
        List<Map<String, Object>> list = orderInvoiceInfoService.queryXMStaisticList(dataStatisticQueryList);
        return R.ok().put(OrderManagementConstant.DATA, list);
    }

    /**
     *  数据统计 - 按发票种类统计
     */
    @ApiOperation("数据统计--按发票种类统计")
    @PostMapping("/queryZLStaisticList")
    public R queryZLStaisticList(@RequestBody DataStatisticQueryList dataStatisticQueryList){
        List<DataStaticsQueryResultList> dataStaticsQueryResultLists = orderInvoiceInfoService.queryZLStaisticList(dataStatisticQueryList);
        return R.ok().put(OrderManagementConstant.DATA, dataStaticsQueryResultLists);
    }

    /**
     *  数据统计 - 导出
     */
    @ApiOperation("数据统计--导出")
    @PostMapping("/exportStaisticList")
        public void exportStaisticList(@RequestBody DataStatisticQueryList dataStatisticQueryList, HttpServletRequest request, HttpServletResponse response){
    //public void exportStaisticList(HttpServletRequest request, HttpServletResponse response){

//        // 测试数据
//        DataStatisticQueryList dataStatisticQueryList1 = new DataStatisticQueryList();
//        dataStatisticQueryList1.setFpzldm("");
//        dataStatisticQueryList1.setTjfs("3");
//        dataStatisticQueryList1.setTime("2022");
//        dataStatisticQueryList1.setJd("");
//        dataStatisticQueryList1.setBaseNsrsbh("w");

        fileOperateService.exportStaisticList(dataStatisticQueryList, request, response);
    }
}
