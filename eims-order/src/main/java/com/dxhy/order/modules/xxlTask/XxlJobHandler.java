package com.dxhy.order.modules.xxlTask;

import com.dxhy.order.modules.entity.NsrsbhTenantRelationEntity;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.utils.JsonUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;


/**
 * 定时任务
 * @auther biaopu
 * @date 2022-10-14
 */
@Component
@Slf4j
public class XxlJobHandler {

    private static String LOGGER = "XXL定时任务";

    @Autowired
    private OpenOderInvoiceIssueService openOderInvoiceIssueService;
    @Autowired
    private RedInvoiceConfirmService redInvoiceConfirmService;
    @Autowired
    private SalerWarningService salerWarningService;
    @Autowired
    private NsrsbhTenantRelationService nsrsbhTenantRelationService;
    @Autowired
    private TenantRdsService tenantRdsService;
    @Autowired
    private SceneTemplateService sceneTemplateService;
    @Autowired
    private AdditionElementService additionElementService;


    /**
     * 定时任务拉取开票结果 （无入参）
     */
    @XxlJob("getTaskInvoiceInfo")
    public void getTaskInvoiceInfoJobHandler()  {
        try{
            log.info("{}，开始执行拉取开票结果任务", LOGGER);
            openOderInvoiceIssueService.getTaskInvoiceInfo();
        }catch (Exception e){
            log.error("{}，执行拉取开票结果任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时任务-选择票据-抽取发票信息 （已加参数）
     * 定时任务入参说明；{"flag":"1","nsrsbh":"91441900797773851T","beginTime":"2022-10-01","endTime":"2022-10-12"}
     *      flag: 非空， flag为0代表其他参数为空，flag为1代表其他参数有值
     *      nsrsbh:     flag为1时 非空， 91441900797773851T
     *      beginTime:  flag为1时 非空，格式 ：yyyy-MM-dd
     *      endTime：   flag为1时 非空，格式 ：yyyy-MM-dd
     */
    @XxlJob("getInvoiceInfosAndItems")
    public void getInvoiceInfosAndItemsJobHandler()  {
        try{
            log.info("{}，开始执行 发票基础信息查询 任务", LOGGER);
            String param = XxlJobHelper.getJobParam();
            redInvoiceConfirmService.getInvoiceInfosAndItemsResult(param);
        }catch (Exception e){
            log.error("{}，执行   发票基础信息查询 异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时拉取红字确认单结果信息 （无入参）
     */
    @XxlJob("pullRedInvoiceConfirmSldh")
    public void pullRedInvoiceConfirmSldhJobHandler()  {
        try{
            log.info("{}，开始执行拉取红字确认单结果信息任务", LOGGER);
            redInvoiceConfirmService.pullRedInvoiceConfirmSldhResult();
        }catch (Exception e){
            log.error("{}，执行拉取红字确认单结果信息任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时拉取红票结果信息  （无入参）
     */
    @XxlJob("pullRedInvoiceResultInfo")
    public void pullRedInvoiceResultInfo()  {
        try{
            log.info("{}，开始执行取红票结果信息任务", LOGGER);
            redInvoiceConfirmService.pullRedInvoiceResultInfoResult();
        }catch (Exception e){
            log.error("{}，执行取红票结果信息任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时查询 我收到的确认单 （已加参数）
     * 请查看getInvoiceInfosAndItems定时任务参数说明
     */
    @XxlJob("queryHzqrxx")
    public void queryHzqrxx()  {
        try{
            log.info("{}，开始执行查询我收到的确认单任务", LOGGER);
            String param = XxlJobHelper.getJobParam();
            redInvoiceConfirmService.queryHzqrxxResult(param);
        }catch (Exception e){
            log.error("{}，执行查询我收到的确认单任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时查询确认单状态(我收到的) 02 03的  （无需参数，调用接口：4.3.6 ，获取确认单状态，明细查询）
     *  ("定时查询 02销方录入待购方确认 03购方录入待销方确认")
     */
    @XxlJob("queryHzqrxxStatus")
    public void queryHzqrxxStatus()  {
        try{
            log.info("{}，开始执行查询确认单状态任务", LOGGER);
            redInvoiceConfirmService.queryHzqrxxStatusResult();
        }catch (Exception e){
            log.error("{}，执行查询确认单状态任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时查询 我发出的确认单  （已加参数，查询uuid，更新到数据库，不需要全部入库只需要更新uuid 和zt 即可）
     * 定时任务入参说明：请查看getInvoiceInfosAndItems定时任务参数说明
     */
    @XxlJob("queryHzqrxxOut")
    public void queryHzqrxxOut()  {
        try{
            log.info("{}，开始执行查询我发出的确认单任务", LOGGER);
            String param = XxlJobHelper.getJobParam();
            redInvoiceConfirmService.queryHzqrxxOutResult(param);
        }catch (Exception e){
            log.error("{}，执行查询我发出的确认单任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 我发起的 确认单状态 根据确认单受理结果的sldh 去定时更新状态。 （无入参  4.3.2 根据受理单号查状态和编号）
     */
    @XxlJob("getComfirmSldStatus")
    public void getComfirmSldStatus()  {
        try{
            log.info("{}，开始执行查询我发起的确认单状态任务", LOGGER);
            redInvoiceConfirmService.getComfirmSldStatusResult();
        }catch (Exception e){
            log.error("{}，执行查询我发起的确认单状态任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时任务对已开发票额度进行预警计算
     */
    @XxlJob("warningTask")
    public void warningTask()  {
        try{
            log.info("{}，开始执行定时任务预警提醒任务", LOGGER);
            DynamicDataSource.setDataSourceDefault();
            List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                Set<String> tenantCodeList = new HashSet<>();
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
                }
                for (String tenantCode : tenantCodeList) {
                    String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                    if (!StringUtils.isEmpty(changeTenantResult)) {
                        log.info("warningTask 切换业务数据源失败tenantCode: {}", tenantCode);
                        continue;
                    }
                    salerWarningService.warningTask();
                }
            } else {
                log.info("warningTask 销方税号租户关联表数据为空");
            }
        }catch (Exception e){
            log.error("{}，执行定时任务预警提醒任务异常：{}", LOGGER, e);
        }
    }


    /**
     * 定时任务查询附加要素
     */
    @XxlJob("queryFjysTask")
    public void queryFjysTask()  {
        try{
            log.info("{}，开始执行附加要素和场景模板查询任务", LOGGER);
            String param = XxlJobHelper.getJobParam();
            Map map = JsonUtils.getInstance().parseObject(param, HashMap.class);

            DynamicDataSource.setDataSourceDefault();
            List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                String nsrsbh = map.get("nsrsbh").toString();
                if (StringUtils.isEmpty(nsrsbh)) {
                    for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                        // 获取数据源
                        String changeTenantResult = tenantRdsService.switchRds(nsrsbhTenantRelationEntity.getTenantCode());
                        if (!StringUtils.isEmpty(changeTenantResult)) {
                            log.info("queryFjysCjmbTask 切换业务数据源失败tenantCode: {}", nsrsbhTenantRelationEntity.getTenantCode());
                            continue;
                        }
                        // 附加要素
                        additionElementService.getAdditionElementTask(nsrsbhTenantRelationEntity.getNsrsbh());
                    }
                } else {
                    String tenantCode = "";
                    for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                        // 获取数据源
                        if (nsrsbhTenantRelationEntity.getNsrsbh().equals(nsrsbh)) {
                            tenantCode = nsrsbhTenantRelationEntity.getTenantCode();
                            break;
                        }
                    }
                    String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                    if (!StringUtils.isEmpty(changeTenantResult)) {
                        log.info("warningTask 切换业务数据源失败tenantCode: {}", tenantCode);
                    }
                    // 附加要素
                    additionElementService.getAdditionElementTask(nsrsbh);
                }
            } else {
                log.info("queryFjysCjmbTask 销方税号租户关联表数据为空");
            }
        }catch (Exception e){
            log.error("{}，执行执行附加要素和场景模板查询异常：{}", LOGGER, e);
        }
    }

    /**
     * 定时任务查询场景模板
     */
    @XxlJob("queryCjmbTask")
    public void queryCjmbTask()  {
        try{
            log.info("{}，开始执行附加要素和场景模板查询任务", LOGGER);
            String param = XxlJobHelper.getJobParam();
            Map map = JsonUtils.getInstance().parseObject(param, HashMap.class);

            DynamicDataSource.setDataSourceDefault();
            List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                String nsrsbh = map.get("nsrsbh").toString();
                if (StringUtils.isEmpty(nsrsbh)) {
                    for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                        // 获取数据源
                        String changeTenantResult = tenantRdsService.switchRds(nsrsbhTenantRelationEntity.getTenantCode());
                        if (!StringUtils.isEmpty(changeTenantResult)) {
                            log.info("queryFjysCjmbTask 切换业务数据源失败tenantCode: {}", nsrsbhTenantRelationEntity.getTenantCode());
                            continue;
                        }
                        // 场景模板
                        sceneTemplateService.getSceneTemplateTask(nsrsbhTenantRelationEntity.getNsrsbh());
                    }
                } else {
                    String tenantCode = "";
                    for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                        // 获取数据源
                        if (nsrsbhTenantRelationEntity.getNsrsbh().equals(nsrsbh)) {
                            tenantCode = nsrsbhTenantRelationEntity.getTenantCode();
                            break;
                        }
                    }
                    String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                    if (!StringUtils.isEmpty(changeTenantResult)) {
                        log.info("warningTask 切换业务数据源失败tenantCode: {}", tenantCode);
                    }
                    // 场景模板
                    sceneTemplateService.getSceneTemplateTask(nsrsbh);
                }
            } else {
                log.info("queryFjysCjmbTask 销方税号租户关联表数据为空");
            }
        }catch (Exception e){
            log.error("{}，执行执行附加要素和场景模板查询异常：{}", LOGGER, e);
        }
    }


}