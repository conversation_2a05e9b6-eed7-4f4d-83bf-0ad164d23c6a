package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.service.OrderInvoiceItemService;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Arrays;


/**
 * 订单开票明细表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 18:31:05
 */
@Api(tags = "发票明细行管理")
@RestController
@RequestMapping("orderInvoiceItem")
@Slf4j
public class OrderInvoiceItemController {

    @Value("${order.invoice.item.temPath}")
    private String orderInvoiceItemPath;

    @Autowired
    private OrderInvoiceItemService orderInvoiceItemService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestBody OrderInvoiceItemEntity orderInvoiceItemEntity) {
        PageUtils page = orderInvoiceItemService.queryPage(orderInvoiceItemEntity);
        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") String id) {
        OrderInvoiceItemEntity orderInvoiceItem = orderInvoiceItemService.getById(id);
        return R.ok().put("orderInvoiceItem", orderInvoiceItem);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody OrderInvoiceItemEntity orderInvoiceItem) {
        orderInvoiceItemService.save(orderInvoiceItem);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody OrderInvoiceItemEntity orderInvoiceItem) {
        orderInvoiceItemService.updateById(orderInvoiceItem);//全部更新
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody String[] ids) {
        orderInvoiceItemService.removeBatchByIds(Arrays.asList(ids));
        return R.ok();
    }

    /**
     * 下载发票明细导入模板
     */
    @ApiOperation("下载发票明细导入模板")
    @PostMapping("downLoadInvoiceItemTemplate")
    public void downLoadInvoiceItemTemplate(HttpServletResponse response) {
        try {
            byte[] bytes = FileUtils.readFileToByteArray(new File(orderInvoiceItemPath));
            if (response != null) {
                // response.setContentType("application/vnd.ms-excel;charset=utf-8");
                response.setContentType("application/octet-stream;charset=utf-8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=\"" + new String(("发票开具项目信息导入模板" + ".xls").getBytes("gb2312"), "ISO8859-1"));
                OutputStream out = response.getOutputStream();
                out.write(bytes);
                out.close();
            }
        } catch (IOException e) {
            log.error("下载发票明细导入模板异常:{}", e);
        }
    }

    /**
     * 上传发票明细信息
     */
    @ApiOperation("上传发票明细信息")
    @PostMapping("uploadInvoiceItemInfo")
    public R uploadInvoiceItemInfo(@RequestParam("file") MultipartFile file, @RequestParam("HSBZ") String hsbs, @RequestParam("baseNsrsbh") String baseNsrsbh) {
        log.info("上传发票明细信息 uploadInvoiceItemInfo入参: file: {},hsbs: {}", file.getOriginalFilename(), hsbs);
        return orderInvoiceItemService.uploadInvoiceItemInfo(file, hsbs, baseNsrsbh);
    }

}
