package com.dxhy.order.modules.controller;

import com.dxhy.order.modules.pojo.dto.OneParamDTO;
import com.dxhy.order.modules.service.TaxClassCodeService;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 税收分类编码表 controller
 * <AUTHOR>
 * @Date 2022/6/27 12:26
 * @Version 1.0
 **/
@RestController
@Api(tags = "税收分类编码模块")
@Slf4j
@RequestMapping("/templateAdditionRelation")
public class TaxClassCodeController {
    private final String modelName = "税收分类编码模块";
    @Autowired
    private TaxClassCodeService taxClassCodeService;

    /**
     * 通过名称搜索列表
     */
    @PostMapping("/listByName")
    @ApiOperation(value = "通过名称搜索列表")
    public R listByName(@RequestBody OneParamDTO oneParamDTO){
        log.info("{} - listByName - {}", this.modelName, oneParamDTO);
        return taxClassCodeService.listByName(oneParamDTO.getStr());
    }

    /**
     * 通过ID搜索下级
     */
    @PostMapping("/listById")
    @ApiOperation(value = "通过ID搜索下级")
    public R listById(@RequestBody OneParamDTO oneParamDTO){
        log.info("{} - listById - {}", this.modelName, oneParamDTO);
        return taxClassCodeService.listById(oneParamDTO.getStr());
    }


}
