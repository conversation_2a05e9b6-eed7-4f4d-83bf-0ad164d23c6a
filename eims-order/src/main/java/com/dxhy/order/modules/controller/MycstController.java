package com.dxhy.order.modules.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dxhy.order.modules.dao.MycstRegionDao;
import com.dxhy.order.modules.entity.MycstRegionEntity;
import com.dxhy.order.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 税航数据查询模块 controller
 * 查询税航数据
 * <AUTHOR>
 * @Date 2024/12/29 12:26
 * @Version 1.0
 **/
@RestController
@Api(tags = "税航数据查询模块")
@Slf4j
@RequestMapping("/mycst")
public class MycstController {

    @Resource
    private MycstRegionDao mycstRegionDao;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String REGION_KEY = "mycstRegion";

    @PostMapping("/getRegionInfo")
    @ApiOperation(value = "获取地区数据")
    public R getRegionInfo(){
        List<MycstRegionEntity> mycstRegionEntities = (List) redisTemplate.opsForList().range(REGION_KEY, 0, -1);
        if(CollectionUtils.isEmpty(mycstRegionEntities)){
            LambdaQueryWrapper<MycstRegionEntity> wrapper = Wrappers.lambdaQuery();
            wrapper.select(MycstRegionEntity::getName);
            mycstRegionEntities = mycstRegionDao.selectList(wrapper);
            if(CollectionUtils.isEmpty(mycstRegionEntities)){
                return R.error("无地区数据");
            }else {
                for(MycstRegionEntity regionEntity : mycstRegionEntities){
                    redisTemplate.opsForList().rightPush(REGION_KEY, regionEntity);
                }
                // 设置过期时间为30天
                // redisTemplate.expire(REGION_KEY, 30, TimeUnit.DAYS);
            }
        }
        return R.ok().put("data", mycstRegionEntities);
    }

}
