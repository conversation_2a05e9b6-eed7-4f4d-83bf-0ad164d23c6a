package com.dxhy.order.openapi.controller;

import com.dxhy.order.modules.entity.BlueInvoicesIssueRes;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.service.InvoiceIssueService;
import com.dxhy.order.modules.service.OpenOderInvoiceIssueService;
import com.dxhy.order.modules.service.RedInvoiceConfirmService;
import com.dxhy.order.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/openInvoice")
@Slf4j
@Api(tags = "对外开票模块")
@RefreshScope
public class OpenOderInvoiceIssueController {

    @Autowired
    OpenOderInvoiceIssueService openOderInvoiceIssueService;

    @Autowired
    private RedInvoiceConfirmService redInvoiceConfirmService;

    @Autowired
    InvoiceIssueService invoiceIssueService;

    /**
     * 发票开具
     */
    @PostMapping("AllocateInvoices")
    @ApiOperation("对外发票开具")
    public String allocateInvoices(@RequestParam("SecretId") String secretId,@RequestParam("Timestamp") String timestamp,@RequestParam("Nonce") String nonce
            ,@RequestParam("Signature") String signature,@RequestParam("encryptCode") String encryptCode,@RequestParam("zipCode") String zipCode,@RequestParam("content") String content) throws Exception {
        log.info("对外发票开具接口AllocateInvoices入参: secretId:{},timestamp:{},nonce:{},signature:{},encryptCode:{},zipCode:{},content:{}",
                secretId,timestamp,nonce,signature,encryptCode,zipCode,content);
        return openOderInvoiceIssueService.allocateInvoices(secretId,timestamp,nonce,signature,encryptCode,zipCode,content);
    }

    /**
     * 发票查询
     */
    @PostMapping("GetOrderInfoAndInvoiceInfo")
    @ApiOperation("对外发票查询")
    public String getOrderInfoAndInvoiceInfo(@RequestParam("SecretId") String secretId,@RequestParam("Timestamp") String timestamp,@RequestParam("Nonce") String nonce
            ,@RequestParam("Signature") String signature,@RequestParam("encryptCode") String encryptCode,@RequestParam("zipCode") String zipCode,@RequestParam("content") String content){
        log.info("对外发票查询接口GetOrderInfoAndInvoiceInfo入参: secretId:{},timestamp:{},nonce:{},signature:{},encryptCode:{},zipCode:{},content:{}",
                secretId,timestamp,nonce,signature,encryptCode,zipCode,content);
        return openOderInvoiceIssueService.getOrderInfoAndInvoiceInfo(secretId,timestamp,nonce,signature,encryptCode,zipCode,content);
    }

    /**
     * 蓝字发票开具接口
     */
    @RequestMapping(method = RequestMethod.POST,value = "blueInvoiceIssue")
    @ApiOperation("蓝字发票开具接口")
    public BlueInvoicesIssueRes blueInvoiceIssue(@RequestBody List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities) {
        log.info("蓝字发票开具接口blueInvoiceIssue start:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntities));
        return openOderInvoiceIssueService.blueInvoiceIssue(orderInvoiceInfoEntities);
    }

//    /**
//     * 定时任务拉取开票结果 [已集成]
//     */
//    @GetMapping("getTaskInvoiceInfo")
//    @ApiOperation("对外发票查询定时任务")
//    @Scheduled(cron = "${order.invoice.invoiceIssue}")
//    public void getTaskInvoiceInfo(){
//        openOderInvoiceIssueService.getTaskInvoiceInfo();
//    }

//    /**
//     * 定时任务获取红字信息表编号及状态 [无需集成]  注释掉（无需加入XXL）RPA获取红字信息表编号及状态
//     */
//    @GetMapping("taxGenerateRedTableResult")
//    @ApiOperation("定时任务获取红字信息表编号及状态")
//    @Scheduled(cron = "${order.invoice.generateRedTableResult}")
//    public void taxGenerateRedTableResult(){
//        openOderInvoiceIssueService.taxGenerateRedTableResult();
//    }

//    /**
//     * 定时任务-选择票据-抽取发票信息 [已集成]
//     */
//    @ApiOperation("选择票据-抽取发票信息")
//    @GetMapping("/getInvoiceInfosAndItems")
//    @Scheduled(cron = "${order.invoice.getInvoiceInfosAndItemsResult}")
//    public R getInvoiceInfosAndItems(){
//        return redInvoiceConfirmService.getInvoiceInfosAndItemsResult();
//    }

//    /**
//     * 定时拉取红字确认单结果信息 [已集成]（无入参）
//     */
//    @ApiOperation("定时拉取红字确认单信息")
//    @GetMapping("/pullRedInvoiceConfirmSldh")
//    @Scheduled(cron = "${order.invoice.pullRedInvoiceConfirmSldhResult}")
//    public R pullRedInvoiceConfirmSldh(){
//        return redInvoiceConfirmService.pullRedInvoiceConfirmSldhResult();
//    }

//    /**
//     * 定时拉取红票结果信息  [已集成]（无需入参）
//     */
//    @ApiOperation("定时拉取红票结果信息")
//    @GetMapping("/pullRedInvoiceResultInfo")
//    @Scheduled(cron = "${order.invoice.pullRedInvoiceResultInfoResult}")
//    public R pullRedInvoiceResultInfo(){
//        return redInvoiceConfirmService.pullRedInvoiceResultInfoResult();
//    }

//    /**
//     * 定时查询 我收到的确认单 [已集成]
//     */
//    @ApiOperation("定时查询 我收到的确认单")
//    @GetMapping("/queryHzqrxx")
//    @Scheduled(cron = "${order.invoice.queryHzqrxxResult}")
//    public R queryHzqrxx(){
//        return redInvoiceConfirmService.queryHzqrxxResult();
//    }

//    /**
//     * 定时查询 确认单状态 02 03的 [已集成] （无需参数，调用接口：4.3.6 ，获取确认单状态，明细查询）
//     */
//    @ApiOperation("定时查询 02销方录入待购方确认 03购方录入待销方确认")
//    @GetMapping("/queryHzqrxxStatus")
//    @Scheduled(cron = "${order.invoice.queryHzqrxxStatusResult}")
//    public R queryHzqrxxStatus(){
//        return redInvoiceConfirmService.queryHzqrxxStatusResult();
//    }

//    /**
//     * 定时查询 我发出的确认单 [已集成]
//     */
//    @ApiOperation("定时查询 我发出的确认单")
//    @GetMapping("/queryHzqrxxOut")
//    @Scheduled(cron = "${order.invoice.queryHzqrxxOutResult}")
//    public R queryHzqrxxOut(){
//        return redInvoiceConfirmService.queryHzqrxxOutResult();
//    }

    /**
     * 维护自己开出发票的 撤销需要的uuid [无需集成]
     */
    /*@ApiOperation("维护自己开出发票的 撤销需要的uuid")
    @GetMapping("/getComfirmJgcxUUid")
    @Scheduled(cron = "${order.invoice.getComfirmJgcxUUid}")
    public R getComfirmJgcxUUid(){
        return redInvoiceConfirmService.getComfirmJgcxUUidResult();
    }*/

//    /**
//     * 我发起的 确认单状态 根据确认单受理结果的sldh 去定时更新状态。  [已集成]  （无需入参  4.3.2 根据受理单号查状态和编号）
//     */
//    @ApiOperation("我发起的 定时更新确认单状态")
//    @GetMapping("/getComfirmSldStatus")
//    @Scheduled(cron = "${order.invoice.getComfirmSldStatus}")
//    public R getComfirmSldStatus(){
//        return redInvoiceConfirmService.getComfirmSldStatusResult();
//    }
}
