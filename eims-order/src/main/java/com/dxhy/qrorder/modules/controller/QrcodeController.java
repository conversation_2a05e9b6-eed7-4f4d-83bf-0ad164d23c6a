package com.dxhy.qrorder.modules.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dxhy.order.config.OpenApiConfig;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.model.page.PageEwmConfigInfo;
import com.dxhy.order.model.page.PageOrderItemInfo;
import com.dxhy.order.model.page.QrcodeOrderInfo;
import com.dxhy.order.modules.entity.InvoiceIssueRes;
import com.dxhy.order.modules.entity.PageQrcodeOrderInfo;
import com.dxhy.order.modules.entity.PageQrcodeOrderItemInfo;
import com.dxhy.order.modules.service.OrderCommonService;
import com.dxhy.order.modules.service.QrcodeService;
import com.dxhy.order.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @ClassName ：QrcodeController
 * @Description ：订单接收controller
 * @date 创建时间: 2022-06-29 09:42
 */
@RestController
@Api(tags = "扫码开票")
@RequestMapping("/qrCode")
@Slf4j
public class QrcodeController {
    
    private static final String LOGGER_MSG = "(二维码扫开)";
    
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#0.00");
    
    @Resource
    QrcodeService qrcodeService;
    
    @Resource
    OrderCommonService apiInvoiceCommonService;
    
    @Resource
    private OpenApiConfig openApiConfig;
    
    /**
     * 生成二维码的接口
     *
     * @param qrcodeOrderInfo
     * @return
     */
    @PostMapping("/generateQrCode")
    @ApiOperation(value = "根据销方相关配置生成静态二维码", notes = "审核开票-根据销方相关配置生成静态二维码")
    public R generateQrcode(
            @ApiParam(name = "orderList", value = "orderList", required = true) @RequestBody QrcodeOrderInfo qrcodeOrderInfo) {

        log.info("{}生成二维码的接口:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(qrcodeOrderInfo));

        //校验项目名称是否重复
        boolean isFlag = checkOrderItemName(qrcodeOrderInfo);
        if (!isFlag) {
            return R.error().put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999).put(OrderManagementConstant.MESSAGE, "项目名称重复!");
        }

	    qrcodeOrderInfo.setTqm(apiInvoiceCommonService.getGenerateShotKey());
        String url = String.format(openApiConfig.configQrCodeScanUrl(), qrcodeOrderInfo.getTqm(), qrcodeOrderInfo.getXhfNsrsbh(), qrcodeOrderInfo.getQrCodeType());
        qrcodeOrderInfo.setQrCodeUrl(url);

        boolean saveQrcodeInfo = qrcodeService.saveQrcodeInfo(qrcodeOrderInfo);
        if (!saveQrcodeInfo) {
            return R.error().put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999).put(OrderManagementConstant.MESSAGE, "数据库保存失败!");
        }

        String imgUrl = String.format(openApiConfig.configQrCodeShortUrl(), qrcodeOrderInfo.getTqm());
        //生成qrcode的base64流
        String qrCodeString = QrCodeUtil.drawLogoQrCode(null, imgUrl, "", qrcodeOrderInfo.getBackGround());

        Map<String, Object> returnMap = new HashMap<>(5);
        returnMap.put("ewm", qrCodeString);
        returnMap.put("qrcodeUrl", imgUrl);

        return R.ok().put(OrderManagementConstant.CODE, OrderInfoContentEnum.SUCCESS.getKey()).put(OrderManagementConstant.MESSAGE, OrderInfoContentEnum.SUCCESS.getMessage())
                .put(OrderManagementConstant.DATA, returnMap);

    }

    /**
     * 二维码列表接口
     *
     * @return
     */
    @PostMapping("/qrCodeList")
    @ApiOperation(value = "静态二维码列表", notes = "审核开票-静态二维码列表")
    public R qrCodeList(@ApiParam(name = "ywlxId", value = "业务类型id") @RequestParam(value = "ywlxId", required = false) String ywlxId,
                        @ApiParam(name = "xmmc", value = "项目名称") @RequestParam(value = "xmmc", required = false) String xmmc,
                        @ApiParam(name = "xhfNsrsbh", value = "销方纳税人识别号", required = true) @RequestParam(value = "xhfNsrsbh",required = false) String xhfNsrsbh,
                        @ApiParam(name = "sjly", value = "来源") @RequestParam(value = "sjly", required = false) String sjly,
                        @ApiParam(name = "pageSize", value = "每页条数") @RequestParam(value = "pageSize", defaultValue = "10") String pageSize,
                        @ApiParam(name = "currentPage", value = "当前页") @RequestParam(value = "currentPage", defaultValue = "1") String currPage) {
        log.info("{}静态二维码列表接口", LOGGER_MSG);
    
        if (StringUtils.isBlank(xhfNsrsbh)) {
            log.error("{},请求税号为空!", LOGGER_MSG);
            return R.error(OrderInfoContentEnum.TAXCODE_ISNULL);
        }
    
        List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("ywlxId", ywlxId);
        paramMap.put("xmmc", xmmc);
        paramMap.put(ConfigureConstant.STRING_PAGE_SIZE, pageSize);
        paramMap.put(ConfigureConstant.STRING_CURR_PAGE, currPage);
        //查询列表数据
        PageUtils pageUtil = qrcodeService.queryQrCodeList(paramMap, shList);
        return R.ok().put("page", pageUtil);
    }
    
    /**
     * 二维码详情接口
     *
     * @return
     */
    @PostMapping("/qrCodeDetail")
    @ApiOperation(value = "静态二维码关联销方信息详情", notes = "审核开票-静态二维码关联销方信息详情")
    public R qrCodeDetail(@ApiParam(name = "qrcodeId", value = "二维码id", required = true) @RequestParam(value = "qrcodeId") String qrcodeId,
                          @ApiParam(name = "xhfNsrsbh", value = "销货方纳税人识别号", required = true) @RequestParam(value = "xhfNsrsbh") String xhfNsrsbh) {
        log.info("{}静态二维码详情接口", LOGGER_MSG);
        if (StringUtils.isBlank(qrcodeId)) {
            return R.error(OrderInfoContentEnum.INVOICE_PARAM_ERROR);
        }
        if (StringUtils.isBlank(xhfNsrsbh)) {
            log.error("{},请求税号为空!", LOGGER_MSG);
            return R.error(OrderInfoContentEnum.TAXCODE_ISNULL);
        }
    
        List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
        Map map = qrcodeService.queryQrCodeDetail(qrcodeId, shList);
        return R.ok().put(OrderManagementConstant.DATA, map);
    }
    
    
    /**
     * 获取二维码图片接口
     *
     * @return
     */
    @PostMapping("/qrCodeImg")
    @ApiOperation(value = "获取二维码图片接口（静态/动态）", notes = "二维码管理-获取二维码图片接口")
    public R qrCodeImg(@ApiParam(name = "qrcodeId", value = "二维码id", required = true) @RequestParam(value = "qrcodeId") String qrcodeId,
                       @ApiParam(name = "type", value = "二维码类型", required = true) @RequestParam(value = "type") String type,
                       @ApiParam(name = "xhfNsrsbh", value = "销货方纳税人识别号", required = true) @RequestParam(value = "xhfNsrsbh") String xhfNsrsbh,
                       @ApiParam(name = "backGround", value = "二维码背景色") @RequestParam(value = "backGround", required = false) String backGround) {
        log.info("{}二维码图片接口", LOGGER_MSG);
        if (StringUtils.isBlank(qrcodeId)) {
            return R.error(OrderInfoContentEnum.INVOICE_PARAM_ERROR);
        }
        if (StringUtils.isBlank(xhfNsrsbh)) {
            log.error("{},请求税号为空!", LOGGER_MSG);
            return R.error(OrderInfoContentEnum.TAXCODE_ISNULL);
        }
    
        List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
        Map map = qrcodeService.queryQrCodeImg(qrcodeId, type, shList, backGround);
        return R.ok().put(OrderManagementConstant.DATA, map);
    }


	/**
	 * 编辑静态码
	 * @return
	 */
	@PostMapping("/updateStaticEwmInfo")
	@ApiOperation(value = "编辑静态码", notes = "审核开票-编辑静态码")
	public R updatEwmInfo(
			@ApiParam(name = "orderList", value = "orderList", required = true) @RequestBody QrcodeOrderInfo qrcodeOrderInfo) {
        log.info("{},编辑静态码的接口，入参:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(qrcodeOrderInfo));
        if (qrcodeOrderInfo == null || StringUtils.isBlank(qrcodeOrderInfo.getId())) {
            return R.error(OrderInfoContentEnum.INVOICE_PARAM_ERROR);
        }
        
        return qrcodeService.updateStaticEwmInfo(qrcodeOrderInfo);
    }


	/**
	 * 删除功能
	 *
	 * @param qrcodeId
	 * @return
	 */
	@PostMapping("/deleteStaticEwmInfo")
	@ApiOperation(value = "删除静态码", notes = "审核开票-删除静态码")
	public R deleteStaticEwmInfo(@ApiParam(name = "qrcodeInfo", value = "二维码id和销货方税号", required = true) @RequestParam String qrcodeIds) {
		log.info("{}二维码详情接口", LOGGER_MSG);
		if (StringUtils.isBlank(qrcodeIds)) {
			return R.error(OrderInfoContentEnum.INVOICE_PARAM_ERROR);
		}
		List<String> idList = Arrays.stream(StringUtils.split(qrcodeIds, ",")).collect(Collectors.toList());

		return qrcodeService.deleteStaticEwmInfo(idList);
	}


    /**
     * 生成二维码时校验项目名称是否重复
     */
    private boolean checkOrderItemName(QrcodeOrderInfo qrcodeOrderInfo) {
        if (ObjectUtil.isNotEmpty(qrcodeOrderInfo.getOrderItemList())) {
            List<PageOrderItemInfo> list = qrcodeOrderInfo.getOrderItemList();
            List<String> xmmcList = new ArrayList<>();
            for (PageOrderItemInfo info : list) {
                xmmcList.add(info.getXmmc());
            }
            HashSet set = new HashSet<>(xmmcList);
            return set.size() == list.size();
        }
        return true;
    }

    /**
     * 优税发票小助手调用 产品无调用(API调用)
     *
     * @param pageQrcodeOrderInfo
     * @return
     */
    @ApiOperation(value = "生成动态码", notes = "扫码开票-生成动态码")
    @PostMapping("/generateDynamicQrCode")
    public InvoiceIssueRes generateDynamicQrCode(@RequestBody PageQrcodeOrderInfo pageQrcodeOrderInfo) {
        log.info("{}，小程序生成动态二维码的接口,入参:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(pageQrcodeOrderInfo));
        //对请求参数处理 处理含有优惠政策的数据
        List<PageQrcodeOrderItemInfo> pageOrderItemInfoList = pageQrcodeOrderInfo.getPageOrderItemInfoList();
        for (PageQrcodeOrderItemInfo orderItem : pageOrderItemInfoList) {
            if (StringUtils.isNotBlank(orderItem.getLslbs())) {
                if (OrderInfoEnum.LSLBS_0.getKey().equals(orderItem.getLslbs())) {
                    orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_1.getKey());
                    orderItem.setZzstsgl(OrderInfoEnum.LSLBS_0.getValue());

                } else if (OrderInfoEnum.LSLBS_1.getKey().equals(orderItem.getLslbs())) {
                    orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_1.getKey());
                    orderItem.setZzstsgl(OrderInfoEnum.LSLBS_1.getValue());

                } else if (OrderInfoEnum.LSLBS_2.getKey().equals(orderItem.getLslbs())) {
                    orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_1.getKey());
                    orderItem.setZzstsgl(OrderInfoEnum.LSLBS_2.getValue());

                } else if (OrderInfoEnum.LSLBS_3.getKey().equals(orderItem.getLslbs())) {
                    orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_0.getKey());
                    orderItem.setZzstsgl("");
                }

            }
        }
        return qrcodeService.generateDynamicQrCode(pageQrcodeOrderInfo);

    }
    
    
    /**
     * 动态码关联订单列表
     */
    @PostMapping("/dynamicQrCodeList")
    @ApiOperation(value = "动态码关联订单列表", notes = "扫码开票-动态码关联订单列表")
    public R dynamicQrCodeList(@ApiParam(name = "startTime", value = "订单开始时间", required = true) @RequestParam(value = "startTime") String startTime,
                               @ApiParam(name = "endTime", value = "订单结束时间", required = true) @RequestParam(value = "endTime") String endTime,
                               @ApiParam(name = "minJe", value = "最小金额", required = true) @RequestParam(value = "minJe", required = false) String minJe,
                               @ApiParam(name = "maxJe", value = "最大金额", required = true) @RequestParam(value = "maxJe", required = false) String maxJe,
                               @ApiParam(name = "ddh", value = "订单号", required = true) @RequestParam(value = "ddh", required = false) String ddh,
                               @ApiParam(name = "currentPage", value = "当前页") @RequestParam(value = "currentPage", required = false) String currPage,
                               @ApiParam(name = "pageSize", value = "页面条数") @RequestParam(value = "pageSize", required = false) String pageSize,
                               @ApiParam(name = "ghfmc", value = "购货方名称", required = true) @RequestParam(value = "ghfmc", required = false) String ghfmc,
                               @ApiParam(name = "fpzldm", value = "发票种类代码", required = true) @RequestParam(value = "fpzldm", required = false) String fpzldm,
                               @ApiParam(name = "kpzt", value = "开票状态", required = true) @RequestParam(value = "kpzt", required = false) String kpzt,
                               @ApiParam(name = "ewmzt", value = "二维码状态 0 未使用 1 已使用 2 已失效 3 已作废", required = true) @RequestParam(value = "ewmzt") String ewmzt,
                               @ApiParam(name = "startValidTime", value = "二维码有效截至日期起") @RequestParam(value = "startValidTime", required = false) String startValidTime,
                               @ApiParam(name = "endValidTime", value = "二维码有效截至日期止") @RequestParam(value = "endValidTime", required = false) String endValidTime,
                               @ApiParam(name = "sjly", value = "来源") @RequestParam(value = "sjly", required = false) String sjly,
                               @ApiParam(name = "xhfNsrsbh", value = "销货方纳税人识别号", required = true) @RequestParam(value = "xhfNsrsbh", required = false) String xhfNsrsbh) {
	    log.info("{}二维码列表接口", LOGGER_MSG);
	    if (StringUtils.isBlank(xhfNsrsbh)) {
		    log.error("{},请求税号为空!", LOGGER_MSG);
		    return R.error(OrderInfoContentEnum.TAXCODE_ISNULL);
	    }

	    List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
	    Map<String, Object> paramMap = new HashMap<>(5);
	    paramMap.put("startTime", startTime);
	    paramMap.put("endTime", endTime);
	    paramMap.put("ddh", ddh);
        if (StringUtils.isNotBlank(minJe)) {
            Double dl = Double.parseDouble(minJe);
            paramMap.put("minJe", DECIMAL_FORMAT.format(dl));
        }
        if (StringUtils.isNotBlank(maxJe)) {
            Double d2 = Double.parseDouble(maxJe);
            paramMap.put("maxJe", DECIMAL_FORMAT.format(d2));
        
        }
        paramMap.put(ConfigureConstant.STRING_CURR_PAGE, currPage);
        paramMap.put(ConfigureConstant.STRING_PAGE_SIZE, pageSize);
        paramMap.put("ghfmc", ghfmc);
        paramMap.put("fpzldm", fpzldm);
        paramMap.put("kpzt", kpzt);
        paramMap.put("ddly", sjly);
        //转换作废时间
        Date startVlidDate = null;
        if (StringUtils.isNotBlank(startValidTime)) {
            startVlidDate = DateUtil.parse(startValidTime, "yyyy-MM-dd");
            startVlidDate = DateUtil.beginOfDay(startVlidDate);
            startValidTime = DateUtil.format(startVlidDate, "yyyy-MM-dd HH:mm:ss");

		}
		Date endVlidDate = null;
		if (StringUtils.isNotBlank(endValidTime)) {
			endVlidDate = DateUtil.parse(endValidTime, "yyyy-MM-dd");
			endVlidDate = DateUtil.endOfDay(endVlidDate);
			endValidTime = DateUtil.format(endVlidDate, "yyyy-MM-dd HH:mm:ss");

		}
		paramMap.put("startValidTime", startValidTime);
		paramMap.put("endValidTime", endValidTime);


        //查询条件 二维码状态 转换 0 未使用 1 已使用 2 已失效 3 已作废
        if (OrderInfoEnum.EWM_STATUS_0.getKey().equals(ewmzt)) {
            // 四个条件互斥
        
            paramMap.put("ewmzt", ewmzt);
            // 为作废
            paramMap.put("zfzt", "0");
            // 未过期
            Date now = new Date();
            if (StringUtils.isNotBlank(startValidTime)) {
                if (!startVlidDate.after(now)) {
                    startVlidDate = DateUtil.beginOfDay(now);
                    startValidTime = DateUtil.format(startVlidDate, "yyyy-MM-dd HH:mm:ss");
                }
                paramMap.put("startValidTime", startValidTime);
            
            } else {
                paramMap.put("startValidTime", DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
            }
        
        } else if (OrderInfoEnum.EWM_STATUS_1.getKey().equals(ewmzt)) {
            paramMap.put("ewmzt", ewmzt);
        } else if (OrderInfoEnum.EWM_STATUS_2.getKey().equals(ewmzt)) {
            //已过期的需要根据失效时间查询 已失效并且为作废的数据
        
            Date now = new Date();
            if (StringUtils.isNotBlank(endValidTime)) {
                if (endVlidDate.before(now)) {
                    paramMap.put("endValidTime", endValidTime);
                } else {
                    paramMap.put("endValidTime", DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
                }
            
            } else {
                paramMap.put("endValidTime", DateUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
            }
            paramMap.put("zfzt", "0");
            paramMap.put("ewmzt", OrderInfoEnum.EWM_STATUS_0.getKey());
        } else if (OrderInfoEnum.EWM_STATUS_3.getKey().equals(ewmzt)) {
            //已作废的根据作废状态查询
            paramMap.put("zfzt", "1");
        
        }
        //查询列表数据
        log.info("查询接口参数:{}", JsonUtils.getInstance().toJsonString(paramMap));
	    PageUtils pageUtil = qrcodeService.queryDynamicQrcodeList(paramMap, shList);
		return R.ok().put("page", pageUtil);
    }

    
    /**
     * 动态码配置信息查询
     */
    @PostMapping("/queryEwmConfigInfo")
    @ApiOperation(value = "动态二维码配置信息查询", notes = "扫码开票-配置信息查询")
    public R queryEwmConfigInfo(
            @ApiParam(name = "xhfNsrsbh", value = "销货方纳税人识别号", required = true) @RequestParam(value = "xhfNsrsbh") String xhfNsrsbh) {
        if (StringUtils.isBlank(xhfNsrsbh)) {
            return R.error().put("9999", "销货方纳税人识别号不能为空!");
        }
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("xhfNsrsbh", xhfNsrsbh);
        
        Map<String, Object> queryEwmConfigInfo = qrcodeService.queryEwmConfigInfo(paramMap);
        return R.ok().put(OrderManagementConstant.DATA, queryEwmConfigInfo);
	}



	/**
	 * 新增或修改动态二维码配置
	 */
	@PostMapping("/updateEwmConfigInfo")
	@ApiOperation(value = "新增或修改动态二维码配置", notes = "扫码开票-新增或修改动态二维码配置")
	public R updateEwmConfigInfo(
			@RequestBody PageEwmConfigInfo ewmConfig) {
        if (ewmConfig == null || StringUtils.isBlank(ewmConfig.getXhfNsrsbh())) {
            return R.error().put("9999", "销货方纳税人识别号不能为空!");
        }
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("xhfNsrsbh", ewmConfig.getXhfNsrsbh());
        
        Map<String, Object> queryEwmConfigInfo = qrcodeService.queryEwmConfigInfo(paramMap);
        boolean b;
        if (queryEwmConfigInfo == null) {
            //新增二维码配置信息
            b = qrcodeService.addEwmConfigInfo(ewmConfig);
        } else {
            //更新二维码配置信息
            b = qrcodeService.updateEwmConfigInfo(ewmConfig);
        }
        if (!b) {
            log.error("添加二维码配置信息失败，税号:{}", ewmConfig.getXhfNsrsbh());
            return R.error().put("9999", "二维码配置信息设置失败!");
        }
        return R.ok().put("0000", "二维码配置信息设置成功!");
        
    }

    /**
     * 动态二维码关联订单详情查询
     */
    @PostMapping("/queryEwmDetailInfo")
    @ApiOperation(value = "动态二维码关联订单详情", notes = "扫码开票-动态二维码关联订单详情信息")
    public R queryEwmDetailInfo(
            @ApiParam(name = "fpqqlsh", value = "发票请求流水号", required = true) @RequestParam(value = "fpqqlsh") String fpqqlsh,
            @ApiParam(name = "xhfNsrsbh", value = "销货方纳税人识别号", required = true) @RequestParam(value = "xhfNsrsbh") String xhfNsrsbh) {
        if (StringUtils.isBlank(fpqqlsh)) {
            return R.error().put("9999", "请求参数不能为空");
        }
        if (StringUtils.isBlank(xhfNsrsbh)) {
            log.error("{},请求税号为空!", LOGGER_MSG);
            return R.error(OrderInfoContentEnum.TAXCODE_ISNULL);
        }

        List<String> shList = NsrsbhUtils.transShListByXhfNsrsbh(xhfNsrsbh);
        Map<String, Object> queryEwmConfigInfo = qrcodeService.queryEwmDetailByFpqqlsh(fpqqlsh, shList);
        return R.ok().put(OrderManagementConstant.DATA, queryEwmConfigInfo);
    }




}
