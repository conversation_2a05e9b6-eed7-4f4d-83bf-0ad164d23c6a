<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>eims-service</artifactId>
        <groupId>com.dxhy</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>eims-api</artifactId>
    <packaging>jar</packaging>

    <name>eims-api</name>

    <properties>
        <mysql.version>5.1.38</mysql.version>
        <apach-httpclient>4.5.1</apach-httpclient>
        <!-- commons-io版本-->
        <commonsIo.version>2.6</commonsIo.version>

        <commons.lang.version>2.6</commons.lang.version>
        <druid.version>1.1.10</druid.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <!-- Import dependency management from Spring Boot (依赖管理：继承一些默认的依赖，工程需要依赖的jar包的管理，申明其他dependency的时候就不需要version) -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- jetty -->
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-server</artifactId>
                <version>${jetty-server.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>${jetty-server.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http</artifactId>
                <version>${jetty-server.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-io</artifactId>
                <version>${jetty-server.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-client</artifactId>
                <version>${jetty-server.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- spring-boot-starter-web (spring-webmvc + tomcat) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commonsIo.version}</version>
            <scope>compile</scope>
        </dependency>



        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter.version}</version>
        </dependency>
        <!-- mysql驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons.lang.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!-- commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.12</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
            <scope>compile</scope>
        </dependency>
        <!-- Spring Boot 事物支持 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <!-- Spring Cloud Nacos Service Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.2.1.RELEASE</version>
            <scope>compile</scope>
        </dependency>

        <!-- Spring Cloud Nacos Service Discovery -->
        <!--<dependency>-->
            <!--<groupId>com.alibaba.cloud</groupId>-->
            <!--<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
            <!--<version>2.2.1.RELEASE</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${apach-httpclient}</version>
        </dependency>
        <!--mq-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dxhy</groupId>
            <artifactId>eims-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--增加eureka-server的依赖-->
        <!--<dependency>-->
        <!--<groupId>org.springframework.cloud</groupId>-->
        <!--<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>-->
        <!--<version>2.0.1.RELEASE</version>-->
        <!--</dependency>-->
    </dependencies>


    <profiles>
        <!--  开发环境  -->
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
                <!-- nacos -->
                <nacos.order.namespace>eims-api</nacos.order.namespace>
                <nacos.server.addr>10.1.5.202:33000</nacos.server.addr>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <!-- db master -->
                <db.master.url>****************************************************************************************************************</db.master.url>
                <db.master.username>invoice</db.master.username>
                <db.master.password>Invoice@.^.98</db.master.password>

            </properties>
            <!--  默认激活  -->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--  测试环境  -->
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
                <!-- nacos -->
                <nacos.order.namespace>eims-api</nacos.order.namespace>
                <nacos.server.addr>10.1.1.106:38848</nacos.server.addr>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <!-- db master -->
                <db.master.url>******************************************************************************************************************</db.master.url>
                <db.master.username>invoice</db.master.username>
                <db.master.password>Biaopudev@1234</db.master.password>

            </properties>
        </profile>
        <!--  yufa环境  -->
        <profile>
            <id>pre</id>
            <properties>
                <profileActive>pre</profileActive>
                <!-- nacos -->
                <nacos.order.namespace>eims-api</nacos.order.namespace>
                <nacos.server.addr>192.168.0.130:33000</nacos.server.addr>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <!-- db master -->
                <db.master.url>******************************************************************************************************************</db.master.url>
                <db.master.username>root</db.master.username>
                <db.master.password>Invoice@98BiaoPucloud</db.master.password>

            </properties>
        </profile>
        <!--  生产环境  -->
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
                <!-- nacos -->
                <nacos.order.namespace>eims-api</nacos.order.namespace>
                <nacos.server.addr>nacos-1.itax.local:8848</nacos.server.addr>
                <nacos.username>nacos</nacos.username>
                <nacos.password>nacos</nacos.password>
                <!-- db master -->
                <db.master.url>********************************************************************************************************************************************************************************************</db.master.url>
                <db.master.username>dxhy</db.master.username>
                <db.master.password>UYX^#okgyGZGGuxy</db.master.password>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!--<plugin>-->
                <!--<artifactId>maven-war-plugin</artifactId>-->
                <!--<version>2.6</version>-->
                <!--<configuration>-->
                    <!--&lt;!&ndash;如果想在没有web.xml文件的情况下构建WAR，请设置为false。&ndash;&gt;-->
                    <!--<failOnMissingWebXml>false</failOnMissingWebXml>-->
                <!--</configuration>-->
            <!--</plugin>-->
        </plugins>
    </build>

</project>