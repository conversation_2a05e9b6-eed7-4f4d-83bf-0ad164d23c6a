package com.dxhy.api.modules.controller;

import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.constant.RespStatusEnum;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.service.CommonInterfaceService;
import com.dxhy.order.modules.service.DzswjLoginService;
import com.dxhy.order.modules.service.InvoiceIssueService;
import com.dxhy.order.modules.service.QrcodeService;
import com.dxhy.order.pojo.ResponseData;
import com.dxhy.order.pojo.ResponseStatus;
import com.dxhy.order.pojo.Result;
import com.dxhy.order.utils.JacksonUtils;
import com.dxhy.order.utils.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.List;


@Api(tags = "对外发票开具")
@RestController
@Slf4j
@RequestMapping("/einvoice")
public class InvoiceIssueController {

    @Autowired
    InvoiceIssueService invoiceIssueService;

    @Autowired
    DzswjLoginService dzswjLoginService;

    @Autowired
    QrcodeService qrcodeService;

    @Resource
    private CommonInterfaceService commonInterfaceService;


    private static final String LOGGER_MSG = "(对外接口暴露调用)";




    @ApiOperation("对外开放接口")
    @PostMapping("/invoice/{interfaceName}")
    public String invoiceIssue(HttpServletRequest request, HttpServletResponse response,
                                        @PathVariable("interfaceName") String interfaceName,
                                        @ApiParam(name = "timestamp", value = "当前时间戳", required = true) @RequestParam(value = "Timestamp") String timestamp,
                                        @ApiParam(name = "nonce", value = "随机正整数", required = true) @RequestParam(value = "Nonce") String nonce,
                                        @ApiParam(name = "secretId", value = "标识用户身份的SecretId", required = true) @RequestParam(value = "SecretId") String secretId,
                                        @ApiParam(name = "signature", value = "请求签名", required = true) @RequestParam(value = "Signature") String signature,
                                        @ApiParam(name = "encryptCode", value = "加密标识 0:不加密,1:加密", required = true) @RequestParam(value = "encryptCode") String encryptCode,
                                        @ApiParam(name = "zipCode", value = "压缩标识 0:不压缩,1:压缩", required = true) @RequestParam(value = "zipCode") String zipCode,
                                        @ApiParam(name = "content", value = "业务请求参数", required = true) @RequestParam(value = "content") String content) {

            log.info("{},请求的secretId:{}，nonce:{},timestamp:{},zipCode:{},encryptCode:{},签名值sign:{}，内容content:{}", LOGGER_MSG, secretId, nonce, timestamp, zipCode, encryptCode, signature, content);
            Result result = new Result();
            ResponseStatus responseStatus = new ResponseStatus();
            ResponseData responseData = new ResponseData();

        try{
            request.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            /**
             * 校验接口入参是否为空
             */
            result = commonInterfaceService.checkInterfaceParam("", interfaceName, timestamp, nonce, secretId, signature, encryptCode, zipCode, content);

            responseStatus = (ResponseStatus) result.get(ConfigurerInfo.RESPONSESTATUS);

            if (!ConfigurerInfo.SUCCSSCODE.equals(responseStatus.getCode())) {
                log.error("{},数据格式校验未通过.", LOGGER_MSG);
                return JsonUtils.getInstance().toJsonString(result);
            }

            /**
             * 鉴权
             */
            result = commonInterfaceService.auth(request, response);
            //获取鉴权结果
            responseStatus = (ResponseStatus) result.get(ConfigurerInfo.RESPONSESTATUS);
            //获取密钥
            String secretKey = (String) result.get(ConfigurerInfo.SECRETKEY);
            if (!ConfigurerInfo.SUCCSSCODE.equals(responseStatus.getCode())) {
                log.error("{},鉴权未通过", LOGGER_MSG);
                return JsonUtils.getInstance().toJsonString(result);
            }
            //解密
            String commonDecrypt2 = commonInterfaceService.commonDecrypt(zipCode,encryptCode,content,secretKey);
            log.debug("{}解密日志：{}", LOGGER_MSG, commonDecrypt2);

            // 调用业务逻辑处理方法
            log.debug("{}开始调用业务方法：{}", LOGGER_MSG, interfaceName);
            String returnJsonString = orderApiHandingBusiness(interfaceName, commonDecrypt2, secretId);
            log.debug("{},调用业务方法完成，返回数据:{}", LOGGER_MSG, returnJsonString);

            //重新组织响应报文
            String data = null;
            if (!StringUtils.isBlank(returnJsonString)) {
                /**
                 * 加密
                 */
                data = commonInterfaceService.commonEncrypt(zipCode,encryptCode,returnJsonString,secretKey);
                result.remove(ConfigurerInfo.SECRETKEY);
                log.debug("{},加密后返回数据:{}", LOGGER_MSG, data);
            }

            if (data != null) {
                responseStatus.setCode(RespStatusEnum.SUCCESS.getCode());
                responseStatus.setMessage(RespStatusEnum.SUCCESS.getDescribe());
                responseData.setContent(data);
                responseData.setEncryptCode(encryptCode);
                responseData.setZipCode(zipCode);
                result.put(ConfigurerInfo.RESPONSESTATUS, responseStatus);
                result.put(ConfigurerInfo.RESPONSEDATA, responseData);
                log.info("{},接口:{}调用成功,返回数据:{}", LOGGER_MSG, interfaceName, JsonUtils.getInstance().toJsonString(result));
                return JsonUtils.getInstance().toJsonString(result);
            }

        }catch (Exception e){
            result.remove(ConfigurerInfo.SECRETKEY);
            log.error("{},接口请求数据出现异常,异常原因为:{}", LOGGER_MSG, e);
        }
        responseStatus.setCode(RespStatusEnum.FAIL.getCode());
        responseStatus.setMessage(RespStatusEnum.FAIL.getDescribe());
        result.put(ConfigurerInfo.RESPONSESTATUS, responseStatus);
        result.remove(ConfigurerInfo.SECRETKEY);
        log.info("{},接口:{}调用失败,返回数据:{}", LOGGER_MSG, interfaceName, JsonUtils.getInstance().toJsonString(result));
        return JsonUtils.getInstance().toJsonString(result);
    }


    /**
     * 销项管理业务处理公共方法
     *
     * @param interfaceName  接口方法
     * @param commonDecrypt2 请求数据明文
     * @param secretId       用户身份id
     * @return String 处理逻辑明文
     */
    public String orderApiHandingBusiness(String interfaceName, String commonDecrypt2, String secretId) {
        //返回参数
        String returnJsonString = "";

        if (ConfigurerInfo.INVOICEISSUE.equals(interfaceName)) {
            /**
             * 3.1 蓝字发票开具接口
             */
            List<InvoiceIssueInfoParam> list = JacksonUtils.parseArray(commonDecrypt2, InvoiceIssueInfoParam.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.invoiceIssue(list);
            long end = System.currentTimeMillis();
            log.debug("{}开票接口耗时:{},请求数量为:{}", LOGGER_MSG, end - start, list.size());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.QUERYINVOICEINFO.equals(interfaceName)) {
            /**
             * 3.2 蓝字发票结果查询接口
             */
            ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzkpqrxxsljgcxReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.queryInvoiceInfo(apiHzkpqrxxsljgcxReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}开票结果查询接口耗时:{},受理单号为:{}", LOGGER_MSG, end - start, apiHzkpqrxxsljgcxReqBO.getSldh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETINVOICEBASEINFO.equals(interfaceName)) {
            /**
             * 3.3 发票基础信息查询
             */
            InvoiceBaseInfoParam invoiceBaseInfoParam = JsonUtils.getInstance().parseObject(commonDecrypt2, InvoiceBaseInfoParam.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getInvoiceBaseInfo(invoiceBaseInfoParam);
            long end = System.currentTimeMillis();
            log.debug("{}发票基础信息查询接口耗时:{},税号为:{}", LOGGER_MSG, end - start, invoiceBaseInfoParam.getNsrsbh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETSINGLEINVOICEINFO.equals(interfaceName)) {
            /**
             * 3.4 单张发票信息查询
             */
            SingleInvoiceInfoParam singleInvoiceInfoParam = JsonUtils.getInstance().parseObject(commonDecrypt2, SingleInvoiceInfoParam.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getSingleInvoiceInfo(singleInvoiceInfoParam);
            long end = System.currentTimeMillis();
            log.debug("{}开票接口耗时:{},数电号码为:{}", LOGGER_MSG, end - start, singleInvoiceInfoParam.getQdfphm());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.REDINVOICECONFIRMINFOENTER.equals(interfaceName)) {
            /**
             * 3.5 红字开票确认信息录入
             */
            ApiHzkpqrxxlrReqBO apiHzkpqrxxlrReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzkpqrxxlrReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.redInvoiceConfirmInfoEnter(apiHzkpqrxxlrReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字开票确认信息录入接口耗时:{},蓝票为:{}", LOGGER_MSG, end - start, apiHzkpqrxxlrReqBO.getLzqdfphm());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETREDINVOICECONFIRMINFO.equals(interfaceName)) {
            /**
             * 3.6 红字开票确认信息受理结果查询
             */
            ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzkpqrxxsljgcxReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getRedInvoiceConfirmInfo(apiHzkpqrxxsljgcxReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字开票确认信息受理结果查询接口耗时:{},受理单号为:{}", LOGGER_MSG, end - start, apiHzkpqrxxsljgcxReqBO.getSldh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETREDINVOICECONFIRMINFODEAL.equals(interfaceName)) {
            /**
             * 3.7 红字发票确认信息处理
             */
            ApiHzfpqrxxclReqBO apiHzfpqrxxclReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzfpqrxxclReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getRedInvoiceConfirmInfoDeal(apiHzfpqrxxclReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字发票确认信息处理接口耗时:{},确认单编号为:{}", LOGGER_MSG, end - start, apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETREDINVOICECONFIRMINFOLIST.equals(interfaceName)) {
            /**
             * 3.8 红字发票确认信息列表查询
             */
            ApiHzfpqrxxlbcxReqBO apiHzfpqrxxlbcxReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzfpqrxxlbcxReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getRedInvoiceConfirmInfoList(apiHzfpqrxxlbcxReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字发票确认信息列表查询接口耗时:{},税号为:{}", LOGGER_MSG, end - start, apiHzfpqrxxlbcxReqBO.getNsrsbh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETREDINVOICECONFIRMINFODETAIL.equals(interfaceName)) {
            /**
             * 3.9 红字发票确认明细信息查询
             */
            ApiHzfpqrmxxxcxReqBO apiHzfpqrmxxxcxReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzfpqrmxxxcxReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getRedInvoiceConfirmInfoDetail(apiHzfpqrmxxxcxReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字发票确认明细信息查询接口耗时:{},红字确认单编号为:{}", LOGGER_MSG, end - start, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.REDINVOICEISSUE.equals(interfaceName)) {
            /**
             * 3.10 红字发票开具受理
             */
            ApiHzfpkjslReqBO apiHzfpkjslReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzfpkjslReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.redInvoiceIssue(apiHzfpkjslReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字发票开具受理接口耗时:{},红字发票确认单编号为:{}", LOGGER_MSG, end - start, apiHzfpkjslReqBO.getHzfpxxqrdbh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        } else if (ConfigurerInfo.GETREDINVOICERESULT.equals(interfaceName)) {
            /**
             * 3.11 红字发票开具受理结果查询
             */
            ApiHzfpkjsljgcxReqBO apiHzfpkjsljgcxReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiHzfpkjsljgcxReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = invoiceIssueService.getRedInvoiceResult(apiHzfpkjsljgcxReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}红字发票开具受理结果查询接口耗时:{},受理单号号为:{}", LOGGER_MSG, end - start, apiHzfpkjsljgcxReqBO.getSldh());
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        }else if (ConfigurerInfo.DZSWJLOGIN.equals(interfaceName)) {
            /**
             * 电子税局登录
             */
            ApiLoginReqBO apiLoginReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiLoginReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = dzswjLoginService.dzswjLogin(apiLoginReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}电子税局登录接口耗时:{}", LOGGER_MSG, end - start);
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        }else if (ConfigurerInfo.SETSMS.equals(interfaceName)) {
            /**
             * 登录设置验证码
             */
            ApiLoginReqBO apiLoginReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiLoginReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = dzswjLoginService.setSms(apiLoginReqBO);
            long end = System.currentTimeMillis();
            log.debug("{} 登录设置验证码接口耗时:{}", LOGGER_MSG, end - start);
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        }else if (ConfigurerInfo.GETCONFIRMQRCODE.equals(interfaceName)) {
            /**
             * 获取实名认证二维码
             */
            ApiLoginReqBO apiLoginReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiLoginReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = dzswjLoginService.getConfirmQrcode(apiLoginReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}获取实名认证二维码接口耗时:{}", LOGGER_MSG, end - start);
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        }else if (ConfigurerInfo.GETCONFIRMSTATUS.equals(interfaceName)) {
            /**
             * 实名认证信息查询
             */
            ApiLoginReqBO apiLoginReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiLoginReqBO.class);
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = dzswjLoginService.getConfirmStatus(apiLoginReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}实名认证信息查询接口耗时:{}", LOGGER_MSG, end - start);
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        }else if (ConfigurerInfo.GETSESSIONINFO.equals(interfaceName)) {
            /**
             * 通过税号调用河北航信接口获取登录信心返回给税航
             */
            ApiLoginReqBO apiLoginReqBO = JsonUtils.getInstance().parseObject(commonDecrypt2, ApiLoginReqBO.class);
            if(StringUtils.isBlank(apiLoginReqBO.getNsrsbh())){
                return  JsonUtils.getInstance().toJsonString(InvoiceIssueRes.error("9999","nsrsbh参数不能为空"));
            }
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = dzswjLoginService.getSessionInfo(apiLoginReqBO);
            long end = System.currentTimeMillis();
            log.debug("{}获取电局登录信息接口耗时:{}", LOGGER_MSG, end - start);
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);

        }else if (ConfigurerInfo.GENERATEDYNAMICCODE.equals(interfaceName)) {
            /**
             * 根据订单信息获取动态二维码
             */
            PageQrcodeOrderInfo pageQrcodeOrderInfo = JsonUtils.getInstance().parseObject(commonDecrypt2, PageQrcodeOrderInfo.class);
            //对请求参数处理 处理含有优惠政策的数据
            List<PageQrcodeOrderItemInfo> pageOrderItemInfo = pageQrcodeOrderInfo.getPageOrderItemInfoList();
            for (PageQrcodeOrderItemInfo orderItem : pageOrderItemInfo) {
                if (StringUtils.isNotBlank(orderItem.getLslbs())) {
                    if (OrderInfoEnum.LSLBS_0.getKey().equals(orderItem.getLslbs())) {
                        orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_1.getKey());
                        orderItem.setZzstsgl(OrderInfoEnum.LSLBS_0.getValue());

                    } else if (OrderInfoEnum.LSLBS_1.getKey().equals(orderItem.getLslbs())) {
                        orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_1.getKey());
                        orderItem.setZzstsgl(OrderInfoEnum.LSLBS_1.getValue());

                    } else if (OrderInfoEnum.LSLBS_2.getKey().equals(orderItem.getLslbs())) {
                        orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_1.getKey());
                        orderItem.setZzstsgl(OrderInfoEnum.LSLBS_2.getValue());

                    } else if (OrderInfoEnum.LSLBS_3.getKey().equals(orderItem.getLslbs())) {
                        orderItem.setYhzcbs(OrderInfoEnum.YHZCBS_0.getKey());
                        orderItem.setZzstsgl("");
                    }

                }
            }
            long start = System.currentTimeMillis();
            InvoiceIssueRes invoiceIssueRes = qrcodeService.generateDynamicQrCode(pageQrcodeOrderInfo);
            long end = System.currentTimeMillis();
            log.debug("{}获取动态二维码信息接口耗时:{}", LOGGER_MSG, end - start);
            returnJsonString = JsonUtils.getInstance().toJsonString(invoiceIssueRes);
        }

        return returnJsonString;
    }

}
