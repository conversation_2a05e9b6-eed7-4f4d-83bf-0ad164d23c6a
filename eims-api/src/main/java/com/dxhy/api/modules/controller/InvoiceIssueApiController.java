package com.dxhy.api.modules.controller;

import com.dxhy.order.modules.entity.AccessTokeReq;
import com.dxhy.order.modules.entity.DdqqlshParam;
import com.dxhy.order.modules.entity.InvoiceIssueInfoParam;
import com.dxhy.order.modules.entity.InvoiceIssueRes;
import com.dxhy.order.modules.service.OpenOderInvoiceIssueService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/einvoice")
public class InvoiceIssueApiController {

    @Autowired
    OpenOderInvoiceIssueService openOderInvoiceIssueService;

    /**
     * 获取token接口
     */
    @ApiOperation("获取token接口")
    @RequestMapping("/getToken")
    public InvoiceIssueRes getToken(@RequestBody AccessTokeReq accessTokeReq) {
        return openOderInvoiceIssueService.getToken(accessTokeReq);
    }

    /**
     * 推送订单接口
     */
    @ApiOperation("推送订单接口")
    @RequestMapping("/invoiceIssue")
    public InvoiceIssueRes invoiceIssue(@RequestBody InvoiceIssueInfoParam issueInfoParam) {
        return openOderInvoiceIssueService.invoiceIssue(issueInfoParam);
    }

    /**
     * 发票查询接口
     */
    @ApiOperation("发票查询接口")
    @RequestMapping("/queryInvoiceInfo")
    public InvoiceIssueRes queryInvoiceInfo(@RequestBody DdqqlshParam ddqqlshParam) {
        return openOderInvoiceIssueService.queryInvoiceInfo(ddqqlshParam);
    }
}
