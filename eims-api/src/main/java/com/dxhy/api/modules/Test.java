package com.dxhy.api.modules;

import cn.hutool.core.codec.Base64;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.utils.JsonUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Test {
    public static void main(String args[]){
//        invoiceIssue();
//        queryInvoiceInfo();
//        redInvoiceConfirmInfoEnter();
//        getRedInvoiceConfirmInfo();
//        redInvoiceIssue();
//        getRedInvoiceResult();
//        getRedInvoiceConfirmInfoDeal();
//        getRedInvoiceConfirmInfoList();
//        getRedInvoiceConfirmInfoDetail();
//        dzswjLogin();
//        setSms();
//        getConfirmQrcode();
//        getConfirmStatus();
        bigDicamalCalculate();

    }
    /**
     * 金额计算
     */
    private static void bigDicamalCalculate(){
        BigDecimal a = new BigDecimal("608.97");
        BigDecimal b = new BigDecimal("-304.49");
        Float aFloat = Float.valueOf("0.56");
        String format = new DecimalFormat("0.00").format(aFloat);;
        System.out.println(Integer.valueOf("2") + 1);
    }

    /**
     * 获取实名认证状态
     */
    private static void getConfirmStatus(){
        ApiLoginReqBO bo = new ApiLoginReqBO();
        bo.setRzid("93eade16de75414fb73f06d932edaa0a");
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 获取实名认证二维码
     */
    private static void getConfirmQrcode(){
        ApiLoginReqBO bo = new ApiLoginReqBO();
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 登录设置验证码
     */
    private static void setSms(){
        ApiLoginReqBO bo = new ApiLoginReqBO();
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        bo.setYzm("000000");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 电子税务局登录
     */
    private static void dzswjLogin(){
        ApiLoginReqBO bo = new ApiLoginReqBO();
        bo.setNsrsbh("92130981MA0G2Q3K6W");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 红字确认单处理
     */
    private static void getRedInvoiceConfirmInfoDeal(){
        ApiHzfpqrxxclReqBO bo = new ApiHzfpqrxxclReqBO();
        bo.setNsrsbh("91441322MADC0CNN9F");
        bo.setHzfpxxqrdbh("44011225011000357972");
        bo.setCllx("01");
        bo.setXsfnsrsbh("91440101MA5AR3WE9Y");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 红字发票确认信息明细查询
     */
    private static void getRedInvoiceConfirmInfoDetail(){
        ApiHzfpqrmxxxcxReqBO bo = new ApiHzfpqrmxxxcxReqBO();
        bo.setHzfpxxqrdbh("458a476a79c942b9aac1b6712321312387");
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }

    /**
     * 红字发票确认信息列表查询
     */
    private static void getRedInvoiceConfirmInfoList(){
        ApiHzfpqrxxlbcxReqBO bo = new ApiHzfpqrxxlbcxReqBO();
        bo.setGxfxz("0");
        bo.setHzqrxxztdm("01");
        bo.setDfnsrmc("");
        bo.setKprqq("2025-01-01");
        bo.setKprqz("2025-01-06");
        bo.setKpzt("y");
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        bo.setCurrent("1");
        bo.setSize("10");
        bo.setLrfsf("0");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 红字信息单录入结果查询
     */
    private static void getRedInvoiceConfirmInfo(){
        ApiHzkpqrxxsljgcxReqBO bo = new ApiHzkpqrxxsljgcxReqBO();
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        bo.setSldh("sunp4q4zddipafpa");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }

    /**
     * 红字信息单录入
     */
    private static void redInvoiceConfirmInfoEnter(){
        ApiHzkpqrxxlrReqBO bo = new ApiHzkpqrxxlrReqBO();
        bo.setGxfxz("0");
        bo.setLzqdfphm("24442000000658856798");
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        bo.setChyymc("开票有误");
        bo.setKprq("2024-12-31");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 红票开具
     */
    private static void redInvoiceIssue(){
        ApiHzfpkjslReqBO bo = new ApiHzfpkjslReqBO();
        bo.setLzqdfphm("24442000000658280255");
        bo.setKprq("2024-12-31");
        bo.setHzfpxxqrdbh("44011225011000357972");
        bo.setHzqrxxztdm("01");
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 红票查询
     */
    private static void getRedInvoiceResult(){
        ApiHzkpqrxxsljgcxReqBO bo = new ApiHzkpqrxxsljgcxReqBO();
        bo.setSldh("1035089182896439296");
        bo.setNsrsbh("91440101MA5AR3WE9Y");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }

    /**
     * 蓝票查询
     */
    private static void queryInvoiceInfo(){
        ApiHzkpqrxxsljgcxReqBO bo = new ApiHzkpqrxxsljgcxReqBO();
        bo.setSldh("1034617241857179648");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println(encode);
    }
    /**
     * 蓝票开具
     */
    private static void invoiceIssue(){
        InvoiceIssueInfoParam param = new InvoiceIssueInfoParam();
        param.setDdqqlsh("2025010617350001");
        param.setFplxdm("002");
        param.setKpfs("0");
        param.setNsrsbh("91442000661493375U");
        param.setGmfsbh("91441322MADC0CNN9F");
        param.setGmfmc("惠州市和兴园林绿化有限公司");
        param.setGmfdz("广州市天河区中山大道");
        param.setGmfdh("87888888");
        param.setGmfyh("中国工商银行股份有限公司广州盈彩支行");
        param.setGmfzh("3602074909100033240");
        param.setJbrzjlx("");
        param.setJbrzjhm("");
        param.setKpr("吴净净");
        param.setHsbz("0");
        param.setJshj("1.09");
        param.setHjje("1.00");
        param.setHjse("0.09");
        param.setBz("对接税航开票-hds测试");
        param.setDdh("DD2025010617350001");
        InvoiceIssueAdditionParam additionParam = new InvoiceIssueAdditionParam();
        additionParam.setFjxxmc("附加项一");
        additionParam.setFjxxz("fj1");
        List<InvoiceIssueAdditionParam> additionParamList = new ArrayList<>();
        additionParamList.add(additionParam);
        param.setDdfjxx(additionParamList);
        InvoiceIssueItemParam itemParam = new InvoiceIssueItemParam();
        List<InvoiceIssueItemParam> itemParamList = new ArrayList<>();
        itemParam.setXh("1");
        itemParam.setZkfs("");
        itemParam.setZkdx("");
        itemParam.setSpbm("1050201060000000000");
        itemParam.setXmmc("床垫/床褥/床护垫/榻榻米床垫");
        itemParam.setGgxh("TEST1");
        itemParam.setDw("个");
        itemParam.setSpsl("1");
        itemParam.setDj("1.00");
        itemParam.setJe("1.00");
        itemParam.setSl("0.09");
        itemParam.setSe("0.09");
        itemParamList.add(itemParam);
        param.setDdmxxx(itemParamList);
        param.setGiveUpReason("不想要");

        String jsonString = JsonUtils.getInstance().toJsonString(Collections.singletonList(param));
        String encode = Base64.encode(jsonString);
        System.out.println(encode);
    }

}
