package com.dxhy.api.interceptor;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * @Auther: admin
 * @Date: 2022/8/25 15:59
 * @Description:
 */
@Configuration
public class EimsApiMvcConfig implements WebMvcConfigurer {

    @Resource
    private EimsApiInterceptor eimsApiInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(eimsApiInterceptor).addPathPatterns("/**").excludePathPatterns("/swagger-resources/**", "/webjars/**","/swagger-ui.html/**",
                "/api", "/einvoice/invoice/getSessionInfo","/api-docs", "/api-docs/**");
    }


    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {

        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

    }
}
