package com.dxhy.api.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;


/**
 * @Auther: admin
 * @Date: 2022/8/25 15:55
 * @Description:
 */
@Slf4j
@Component
public class EimsApiInterceptor implements HandlerInterceptor {


    @Resource
    private TenantRdsService tenantRdsService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try{
            log.info(request.getServletPath());
            log.info(request.getRequestURI());

            String entId = request.getHeader("entId");
            log.debug("get entId: {}", entId);
            if (StringUtils.isEmpty(entId)) {
                String s = JSONObject.toJSONString(R.error("entId is null"));
                returnJson(response, s);
                return false;
            }
            //税号查询tenantId

            String changeTenantResult = tenantRdsService.switchRdsBytaxNo(entId);
            if(StringUtils.isNotEmpty(changeTenantResult)){
                log.debug("租户身份认证失败: {}", changeTenantResult);
                String s = JSONObject.toJSONString(R.error("租户身份认证失败: " + changeTenantResult));
                returnJson(response, s);
                return false;
            }

        } catch (Exception e) {
            log.error("租户身份认证异常 : {}", e);
            String s = JSONObject.toJSONString(R.error("身份校验异常,请联系管理员 "));
            returnJson(response, s);
            return false;
        }


        return true;
    }

    private void returnJson(HttpServletResponse response, String s) {
        PrintWriter printWriter = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=utf-8");
        try {
            printWriter = response.getWriter();
            printWriter.print(s);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (printWriter != null) {
                printWriter.close();
            }
        }


    }
}
