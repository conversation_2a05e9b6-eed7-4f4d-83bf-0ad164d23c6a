package com.dxhy.api.util;


import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Map;
import java.util.TreeMap;
/**
 * @Auther: admin
 * @Date: 2022/9/27 10:13
 * @Description:
 */
public class SignUtil {

    public static final String SIGN_TYPE_0 = "0";//HmacSHA256
    public static final String SIGN_TYPE_1 = "1";//MD5
    public static final String HMAC_SHA256 = "HmacSHA256";
    public static final String ENCODING = "UTF-8";
    public static final String LF = "\n";
    public static final String SPE1 = ",";
    public static final String SPE2 = ":";
    public static final String SPE3 = "&";
    public static final String SPE4 = "=";
    public static final String SPE5 = "?";

    public static String sign(String signType, String appsecret, Map<String, String> querys) {
        String signature = "";
        try {
//            signType = StringUtil.validateStr(signType, SIGN_TYPE_0);//默认 HmacSHA256
            if(SIGN_TYPE_0.equals(signType)){
                Mac hmacSha256 = Mac.getInstance(HMAC_SHA256);
                byte[] keyBytes = appsecret.getBytes(ENCODING);
                hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, HMAC_SHA256));
                signature = new String(Base64.encodeBase64(hmacSha256.doFinal(buildResource(querys).getBytes(ENCODING))),
                        ENCODING);
            }
            if(SIGN_TYPE_1.equals(signType)){
                querys.put("appsecret", appsecret);
                signature = MD5Util.getMd5(buildResource2(querys)).toUpperCase();
                querys.remove("appsecret");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return signature;
    }

    private static String buildResource(Map<String, String> querys) {
        StringBuilder sb = new StringBuilder();
        Map<String, String> sortMap = new TreeMap<String, String>();
        if (null != querys) {
            for (Map.Entry<String, String> query : querys.entrySet()) {
                if (!StringUtils.isBlank(query.getKey())) {
                    sortMap.put(query.getKey(), query.getValue());
                }
            }
        }

        StringBuilder sbParam = new StringBuilder();
        for (Map.Entry<String, String> item : sortMap.entrySet()) {
            if (!StringUtils.isBlank(item.getKey())) {
                if (0 < sbParam.length()) {
                    sbParam.append(SPE3);
                }
                sbParam.append(item.getKey());
                if (!StringUtils.isBlank(item.getValue())) {
                    sbParam.append(SPE4).append(item.getValue());
                }
            }
        }
        if (0 < sbParam.length()) {
            sb.append(SPE5);
            sb.append(sbParam);
        }
        System.out.println(">>>>>>>>>>>>>>>>>>>>");
        System.out.println(sb.toString());
        System.out.println(">>>>>>>>>>>>>>>>>>>>");
        return sb.toString();
    }
    private static String buildResource2(Map<String, String> querys) {
        StringBuilder sb = new StringBuilder();
        Map<String, String> sortMap = new TreeMap<String, String>();
        if (null != querys) {
            for (Map.Entry<String, String> query : querys.entrySet()) {
                if (!StringUtils.isBlank(query.getKey())) {
                    sortMap.put(query.getKey(), query.getValue());
                }
            }
        }

        StringBuilder sbParam = new StringBuilder();
        for (Map.Entry<String, String> item : sortMap.entrySet()) {
            if (!StringUtils.isBlank(item.getKey())) {
                if (!StringUtils.isBlank(item.getValue())) {
                    sbParam.append(item.getValue());
                }
            }
        }
        if (0 < sbParam.length()) {
            sb.append(sbParam);
        }
        System.out.println(">>>>>>>>>>>>>>>>>>>>");
        System.out.println(sb.toString());
        System.out.println(">>>>>>>>>>>>>>>>>>>>");
        return sb.toString();
    }
}
