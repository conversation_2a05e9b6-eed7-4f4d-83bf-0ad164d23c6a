# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
  port: 18114
  servlet:
    context-path: /

spring:
  application:
    name: einvoiceapi
  profiles:
    active: @profileActive@
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

  freemarker:
    suffix: .html
    request-context-attribute: request
  # 同一个domain里面的MBean要求name唯一 So <<
  jmx:
    default-domain: dxyun-uic
  # Nacos config
  cloud:
    nacos:
      config:
        # Nacos config 地址
        server-addr: @nacos.server.addr@
        # Nacos config 命名空间,对应配置中的名称(sims_order_namespace)
        namespace: @nacos.order.namespace@
        # Nacos config 分组
        group: eims-api
        # Nacos config 登录用户名
        username: @nacos.username@
        # Nacos config 登录密码
        password: @nacos.password@
        # Nacos config 配置文件前缀
        prefix: eims-api
        # Nacos config 配置文件后缀,拼接完URL需要对应NacosServer中的dataId对应配置,${prefix}-${spring.profiles.active}.${file-extension}
        file-extension: yaml
        extension-configs:
          #Nacos config 配置 mysql数据库,数据库只能使用一个,要么mysql,要么Oracle,要么weblogic方式
          - data-id: eims-api-mysql-${spring.profiles.active}.yaml
            group: eims-api
            refresh: true
          #Nacos config 配置 redis,redis使用方式只能用一个,要么redis,要么redis-sentinel
          - data-id: eims-api-redis-${spring.profiles.active}.yaml
            group: eims-api
            refresh: true
            #Nacos config 配置 redis,redis使用方式只能用一个,要么redis,要么redis-sentinel
#          - data-id: eims-order-eureka-${spring.profiles.active}.yaml
#            group: eims-order
#            refresh: true

      # Nacos discovery 配置
      discovery:
        # Nacos discovery 服务地址
        server-addr: @nacos.server.addr@
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 主数据源  默认数据库连接（配置库）
      master:
        driver-class-name: com.mysql.jdbc.Driver
        url: @db.master.url@
        username: @db.master.username@
        password: @db.master.password@

      # 初始化时建立物理连接的个数
      initial-size: 10
      # 最大连接池数量
      max-active: 100
      # 最小连接池数量
      min-idle: 10
      # 获取连接时最大等待时间，单位毫秒
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 连接保持空闲而不被驱逐的最小时间
      min-evictable-idle-time-millis: 300000
      # 用来检测连接是否有效的sql，要求是一个查询语句
      validation-query: SELECT 1 FROM DUAL
      # 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭。
      pool-prepared-statements: false
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。
      max-pool-prepared-statement-per-connection-size: 0
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计；配置监控统计拦截的filters，stat:监控统计、log4j：日志记录、wall：防御sql注入
      filters: stat,wall,log4j2
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
#mybatis
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  #typeAliasesPackage: com.dxhy.invoice.digitalAccount.dao,com.dxhy.invoice.digitalAccount.entity,com.dxhy.invoice.digitalAccount.vo,com.dxhy.invoice.digitalAccount.modules.scaninvoice.domain
  typeAliasesPackage: com.dxhy
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 0
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    #capital-mode: true
    # Sequence序列接口实现类配置
    #key-generator: com.baomidou.mybatisplus.incrementer.OracleKeyGenerator
    #逻辑删除配置
    logic-delete-value: -1
    logic-not-delete-value: 0
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl



