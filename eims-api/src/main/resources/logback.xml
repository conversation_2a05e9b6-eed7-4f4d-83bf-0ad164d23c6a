<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!--加载spring的日志配置文件 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <property name="LOG_PATH" value="/data/logs"/>
    <!--设置系统日志目录-->
    <property name="APP_DIR" value="eims-api"/>
    <!-- 记录日志天数 -->
    <property name="MAX_HISTORY" value="30"/>
    <!-- 每个日志文件大小 -->
    <property name="MAX_FILE_SIZE" value="1GB"/>
    <!-- 日志文件总大小 -->
    <property name="TOTAL_SIZE" value="50GB"/>
    <!-- 日志格式 -->
    <!--    <property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%line]- %msg%n "/>-->
    <property name="PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%thread]) %clr(%-40.40logger{39}){cyan} %clr(:){faint} %clr([%line]) %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>


    <!-- 项目名称 -->
    <contextName>${APP_DIR}</contextName>

    <!--程序执行日志记录-->
    <appender name="process" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <file>${LOG_PATH}/${APP_DIR}/process.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/process.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--滚存策略 ，maxHistory为 日志能够保存的条数。-->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 超过10MB时，触发滚动策略 -->
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${TOTAL_SIZE}</totalSizeCap>
        </rollingPolicy>
        <!--临界值过滤器，过滤掉低于指定临界值的日志。当日志级别等于或高于临界值时，过滤器返回NEUTRAL；当日志级别低于临界值时，日志会被拒绝。-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!--程序出现异常需要处理的日志记录，此数据一旦出现意味出现了不可用的危险！！！-->
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <file>${LOG_PATH}/${APP_DIR}/error.log</file>


        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_DIR}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--滚存策略 ，maxHistory为 日志能够保存的条数。-->
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <!-- 超过10MB时，触发滚动策略 -->
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${TOTAL_SIZE}</totalSizeCap>
        </rollingPolicy>

        <!--将过滤日志级别设置为error级别，所有error级别的日志交给appender处理，非error级别的日志，被过滤掉。-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <!--encoder 默认配置为PatternLayoutEncoder -->
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>

    <logger name="org.springframework" level="info"/>
    <logger name="springfox.documentation" level="info"/>
    <logger name="org.apache.http" level="info"/>
    <logger name="org.apache.zookeeper" level="warn"/>
    <logger name="org.springframework.web.servlet" level="warn"/>
    <logger name="org.hibernate.validator" level="warn"/>
    <logger name="springfox.documentation.spring.web" level="warn"/>
    <logger name="com.xxl.job.core" level="warn"/>
    <logger name="com.elephant" level="debug"/>
    <logger name="com.aisino" level="warn"/>
    <logger name="io.netty" level="info"/>
    <logger name="org.eclipse.jetty" level="info"/>
    <logger name="com.netflix.discovery" level="warn"/>
    <logger name="org.apache.curator" level="warn"/>
    <logger name="com.zaxxer.hikari" level="info"/>
    <logger name="com.alibaba.spring" level="info"/>
    <logger name="org.mybatis.spring" level="info"/>
    <logger name="org.mongodb.driver" level="info"/>
    <logger name="io.lettuce.core" level="info"/>
    <logger name="com.dxhy.itax.logutil" level="warn"/>
    <logger name="de.codecentric.boot.admin.client" level="warn"/>
    <logger name="org.apache.fontbox" level="warn"/>
    <logger name="com.alibaba.nacos.client" level="warn"/>
    <logger name="com.alibaba.cloud.nacos.client" level="warn"/>

    <!-- 生产环境下，将此级别配置为适合的级别，以免日志文件太多或影响程序性能 -->
    <root level="debug">
        <appender-ref ref="error"/>
        <appender-ref ref="process"/>
        <!-- 生产环境将请stdout去掉 -->
        <appender-ref ref="stdout"/>

    </root>
</configuration>
