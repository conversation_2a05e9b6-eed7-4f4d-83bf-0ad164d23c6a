package test;




import com.dxhy.EimsApiApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = EimsApiApplication.class)
public class ApplicationTest {





    @Test
    public void test() {
//        String signType = "0";
//        String appsecret = "644f71cb555e40feb320e0c79440e3c0";
//        Map<String,String> query = new HashMap<>();
//        query.put("appid","324bd2eee34e4d1f9d111044f0f00b73 ");
//        query.put("content","");
//        query.put("serviceid","KP10001");
//        String sign = SignUtil.sign(signType, appsecret, query);
//        System.out.println( sign);



    }
}
