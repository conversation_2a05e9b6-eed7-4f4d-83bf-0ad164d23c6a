package test;



import com.dxhy.invoice.EimsInvoiceApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = EimsInvoiceApplication.class)
public class ApplicationTest {





    @Test
    public void test() {
        String je1 = "5.00";
        String slv = "0.03";
        BigDecimal je = new BigDecimal(5.00);
        BigDecimal slv1 = new BigDecimal(slv).add(new BigDecimal(1));
        String bhsje = je.divide(slv1,2, BigDecimal.ROUND_HALF_UP).toString();
        System.out.println(bhsje);
        Float  sl = Float.valueOf("3%".replace("%",""))/100;
        System.out.println(sl);
        BigDecimal slv11 = new BigDecimal(sl.toString()).add(new BigDecimal(1));
        System.out.println(slv11);
    }
}
