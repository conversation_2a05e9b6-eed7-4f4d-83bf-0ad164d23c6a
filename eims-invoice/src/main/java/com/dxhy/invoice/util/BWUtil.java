package com.dxhy.invoice.util;

import com.baiwang.open.client.login.BopLoginClient;
import com.baiwang.open.client.login.BopLoginResponse;
import com.baiwang.open.client.login.PasswordLoginClient;
import com.baiwang.open.client.login.PasswordLoginConfig;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BWUtil {

    private static final String LOGGER_MSG = "(百望Client请求)";

    /**
     * 百望 获取token
     *
     * @param url       请求地址
     * @param appKey    appKey
     * @param appSecret appSecret
     * @param username  用户名
     * @param password  用户密码
     * @param userSalt  用户盐值
     * @return
     */
    public static String generateUrl(String url, String appKey, String appSecret, String username, String password, String userSalt) {
        log.info("{}，获取token入参，url：{}，appkey：{}，appSecret：{}，username：{}，password：{}，userSalt：{}", LOGGER_MSG, url, appKey, appSecret, username, password, userSalt);
        PasswordLoginConfig loginConfig = new PasswordLoginConfig();
        loginConfig.setUrl(url);
        loginConfig.setClientId(appKey);
        loginConfig.setClientSecret(appSecret);
        loginConfig.setUsername(username);
        loginConfig.setPassword(password);
        loginConfig.setUserSalt(userSalt);
        BopLoginClient loginClient = new PasswordLoginClient(loginConfig);
        // 获取token, token相关说明请参考 文档中心 -> 用户授权说明
        BopLoginResponse loginResponse = loginClient.login();
        // 重载方法，可以传自定义请求唯一标识
        // BopLoginResponse loginResponse = loginClient.login(requestId);
        String token = loginResponse.getResponse().getAccessToken();
        return token;
    }


}
