package com.dxhy.invoice.util;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.order.constant.RedisConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MycstUtil {

    private static final String LOGGER_MSG = "(税航票帮手请求)";
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private InvoiceConfig invoiceConfig;

    /**
     * 票帮手 获取token
     * @return
     */
    public String getToken(){
        String token = (String) redisTemplate.opsForValue().get(RedisConstant.MYCST_TOKEN_CODE_KEY+invoiceConfig.getMycstUserCode());
        if(StringUtils.isBlank(token)){
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("UserName",invoiceConfig.getMycstUserCode());
            paramMap.put("Password1",invoiceConfig.getMycstUserPwd());
            //请求参数拼接形式调用票帮手获取token接口
            log.info("{}，获取token入参，url：{}，userCode：{}，userPwd：{}", LOGGER_MSG, invoiceConfig.getMycstGetTokenUrl(), invoiceConfig.getMycstUserCode(), invoiceConfig.getMycstUserPwd());
            String result = HttpUtils.doPost(invoiceConfig.getMycstGetTokenUrl(), paramMap);
            log.info("请求票帮手获取token接口地址{},返回结果{}",invoiceConfig.getMycstGetTokenUrl(),result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            token = jsonObject.getString("ID");
            redisTemplate.opsForValue().set(RedisConstant.MYCST_TOKEN_CODE_KEY+invoiceConfig.getMycstUserCode(),token,RedisConstant.REDIS_EXPIRE_TIME_1DAYS, TimeUnit.SECONDS);
        }
        return token;
    }

    /**
     * 传入对象将字段名和值转换为&拼接的形式
     * @param obj
     * @return
     */
    public String convertObjectToQueryString(Object obj) {
        StringBuilder queryString = new StringBuilder();
        // 获取对象的类类型
        Class<?> clazz = obj.getClass();
        // 获取类中的所有字段
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true); // 设置可访问性
            try {
                // 获取字段名和字段值
                String key = field.getName();
                Object value = field.get(obj);

                // 处理null值和拼接字符串
                if (value != null) {
                    if (queryString.length() > 0) {
                        queryString.append("&");
                    }
                    queryString.append(key).append("=").append(value.toString());
                }

            } catch (IllegalAccessException e) {
                e.printStackTrace(); // 处理异常
            }
        }
        return queryString.toString();
    }

}
