package com.dxhy.invoice.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.modules.entity.AccessTokeReq;
import com.dxhy.order.modules.entity.AccessTokenBean;
import com.dxhy.order.modules.entity.GlobalInfo;
import com.dxhy.order.utils.Base64Encoding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
@Slf4j
@Component
public class HttpUtils {

    private static String tokenUrl;

    @Value("${dxKfpt.accessToken.url}")
    public void setTokenUrl(String tokenUrl) {
        HttpUtils.tokenUrl = tokenUrl;

    }


    private static String appSecret;

    @Value("${dxKfpt.accessToken.appSecret}")
    public void setAppSecret(String appSecret) {
        HttpUtils.appSecret = appSecret;

    }


    private static String appKey;

    @Value("${dxKfpt.accessToken.appKey}")
    public void setAppKey(String appKey) {
        HttpUtils.appKey = appKey;

    }
    private static String entCode;

    @Value("${dxKfpt.accessToken.entCode}")
    public void setEntCode(String entCode) {
        HttpUtils.entCode = entCode;

    }
    private static final String LOGGER_MSG = "(请求http访问)";
    
    /**
     * 执行post请求
     *
     * @param url
     * @param paramMap
     * @return
     * @throws IOException
     */
    public static String doPost(String url, Map<String, ?> paramMap) {
        Map<String, Object> requestMap = new HashMap<>(paramMap);
        long startTime = System.currentTimeMillis();
        log.debug("{}以Map调用post请求url:{}",LOGGER_MSG,url);
        String body = HttpRequest.post(url).form(requestMap).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Map调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }
    
    public static String doPost(String url, String request,TaxpayerInfo taxpayerInfo) {
        long startTime = System.currentTimeMillis();
        String requestId = DistributedKeyMaker.generateShotKeyNew();
        String sessionId = DistributedKeyMaker.generateShotKeyNew();
        long timestamp = Instant.now().toEpochMilli();
        String reqUrl = url+"?jrzh="+taxpayerInfo.getJrzh()+"&jrmm="+taxpayerInfo.getJrmm()+"&sjhm="+taxpayerInfo.getSjhm()+"&sessionId="+sessionId+"&requestId="+requestId+"&timestamp="+timestamp;

        log.debug("{}以Json字符串调用post请求url:{},参数:{}", LOGGER_MSG, reqUrl,request);
        String body = HttpRequest.post(reqUrl).body(request).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},耗时:{},返回参数:{}", LOGGER_MSG, reqUrl, endTime - startTime,body);
        return body;
    }

    public static String doPost(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},参数:{}", LOGGER_MSG, url,request);
        String body = HttpRequest.post(url).body(request).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},耗时:{},返回参数:{}", LOGGER_MSG, url, endTime - startTime,body);
        return body;
    }
    
    public static String doPostWithHeader(String url, String data, Map<String, String> header) {
        long startTime = System.currentTimeMillis();
        log.debug("{}带head调用post请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.post(url).addHeaders(header).body(data).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}带head调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }
    
    public static String doPostFormWithHeader(String url, Map<String, ?> paramMap, Map<String, String> header) {
        Map<String, Object> requestMap = new HashMap<>(paramMap);
        long startTime = System.currentTimeMillis();
        log.debug("{}带head和form调用post请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.post(url).addHeaders(header).form(requestMap).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}带head和form调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }
    
    public static String doGetWithHeader(String url, Map<String, String> header) {
        long startTime = System.currentTimeMillis();
        log.debug("{}带head调用get请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.get(url).addHeaders(header).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}带head调用get请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }
    
    public static String doGet(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{}以字符串调用get请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.get(url).body(request).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以字符串调用get请求url:{},耗时:{},返回参数:{}", LOGGER_MSG, url, endTime - startTime,body);
        return body;
    }
    public static String doPostToDx(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{},进入token申请", LOGGER_MSG);
        AccessTokeReq access_token = new AccessTokeReq();
        access_token.setAppKey(appKey);
        access_token.setAppSecret(appSecret);
        log.debug("{},请求token,请求参数为:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(access_token));
        String doPost = HttpUtils.doPost(tokenUrl, JsonUtils.getInstance().toJsonString(access_token));
        log.debug("{},请求token,返回参数为:{}", LOGGER_MSG, doPost);
        AccessTokenBean accessTokenBean = JsonUtils.getInstance().parseObject(doPost, AccessTokenBean.class);
        GlobalInfo globalInfo = buildGlobalInfo(request,"");
        String requestUrl = url + ConfigureConstant.STRING_OPEN_API_ACCESS_TOKEN + accessTokenBean.getAccess_token();
        log.debug("{},以Json字符串调用post请求url:{} 请求参数:{}", LOGGER_MSG, requestUrl, JsonUtils.getInstance().toJsonString(globalInfo));
        String body = HttpRequest.post(requestUrl).body(JsonUtils.getInstance().toJsonString(globalInfo)).timeout(600000).header("Accept-Encoding", "", true).execute().body();
//        String body = HttpRequest.post(requestUrl).body(JsonUtils.getInstance().toJsonString(globalInfo)).timeout(600000).header("Content-Type","application/json").execute().body();

        long endTime = System.currentTimeMillis();
        log.debug("{},以Json字符串调用post请求url:{},耗时:{}返回参数:{}", LOGGER_MSG, requestUrl, endTime - startTime, body);
        return body;
    }
    private static GlobalInfo buildGlobalInfo(String content, String nsrsbh) {
        GlobalInfo globalInfo = new GlobalInfo();
        globalInfo.setZipCode("0");
        globalInfo.setEncryptCode("0");
        globalInfo.setDataExchangeId(RandomUtil.randomNumbers(ConfigurerInfo.INT_32));
        globalInfo.setEntCode(entCode);
        globalInfo.setContent(Base64Encoding.encode(content));
        return globalInfo;
    }
}
