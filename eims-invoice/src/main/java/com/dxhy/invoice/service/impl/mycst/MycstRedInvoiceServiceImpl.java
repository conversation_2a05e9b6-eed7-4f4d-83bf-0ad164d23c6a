package com.dxhy.invoice.service.impl.mycst;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.constant.MycstFplxEnum;
import com.dxhy.invoice.dao.SldhFpqqlshInfoMapper;
import com.dxhy.invoice.dao.SldhHzqrdDao;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.SldhHzqrdEntity;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.RedInvoiceService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.invoice.util.MycstUtil;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.model.MycstRedConfirmItem;
import com.dxhy.order.model.MycstRedConfirmListReq;
import com.dxhy.order.model.MycstRedConfirmReq;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * @Auther: admin
 * @Date: 2022/9/6 09:58
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.SHRPA)
public class MycstRedInvoiceServiceImpl implements RedInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private SldhHzqrdDao sldhHzqrdDao;

    @Resource
    private SldhFpqqlshInfoMapper sldhFpqqlshInfoMapper;

    @Resource
    private MycstUtil mycstUtil;

    @Override
    public R redConfirm(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        MycstRedConfirmReq mycstRedConfirmReq = new MycstRedConfirmReq();

        //1、获取token
        String token = mycstUtil.getToken();
        //2、参数转换
        convertToMycstRedConfirmReq(mycstRedConfirmReq,redConfirmReq);
        String url = invoiceConfig.getMycstRedConfirmApply();
        //3、json格式化
        String jsonString = JsonUtils.getInstance().toJsonString(mycstRedConfirmReq);
        //4、base64转换
        String data = "["+ Base64.encode(jsonString)+"]";
        //5、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setData(data);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        //6、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);
        //7、发送请求
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(url, param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-确认单申请请求完成,耗时：{},返回参数：{}", endTime - startTime, result);
        MycstRedConfirmResponse response = JSONObject.parseObject(result, MycstRedConfirmResponse.class);
        if(response != null){
            if (StringUtils.equals(response.getResult(),"1")) {

                //保存信息到数据库  返回受理单号   确认单结果查询 查库返回信息
                SldhHzqrdEntity sldhHzqrdEntity = new SldhHzqrdEntity();
                String  sldh = RandomUtil.randomString(16);
                sldhHzqrdEntity.setSldh(sldh);
                sldhHzqrdEntity.setHzfpxxqrdbh(response.getXxbbh());
                sldhHzqrdEntity.setHzqrxxztdm(response.getZtdm());
                sldhHzqrdDao.insert(sldhHzqrdEntity);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sldh",sldh);
                return R.ok().put("data",jsonObject.toJSONString());
            } else {
                log.error("税航票帮手-确认单申请请求返回失败,{}",response.getMessage());
                return R.error("9999", response.getMessage());
            }
        }else {
            log.error("税航票帮手-确认单申请请求返回数据为空");
            return R.error("9999", "确认单申请失败");
        }
    }



    @Override
    public R getRedConfirmResult(RedConfirmReq redConfirmReqt, TaxpayerInfo taxpayerInfo) {
        String  sldh = redConfirmReqt.getSldh();
        QueryWrapper<SldhHzqrdEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sldh",sldh);
        SldhHzqrdEntity sldhHzqrdEntity = sldhHzqrdDao.selectOne(queryWrapper);
        RedInvoiceConfirmSldhRes redInvoiceConfirmSldhRes = new RedInvoiceConfirmSldhRes();
        redInvoiceConfirmSldhRes.setHzfpxxqrdbh(sldhHzqrdEntity.getHzfpxxqrdbh());
        redInvoiceConfirmSldhRes.setHzqrxxztDm(sldhHzqrdEntity.getHzqrxxztdm());

        return R.ok().put("data",JsonUtils.getInstance().toJsonString(redInvoiceConfirmSldhRes));
    }

    @Override
    public R redInvoiceIssue(RedInvoiceIssueReq redInvoiceIssueReq, TaxpayerInfo taxpayerInfo) {
        String requestId = redInvoiceIssueReq.getRequestId();
        //1、获取token
        String token = mycstUtil.getToken();
        //2、业务数据填充
        MycstInvoiceReq mycstInvoiceReq = convertMycstInvoiceReq(redInvoiceIssueReq);
        //3、json格式化
        String jsonString = JsonUtils.getInstance().toJsonString(mycstInvoiceReq);
        log.info("税航票帮手发票开具业务数据，税号：{}， request：{}", redInvoiceIssueReq.getNsrsbh(), jsonString);
        //4、base64转换
        String data = "[" + Base64.encode(jsonString) + "]";
        //5、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setData(data);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        //6、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);
        //7、发送请求
        log.info("税航票帮手-红字发票开具请求开始，requestId:{}", requestId);
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getMycstFpkjUrl(), param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-红字发票开具请求完成,requestId:{},耗时：{},返回参数：{}", requestId, endTime - startTime, result);
        //8、解析响应
        MycstCommonResponse response = JSONObject.parseObject(result, MycstCommonResponse.class);
        if (response != null) {
            String resultCode = response.getResult();
            String resultMessage = response.getMessage();
            if (StringUtils.equals(resultCode, ConfigureConstant.STRING_1)) {
                if (response.getSucessList().size() > 0) {
                    RedInvoiceConfirmSldh redInvoiceConfirmSldh = new RedInvoiceConfirmSldh();
                    redInvoiceConfirmSldh.setSldh(response.getSucessList().get(0).getXtlsh());
                    Map map = new HashMap();
                    map.put("data",JsonUtils.getInstance().toJsonString(redInvoiceConfirmSldh));
                    //成功
                    return R.ok(map);
                } else if (response.getErrList().size() > 0) {
                    List<MycstInvoiceError> errorList = response.getErrList();
                    log.error("税航票帮手以下发票开具失败：" + JsonUtils.getInstance().toJsonString(errorList));
                    return R.error("9999", "发票开具失败：" + resultMessage);
                } else {
                    log.error("税航票帮手未返回成功或失败列表");
                    return R.error("9999", "发票开具失败,无成功或失败列表");
                }
            } else {
                log.error("税航票帮手返回失败,异常信息为:{}",resultMessage);
                return R.error("9999", "发票开具失败:" + resultMessage);
            }
        } else {
            log.error("税航票帮手返回数据为空");
            return R.error("9999", "发票开具失败，返回数据为空");
        }
    }

    /**
     * 税航票帮手 专普电票接口请求报文填充
     *
     * @return
     */
    private MycstInvoiceReq convertMycstInvoiceReq(RedInvoiceIssueReq redInvoiceIssueReq) {
        MycstInvoiceReq mycstInvoiceReq = new MycstInvoiceReq();
        mycstInvoiceReq.setXTLSH(redInvoiceIssueReq.getRequestId());
        mycstInvoiceReq.setKHMC(redInvoiceIssueReq.getDfnsrmc());
        mycstInvoiceReq.setKHSH(redInvoiceIssueReq.getDfnsrsbh());
        mycstInvoiceReq.setYFPHM(redInvoiceIssueReq.getLzqdfphm());
        //红字信息表编号
        mycstInvoiceReq.setXXBBH(redInvoiceIssueReq.getHzfpxxqrdbh());
        //发票种类转换
        MycstFplxEnum fplxEnum = MycstFplxEnum.getCodeValue(redInvoiceIssueReq.getFpzl());
        mycstInvoiceReq.setFPZL(ObjectUtils.isNotEmpty(fplxEnum) ? fplxEnum.getMycstFplx() : "");
        //补充明细
        List<MycstInvoiceItem> items = new ArrayList<>();
        if (redInvoiceIssueReq.getItemEntityList().size() > 0) {
            for (OrderInvoiceItemEntity orderInvoiceItemEntity : redInvoiceIssueReq.getItemEntityList()) {
                MycstInvoiceItem item = new MycstInvoiceItem();
                item.setCPMC(orderInvoiceItemEntity.getXmmc());
                //型号 折扣时不传或为空
                item.setCPXH(orderInvoiceItemEntity.getGgxh());
                //单位 折扣时不传或为空
                item.setCPDW(orderInvoiceItemEntity.getDw());
                item.setSL(orderInvoiceItemEntity.getSl());
                //产品数量 红字或折扣行时传负数
                item.setCPSL(orderInvoiceItemEntity.getXmsl());
                //金额 红字或折扣行时传负数
                item.setBHSJE(orderInvoiceItemEntity.getJe());
                //税额 红字或折扣行时传负数
                item.setSE(orderInvoiceItemEntity.getSe());
                //扣除额 红字或折扣行时传负数
                item.setKCJE(orderInvoiceItemEntity.getKce());
                // 不含税单价 折扣时为空或不传 红蓝票都传正数
                if(StringUtils.equals(orderInvoiceItemEntity.getFphxz(),"1")){
                    item.setZDYCPDJ("");
                }else {
                    item.setZDYCPDJ(orderInvoiceItemEntity.getDj());
                }
                item.setFLBM(orderInvoiceItemEntity.getSpbm());
                item.setXSYH(orderInvoiceItemEntity.getYhzcbs());
                if ("免税".equals(orderInvoiceItemEntity.getSl())) {
                    item.setXSYH("1");
                    item.setLSLBZ("1");
                    item.setYHSM("免税");
                } else if("不征税".equals(orderInvoiceItemEntity.getSl())){
                    item.setXSYH("1");
                    item.setLSLBZ("2");
                    item.setYHSM("不征税");
                } else {
                    item.setLSLBZ("");
                }
                //暂无
                item.setJZJTLX("");
                items.add(item);
            }
        }
        //发票明细
        mycstInvoiceReq.setITEM(items);
        mycstInvoiceReq.setCHYY(redInvoiceIssueReq.getChyy());
        //最终业务数据的请求体
        return mycstInvoiceReq;
    }

    @Override
    public R getRedInvoiceInfo(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        log.info("税航票帮手-红票查询请求开始执行，入参：{}", JsonUtils.getInstance().toJsonString(redConfirmReq));
        //发票种类
        String fpzl = redConfirmReq.getFplxdm();
        //发票种类转换
        MycstFplxEnum fplxEnum = MycstFplxEnum.getCodeValue(fpzl);
        //订单号
        String ddh = redConfirmReq.getSldh();
        //响应数据
        RedInvoiceResultInfoRes redInvoiceResultInfoRes = new RedInvoiceResultInfoRes();
        redInvoiceResultInfoRes.setHzfpxxqrdbh(redConfirmReq.getHzxxbbh());
        redInvoiceResultInfoRes.setJshj(redConfirmReq.getJshj());
        redInvoiceResultInfoRes.setXsfmc(redConfirmReq.getXhfmc());
        redInvoiceResultInfoRes.setXsfnsrsbh(StringUtils.isBlank(redConfirmReq.getXhfnsrsbh()) ? redConfirmReq.getNsrsbh() :redConfirmReq.getXhfnsrsbh());
        redInvoiceResultInfoRes.setHzqrxxztDm(redConfirmReq.getHzxxbzt());
        //1、获取token
        String token = mycstUtil.getToken();
        //2、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setFpzl(fplxEnum.getMycstFplx());
        mycstCommonRequest.setXtlsh(ddh);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        log.info("税航票帮手-异步获取红票开票结果业务参数：{}",JsonUtils.getInstance().toJsonString(mycstCommonRequest));
        //3、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);
        //4、接口调用
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getMycstGetKpjgUrl(), param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-异步获取红票开票结果请求完成,requestId:{},耗时：{},返回参数：{}", ddh, endTime - startTime, result);
        MycstGetKpjgResponse response = JSONObject.parseObject(result, MycstGetKpjgResponse.class);
        if (response != null) {
            if (StringUtils.equals(response.getResult(), "1")) {
                redInvoiceResultInfoRes.setQdfphm(response.getFPHM());
                redInvoiceResultInfoRes.setPdfxzUrl(response.getSJURL_PDF());
                redInvoiceResultInfoRes.setOfdxzUrl(response.getSJURL_OFD());
                redInvoiceResultInfoRes.setKprq(StringUtils.isNotBlank(response.getTIME()) ? response.getTIME() : response.getKPRQ());
                String data = JsonUtils.getInstance().toJsonString(redInvoiceResultInfoRes);
                log.info("税航票帮手-红票请求流水号:{}，发票查询请求成功：{}", ddh, data);
                return R.ok("0000", "红票查询请求成功").put("data", data);
            } else {
                log.error("税航票帮手返回失败");
                return R.error("9999", "异步获取红票数据失败: " + response.getMessage());
            }
        } else {
            log.error("税航票帮手返回数据为空");
            return R.error("9999", "异步获取红票数据失败");
        }
    }

    @Override
    public R redConfirmList(RedConfirmListReq redConfirmListReq, TaxpayerInfo taxpayerInfo) {
        MycstRedConfirmListReq mycstRedConfirmListReq = new MycstRedConfirmListReq();
        convertToMycstRedConfirmListReq(mycstRedConfirmListReq,redConfirmListReq);
        String url = invoiceConfig.getMycstRedConfirmList();
        //1、获取token
        String token = mycstUtil.getToken();


        mycstRedConfirmListReq.setToken(token);
        mycstRedConfirmListReq.setSpid(taxpayerInfo.getSpid());
        //6、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstRedConfirmListReq);
        //7、发送请求
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(url, param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-查询确认单列表请求完成,耗时：{},返回参数：{}", endTime - startTime, result);
        MycstRedConfirmResponse response = JSONObject.parseObject(result, MycstRedConfirmResponse.class);
        if(response != null){
            if (StringUtils.equals(response.getResult(),"1")) {

                CommonQueryHzqrxxRes commonQueryHzqrxxRes= buildRedConfirmListParam(response);

                return R.ok().put("data",JsonUtils.getInstance().toJsonString(commonQueryHzqrxxRes));

            } else {
                log.error("税航票帮手-查询确认单列表请求返回失败,{}",response.getMessage());
                return R.error("9999", response.getMessage());
            }
        }else {
            log.error("税航票帮手-查询确认单列表返回数据为空");
            return R.error("9999", "税航票帮手-查询确认单列表返回数据为空");
        }
    }




    @Override
    public R getRedConfirmInfo(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        //1、获取token
        String token = mycstUtil.getToken();
        //2、map填充参数
        Map paramMap = new HashMap();
        paramMap.put("spid",taxpayerInfo.getSpid());
        paramMap.put("token",token);
        paramMap.put("uuid",redConfirmHandle.getHzqrdbh());
        paramMap.put("xfsh",redConfirmHandle.getXsfnsrsbh());
        //3、调用接口
        log.info("税航票帮手-获取红字发票确认明细业务参数：{}",paramMap);
        String res = HttpUtils.doPost(invoiceConfig.getMycstRedConfirmInfo(), paramMap);
        log.info("税航票帮手-获取红字发票确认明细请求完成,返回参数：{}", res);
        //4、解析响应（com.dxhy.order.modules.entity.HzqrxxmxListRes.sll和sl1 不一致）
        QueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().parseObject(res, QueryHzqrxxRes.class);
        if(queryHzqrxxRes != null){
            if (ConfigureConstant.STRING_1.equals(queryHzqrxxRes.getResult())) {
                if(!CollectionUtils.isEmpty(queryHzqrxxRes.getHzqrxxmxList())){
                    queryHzqrxxRes.getHzqrxxmxList().forEach(n->{
                        n.setSll(n.getSl1());
                    });
                }
                return R.ok().put("data",queryHzqrxxRes);
            }else {
                return R.error("红字确认单明细信息查询异常:"+queryHzqrxxRes.getMessage());
            }
        }else {
            return R.error("红字确认单明细信息查询异常:返回为空");
        }
    }

    @Override
    public R redConfirmHandle(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        //1、获取token
        String token = mycstUtil.getToken();
        //2、map封装参数
        Map paramMap = new HashMap();
        paramMap.put("spid",taxpayerInfo.getSpid());
        paramMap.put("token",token);
        paramMap.put("xxbbh",redConfirmHandle.getHzqrdbh());
        paramMap.put("kprq",redConfirmHandle.getKprq());
        paramMap.put("sqly",redConfirmHandle.getSqly());
        String cllx = redConfirmHandle.getCllx();
        if(StringUtils.equals(cllx,"01")){
            paramMap.put("qrlx", "Q");
        } else if (StringUtils.equals(cllx,"02")) {
            paramMap.put("qrlx", "Y");
        } else {
            paramMap.put("qrlx", "N");
        }
        //3、接口调用
        log.info("税航票帮手-红字信息表处理业务参数：{}",paramMap);
        String res = HttpUtils.doPost(invoiceConfig.getMycstRedConfirmHandle(), paramMap);
        log.info("税航票帮手-红字信息表处理请求完成,返回参数：{}", res);
        //4、解析响应
        MycstCommonResponse response = JsonUtils.getInstance().parseObject(res, MycstCommonResponse.class);
        if(response != null){
            if (ConfigureConstant.STRING_1.equals(response.getResult())) {
                JSONObject resp = new JSONObject();
                resp.put("code","Y");
                return R.ok().put("data",resp.toJSONString());
            }else {
                return R.error("红字确认单明细信息查询异常："+response.getMessage());
            }
        }else {
            return R.error("红字确认单明细信息查询异常：返回为空");
        }
    }

    private void convertToMycstRedConfirmListReq(MycstRedConfirmListReq mycstRedConfirmListReq, RedConfirmListReq redConfirmListReq) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate kprqq = LocalDate.parse(redConfirmListReq.getKprqq(), formatter);
            DateTimeFormatter outFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String format = kprqq.format(outFormatter);

            LocalDate kprqz = LocalDate.parse(redConfirmListReq.getKprqz(), formatter);
            String format1 = kprqz.format(outFormatter);

            mycstRedConfirmListReq.setQsrq(format);
            mycstRedConfirmListReq.setJzrq(format1);
            mycstRedConfirmListReq.setPageindex(redConfirmListReq.getCurrent());
            mycstRedConfirmListReq.setPagesize(redConfirmListReq.getSize());
            mycstRedConfirmListReq.setSqly(redConfirmListReq.getLrfsf());//税航    0 购方  1销方
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private void convertToMycstRedConfirmReq(MycstRedConfirmReq mycstRedConfirmReq, RedConfirmReq redConfirmReq) {

        // 001 数电专  002 数电普      税航 020数电专   021数电普
        if("001".equals(redConfirmReq.getFplxdm())){
            mycstRedConfirmReq.setFpzl("020");
        }else  if("002".equals(redConfirmReq.getFplxdm())){
            mycstRedConfirmReq.setFpzl("021");
        }

        mycstRedConfirmReq.setKprq(redConfirmReq.getKprq());
        if("0".equals(redConfirmReq.getGxfxz())){
            mycstRedConfirmReq.setSqly("8");//税航 申请来源 7购方申请 8销方申请
        }else{
            mycstRedConfirmReq.setSqly("7");//税航 申请来源 7购方申请 8销方申请
        }
        if ("开票有误".equals(redConfirmReq.getChyymc())) {
            mycstRedConfirmReq.setChyy("01");
        } else if ("销货退回".equals(redConfirmReq.getChyymc())) {
            mycstRedConfirmReq.setChyy("02");
        } else if ("服务中止".equals(redConfirmReq.getChyymc())) {
            mycstRedConfirmReq.setChyy("03");
        } else if ("销售折让".equals(redConfirmReq.getChyymc())) {
            mycstRedConfirmReq.setChyy("04");
        }
        mycstRedConfirmReq.setFphm(redConfirmReq.getLzqdfphm());
        mycstRedConfirmReq.setGfmc(redConfirmReq.getGhfmc());
        mycstRedConfirmReq.setGfsh(redConfirmReq.getGhfnsrsbh());
        mycstRedConfirmReq.setXfmc(redConfirmReq.getXhfmc());
        mycstRedConfirmReq.setXfsh(redConfirmReq.getXhfnsrsbh());
        //要冲销金额合计
        mycstRedConfirmReq.setHjje(redConfirmReq.getHjje());
        //要冲销税额合计
        mycstRedConfirmReq.setHjse(redConfirmReq.getHjse());
        List<MycstRedConfirmItem> fyxm = new ArrayList<>();
        mycstRedConfirmReq.setFyxm(fyxm);
        for (Hzmx hzmx : redConfirmReq.getHzmx()) {

            MycstRedConfirmItem mycstRedConfirmItem = new MycstRedConfirmItem();
            //含税标记 Y是 N否
            mycstRedConfirmItem.setHsbz("N");
            mycstRedConfirmItem.setSpmc(hzmx.getSpmc());
            mycstRedConfirmItem.setGgxh(hzmx.getGgxh());
            mycstRedConfirmItem.setDw(hzmx.getDw());
            mycstRedConfirmItem.setSpsl(hzmx.getSpsl());
            mycstRedConfirmItem.setSpdj(hzmx.getSpdj());
            //明细要冲销金额
            mycstRedConfirmItem.setJe(hzmx.getJe());
            //明细要冲销税额
            mycstRedConfirmItem.setSe(hzmx.getSe());
            //明细要冲销数量
            mycstRedConfirmItem.setSl(hzmx.getSl());
            mycstRedConfirmItem.setSpbm(hzmx.getSpbm());
            fyxm.add(mycstRedConfirmItem);
        }
    }


    private CommonQueryHzqrxxRes buildRedConfirmListParam(MycstRedConfirmResponse response) {
        CommonQueryHzqrxxRes commonQueryHzqrxxRes =  new CommonQueryHzqrxxRes();
        List<QueryHzqrxxRes> records = new ArrayList<>();
        commonQueryHzqrxxRes.setRecords(records);
        commonQueryHzqrxxRes.setTotal(response.getTotal());

        response.getRows().forEach(x->{
            QueryHzqrxxRes queryHzqrxxRes = new QueryHzqrxxRes();
            queryHzqrxxRes.setUuid(x.getUuid());
            queryHzqrxxRes.setChyyDm(x.getChyyDm());
            queryHzqrxxRes.setLzfphm(x.getFphm());
            queryHzqrxxRes.setFprzztDm(x.getFprzztDm());
            queryHzqrxxRes.setGmfmc(x.getGfmc());
            queryHzqrxxRes.setGmfnsrsbh(x.getGfsh());
            queryHzqrxxRes.setLzhjje(x.getHjje());
            queryHzqrxxRes.setLzhjse(x.getHjse());
            queryHzqrxxRes.setHzfpxxqrdbh(x.getHzfpxxqrdbh());
            queryHzqrxxRes.setHzqrxxztDm(x.getHzqrxxztDm());
            queryHzqrxxRes.setYkjhzfpbz(x.getYkjhzfpbz());
            List<HzqrxxmxListRes> hzqrxxmxList = new ArrayList<>();
            queryHzqrxxRes.setHzqrxxmxList(hzqrxxmxList);
            x.getFyxm().forEach(k->{
                HzqrxxmxListRes hzqrxxmxListRes = new HzqrxxmxListRes();
                hzqrxxmxListRes.setSpfwjc(k.getBmjc());
                hzqrxxmxListRes.setFpspdj(k.getSpdj());
                hzqrxxmxListRes.setSll(k.getSl());
                hzqrxxmxListRes.setFpspsl(k.getSpsl());
                hzqrxxmxListRes.setJe(k.getJe());
                hzqrxxmxListRes.setSe(k.getSe());
                hzqrxxmxListRes.setDw(k.getDw());
                hzqrxxmxListRes.setGgxh(k.getGgxh());
                hzqrxxmxListRes.setSphfwssflhbbm(k.getSpbm());
                hzqrxxmxListRes.setHwhyslwfwmc(k.getSpmc());
                hzqrxxmxList.add(hzqrxxmxListRes);
            });
            records.add(queryHzqrxxRes);
        });
        return commonQueryHzqrxxRes;
    }
}
