package com.dxhy.invoice.service.impl.mycst;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.constant.MycstFplxEnum;
import com.dxhy.invoice.constant.MycstJtgjlxEnum;
import com.dxhy.invoice.dao.SldhFpqqlshInfoMapper;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.SldhFpqqlshInfo;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.BlueInvoiceService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.invoice.util.MycstUtil;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.SHRPA)
public class MycstBlueInvoiceServiceImpl implements BlueInvoiceService {
    @Resource
    private InvoiceConfig invoiceConfig;
    @Resource
    private SldhFpqqlshInfoMapper sldhFpqqlshInfoMapper;
    @Resource
    private MycstUtil mycstUtil;

    @Override
    public R queryInvoiceInfo(JSONObject jsonObject, TaxpayerInfo taxpayerInfo) {
        log.info("税航票帮手-发票查询请求开始执行，入参：{}", jsonObject.toJSONString());
        //发票种类
        String fpzl = jsonObject.getString("fpzl");
        //发票种类转换
        MycstFplxEnum fplxEnum = MycstFplxEnum.getCodeValue(fpzl);
        //订单请求流水号
        String ddqqlsh = jsonObject.getString("DDQQLSH");
        //订单号
        String ddh = jsonObject.getString("DDH");
        //1、获取token
        String token = mycstUtil.getToken();
        //2、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setFpzl(fplxEnum.getMycstFplx());
        mycstCommonRequest.setXtlsh(ddh);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        //3、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);
        //4、接口调用
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getMycstGetKpjgUrl(), param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-异步获取开票结果请求完成,requestId:{},耗时：{},返回参数：{}", ddqqlsh, endTime - startTime, result);
        MycstGetKpjgResponse response = JSONObject.parseObject(result, MycstGetKpjgResponse.class);
        if (response != null) {
            if (StringUtils.equals(response.getResult(), "1")) {
                JSONObject data = new JSONObject();
                data.put("OFDZJL", response.getSJURL_OFD());
                data.put("PDFZJL", response.getSJURL_PDF());
                data.put("QDFPHM", response.getFPHM());
                data.put("KPRQ", response.getTIME());
                log.info("税航票帮手-发票请求流水号:{}，发票查询请求成功：{}", ddqqlsh, JsonUtils.getInstance().toJsonString(data));
                return R.ok("0000", "发票查询请求成功").put("data", data);
            } else {
                log.error("税航票帮手返回失败");
                return R.error("9999", "异步获取发票数据失败：" + response.getMessage());
            }
        } else {
            log.error("税航票帮手返回数据为空");
            return R.error("9999", "异步获取发票数据失败");
        }
    }

    /**
     * 税航票帮手 普电专票开具接口（蓝）
     *
     * @param orderInvoiceInfoEntity
     * @param taxpayerInfo
     * @return
     */
    @Override
    public R kp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) {
        log.info("税航票帮手-发票开具，入参:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
        // 自定义请求唯一标识
        String requestId = orderInvoiceInfoEntity.getFpqqlsh();
        //1、获取token
        String token = mycstUtil.getToken();
        //2、业务数据填充
        MycstInvoiceReq mycstInvoiceReq = convertMycstInvoiceReq(orderInvoiceInfoEntity);
        //3、json格式化
        String jsonString = JsonUtils.getInstance().toJsonString(mycstInvoiceReq);
        log.info("税航票帮手发票开具业务数据，税号：{}， request：{}", orderInvoiceInfoEntity.getXhfNsrsbh(), jsonString);
        //4、base64转换
        String data = "["+ Base64.encode(jsonString)+"]";
        //5、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setData(data);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        //6、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);
        //7、发送请求
        log.info("税航票帮手-发票开具请求开始，requestId:{}", requestId);
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getMycstFpkjUrl(), param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-发票开具请求完成,requestId:{},耗时：{},返回参数：{}", requestId, endTime - startTime, result);
        MycstCommonResponse response = JSONObject.parseObject(result, MycstCommonResponse.class);
        if(response != null){
            String resultCode = response.getResult();
            String resultMessage = response.getMessage();
            if (StringUtils.equals(resultCode,ConfigureConstant.STRING_1)) {
                if (response.getSucessList().size() > 0) {
                    for (MycstInvoiceSuccess out : response.getSucessList()) {
                        SldhFpqqlshInfo sldhFpqqlshInfo1 = sldhFpqqlshInfoMapper.getOneByFpqqlsh(requestId);
                        if (ObjectUtils.isNotEmpty(sldhFpqqlshInfo1)) {
                            boolean b = sldhFpqqlshInfoMapper.updateByFpqqlsh(requestId) > 0;
                            if (b) {
                                log.info("税航票帮手发票开具-已存在数据且更新状态成功，requestId:{}", requestId);
                            }else {
                                log.info("税航票帮手发票开具-已存在数据且更新状态失败，requestId:{}", requestId);
                            }

                        }

                        log.info("税航票帮手发票开具，入库请求的开票数据开始，requestId:{}", requestId);
                        SldhFpqqlshInfo sldhFpqqlshInfo = new SldhFpqqlshInfo();
                        sldhFpqqlshInfo.setSldh("");
                        sldhFpqqlshInfo.setFpqqlsh(requestId);
                        //数电发票号码=发票代码+发票号码
                        sldhFpqqlshInfo.setQdfphm(out.getFpdm()+out.getFphm());
                        String kprq = StringUtils.isNotBlank(out.getTime()) ? out.getTime() : out.getKprq();
                        sldhFpqqlshInfo.setKprq(StringUtils.isNotBlank(kprq) ? kprq : null);
                        sldhFpqqlshInfo.setNsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                        sldhFpqqlshInfo.setIs_delete("0");
                        boolean b = sldhFpqqlshInfoMapper.insert(sldhFpqqlshInfo) > 0;
                        if (b) {
                            log.info("税航发票发票开具-入库请求的开票数据成功，requestId:{}", requestId);
                        }else {
                            log.info("税航发票开具-入库请求的开票数据失败，requestId:{}", requestId);
                        }
                    }
                } else if(response.getErrList().size() > 0){
                    List<MycstInvoiceError> errorList = response.getErrList();
                    log.error("税航票帮手以下发票开具失败："+JsonUtils.getInstance().toJsonString(errorList));
                    return R.error("9999", "发票开具失败："+resultMessage);
                } else {
                    log.error("税航票帮手未返回成功或失败列表");
                    return R.error("9999", "发票开具失败,无成功或失败列表");
                }
            } else {
                log.error("税航票帮手返回失败");
                return R.error("9999", "发票开具失败:"+resultMessage);
            }
        }else {
            log.error("税航票帮手返回数据为空");
            return R.error("9999", "发票开具失败，返回数据为空");
        }
        return R.ok();
    }

    /**
     * 税航票帮手 专普电票接口请求报文填充
     *
     * @return
     */
    private MycstInvoiceReq convertMycstInvoiceReq(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        MycstInvoiceReq mycstInvoiceReq = new MycstInvoiceReq();
        mycstInvoiceReq.setXTLSH(orderInvoiceInfoEntity.getDdh());
        mycstInvoiceReq.setKHMC(orderInvoiceInfoEntity.getGhfMc());
        mycstInvoiceReq.setKHSH(orderInvoiceInfoEntity.getGhfNsrsbh());
        mycstInvoiceReq.setKHDZ(orderInvoiceInfoEntity.getGhfDz());
        mycstInvoiceReq.setKHKHYHZH(orderInvoiceInfoEntity.getGhfZh());
        String fpzlDm = orderInvoiceInfoEntity.getFpzlDm();
        //发票种类转换
        MycstFplxEnum fplxEnum = MycstFplxEnum.getCodeValue(fpzlDm);
        mycstInvoiceReq.setFPZL(ObjectUtils.isNotEmpty(fplxEnum) ? fplxEnum.getMycstFplx() : "");
        mycstInvoiceReq.setBZ(orderInvoiceInfoEntity.getBz());
        mycstInvoiceReq.setKPR(orderInvoiceInfoEntity.getKpr());
        mycstInvoiceReq.setSKR(orderInvoiceInfoEntity.getSkr());
        mycstInvoiceReq.setFHR(orderInvoiceInfoEntity.getFhr());
        mycstInvoiceReq.setQYKHYHZH(orderInvoiceInfoEntity.getXhfYh()+" "+orderInvoiceInfoEntity.getXhfZh());
        mycstInvoiceReq.setQYDZDH(orderInvoiceInfoEntity.getXhfDz()+" "+orderInvoiceInfoEntity.getXhfDh());
        mycstInvoiceReq.setQDBZ(orderInvoiceInfoEntity.getQdBz());
        mycstInvoiceReq.setKHYJ(orderInvoiceInfoEntity.getGhfYx());
        mycstInvoiceReq.setKHSJ(orderInvoiceInfoEntity.getGhfSj());
        String jdc = "";
        if(StringUtils.equals(fpzlDm,"003")){
            jdc = "1";
        } else if (StringUtils.equals(fpzlDm,"104")) {
            jdc = "3";
        } else if (StringUtils.equals(fpzlDm,"005")) {
            jdc = "2";
        }
        mycstInvoiceReq.setJDC(jdc);
        //暂无
        mycstInvoiceReq.setDBTS("");
        //特定业务标识（不含特定业务信息的) SGFP 农产品收购 LCPXS 自产农产品销售，XT 稀土 FWHS 报废产品收购
        String zsfs = orderInvoiceInfoEntity.getTdyw();
        if(StringUtils.equals(zsfs,OrderInfoEnum.ORDER_QD_TDYS_02.getKey())){
            zsfs = "XT";
        } else if (StringUtils.equals(zsfs,OrderInfoEnum.ORDER_QD_TDYS_12.getKey())) {
            zsfs = "LCPXS";
        } else if (StringUtils.equals(zsfs,OrderInfoEnum.ORDER_QD_TDYS_16.getKey())) {
            zsfs = "SGFP";
        } else if (StringUtils.equals(zsfs,OrderInfoEnum.ORDER_QD_TDYS_24.getKey())) {
            zsfs = "FWHS";
        }
        mycstInvoiceReq.setZSFS(zsfs);

        List<MycstInvoiceItem> items = new ArrayList<>();
        if (orderInvoiceInfoEntity.getItemEntityList().size() > 0) {
            for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceInfoEntity.getItemEntityList()) {
                MycstInvoiceItem item = new MycstInvoiceItem();
                item.setCPMC(orderInvoiceItemEntity.getXmmc());
                item.setCPXH(orderInvoiceItemEntity.getGgxh());
                item.setCPDW(orderInvoiceItemEntity.getDw());
                item.setSL(orderInvoiceItemEntity.getSl());
                item.setCPSL(orderInvoiceItemEntity.getXmsl());
                //前边已经做了价税分离，此处拿到的都是不含税的
                item.setBHSJE(orderInvoiceItemEntity.getJe());
                item.setZDYCPDJ(orderInvoiceItemEntity.getDj());
                item.setSE(orderInvoiceItemEntity.getSe());
                item.setFLBM(orderInvoiceItemEntity.getSpbm());
                item.setXSYH(orderInvoiceItemEntity.getYhzcbs());
                if ("免税".equals(orderInvoiceItemEntity.getSl())) {
                    item.setXSYH("1");
                    item.setLSLBZ("1");
                    item.setYHSM("免税");
                } else if("不征税".equals(orderInvoiceItemEntity.getSl())){
                    item.setXSYH("1");
                    item.setLSLBZ("2");
                    item.setYHSM("不征税");
                } else {
                    item.setLSLBZ("");
                }
                item.setKCJE(orderInvoiceItemEntity.getKce());
                //暂无
                item.setJZJTLX("");
                items.add(item);
            }
        }
        //发票明细
        mycstInvoiceReq.setITEM(items);
        //特定业务
        List<MycstInvoiceTdyw> tdyws = new ArrayList<>();
        InvoiceTdywEntity invoiceTdywEntity = orderInvoiceInfoEntity.getInvoiceTdywEntity();
        //机动车
        InvoiceJdcxxEntity jdcxxEntity = null;
        //二手车
        InvoiceEscxxEntity escxxEntity = null;
        if(ObjectUtils.isNotEmpty(invoiceTdywEntity)){
            String tdys = invoiceTdywEntity.getTdys();
            if(StringUtils.isNotBlank(tdys)){
                if(StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(),tdys)){
                    //建筑服务
                    InvoiceJzfwxxEntity jzfwxxEntity = invoiceTdywEntity.getJzfwxx();
                    if(ObjectUtils.isNotEmpty(jzfwxxEntity)){
                        MycstInvoiceTdyw tdyw = new MycstInvoiceTdyw();
                        tdyw.setGZFW_XMBH(jzfwxxEntity.getTdzzsxmbh());
                        tdyw.setGZFW_KQYBYBH(jzfwxxEntity.getKqysssxbgglbm());
                        tdyw.setGZFW_FSD(jzfwxxEntity.getJzfwfsd());
                        tdyw.setGZFW_XXDZ(jzfwxxEntity.getJzfwxxdz());
                        tdyw.setGZFW_XMMC(jzfwxxEntity.getJzfwmc());
                        tdyw.setGZFW_KSBZ(ConfigureConstant.STRING_0);
                        if(StringUtils.isNotBlank(jzfwxxEntity.getKdsbz()) && StringUtils.equals(jzfwxxEntity.getKdsbz(), ConfigureConstant.STRING_Y)){
                            tdyw.setGZFW_KSBZ(ConfigureConstant.STRING_1);
                        }
                        tdyws.add(tdyw);
                    }
                }else if(StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(),tdys)){
                    //货物运输
                    List<InvoiceHwysxxEntity> hwysxxEntityList = invoiceTdywEntity.getHwysxx();
                    if(CollectionUtils.isNotEmpty(hwysxxEntityList)){
                        for(InvoiceHwysxxEntity hwysxxEntity : hwysxxEntityList){
                            MycstInvoiceTdyw tdyw = new MycstInvoiceTdyw();
                            tdyw.setHWYS_QYD(hwysxxEntity.getQyd());
                            tdyw.setHWYS_DDD(hwysxxEntity.getDdd());
                            tdyw.setHWYS_YSGJ(hwysxxEntity.getYsgjzl());
                            tdyw.setHWYS_YSHWMC(hwysxxEntity.getYshwmc());
                            tdyw.setHWYS_YSGJTH(hwysxxEntity.getYsgjph());
                            tdyws.add(tdyw);
                        }
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(),tdys)) {
                    //不动产销售
                    List<InvoiceBdcxsxxEntity> bdcxsxxEntityList = invoiceTdywEntity.getBdcxsxx();
                    if(CollectionUtils.isNotEmpty(bdcxsxxEntityList)){
                        for (InvoiceBdcxsxxEntity bdcxsxxEntity : bdcxsxxEntityList){
                            MycstInvoiceTdyw tdyw = new MycstInvoiceTdyw();
                            tdyw.setBDCXS_CJJE(bdcxsxxEntity.getSjcjhsje());
                            tdyw.setBDCXS_DQDZ(bdcxsxxEntity.getBdcdq());
                            tdyw.setBDCXS_XXDZ(bdcxsxxEntity.getBdcdz());
                            tdyw.setBDCXS_JSJG(bdcxsxxEntity.getHdjsjg());
                            tdyw.setBDCXS_HTBH(bdcxsxxEntity.getBdcwqhtbh());
                            tdyw.setBDCXS_CQZH(bdcxsxxEntity.getCqzsh());
                            tdyw.setBDCXS_KQBZ(ConfigureConstant.STRING_0);
                            if(StringUtils.isNotBlank(bdcxsxxEntity.getKdsbz()) && StringUtils.equals(bdcxsxxEntity.getKdsbz(), ConfigureConstant.STRING_Y)){
                                tdyw.setBDCXS_KQBZ(ConfigureConstant.STRING_1);
                            }
                            tdyw.setBDCXS_MJDW(bdcxsxxEntity.getMjdw());
                            tdyw.setBDCXS_XMBH(bdcxsxxEntity.getTdzzsxmbh());
                            tdyws.add(tdyw);
                        }
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(),tdys)) {
                    //不动产租赁
                    List<InvoiceBdczlxxEntity> bdczlxxEntityList = invoiceTdywEntity.getBdczlxx();
                    if(CollectionUtils.isNotEmpty(bdczlxxEntityList)){
                        for (InvoiceBdczlxxEntity bdczlxxEntity : bdczlxxEntityList){
                            MycstInvoiceTdyw tdyw = new MycstInvoiceTdyw();
                            //租赁地区和详细地址
                            tdyw.setBDC_SF(bdczlxxEntity.getBdcdq());
                            tdyw.setBDC_XXDZ(bdczlxxEntity.getBdcdz());
                            //租赁起止时间
                            String zlqqz = bdczlxxEntity.getZlqqz();
                            String[] dateArr = zlqqz.split(" ");
                            if(dateArr.length == 2){
                                tdyw.setBDC_QSRQ(dateArr[0]);
                                tdyw.setBDC_JZRQ(dateArr[1]);
                            }else if(dateArr.length ==4){
                                tdyw.setBDC_QSRQ(dateArr[0]+" "+dateArr[1]);
                                tdyw.setBDC_JZRQ(dateArr[2]+" "+dateArr[3]);
                            }else {
                                log.error("租赁期起止格式有误，{}",zlqqz);
                                throw new RuntimeException("租赁期起止格式有误："+zlqqz);
                            }
                            tdyw.setBDC_KSBZ(ConfigureConstant.STRING_0);
                            if(StringUtils.isNotBlank(bdczlxxEntity.getKdsbz()) && StringUtils.equals(bdczlxxEntity.getKdsbz(), ConfigureConstant.STRING_Y)){
                                tdyw.setBDC_KSBZ(ConfigureConstant.STRING_1);
                            }
                            tdyw.setBDC_CQJH(bdczlxxEntity.getCqzsh());
                            tdyw.setBDC_PJDW(bdczlxxEntity.getMjdw());
                            tdyws.add(tdyw);
                        }
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(),tdys)) {
                    //旅客运输
                    List<InvoiceLkysfwxxEntity> lkysfwxxEntityList = invoiceTdywEntity.getLkysfwxx();
                    if(CollectionUtils.isNotEmpty(lkysfwxxEntityList)) {
                        for (InvoiceLkysfwxxEntity lkysfwxxEntity : lkysfwxxEntityList) {
                            MycstInvoiceTdyw tdyw = new MycstInvoiceTdyw();
                            tdyw.setLKYS_CXR(lkysfwxxEntity.getCxr());
                            tdyw.setLKYS_CFD(lkysfwxxEntity.getLkyscfd());
                            tdyw.setLKYS_DDD(lkysfwxxEntity.getLkysddd());
                            tdyw.setLKYS_DJ(lkysfwxxEntity.getZwdj());
                            tdyw.setLKYS_CXRQ(lkysfwxxEntity.getCxrq());
                            tdyw.setLKYS_CXRZJHM(lkysfwxxEntity.getCxrzjhm());
                            tdyw.setLKYS_CXRZJLX(lkysfwxxEntity.getCxrzjlx());
                            if(StringUtils.isNotBlank(lkysfwxxEntity.getJtgjlx())){
                                //交通工具类型转换
                                MycstJtgjlxEnum mycstJtgjlxEnum = MycstJtgjlxEnum.getCodeValue(lkysfwxxEntity.getJtgjlx());
                                tdyw.setLKYS_JTGJLX(ObjectUtils.isNotEmpty(mycstJtgjlxEnum) ? mycstJtgjlxEnum.getMycstJtgjlx() : "");
                            }
                            tdyws.add(tdyw);
                        }
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(),tdys)) {
                    //机动车
                    jdcxxEntity = invoiceTdywEntity.getJdcxx();
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(),tdys)) {
                    //二手车
                    escxxEntity = invoiceTdywEntity.getEscxx();
                }else {
                    log.error("不支持的特定业务类型，{}",tdys);
                    throw new RuntimeException("组织报文异常，不支持的特定业务类型："+tdys);
                }
            }
        }
        //特定业务
        mycstInvoiceReq.setTDYW(tdyws);
        //不传默认2，立即开票
        mycstInvoiceReq.setFPZT("");
        List<MycstInvoiceFjxx> fjxxs = new ArrayList<>();
        if(orderInvoiceInfoEntity.getInfoEntityList().size() > 0){
            for (InvoiceAdditionInfoEntity additionInfoEntity : orderInvoiceInfoEntity.getInfoEntityList()){
                MycstInvoiceFjxx fjxx = new MycstInvoiceFjxx();
                fjxx.setFJXXMC(additionInfoEntity.getFjxxmc());
                fjxx.setFJXXLY(additionInfoEntity.getFjxxz());
                fjxxs.add(fjxx);
            }
        }
        mycstInvoiceReq.setFJXX(fjxxs);
        mycstInvoiceReq.setZRRBZ(orderInvoiceInfoEntity.getSpfzrrbs());
        //显示销方、购方银行信息
        mycstInvoiceReq.setXSXFXX(ConfigureConstant.STRING_0);
        mycstInvoiceReq.setXSGFXX(ConfigureConstant.STRING_0);
        if(StringUtils.isNotBlank(orderInvoiceInfoEntity.getSfzsxfyhzh()) && StringUtils.equals(orderInvoiceInfoEntity.getSfzsxfyhzh(), ConfigureConstant.STRING_Y)){
            mycstInvoiceReq.setXSXFXX(ConfigureConstant.STRING_1);
        }
        if(StringUtils.isNotBlank(orderInvoiceInfoEntity.getSfzsgfyhzh()) && StringUtils.equals(orderInvoiceInfoEntity.getSfzsgfyhzh(), ConfigureConstant.STRING_Y)){
            mycstInvoiceReq.setXSGFXX(ConfigureConstant.STRING_1);
        }

        mycstInvoiceReq.setQXYHSM(orderInvoiceInfoEntity.getGiveUpReason());
        //差额明细
        List<InvoiceCezsEntity> cezslist = orderInvoiceInfoEntity.getCezslist();
        if(CollectionUtils.isNotEmpty(cezslist)){
            List<MycstInvoiceCemx> cemxs = new ArrayList<>();
            for(InvoiceCezsEntity cezsEntity : cezslist){
                MycstInvoiceCemx mycstInvoiceCemx = new MycstInvoiceCemx();
                mycstInvoiceCemx.setPZLX(cezsEntity.getPzlx());
                mycstInvoiceCemx.setFPDM(cezsEntity.getFpdm());
                mycstInvoiceCemx.setFPHM(cezsEntity.getFphm());
                mycstInvoiceCemx.setZZFPHM(cezsEntity.getZzfphm());
                mycstInvoiceCemx.setPZHM(cezsEntity.getPzhm());
                mycstInvoiceCemx.setKJRQ(cezsEntity.getKjrq());
                mycstInvoiceCemx.setHJJE(cezsEntity.getHjje());
                mycstInvoiceCemx.setKCE(cezsEntity.getKce());
                mycstInvoiceCemx.setBZ(cezsEntity.getBz());
                cemxs.add(mycstInvoiceCemx);
            }
            mycstInvoiceReq.setCEMX(cemxs);
        }
        //二手车信息
        List<MycstInvoiceEscxx> escxxs = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(escxxEntity)){
            MycstInvoiceEscxx mycstInvoiceEscxx = new MycstInvoiceEscxx();
            mycstInvoiceEscxx.setCPXH(escxxEntity.getCpxh());
            mycstInvoiceEscxx.setDJZH(escxxEntity.getDjzh());
            mycstInvoiceEscxx.setCPHM(escxxEntity.getCpzh());
            mycstInvoiceEscxx.setCLSBH(escxxEntity.getClsbdm());
            mycstInvoiceEscxx.setCLLXDM(escxxEntity.getCllx());
            mycstInvoiceEscxx.setMFDH(escxxEntity.getEscXhfdh());
            mycstInvoiceEscxx.setMFDZ(escxxEntity.getEscXhfdz());
            mycstInvoiceEscxx.setMFMC(escxxEntity.getEscXhfmc());
            mycstInvoiceEscxx.setMFSH(escxxEntity.getEscXhfdm());
            mycstInvoiceEscxx.setMFYH(escxxEntity.getScKhyh());
            mycstInvoiceEscxx.setMFYHZH(escxxEntity.getScYhzh());
            mycstInvoiceEscxx.setCLGLSMC(escxxEntity.getZrdclglsmc());
            escxxs.add(mycstInvoiceEscxx);
            mycstInvoiceReq.setESCXX(escxxs);
        }
        //机动车信息
        List<MycstInvoiceJdcxx> jdcxxs = new ArrayList<>();
        if(ObjectUtils.isNotEmpty(jdcxxEntity)){
            MycstInvoiceJdcxx mycstInvoiceJdcxx = new MycstInvoiceJdcxx();
            mycstInvoiceJdcxx.setCD(jdcxxEntity.getClcd());
            mycstInvoiceJdcxx.setCPXH(jdcxxEntity.getCpxh());
            mycstInvoiceJdcxx.setHGZH(jdcxxEntity.getHgzh());
            mycstInvoiceJdcxx.setJKJMH(jdcxxEntity.getJkzmsh());
            mycstInvoiceJdcxx.setSJDH(jdcxxEntity.getSjdh());
            mycstInvoiceJdcxx.setDW(jdcxxEntity.getDw());
            mycstInvoiceJdcxx.setXCRS(jdcxxEntity.getXcrs());
            jdcxxs.add(mycstInvoiceJdcxx);
            mycstInvoiceReq.setJDCXX(jdcxxs);
        }
        //显示销方、购方地址电话信息
        mycstInvoiceReq.setXSXFDZ(ConfigureConstant.STRING_0);
        mycstInvoiceReq.setXSGFDZ(ConfigureConstant.STRING_0);
        if(StringUtils.isNotBlank(orderInvoiceInfoEntity.getSfzsxfdzdh()) && StringUtils.equals(orderInvoiceInfoEntity.getSfzsxfdzdh(), ConfigureConstant.STRING_Y)){
            mycstInvoiceReq.setXSXFDZ(ConfigureConstant.STRING_1);
        }
        if(StringUtils.isNotBlank(orderInvoiceInfoEntity.getSfzsgfdzdh()) && StringUtils.equals(orderInvoiceInfoEntity.getSfzsgfdzdh(), ConfigureConstant.STRING_Y)){
            mycstInvoiceReq.setXSGFDZ(ConfigureConstant.STRING_1);
        }
        mycstInvoiceReq.setXSRRXX("1");
        //最终业务数据的请求体
        return mycstInvoiceReq;
    }

}
