package com.dxhy.invoice.service;

import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.pojo.InvoiceInfoReq;

/**
 * @Auther: admin
 * @Date: 2022/9/13 15:34
 * @Description:
 */
public interface InvoiceInfoService {
    R getInvoiceInfos(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) throws Exception;

    R getSingleInvoiceInfo(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo);
}
