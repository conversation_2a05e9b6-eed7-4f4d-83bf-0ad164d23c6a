package com.dxhy.invoice.service;

import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.pojo.DeptInfo;

import java.util.Map;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:27
 * @Description:
 */
public interface TaxpayerInfoService {
    TaxpayerInfo getTaxpayerInfo(String nsrsbh, Map<String,String> params);

    R addTaxpayerInfo(DeptInfo deptInfo);

    R getQrcode(String nsrsbh);

    R getQrcodeStatus(String nsrsbh,String rzid);
}
