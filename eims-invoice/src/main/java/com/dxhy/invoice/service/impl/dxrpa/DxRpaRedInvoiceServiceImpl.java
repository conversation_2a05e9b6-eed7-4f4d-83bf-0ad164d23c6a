package com.dxhy.invoice.service.impl.dxrpa;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.dao.SldhFpqqlshInfoMapper;
import com.dxhy.invoice.dao.SldhHzqrdDao;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.SldhHzqrdEntity;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.RedInvoiceService;
import com.dxhy.invoice.util.Base64Util;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.model.dxOpenPlatform.*;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.pojo.*;
import com.dxhy.order.utils.Base64Encoding;
import com.obs.services.ObsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: admin
 * @Date: 2022/9/6 09:58
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.DXRPA)
public class DxRpaRedInvoiceServiceImpl implements RedInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private SldhHzqrdDao sldhHzqrdDao;

    @Resource
    private SldhFpqqlshInfoMapper sldhFpqqlshInfoMapper;

    @Override
    public R redConfirm(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        DXRedConfirmReq dxRedConfirmReq = new DXRedConfirmReq();
        //对象转换
        convertToDXRedConfirmReq(dxRedConfirmReq,redConfirmReq);
        String url = invoiceConfig.getRedConfirmApply();
        String res = HttpUtils.doPostToDx(url, JsonUtils.getInstance().toJsonString(dxRedConfirmReq));
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            DXApplyRedConfirmResp dxApplyRedConfirmResp = JsonUtils.getInstance().parseObject(Base64Util.decode(dxPlatFormResponse.getContent()), DXApplyRedConfirmResp.class);
            //保存信息到数据库  返回受理单号   确认单结果查询 查库返回信息
            SldhHzqrdEntity sldhHzqrdEntity = new SldhHzqrdEntity();
            String  sldh = RandomUtil.randomString(16);
            sldhHzqrdEntity.setSldh(sldh);
            sldhHzqrdEntity.setHzfpxxqrdbh(dxApplyRedConfirmResp.getHZQRDSCJG().getHZFPXXQRDBH());
            sldhHzqrdEntity.setHzqrxxztdm(dxApplyRedConfirmResp.getHZQRDSCJG().getHZQRXXZTDM());
            sldhHzqrdEntity.setUuid(dxApplyRedConfirmResp.getHZQRDSCJG().getUUID());
            sldhHzqrdDao.insert(sldhHzqrdEntity);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sldh",sldh);
            return R.ok().put("data",jsonObject.toJSONString());
        }

        return R.error("红字确认单申请失败");

    }

    private void convertToDXRedConfirmReq(DXRedConfirmReq dxRedConfirmReq, RedConfirmReq redConfirmReq) {
        dxRedConfirmReq.setNSRSBH(redConfirmReq.getNsrsbh());
        dxRedConfirmReq.setLRFSF(redConfirmReq.getGxfxz());
        dxRedConfirmReq.setXSFNSRSBH(redConfirmReq.getXhfnsrsbh());
        dxRedConfirmReq.setXSFMC(redConfirmReq.getXhfmc());
        dxRedConfirmReq.setGMFNSRSBH(redConfirmReq.getGhfnsrsbh());
        dxRedConfirmReq.setGMFMC(redConfirmReq.getGhfmc());
        dxRedConfirmReq.setLZFPDM(redConfirmReq.getFpdm());
        dxRedConfirmReq.setLZFPHM(redConfirmReq.getFphm());
        dxRedConfirmReq.setLZFPQDHM(redConfirmReq.getLzqdfphm());
        dxRedConfirmReq.setSFZZFPBZ("N");
        dxRedConfirmReq.setLZKPRQ(redConfirmReq.getKprq());
        dxRedConfirmReq.setLZHJJE(redConfirmReq.getHjje());
        dxRedConfirmReq.setLZHJSE(redConfirmReq.getHjse());
        dxRedConfirmReq.setLZFPPZDM(redConfirmReq.getFplxdm().substring(1));
        dxRedConfirmReq.setLZFPTDYSLXDM("");
        //全部或部分红冲，不用加 - 号
        dxRedConfirmReq.setHZCXJE(redConfirmReq.getHjje());
        dxRedConfirmReq.setHZCXSE(redConfirmReq.getHjse());
        dxRedConfirmReq.setCHYYDM(redConfirmReq.getChyymc());
        List<DXRedConfirmItem> dxRedConfirmItemList = new ArrayList<>();
        for (Hzmx hzmx : redConfirmReq.getHzmx()) {
            DXRedConfirmItem dxRedConfirmItem = new DXRedConfirmItem();
            dxRedConfirmItem.setSPHFWSSFLHBBM(hzmx.getSpbm());
            dxRedConfirmItem.setXMMC(hzmx.getSpmc());
            dxRedConfirmItem.setGGXH(hzmx.getGgxh());
            dxRedConfirmItem.setDW(hzmx.getDw());
            dxRedConfirmItem.setFPSPDJ(hzmx.getSpdj());
            dxRedConfirmItem.setFPSPSL(hzmx.getSpsl());
            dxRedConfirmItem.setJE(hzmx.getJe());
            dxRedConfirmItem.setSE(hzmx.getSe());
            dxRedConfirmItem.setSLV(hzmx.getSl());
            dxRedConfirmItemList.add(dxRedConfirmItem);
        }
        dxRedConfirmReq.setHZQRDMXLIST(dxRedConfirmItemList);
    }
    @Override
    public R getRedConfirmResult(RedConfirmReq redConfirmReqt, TaxpayerInfo taxpayerInfo) {
        String  sldh = redConfirmReqt.getSldh();
        QueryWrapper<SldhHzqrdEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sldh",sldh);
        SldhHzqrdEntity sldhHzqrdEntity = sldhHzqrdDao.selectOne(queryWrapper);
        RedInvoiceConfirmSldhRes redInvoiceConfirmSldhRes = new RedInvoiceConfirmSldhRes();
        redInvoiceConfirmSldhRes.setHzfpxxqrdbh(sldhHzqrdEntity.getHzfpxxqrdbh());
        redInvoiceConfirmSldhRes.setHzqrxxztDm(sldhHzqrdEntity.getHzqrxxztdm());
        return R.ok().put("data",redInvoiceConfirmSldhRes);
    }

    @Override
    public R redInvoiceIssue(RedInvoiceIssueReq redInvoiceIssueReq, TaxpayerInfo taxpayerInfo) {
        DXRedInvoiceReq dxRedInvoiceReq = new DXRedInvoiceReq();
        //2、参数转换
        convertToDXRedInvoiceReq(dxRedInvoiceReq,redInvoiceIssueReq);
        //3、json格式化
        String jsonString = JsonUtils.getInstance().toJsonString(dxRedInvoiceReq);
        log.info("大象通道红票开具业务数据，税号：{}， request：{}", redInvoiceIssueReq.getNsrsbh(), jsonString);
        String url = invoiceConfig.getDxRedInvoiceIssue();
        String res = HttpUtils.doPostToDx(url, jsonString);
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            DXRedInvoiceResp dxRedInvoiceResp = JsonUtils.getInstance().parseObject(Base64Util.decode(dxPlatFormResponse.getContent()), DXRedInvoiceResp.class);
            RedInvoiceConfirmSldh redInvoiceConfirmSldh = new RedInvoiceConfirmSldh();
            redInvoiceConfirmSldh.setSldh(dxRedInvoiceResp.getFPKJJG().getFPHM());
            redInvoiceConfirmSldh.setKprq(DateUtil.parse(dxRedInvoiceResp.getFPKJJG().getKPRQ()));
            Map map = new HashMap();
            map.put("data",JsonUtils.getInstance().toJsonString(redInvoiceConfirmSldh));
            //成功
            return R.ok(map);
        }

        return R.error("9999","红字发票开具失败");
    }

    private void convertToDXRedInvoiceReq(DXRedInvoiceReq dxRedInvoiceReq, RedInvoiceIssueReq redInvoiceIssueReq) {
        DXRedInvoiceReq.RedInvoice redInvoice = new DXRedInvoiceReq.RedInvoice();
        redInvoice.setFPQQLSH(redInvoiceIssueReq.getRequestId());
        redInvoice.setDDH("");
        redInvoice.setNSRSBH(redInvoiceIssueReq.getNsrsbh());
        redInvoice.setKPR("开票人");
        redInvoice.setHZQRXXDBH(redInvoiceIssueReq.getHzfpxxqrdbh());
        redInvoice.setKCE("");
        redInvoice.setBZ("");
        redInvoice.setYHM("");
        dxRedInvoiceReq.setREDINVOICE(redInvoice);
    }
    @Override
    public R getRedInvoiceInfo(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        RedInvoiceResultInfoRes redInvoiceResultInfoRes = new RedInvoiceResultInfoRes();
        redInvoiceResultInfoRes.setHzfpxxqrdbh(redConfirmReq.getHzxxbbh());
        redInvoiceResultInfoRes.setJshj(redConfirmReq.getJshj());
        redInvoiceResultInfoRes.setXsfmc(redConfirmReq.getXhfmc());
        redInvoiceResultInfoRes.setXsfnsrsbh(StringUtils.isBlank(redConfirmReq.getXhfnsrsbh()) ? redConfirmReq.getNsrsbh() :redConfirmReq.getXhfnsrsbh());
        redInvoiceResultInfoRes.setHzqrxxztDm(redConfirmReq.getHzxxbzt());
        redInvoiceResultInfoRes.setQdfphm(redConfirmReq.getSldh());
        redInvoiceResultInfoRes.setKprq(redConfirmReq.getKprq());
        String url = invoiceConfig.getFileDownload();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("NSRSBH",taxpayerInfo.getNsrsbh());
        jsonObject1.put("FPHM",redConfirmReq.getSldh());
        jsonObject1.put("KPRQ",redConfirmReq.getKprq());
        jsonObject1.put("WJLX","PDF");
        String res = HttpUtils.doPostToDx(url,jsonObject1.toJSONString());
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {

            DxQdResponse dxQdResponse = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(dxPlatFormResponse.getContent()), DxQdResponse.class);
            String ep = "obs.cn-north-4.myhuaweicloud.com";
            String ak = "G8S6VLXLK5HSQX2EWUNI";
            String sk = "SAR4z9SGW9gxJOyn6xLljlHGzyfwWMaHLgtRLb0B";
            String bn = "qst-pdf";
            ObsClient obsClient = new ObsClient(ak, sk, ep);
            // 上传对象 保存到华为云OBS
            obsClient.putObject(bn, redConfirmReq.getSldh()+".pdf", new ByteArrayInputStream(Base64.decodeBase64(dxQdResponse.getBSWJ().getWJL())));
            redInvoiceResultInfoRes.setPdfxzUrl(invoiceConfig.getDownloadUrl()+redConfirmReq.getSldh()+".pdf");
            String data = JsonUtils.getInstance().toJsonString(redInvoiceResultInfoRes);
            log.info("大象RPA发票查询请求成功：{}", data);
            return R.ok("0000", "红票查询请求成功").put("data", data);
        }
        return R.error("9999", "异步获取红票数据失败");
    }

    @Override
    public R redConfirmList(RedConfirmListReq redConfirmListReq, TaxpayerInfo taxpayerInfo) {
        DXRedConfirmListReq dxRedConfirmListReq = new DXRedConfirmListReq();
        convertToDXRedConfirmListReq(dxRedConfirmListReq,redConfirmListReq);
        String url = invoiceConfig.getGetRedConfirmList();
        String res = HttpUtils.doPostToDx(url, JsonUtils.getInstance().toJsonString(dxRedConfirmListReq));
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            DXApplyRedConfirmResp dxApplyRedConfirmResp = JsonUtils.getInstance().parseObject(Base64Util.decode(dxPlatFormResponse.getContent()), DXApplyRedConfirmResp.class);
            final DXRedConfirmListResult hzqrxxlb = dxApplyRedConfirmResp.getHZQRXXLB();
            CommonQueryHzqrxxRes commonQueryHzqrxxRes = new CommonQueryHzqrxxRes();
            convertToCommonQueryHzqrxxRes(hzqrxxlb,commonQueryHzqrxxRes);
            return R.ok().put("data",commonQueryHzqrxxRes);
        }
        return R.error("红字确认单列表查询异常");
    }




    @Override
    public R getRedConfirmInfo(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        Map map = new HashMap();
        map.put("NSRSBH",redConfirmHandle.getNsrsbh());
        map.put("HZQRDUUID",redConfirmHandle.getUuid());
        map.put("XSFNSRSBH",redConfirmHandle.getXsfnsrsbh());
        String url = invoiceConfig.getGetRedConfirmInfo();
        String res = HttpUtils.doPostToDx(url, JsonUtils.getInstance().toJsonString(map));
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            DXApplyRedConfirmResp dxApplyRedConfirmResp = JsonUtils.getInstance().parseObject(Base64Util.decode(dxPlatFormResponse.getContent()), DXApplyRedConfirmResp.class);
            QueryHzqrxxRes queryHzqrxxRes = dxApplyRedConfirmResp.getHZQRDXX();
            return R.ok().put("data",queryHzqrxxRes);
        }
        return R.error("红字确认单明细信息查询异常");
    }

    @Override
    public R redConfirmHandle(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        Map map = new HashMap();
        map.put("NSRSBH",redConfirmHandle.getNsrsbh());
        map.put("UUID",redConfirmHandle.getUuid());
        map.put("HZQRDBH",redConfirmHandle.getHzqrdbh());
        map.put("CLRY",redConfirmHandle.getHzqrdbh());
        map.put("QRLX",redConfirmHandle.getCllx());
        String url = invoiceConfig.getRedConfirmHandle();
        String res = HttpUtils.doPostToDx(url, JsonUtils.getInstance().toJsonString(map));
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            JSONObject jsonObject = JsonUtils.getInstance().parseObject(Base64Util.decode(dxPlatFormResponse.getContent()), JSONObject.class);
            final JSONObject clxx = jsonObject.getJSONObject("CLXX");
            JSONObject resp = new JSONObject();
            resp.put("code","Y");
            return R.ok().put("data",resp.toJSONString());
        }
        return R.error("红字确认单处理接口异常");
    }

    private void convertToDXRedConfirmListReq(DXRedConfirmListReq dxRedConfirmListReq, RedConfirmListReq redConfirmListReq) {
        BeanUtil.copyProperties(redConfirmListReq,dxRedConfirmListReq);
        dxRedConfirmListReq.setKPZT("N");
        dxRedConfirmListReq.setCXYM(redConfirmListReq.getCurrent());
        dxRedConfirmListReq.setTS(redConfirmListReq.getSize());
    }
    private void convertToCommonQueryHzqrxxRes(DXRedConfirmListResult hzqrxxlb, CommonQueryHzqrxxRes commonQueryHzqrxxRes) {
        commonQueryHzqrxxRes.setCurrent(hzqrxxlb.getCXYM());
        commonQueryHzqrxxRes.setRecords(hzqrxxlb.getHZQRD());
        int i = Integer.valueOf(hzqrxxlb.getZTS()) % Integer.valueOf(hzqrxxlb.getMYTS());
        int page = Integer.valueOf(hzqrxxlb.getZTS()) / Integer.valueOf(hzqrxxlb.getMYTS());
        commonQueryHzqrxxRes.setPages(i==0?String.valueOf(page):String.valueOf(page+1));
    }
}
