package com.dxhy.invoice.service.impl.mycst;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.TaxBureauService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.MycstUtil;
import com.dxhy.order.modules.entity.ApiLoginReqBO;
import com.dxhy.order.pojo.MycstCommonRequest;
import com.dxhy.order.pojo.MycstCommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
/**
 * @Auther: admin
 * @Date: 2022/9/6 09:58
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.SHRPA)
public class TaxBureauServiceImpl implements TaxBureauService {

    @Resource
    private InvoiceConfig invoiceConfig;
    @Resource
    private MycstUtil mycstUtil;


    @Override
    public R login(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo) {


        //1、获取token
        String token = mycstUtil.getToken();

        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        map.put("isshare","1");
        map.put("dzswjyh",apiLoginReqBO.getDzswjyh());
        map.put("dzswjmm",apiLoginReqBO.getDzswjmm());

        //5、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        mycstCommonRequest.setDzswjyh(apiLoginReqBO.getDzswjyh());
        mycstCommonRequest.setDzswjmm(apiLoginReqBO.getDzswjmm());
        //6、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getLoginUrl(), param);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-电子税局登录请求完成，耗时：{}，返回参数：{}", endTime - startTime, result);


//        String result = "{\"Result\":\"1\",\"Message\":\"正常登陆\",\"DATA\":\"MTM4KioqKjQwMjU=\",\"dlfs\":\"\",\"ISSMS\":\"1\",\"PHONE\":\"138****4025\",\"Userinfo\":\"\",\"UserLimitInfo\":\"\",\"Tokeninfo\":\"\",\"Code\":\"010\"}";

        MycstCommonResponse response = JSONObject.parseObject(result, MycstCommonResponse.class);

        if(response != null){
            if (StringUtils.equals(response.getResult(),"1")) {
                JSONObject jsonObj = new JSONObject();
                jsonObj.put("isSms",response.getISSMS());
                return R.ok().put("data", jsonObj);


            } else {
                log.error("税航票帮手电子税局登录接口返回失败");
                return R.error("9999", response.getMessage());
            }
        }else {
            log.error("税航票帮手电子税局登录接口");
            return R.error("9999", response.getMessage());
        }
    }

    @Override
    public R setSms(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo) {
        //1、获取token
        String token = mycstUtil.getToken();



        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        map.put("yzm",apiLoginReqBO.getYzm());
        map.put("spid",taxpayerInfo.getSpid());



        //5、封装完整请求体
        MycstCommonRequest mycstCommonRequest = new MycstCommonRequest();
        mycstCommonRequest.setToken(token);
        mycstCommonRequest.setSpid(taxpayerInfo.getSpid());
        mycstCommonRequest.setYzm(apiLoginReqBO.getYzm());
        //6、转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstCommonRequest);



        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getSetSmsUrl(), param);
        long endTime = System.currentTimeMillis();

//        String  result = "{\"Result\":\"1\",\"Message\":\"正常登陆\",\"DATA\":\"登陆成功\"}";

        log.info("税航票帮手-登录设置验证码请求完成，耗时：{}，返回参数：{}", endTime - startTime, result);
        MycstCommonResponse response = JSONObject.parseObject(result, MycstCommonResponse.class);
        if(response != null){
            if (StringUtils.equals(response.getResult(),"1")) {
                return R.ok("0000",response.getMessage());

            } else {
                log.error("税航票帮手-登录设置验证码接口返回失败");
                return R.error("9999", response.getMessage());
            }
        }else {
            log.error("税航票帮手返回数据为空");
            return R.error("9999", response.getMessage());
        }
    }

    @Override
    public R getConfirmQrcode(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo) {
        //1、获取token
        String token = mycstUtil.getToken();



        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        map.put("spid",taxpayerInfo.getSpid());



        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getConfirmQrcodeUrl(), map);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-获取扫脸二维码请求完成，耗时：{}，返回参数：{}", endTime - startTime, result);

//        String result="{\"Result\":\"1\",\"Message\":\"正常\",\"Qrcode\":\"qrcode_id=RfV22QsNs9PBRSXDrW+LUSu0jgDWL6ks5VW8OQJugK8FAdAvmDp7i7Yobk7zzkNM\\u0026areaPrefix=2102\\u0026interfaceCode=0004\"}";


        MycstCommonResponse response = JSONObject.parseObject(result, MycstCommonResponse.class);
        if(response != null){
            if (StringUtils.equals(response.getResult(),"1")) {
                JSONObject jsonObj = new JSONObject();
                jsonObj.put("qrCode",response.getQrcode());
                return R.ok().put("data", jsonObj);

            } else {
                log.error("税航票帮手-获取扫脸二维码接口返回失败");
                return R.error("9999", response.getMessage());
            }
        }else {
            log.error("税航票帮手获取扫脸二维码返回数据为空");
            return R.error("9999", response.getMessage());
        }
    }

    @Override
    public R getConfirmStatus(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo) {
        //1、获取token
        String token = mycstUtil.getToken();



        Map<String,Object> map = new HashMap<>();
        map.put("token",token);
        map.put("spid",taxpayerInfo.getSpid());



        long startTime = System.currentTimeMillis();
        String result = HttpUtils.doPost(invoiceConfig.getConfirmStatusUrl(), map);
        long endTime = System.currentTimeMillis();
        log.info("税航票帮手-获取扫脸状态请求完成，耗时：{}，返回参数：{}", endTime - startTime, result);
        MycstCommonResponse response = JSONObject.parseObject(result, MycstCommonResponse.class);
        if(response != null){
            if (StringUtils.equals(response.getResult(),"1")) {
                JSONObject jsonObj = new JSONObject();
                // 对外接口 slzt 1待扫码  2扫码成功   3失效
                // 税航返回   Y-需要扫脸认证     N-不用扫脸认证
                if("Y".equals(response.getSfsl())){
                    jsonObj.put("slzt","1");
                }else if("N".equals(response.getSfsl())){
                    jsonObj.put("slzt","2");
                }else{
                    jsonObj.put("slzt","3");
                }

                return R.ok().put("data", jsonObj);

            } else {
                log.error("税航票帮手-获取扫脸状态接口返回失败");
                return R.error("9999", response.getMessage());
            }
        }else {
            log.error("税航票帮手获取扫脸状态返回数据为空");
            return R.error("9999", response.getMessage());
        }
    }
}
