package com.dxhy.invoice.service.impl.dxrpa;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.constant.MycstJtgjlxEnum;
import com.dxhy.invoice.dao.SldhFpqqlshInfoMapper;
import com.dxhy.invoice.dao.TaxpayerInfoMapper;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.entity.*;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.BlueInvoiceService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.model.dxOpenPlatform.DxPlatFormResponse;
import com.dxhy.order.model.dxOpenPlatform.DxQdResponse;
import com.dxhy.order.model.dxOpenPlatform.FPKJJG;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.utils.Base64Encoding;
import com.obs.services.ObsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.DXRPA)
public class DxRpaBlueInvoiceServiceImpl implements BlueInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxpayerInfoMapper taxpayerInfoMapper;
    @Resource
    private SldhFpqqlshInfoMapper sldhFpqqlshInfoMapper;


    @Override
    public R kp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) throws Exception {
        String requestId = orderInvoiceInfoEntity.getFpqqlsh();

        SldhFpqqlshInfo sldhFpqqlshInfo1 = sldhFpqqlshInfoMapper.getOneByFpqqlsh(requestId);
        if (ObjectUtils.isNotEmpty(sldhFpqqlshInfo1)) {
            boolean b = sldhFpqqlshInfoMapper.updateByFpqqlsh(requestId) > 0;
            if (!b) {
                log.info("DXRPA-发票开具-已存在数据且更新状态失败，requestId:{}", requestId);
                return R.error("9999","流水号已存在,状态更新失败");
            }
            log.info("DXRPA-发票开具-已存在数据且更新状态成功，requestId:{}", requestId);
        }
        InvoiceIssueRequest invoiceIssueRequest =new InvoiceIssueRequest();
        invoiceIssueRequest.setNSRSBH(taxpayerInfo.getNsrsbh());
        InvoiceIssueInfo invoiceIssueInfo = new InvoiceIssueInfo();
        convertToInvoiceIssueRequest(orderInvoiceInfoEntity,invoiceIssueInfo);
        invoiceIssueRequest.setINVOICE(invoiceIssueInfo);
        //默认普通蓝字发票
        String url = invoiceConfig.getLzfpKpUrl();
        if(StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(),orderInvoiceInfoEntity.getTdyw())){
            url = invoiceConfig.getJzfwKpUrl();
        } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getHwysKpUrl();
        } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getBdcxsKpUrl();
        } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getBdczlKpUrl();
        } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getLkysKpUrl();
        } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_12.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getZcncpxsKpUrl();
        } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_16.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getNcpsgKpUrl();
        }else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getEscKpUrl();
        }else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(),orderInvoiceInfoEntity.getTdyw())) {
            url = invoiceConfig.getJdcKpUrl();
        }
        String res = HttpUtils.doPostToDx(url,JsonUtils.getInstance().toJsonString(invoiceIssueRequest));
        // returnstateinfo里面的returnCode=0000 表示成功
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {

            DxQdResponse dxQdResponse = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(dxPlatFormResponse.getContent()), DxQdResponse.class);
            if("000000".equals(dxQdResponse.getZtdm()) && ObjectUtils.isNotEmpty(dxQdResponse.getFPKJJG())){
                FPKJJG fpkjjg = dxQdResponse.getFPKJJG();

                log.info("DXRPA-发票开具，入库请求的开票数据开始，requestId:{}", requestId);
                SldhFpqqlshInfo sldhFpqqlshInfo = new SldhFpqqlshInfo();
                sldhFpqqlshInfo.setSldh("");
                sldhFpqqlshInfo.setFpqqlsh(requestId);
                sldhFpqqlshInfo.setQdfphm(fpkjjg.getFphm());
                sldhFpqqlshInfo.setKprq(fpkjjg.getKprq());
                sldhFpqqlshInfo.setFpdm(fpkjjg.getZZFP_DM());
                sldhFpqqlshInfo.setFphm(fpkjjg.getZZFP_HM());
                sldhFpqqlshInfo.setNsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                sldhFpqqlshInfo.setIs_delete("0");
                boolean b = sldhFpqqlshInfoMapper.insert(sldhFpqqlshInfo) > 0;

                log.info("DXRPA-发票开具-入库请求的开票数据，requestId:{},入库结果:{}", requestId,b);
                return R.ok();
            }else{
                return R.error(dxQdResponse.getZtdm(),dxQdResponse.getZtxx());
            }
        }
        return R.error(dxPlatFormResponse.getReturnStateInfo().getReturnCode(),dxPlatFormResponse.getReturnStateInfo().getReturnMessage());
    }

    @Override
    public R queryInvoiceInfo(JSONObject jsonObject, TaxpayerInfo taxpayerInfo) {
        String fpqqlsh = jsonObject.getString("DDQQLSH");
        SldhFpqqlshInfo sldhFpqqlshInfo = sldhFpqqlshInfoMapper.getOneByFpqqlsh(fpqqlsh);

        JSONObject data = new JSONObject();
        data.put("QDFPHM",sldhFpqqlshInfo.getQdfphm());
        data.put("KPRQ",sldhFpqqlshInfo.getKprq());
        data.put("OFDZJL","");
        if(StringUtils.isNotBlank(sldhFpqqlshInfo.getFpdm())){
            data.put("FPDM",sldhFpqqlshInfo.getFpdm());
            data.put("FPHM",sldhFpqqlshInfo.getFphm());
        }

        String url = invoiceConfig.getFileDownload();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("NSRSBH",taxpayerInfo.getNsrsbh());
        jsonObject1.put("FPHM",sldhFpqqlshInfo.getQdfphm());
        jsonObject1.put("KPRQ", DateUtil.parse(sldhFpqqlshInfo.getKprq()).toString("yyyy-MM-dd HH:mm:ss"));

        String res = HttpUtils.doPostToDx(url,jsonObject1.toJSONString());
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {

            DxQdResponse dxQdResponse = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(dxPlatFormResponse.getContent()), DxQdResponse.class);

            String ep = "obs.cn-north-4.myhuaweicloud.com";
            String ak = "G8S6VLXLK5HSQX2EWUNI";
            String sk = "SAR4z9SGW9gxJOyn6xLljlHGzyfwWMaHLgtRLb0B";
            String bn = "qst-pdf";
            ObsClient obsClient = new ObsClient(ak, sk, ep);
            // 上传对象 保存到华为云OBS
            obsClient.putObject(bn, sldhFpqqlshInfo.getQdfphm()+".pdf", new ByteArrayInputStream(Base64.decodeBase64(dxQdResponse.getBSWJ().getWJL())));
            data.put("PDFZJL",invoiceConfig.getDownloadUrl()+sldhFpqqlshInfo.getQdfphm()+".pdf");

        }
        return R.ok("0000","发票开具请求成功").put("data",data);
    }


    public static void main(String[] args) {
        System.out.println("-----------------------------------------------------------------");
        System.out.println(DateUtil.parse("2023-10-24 22:39:24").toString("yyyy-MM-dd HH:mm:ss"));
        String ep = "obs.cn-north-4.myhuaweicloud.com";
        String ak = "G8S6VLXLK5HSQX2EWUNI";
        String sk = "SAR4z9SGW9gxJOyn6xLljlHGzyfwWMaHLgtRLb0B";
        String bn = "qst-pdf";
        ObsClient obsClient = new ObsClient(ak, sk, ep);

        // 上传对象
        obsClient.putObject(bn, "testObject.txt", new ByteArrayInputStream("Hello OBS".getBytes()));
    }




    private void convertToInvoiceIssueRequest(OrderInvoiceInfoEntity orderInvoiceInfoEntity, InvoiceIssueInfo invoiceIssueInfo) {
        invoiceIssueInfo.setFPQQLSH(orderInvoiceInfoEntity.getFpqqlsh());
        invoiceIssueInfo.setBMB_BBH("36.0");
        invoiceIssueInfo.setXSF_NSRSBH(orderInvoiceInfoEntity.getXhfNsrsbh());
        invoiceIssueInfo.setXSF_MC(orderInvoiceInfoEntity.getXhfMc());
        invoiceIssueInfo.setXSF_DZ(orderInvoiceInfoEntity.getXhfDz());
        invoiceIssueInfo.setXSF_DH(orderInvoiceInfoEntity.getXhfDh());
        invoiceIssueInfo.setXSF_YH(orderInvoiceInfoEntity.getXhfYh());
        invoiceIssueInfo.setXSF_YHZH(orderInvoiceInfoEntity.getXhfZh());
        invoiceIssueInfo.setGMF_NSRSBH(orderInvoiceInfoEntity.getGhfNsrsbh());
        invoiceIssueInfo.setGMF_MC(orderInvoiceInfoEntity.getGhfMc());
        invoiceIssueInfo.setGMF_DZ(orderInvoiceInfoEntity.getGhfDz());
        invoiceIssueInfo.setGMF_DH(orderInvoiceInfoEntity.getGhfDh());
        invoiceIssueInfo.setGMF_YH(orderInvoiceInfoEntity.getGhfYh());
        invoiceIssueInfo.setGMF_YHZH(orderInvoiceInfoEntity.getGhfZh());
        //  订单请求 002-数电普票     001-数电专票
        if ("002".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            invoiceIssueInfo.setFPZLDM("02");
        } else if ("001".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            invoiceIssueInfo.setFPZLDM("01");
        }else{
            invoiceIssueInfo.setFPZLDM(orderInvoiceInfoEntity.getFpzlDm());
        }
        invoiceIssueInfo.setKPLX("0");
        invoiceIssueInfo.setHJJE(orderInvoiceInfoEntity.getHjbhsje());
        invoiceIssueInfo.setJSHJ(orderInvoiceInfoEntity.getJshj());
        invoiceIssueInfo.setHSBZ("0");
        invoiceIssueInfo.setHJSE(orderInvoiceInfoEntity.getKpse());
        List<InvoiceInfoDetail> INVOICE_DETAIL = new ArrayList<>();
        invoiceIssueInfo.setINVOICE_DETAIL(INVOICE_DETAIL);
        final List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
        for (int i = 0; i < itemEntityList.size(); i++) {
            final OrderInvoiceItemEntity orderInvoiceItemEntity = itemEntityList.get(i);
            InvoiceInfoDetail invoiceInfoDetail = new InvoiceInfoDetail();
            invoiceInfoDetail.setXMXH(Integer.valueOf(orderInvoiceItemEntity.getXh()));
//            invoiceInfoDetail.setXMXH(i+1);
//            invoiceInfoDetail.setMXXH(i+1);
            if("0".equals(orderInvoiceItemEntity.getFphxz())){
                invoiceInfoDetail.setFPHXZ("00");
            }else if("1".equals(orderInvoiceItemEntity.getFphxz())){
                invoiceInfoDetail.setFPHXZ("01");
            }else if("2".equals(orderInvoiceItemEntity.getFphxz())){
                invoiceInfoDetail.setFPHXZ("02");
            }
            invoiceInfoDetail.setXMMC(orderInvoiceItemEntity.getXmmc());
            invoiceInfoDetail.setGGXH(orderInvoiceItemEntity.getGgxh());
            invoiceInfoDetail.setDW(orderInvoiceItemEntity.getDw());
            invoiceInfoDetail.setXMSL(orderInvoiceItemEntity.getXmsl());
            invoiceInfoDetail.setXMDJ(orderInvoiceItemEntity.getDj());
            invoiceInfoDetail.setXMJE(orderInvoiceItemEntity.getJe());
            invoiceInfoDetail.setSE(orderInvoiceItemEntity.getSe());
            //没有含税金额  后续调试在修改参数
            BigDecimal hsje = new BigDecimal(orderInvoiceItemEntity.getJe()).add(new BigDecimal(orderInvoiceItemEntity.getSe()));
            invoiceInfoDetail.setHSJE(hsje.toPlainString());
            invoiceInfoDetail.setSPBM(orderInvoiceItemEntity.getSpbm());
            invoiceInfoDetail.setYHZCBS(orderInvoiceItemEntity.getYhzcbs());
            invoiceInfoDetail.setSL(orderInvoiceItemEntity.getSl());
            //发票明细
            INVOICE_DETAIL.add(invoiceInfoDetail);
        }
        final List<InvoiceAdditionInfoEntity> infoEntityList = orderInvoiceInfoEntity.getInfoEntityList();
        List<InvoiceQdFjys> FJYSLIST = infoEntityList.stream().map(this::convert).collect(Collectors.toList());
        //附加要素信息
        invoiceIssueInfo.setFJYSLIST(FJYSLIST);
        //获取特定业务信息
        InvoiceTdywEntity invoiceTdywEntity = orderInvoiceInfoEntity.getInvoiceTdywEntity();
        if(ObjectUtils.isNotEmpty(invoiceTdywEntity)){
            String tdys = invoiceTdywEntity.getTdys();
            if(StringUtils.isNotBlank(tdys)){
                if(StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(),tdys)){
                    //建筑服务
                    InvoiceJzfwxxEntity jzfwxxEntity = invoiceTdywEntity.getJzfwxx();
                    if(ObjectUtils.isNotEmpty(jzfwxxEntity)){
                        InvoiceConstructInfo constructInfo = new InvoiceConstructInfo();
                        constructInfo.setTDZZSXMBH(jzfwxxEntity.getTdzzsxmbh());
                        constructInfo.setJZFWFSD(jzfwxxEntity.getJzfwfsd());
                        constructInfo.setXXDZ(jzfwxxEntity.getJzfwxxdz());
                        constructInfo.setJZXMMC(jzfwxxEntity.getJzfwmc());
                        constructInfo.setKDSBZ(ConfigureConstant.STRING_0);
                        if(StringUtils.isNotBlank(jzfwxxEntity.getKdsbz()) && StringUtils.equals(jzfwxxEntity.getKdsbz(), ConfigureConstant.STRING_Y)){
                            constructInfo.setKDSBZ(ConfigureConstant.STRING_1);
                        }
                        invoiceIssueInfo.setCONSTRUCT_INFO(constructInfo);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(),tdys)) {
                    //货物运输
                    List<InvoiceHwysxxEntity> hwysxxEntityList = invoiceTdywEntity.getHwysxx();
                    if(CollectionUtils.isNotEmpty(hwysxxEntityList)){
                        List<InvoiceTransportDetail> transportDetailList = new ArrayList<>();
                        for(InvoiceHwysxxEntity hwysxxEntity : hwysxxEntityList){
                            InvoiceTransportDetail transportDetail = new InvoiceTransportDetail();
                            transportDetail.setQYD(hwysxxEntity.getQyd());
                            transportDetail.setDDD(hwysxxEntity.getDdd());
                            transportDetail.setYSGJZL(hwysxxEntity.getYsgjzl());
                            transportDetail.setYSHWMC(hwysxxEntity.getYshwmc());
                            transportDetail.setYSGJPH(hwysxxEntity.getYsgjph());
                            transportDetailList.add(transportDetail);
                        }
                        invoiceIssueInfo.setTRANSPORT_DETAIL(transportDetailList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(),tdys)) {
                    //不动产销售
                    List<InvoiceBdcxsxxEntity> bdcxsxxEntityList = invoiceTdywEntity.getBdcxsxx();
                    if(CollectionUtils.isNotEmpty(bdcxsxxEntityList)){
                        for (InvoiceBdcxsxxEntity bdcxsxxEntity : bdcxsxxEntityList){
                            InvoiceSaleInfo  invoiceSaleInfo = new InvoiceSaleInfo();
                            invoiceSaleInfo.setSJCJHSJE(bdcxsxxEntity.getSjcjhsje());
                            invoiceSaleInfo.setBDCDZ(bdcxsxxEntity.getBdcdz());
                            invoiceSaleInfo.setHDJSJG(bdcxsxxEntity.getHdjsjg());
                            invoiceSaleInfo.setBDCWQHTBH(bdcxsxxEntity.getBdcwqhtbh());
                            invoiceSaleInfo.setCQZSH(bdcxsxxEntity.getCqzsh());
                            invoiceSaleInfo.setKDSBZ(ConfigureConstant.STRING_0);
                            if(StringUtils.isNotBlank(bdcxsxxEntity.getKdsbz()) && StringUtils.equals(bdcxsxxEntity.getKdsbz(), ConfigureConstant.STRING_Y)){
                                invoiceSaleInfo.setKDSBZ(ConfigureConstant.STRING_1);
                            }
                            invoiceSaleInfo.setMJDW(bdcxsxxEntity.getMjdw());
                            invoiceSaleInfo.setTDZZSXMBH(bdcxsxxEntity.getTdzzsxmbh());
                            invoiceIssueInfo.setSALE_INFO(invoiceSaleInfo);
                        }
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(),tdys)) {
                    //不动产租赁
                    List<InvoiceBdczlxxEntity> bdczlxxEntityList = invoiceTdywEntity.getBdczlxx();
                    if(CollectionUtils.isNotEmpty(bdczlxxEntityList)){
                        for (InvoiceBdczlxxEntity bdczlxxEntity : bdczlxxEntityList){
                            InvoiceLeaseInfo leaseInfo = new InvoiceLeaseInfo();
                            //租赁地区和详细地址
                            leaseInfo.setBDCDZ(bdczlxxEntity.getBdcdz());
                            //租赁起止时间
                            leaseInfo.setZLQQZ(bdczlxxEntity.getZlqqz());
                            leaseInfo.setKDSBZ(ConfigureConstant.STRING_0);
                            if(StringUtils.isNotBlank(bdczlxxEntity.getKdsbz()) && StringUtils.equals(bdczlxxEntity.getKdsbz(), ConfigureConstant.STRING_Y)){
                                leaseInfo.setKDSBZ(ConfigureConstant.STRING_1);
                            }
                            leaseInfo.setCQZSH(bdczlxxEntity.getCqzsh());
                            leaseInfo.setMJDW(bdczlxxEntity.getMjdw());
                            invoiceIssueInfo.setLEASE_INFO(leaseInfo);
                        }
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(),tdys)) {
                    //旅客运输
                    List<InvoiceLkysfwxxEntity> lkysfwxxEntityList = invoiceTdywEntity.getLkysfwxx();
                    if(CollectionUtils.isNotEmpty(lkysfwxxEntityList)) {
                        List<InvoiceTravelersDetail> travelersDetailList = new ArrayList<>();
                        for (InvoiceLkysfwxxEntity lkysfwxxEntity : lkysfwxxEntityList) {
                            InvoiceTravelersDetail travelersDetail = new InvoiceTravelersDetail();
                            travelersDetail.setCXR(lkysfwxxEntity.getCxr());
                            travelersDetail.setCFD(lkysfwxxEntity.getLkyscfd());
                            travelersDetail.setDDD(lkysfwxxEntity.getLkysddd());
                            travelersDetail.setZWDJ(lkysfwxxEntity.getZwdj());
                            travelersDetail.setCXRQ(lkysfwxxEntity.getCxrq());
                            travelersDetail.setSFZJHM(lkysfwxxEntity.getCxrzjhm());
                            travelersDetail.setCXRZJLXDM(lkysfwxxEntity.getCxrzjlx());
                            if(StringUtils.isNotBlank(lkysfwxxEntity.getJtgjlx())){
                                //交通工具类型转换
                                MycstJtgjlxEnum mycstJtgjlxEnum = MycstJtgjlxEnum.getCodeValue(lkysfwxxEntity.getJtgjlx());
                                travelersDetail.setJTGJLXDM(ObjectUtils.isNotEmpty(mycstJtgjlxEnum) ? mycstJtgjlxEnum.getMycstJtgjlx() : "");
                            }
                            travelersDetailList.add(travelersDetail);
                        }
                        invoiceIssueInfo.setTRAVELERS_DETAIL(travelersDetailList);
                    }
                }else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(),tdys)) {
                    // 二手车
                    InvoiceEscxxEntity escxxEntity = invoiceTdywEntity.getEscxx();
                    if (ObjectUtils.isNotEmpty(escxxEntity)) {
                        InvoiceOldCarSellDetail escDetail = new InvoiceOldCarSellDetail();
                        escDetail.setCPZH(escxxEntity.getCpzh());
                        escDetail.setDJZH(escxxEntity.getDjzh());
                        escDetail.setCLLX_DM(escxxEntity.getCllx());
                        escDetail.setCLSBDH(escxxEntity.getClsbdm());
                        escDetail.setCPXH(escxxEntity.getCpxh());
                        escDetail.setZRDCLGLSMC(escxxEntity.getZrdclglsmc());
                        escDetail.setJYDWNSRMC(escxxEntity.getJypmdwMc());
                        escDetail.setJYDWNSRSBH(escxxEntity.getJypmdwSh());
                        escDetail.setJYDWDZ(escxxEntity.getJypmdwDz());
                        escDetail.setJYDWKHYH(escxxEntity.getJypmdwKhyh());
                        escDetail.setJYDWYHZH(escxxEntity.getJypmdwYhzh());
                        escDetail.setJYDWDH(escxxEntity.getJypmdwDh());
                        escDetail.setPMDWNSRMC(escxxEntity.getJypmdwMc());
                        escDetail.setPMDWNSRSBH(escxxEntity.getJypmdwSh());
                        escDetail.setPMDWDZ(escxxEntity.getJypmdwDz());
                        escDetail.setPMDWKHYH(escxxEntity.getJypmdwKhyh());
                        escDetail.setPMDWYHZH(escxxEntity.getJypmdwYhzh());
                        escDetail.setPMDWDH(escxxEntity.getJypmdwDh());
                        escDetail.setESCSCNSRMC(escxxEntity.getScMc());
                        escDetail.setESCSCNSRSBH(escxxEntity.getScSh());
                        escDetail.setESCSCDZ(escxxEntity.getScDz());
                        escDetail.setESCSCKHYH(escxxEntity.getScKhyh());
                        escDetail.setESCSCYHZH(escxxEntity.getScYhzh());
                        escDetail.setESCSCDH(escxxEntity.getScDh());
                        invoiceIssueInfo.setOLDCARSELL_DETAIL(Collections.singletonList(escDetail));
                    }
                }else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(),tdys)) {
                    //机动车
                    InvoiceJdcxxEntity jdcxxEntity = invoiceTdywEntity.getJdcxx();
                    if(ObjectUtils.isNotEmpty(jdcxxEntity)){
                        InvoiceVehicleSellDetail jdcDetail = new InvoiceVehicleSellDetail();
                        jdcDetail.setCD(jdcxxEntity.getClcd());
                        jdcDetail.setCLDW(jdcxxEntity.getDw());
                        jdcDetail.setCLLX_DM(jdcxxEntity.getCllx());
                        jdcDetail.setCLSBDH(jdcxxEntity.getClsbdh());
                        jdcDetail.setCPXH(jdcxxEntity.getCpxh());
                        jdcDetail.setFDJHM(jdcxxEntity.getFdjhm());
                        jdcDetail.setHGZH(jdcxxEntity.getHgzh());
                        jdcDetail.setJDCTZCLSBDHUUID(jdcxxEntity.getClsbdhuuid());
                        jdcDetail.setJKZMSH(jdcxxEntity.getJkzmsh());
                        jdcDetail.setSCQYMC(jdcxxEntity.getScqymc());
                        jdcDetail.setSJDH(jdcxxEntity.getSjdh());
                        jdcDetail.setWSPZHM(jdcxxEntity.getWspzhm());
                        jdcDetail.setXCRS(jdcxxEntity.getXcrs());
                        invoiceIssueInfo.setVEHICLESELL_DETAIL(Collections.singletonList(jdcDetail));
                    }
                }else {
                    log.error("不支持的特定业务类型，{}",tdys);
                    throw new RuntimeException("组织报文异常，不支持的特定业务类型："+tdys);
                }
            }
        }
    }

    private InvoiceQdFjys convert(InvoiceAdditionInfoEntity invoiceAdditionInfoEntity) {
        InvoiceQdFjys invoiceQdFjys = new InvoiceQdFjys();
        invoiceQdFjys.setFJYSLX(invoiceAdditionInfoEntity.getSjnr());
        invoiceQdFjys.setFJYSMC(invoiceAdditionInfoEntity.getFjxxmc());
        invoiceQdFjys.setFJYSZ(invoiceAdditionInfoEntity.getFjxxz());
        return invoiceQdFjys;
    }


}
