package com.dxhy.invoice.service.impl.rpa;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.dao.TaxpayerInfoMapper;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.BlueInvoiceService;
import com.dxhy.invoice.util.DistributedKeyMaker;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.modules.entity.InvoiceAdditionInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.pojo.HuiqiBlueInvoiceReq;
import com.dxhy.order.pojo.HuiqiCommonRes;
import com.dxhy.order.pojo.InvoiceItem;
import com.dxhy.order.pojo.XmmxZk;
import com.dxhy.order.pojo.single.Fjys;
import com.dxhy.order.pojo.single.Fjysxx;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.RPA)
public class RpaBlueInvoiceServiceImpl implements BlueInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxpayerInfoMapper taxpayerInfoMapper;

    @Override
    public R kp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) {

        return interfaceKp(orderInvoiceInfoEntity, taxpayerInfo);

    }

    @Override
    public R queryInvoiceInfo(JSONObject jsonObject, TaxpayerInfo taxpayerInfo) {

        return interfaceGetInvoiceInfo(jsonObject.getString("DDQQLSH"),taxpayerInfo);

    }

    private R interfaceGetInvoiceInfo(String ddqqlsh,TaxpayerInfo taxpayerInfo) {


        String sldh = taxpayerInfoMapper.getSldhByFpqqlsh(ddqqlsh);
        log.info("根据发票请求流水号:{},查出sldh为:{}",ddqqlsh,sldh);
        String selectBlueInvoiceInfoUrl = invoiceConfig.getQueryInvoiceInfoUrl();
        String requestId = DistributedKeyMaker.generateShotKeyNew();
        String sessionId = DistributedKeyMaker.generateShotKeyNew();
        long timestamp = Instant.now().toEpochMilli();
        String reqUrl = selectBlueInvoiceInfoUrl+"?jrzh="+taxpayerInfo.getNsrsbh();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sldh",sldh);
        String res = HttpUtils.doPost(reqUrl, jsonObject.toJSONString());



        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(res, HuiqiCommonRes.class);

        if ("200".equals(huiqiCommonRes.getCode())){
            //在判断result中的clzt 是否是成功

            JSONObject parse = JSONObject.parseObject(huiqiCommonRes.getResult());

            //任务处理成功
            String qdfphm = parse.getString("qdfphm");
            String fpxzUrl = parse.getString("fpxzUrl");
            String pdfxzUrl = parse.getString("pdfxzUrl");
            String kprq = parse.getString("kprq");
            JSONObject data = new JSONObject();
            data.put("QDFPHM",qdfphm);
            data.put("KPRQ",kprq);
            data.put("OFDZJL",fpxzUrl);
            data.put("PDFZJL",pdfxzUrl);
            return R.ok("0000","发票开具请求成功").put("data",data);


        }else if("300".equals(huiqiCommonRes.getCode())){
            //任务处理中 code 300111
            return R.ok("300111","发票开具中,请稍后查询...");
        }else{
            String code= huiqiCommonRes.getCode();
            String msg=  huiqiCommonRes.getMessage();
            return R.ok(code,msg);
        }

    }



    private R interfaceKp(OrderInvoiceInfoEntity orderInvoiceInfoEntity,TaxpayerInfo taxpayerInfo) {
        HuiqiBlueInvoiceReq huiqiBlueInvoiceReq = convert2BlueInvoiceInfo(orderInvoiceInfoEntity);
        log.info("发票请求流水号:{},转换为接口开票实体信息:{}",orderInvoiceInfoEntity.getFpqqlsh(),JSONObject.toJSONString(huiqiBlueInvoiceReq));
        String blueInvoiceIssueUrl = invoiceConfig.getRpaInvoiceIssueUrl()+"?jrzh="+taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(blueInvoiceIssueUrl, JsonUtils.getInstance().toJsonString(huiqiBlueInvoiceReq));
        JSONObject parse = JSONObject.parseObject(res);
        Boolean success = parse.getBoolean("success");
        if (success){
            String sldh = parse.getJSONObject("result").getString("sldh");
            log.info("发票请求流水号:{},开票请求成功返回的sldh:{}",orderInvoiceInfoEntity.getFpqqlsh(),sldh);
            //保存受理单号与发票请求流水号关联关系
            //重试时  删除原有数据
            taxpayerInfoMapper.updateStatusByFpqqlsh(orderInvoiceInfoEntity.getFpqqlsh());
            boolean b =  taxpayerInfoMapper.saveSldhAndFpqqlsh(sldh,orderInvoiceInfoEntity.getFpqqlsh())>0;
            log.info("发票请求流水号:{},sldh:{},保存结果:{}",orderInvoiceInfoEntity.getFpqqlsh(),sldh,b);
            return R.ok("0000","发票开具请求成功");
        }else{
            String code= parse.getString("code");
            String msg= parse.getString("message");
            return R.ok(code,msg);
        }



    }





    private HuiqiBlueInvoiceReq convert2BlueInvoiceInfo(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        HuiqiBlueInvoiceReq huiqiBlueInvoiceReq = new HuiqiBlueInvoiceReq();
        if ("0".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            huiqiBlueInvoiceReq.setFppzDm("02");
        } else if ("1".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            huiqiBlueInvoiceReq.setFppzDm("01");
        }
        huiqiBlueInvoiceReq.setGmfDz(StringUtils.isBlank(orderInvoiceInfoEntity.getGhfDz())?"":orderInvoiceInfoEntity.getGhfDz());
        huiqiBlueInvoiceReq.setGmfKhh(StringUtils.isBlank(orderInvoiceInfoEntity.getGhfYh())?"":orderInvoiceInfoEntity.getGhfYh());
        huiqiBlueInvoiceReq.setGmfLxdh(StringUtils.isBlank(orderInvoiceInfoEntity.getGhfDh())?"":orderInvoiceInfoEntity.getGhfDh());
        huiqiBlueInvoiceReq.setGmfMc(StringUtils.isBlank(orderInvoiceInfoEntity.getGhfMc())?"":orderInvoiceInfoEntity.getGhfMc());
        huiqiBlueInvoiceReq.setGmfNsrsbh(StringUtils.isBlank(orderInvoiceInfoEntity.getGhfNsrsbh())?"":orderInvoiceInfoEntity.getGhfNsrsbh());
        huiqiBlueInvoiceReq.setGmfYhzh(StringUtils.isBlank(orderInvoiceInfoEntity.getGhfZh())?"":orderInvoiceInfoEntity.getGhfZh());
        huiqiBlueInvoiceReq.setBz(orderInvoiceInfoEntity.getBz());
        //订单请求  含税标志 0 不含税 1含税   慧企 rpa 接口  含税标志(Y-是；N-否)
        if("0".equals(orderInvoiceInfoEntity.getHsbz())){
            huiqiBlueInvoiceReq.setHsbz("N");
        }else{
            huiqiBlueInvoiceReq.setHsbz("Y");
        }
        // 价税合计 合计金额 合计税额
        huiqiBlueInvoiceReq.setJshj(orderInvoiceInfoEntity.getJshj());
        huiqiBlueInvoiceReq.setHjje(orderInvoiceInfoEntity.getHjbhsje());
        huiqiBlueInvoiceReq.setHjse(orderInvoiceInfoEntity.getKpse());

        huiqiBlueInvoiceReq.setKce(orderInvoiceInfoEntity.getKce());
        if(StringUtils.isBlank(orderInvoiceInfoEntity.getKce())){
            huiqiBlueInvoiceReq.setKce("0");
        }
        //自然人标识  0否  1是
        if("0".equals(orderInvoiceInfoEntity.getSpfzrrbs())){
            huiqiBlueInvoiceReq.setGmfzrrbs("N");
        }else{
            huiqiBlueInvoiceReq.setGmfzrrbs("Y");
        }
        if(StringUtils.isBlank(orderInvoiceInfoEntity.getGiveUpReason())){
            huiqiBlueInvoiceReq.setKjly("");
        }else{

            huiqiBlueInvoiceReq.setKjly(orderInvoiceInfoEntity.getGiveUpReason() == "2"?"05":"04");
        }

        List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        huiqiBlueInvoiceReq.setMxzbList(invoiceItems);

        for (int i = 0; i < itemEntityList.size(); i++) {
            OrderInvoiceItemEntity x = itemEntityList.get(i);

            InvoiceItem invoiceItem = new InvoiceItem();
            invoiceItem.setXh(i+1);
            invoiceItem.setSphfwssflhbbm(x.getSpbm());
            invoiceItem.setFphxzdm(x.getFphxz());
            invoiceItem.setGgxh(x.getGgxh());
            invoiceItem.setDw(x.getDw());
            //发票行性质 0正常商品行  1折扣行  2被折扣行
            if("1".equals(x.getFphxz())){
                invoiceItem.setZkje(new BigDecimal(x.getJe()).abs().toString());
                //取被折扣行的商品编码
                OrderInvoiceItemEntity orderInvoiceItemEntity = itemEntityList.get(i-1);
                invoiceItem.setSphfwssflhbbm(orderInvoiceItemEntity.getSpbm());
                invoiceItem.setGgxh("");
                invoiceItem.setDw("");
            }
            invoiceItem.setKce(x.getKce());
            if(StringUtils.isBlank(x.getKce())){
                invoiceItem.setKce("0");
            }
            invoiceItem.setHwhyslwfwmc(x.getXmmc());


            if(StringUtils.isBlank(x.getXmsl())){
                invoiceItem.setSpsl("");
            }else{
                invoiceItem.setSpsl(x.getXmsl());
            }
//            if(StringUtils.isBlank(x.getDj())){
//                invoiceItem.setDj("");
//            }else{
//                invoiceItem.setDj(x.getDj());
//            }

            //
            if(!x.getSl().contains("%")&& x.getSl().contains("0.")){

                //订单请求  含税标志 0 不含税 1含税   慧企 rpa 接口  含税标志(Y-是；N-否)
                BigDecimal slv = new BigDecimal(x.getSl()).add(new BigDecimal(1));
                if("0".equals(orderInvoiceInfoEntity.getHsbz())){
                    invoiceItem.setJe(x.getJe());
                    invoiceItem.setBhsje(x.getJe());
                    String hsje = new BigDecimal(x.getJe()).multiply(slv).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                    invoiceItem.setHsje(hsje);
                    if(StringUtils.isNotBlank(x.getDj())){
                        invoiceItem.setBhsdj(x.getDj());
                        invoiceItem.setDj(x.getDj());
                        String hsdj = new BigDecimal(x.getDj()).multiply(slv).setScale(13, BigDecimal.ROUND_HALF_UP).toString();
                        invoiceItem.setHsdj(hsdj);

                    }else{
                        invoiceItem.setHsdj("");
                        invoiceItem.setBhsdj("");
                        invoiceItem.setDj("");
                    }

                }else{
                    invoiceItem.setHsje(x.getJe());
                    String bhsje = new BigDecimal(x.getJe()).divide(slv,2,BigDecimal.ROUND_HALF_UP).toString();
                    invoiceItem.setBhsje(bhsje);
                    invoiceItem.setJe(bhsje);
                    if(StringUtils.isNotBlank(x.getDj())){
                        invoiceItem.setHsdj(x.getDj());
                        String bhsdj = new BigDecimal(x.getDj()).divide(slv,13, BigDecimal.ROUND_HALF_UP).toString();
                        invoiceItem.setBhsdj(bhsdj);
                        invoiceItem.setDj(bhsdj);
                        //重新计算不含税金额
                        String bhsjeNew = new BigDecimal(bhsdj).multiply(new BigDecimal(x.getXmsl())).setScale(2, RoundingMode.HALF_UP).toPlainString();
                        invoiceItem.setBhsje(bhsjeNew);
                        invoiceItem.setJe(bhsjeNew);
                    }else{
                        invoiceItem.setHsdj("");
                        invoiceItem.setBhsdj("");
                        invoiceItem.setDj("");
                    }
                }
                NumberFormat num = NumberFormat.getPercentInstance();
                num.setMaximumIntegerDigits(3);
                num.setMaximumFractionDigits(2);
                invoiceItem.setSlv(num.format(Double.valueOf(x.getSl())));
            }else if(x.getSl().contains("%")&&!"0%".equals(x.getSl())){
                //rpa 免税不征税传0
                invoiceItem.setSlv(x.getSl());
                //订单请求  含税标志 0 不含税 1含税   慧企 rpa 接口  含税标志(Y-是；N-否)
                //税率 转换为小数
                Float  sl = Float.valueOf(x.getSl().replace("%",""))/100;
                BigDecimal slv = new BigDecimal(sl.toString()).add(new BigDecimal(1));
                if("0".equals(orderInvoiceInfoEntity.getHsbz())){
                    invoiceItem.setBhsje(x.getJe());
                    invoiceItem.setJe(x.getJe());
                    String hsje = new BigDecimal(x.getJe()).multiply(slv).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                    invoiceItem.setHsje(hsje);
                    if(StringUtils.isNotBlank(x.getDj())){
                        invoiceItem.setBhsdj(x.getDj());
                        invoiceItem.setDj(x.getDj());
                        String hsdj = new BigDecimal(x.getDj()).multiply(slv).setScale(13, BigDecimal.ROUND_HALF_UP).toString();
                        invoiceItem.setHsdj(hsdj);
                    }else{
                        invoiceItem.setHsdj("");
                        invoiceItem.setBhsdj("");
                        invoiceItem.setDj("");
                    }

                }else{
                    invoiceItem.setHsje(x.getJe());
                    String bhsje = new BigDecimal(x.getJe()).divide(slv,2, BigDecimal.ROUND_HALF_UP).toString();
                    invoiceItem.setBhsje(bhsje);
                    invoiceItem.setJe(bhsje);
                    if(StringUtils.isNotBlank(x.getDj())){
                        invoiceItem.setHsdj(x.getDj());
                        String bhsdj = new BigDecimal(x.getDj()).divide(slv,13, BigDecimal.ROUND_HALF_UP).toString();
                        invoiceItem.setBhsdj(bhsdj);
                        invoiceItem.setDj(bhsdj);
                        //重新计算不含税金额
                        String bhsjeNew = new BigDecimal(bhsdj).multiply(new BigDecimal(x.getXmsl())).setScale(2, RoundingMode.HALF_UP).toPlainString();
                        invoiceItem.setBhsje(bhsjeNew);
                        invoiceItem.setJe(bhsjeNew);
                    }else{
                        invoiceItem.setHsdj("");
                        invoiceItem.setBhsdj("");
                        invoiceItem.setDj("");
                    }
                }

            }else{
                //rpa 免税不征税传0
                invoiceItem.setSlv("0");
                invoiceItem.setHsje(x.getJe());
                invoiceItem.setBhsje(x.getJe());
                invoiceItem.setJe(x.getJe());
                if(StringUtils.isNotBlank(x.getDj())){
                    invoiceItem.setHsdj(x.getDj());
                    invoiceItem.setBhsdj(x.getDj());
                    invoiceItem.setDj(x.getDj());
                }else{
                    invoiceItem.setHsdj("");
                    invoiceItem.setBhsdj("");
                    invoiceItem.setDj("");
                }

            }
            if("免税".equals(x.getSl())){
                invoiceItem.setZzstsgl("03");
            }else if("不征税".equals(x.getSl())){
                invoiceItem.setZzstsgl("04");
            }else{
                invoiceItem.setZzstsgl("");
            }


            invoiceItem.setSe(x.getSe());
            if (StringUtils.isNotBlank(x.getByzd2())) {
                XmmxZk xmmxZk = new XmmxZk();
                xmmxZk.setZkfs(String.format("%2s", x.getByzd2()).replace(" ", "0"));
//                xmmxZk.setZkdx(String.format("%2s", x.getByzd3()).replace(" ", "0"));
                xmmxZk.setZkdx(x.getByzd3());
                invoiceItem.setXmmxZk(xmmxZk);
            }
            invoiceItems.add(invoiceItem);


        }
        Fjysxx fjysxx = new Fjysxx();
        List<Fjys> fjysList = new ArrayList<>();
        fjysxx.setFjysxmzxx(fjysList);
        huiqiBlueInvoiceReq.setFjysxx(fjysxx);
        List<InvoiceAdditionInfoEntity> infoEntityList = orderInvoiceInfoEntity.getInfoEntityList();
        infoEntityList.forEach(x->{
            Fjys fjys = new Fjys();
            fjys.setFjysxmmc(x.getFjxxmc());
            fjys.setFjysz(x.getFjxxz());
            //订单侧   1 文本型 2 数值型 3 日期型   慧企接口侧  number：数字，date：日期，string：字符 串
            if("1".equals(x.getSjnr())){
                fjys.setSjlx1("string");
            }
            if("2".equals(x.getSjnr())){
                fjys.setSjlx1("number");
            }
            if("3".equals(x.getSjnr())){
                fjys.setSjlx1("date");
            }
            fjysList.add(fjys);
        });


        return huiqiBlueInvoiceReq;
    }


    
}
