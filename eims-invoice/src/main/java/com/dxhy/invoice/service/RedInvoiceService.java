package com.dxhy.invoice.service;

import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.pojo.RedConfirmHandle;
import com.dxhy.order.pojo.RedConfirmListReq;
import com.dxhy.order.pojo.RedConfirmReq;
import com.dxhy.order.pojo.RedInvoiceIssueReq;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
public interface RedInvoiceService {

    R redConfirm(RedConfirmReq redConfirmReq ,TaxpayerInfo taxpayerInfo);

    R getRedConfirmResult(RedConfirmReq redConfirmReq ,TaxpayerInfo taxpayerInfo);

    R redInvoiceIssue(RedInvoiceIssueReq redInvoiceIssueReq ,TaxpayerInfo taxpayerInfo);

    R getRedInvoiceInfo(RedConfirmReq redConfirmReq ,TaxpayerInfo taxpayerInfo);

    R redConfirmList(RedConfirmListReq redConfirmListReq ,TaxpayerInfo taxpayerInfo);

    R getRedConfirmInfo(RedConfirmHandle redConfirmHandle ,TaxpayerInfo taxpayerInfo);

    R redConfirmHandle(RedConfirmHandle redConfirmHandle ,TaxpayerInfo taxpayerInfo);
}
