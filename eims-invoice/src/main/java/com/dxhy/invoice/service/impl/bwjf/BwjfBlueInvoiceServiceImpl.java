package com.dxhy.invoice.service.impl.bwjf;

import com.alibaba.fastjson.JSONObject;
import com.baiwang.open.client.BWRestClient;
import com.baiwang.open.client.IBWClient;
import com.baiwang.open.entity.request.OutputFormatQueryQdInvoiceRequest;
import com.baiwang.open.entity.request.OutputInvoiceIssueRequest;
import com.baiwang.open.entity.request.node.OutputFormatQueryQdInvoiceData;
import com.baiwang.open.entity.request.node.OutputInvoiceIssueInvoiceDetail;
import com.baiwang.open.entity.request.node.OutputInvoiceIssuePreInvoice;
import com.baiwang.open.entity.response.OutputFormatQueryQdInvoiceResponse;
import com.baiwang.open.entity.response.OutputInvoiceIssueResponse;
import com.baiwang.open.entity.response.node.OutputInvoiceIssueInvoiceResult;
import com.baiwang.open.exception.BWOpenException;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.dao.SldhFpqqlshInfoMapper;
import com.dxhy.invoice.dao.TaxpayerInfoMapper;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.SldhFpqqlshInfo;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.BlueInvoiceService;
import com.dxhy.invoice.util.BWUtil;
import com.dxhy.invoice.util.Base64Util;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.pojo.BwjfBlueInvoiceReq;
import com.dxhy.order.pojo.BwjfCommonRequest;
import com.dxhy.order.pojo.BwjfCommonResponse;
import com.dxhy.order.pojo.BwjfInvoiceItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.BWJF)
public class BwjfBlueInvoiceServiceImpl implements BlueInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxpayerInfoMapper taxpayerInfoMapper;
    @Resource
    private SldhFpqqlshInfoMapper sldhFpqqlshInfoMapper;


    //@Override
    public R kpUnse(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) throws Exception {
        return interfaceKp(orderInvoiceInfoEntity, taxpayerInfo);
    }

    @Override
    public R queryInvoiceInfo(JSONObject jsonObject, TaxpayerInfo taxpayerInfo) {
        return interfaceGetInvoiceInfo(jsonObject.getString("DDQQLSH"), taxpayerInfo);
    }

    private R interfaceGetInvoiceInfo(String ddqqlsh, TaxpayerInfo taxpayerInfo) {
        log.info("百望-发票查询请求开始执行，入参：{}", ddqqlsh);
        SldhFpqqlshInfo sldhFpqqlshInfo = sldhFpqqlshInfoMapper.getOneByFpqqlsh(ddqqlsh);
        JSONObject data = new JSONObject();
        data.put("QDFPHM", sldhFpqqlshInfo.getQdfphm());
        data.put("KPRQ", sldhFpqqlshInfo.getKprq());
        // 获取板式文件
        R result = getBswj(sldhFpqqlshInfo, taxpayerInfo);
        if ("0000".equals(result.get("code").toString())) {
            SldhFpqqlshInfo sldhFpqqlshInfo1 = (SldhFpqqlshInfo) result.get("data");
            data.put("OFDZJL", sldhFpqqlshInfo1.getOfdUrl());
            data.put("PDFZJL", sldhFpqqlshInfo1.getPdfUrl());
            log.info("百望-发票请求流水号:{}，发票查询请求成功：{}", ddqqlsh, JsonUtils.getInstance().toJsonString(data));
            return R.ok("0000", "发票查询请求成功").put("data", data);
        } else {
            log.error("百望-发票请求流水号:{}，发票查询请求失败", ddqqlsh);
            return R.error(result.get("code").toString(), result.get("msg").toString());
        }
    }


//    private R interfaceGetInvoiceInfo(String ddqqlsh, TaxpayerInfo taxpayerInfo) {
//        String blueInvoiceIssueUrl = invoiceConfig.getBwjfBlueInvoiceIssueUrl();
//        BwjfCommonRequest bwjfCommonRequest = new BwjfCommonRequest();
//        bwjfCommonRequest.setAppid(taxpayerInfo.getAppid());
//        bwjfCommonRequest.setSignType("3");//不加密
//        bwjfCommonRequest.setSignature("");
//        bwjfCommonRequest.setServiceid("KP10001-1");
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("id", ddqqlsh);
//        bwjfCommonRequest.setContent(Base64Util.encode(jsonObject.toJSONString()));
//        String res = HttpUtils.doPost(blueInvoiceIssueUrl, JsonUtils.getInstance().toJsonString(bwjfCommonRequest));
//        BwjfCommonResponse result = JSONObject.parseObject(res, BwjfCommonResponse.class);
//        if ("0".equals(result.getCode())) {
//            BwjfBlueInvoiceInfoResponse result1 = JSONObject.parseObject(result.getData(), BwjfBlueInvoiceInfoResponse.class);
//            log.info("发票请求流水号:{},发票查询请求成功", ddqqlsh);
//            JSONObject data = new JSONObject();
//            data.put("QDFPHM", result1.getQdfphm());
//            data.put("KPRQ", result1.getKprq());
//            data.put("OFDZJL", result1.getPdf());
//            data.put("PDFZJL", result1.getPdf());
//            return R.ok("0000", "发票查询请求成功").put("data", data);
//        } else {
//            String code = result.getCode();
//            String msg = result.getMsg();
//            log.error("发票请求流水号:{},发票查询请求失败--{}", ddqqlsh, msg);
//            return R.ok(code, msg);
//        }
//
//
//    }


    private R interfaceKp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) throws Exception {
        BwjfBlueInvoiceReq huiqiBlueInvoiceReq = convert2BlueInvoiceInfo(orderInvoiceInfoEntity, taxpayerInfo);
        log.info("发票请求流水号:{},转换为接口开票实体信息:{}", orderInvoiceInfoEntity.getFpqqlsh(), JSONObject.toJSONString(huiqiBlueInvoiceReq));
        String blueInvoiceIssueUrl = invoiceConfig.getBwjfBlueInvoiceIssueUrl();
        BwjfCommonRequest bwjfCommonRequest = new BwjfCommonRequest();
        bwjfCommonRequest.setAppid(taxpayerInfo.getAppid());
        bwjfCommonRequest.setSignType("3");//不加密
        bwjfCommonRequest.setSignature("");
        bwjfCommonRequest.setServiceid("KP10001");
        bwjfCommonRequest.setContent(Base64Util.encode(JSONObject.toJSONString(huiqiBlueInvoiceReq)));
        String res = HttpUtils.doPost(blueInvoiceIssueUrl, JsonUtils.getInstance().toJsonString(bwjfCommonRequest));
        BwjfCommonResponse result = JSONObject.parseObject(res, BwjfCommonResponse.class);
        if ("0".equals(result.getCode())) {

            log.info("发票请求流水号:{},开票请求成功", orderInvoiceInfoEntity.getFpqqlsh());

            return R.ok("0000", "发票开具请求成功");
        } else {
            String code = result.getCode();
            String msg = result.getMsg();
            log.error("发票请求流水号:{},开票请求失败--{}", orderInvoiceInfoEntity.getFpqqlsh(), msg);
            return R.ok(code, msg);
        }


    }


    /**
     * 百望 普电专票开具接口
     *
     * @param orderInvoiceInfoEntity
     * @param taxpayerInfo
     * @return
     */
    @Override
    public R kp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) {

        log.info("百望-发票开具，入参:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
        // 自定义请求唯一标识
        String requestId = orderInvoiceInfoEntity.getFpqqlsh();
        try {

            // 1-获取token
            String token = BWUtil.generateUrl(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret(), invoiceConfig.getBwjfUsername(), invoiceConfig.getBwjfPassword(), invoiceConfig.getBwjfUserSalt());
            // 2-请求报文填充
            OutputInvoiceIssueRequest request = convertBWIssueReq(orderInvoiceInfoEntity);
            log.info("百望-发票开具-请求报文，税号：{}， request：{}", orderInvoiceInfoEntity.getXhfNsrsbh(), JsonUtils.getInstance().toJsonString(request));
            // 3-初始化客户端
            IBWClient client = new BWRestClient(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret());
            log.info("百望-发票开具-初始化客户端完成，税号：{}", orderInvoiceInfoEntity.getXhfNsrsbh());

            // 4-发送请求
            log.info("百望-发票开具-client请求开始，requestId:{}", requestId);
            long startTime = System.currentTimeMillis();
            OutputInvoiceIssueResponse response = client.outputInvoice().issue(request, token);
            long endTime = System.currentTimeMillis();
            log.info("百望-发票开具-client请求完成，，requestId:{}：{}，耗时：{}，返回参数：{}", requestId, endTime - startTime, JsonUtils.getInstance().toJsonString(response));
            if (response.getSuccess()) {
                if (response.getResponse().getSuccess().size() > 0) {
                    for (OutputInvoiceIssueInvoiceResult out : response.getResponse().getSuccess()) {
                        SldhFpqqlshInfo sldhFpqqlshInfo1 = sldhFpqqlshInfoMapper.getOneByFpqqlsh(requestId);
                        if (ObjectUtils.isNotEmpty(sldhFpqqlshInfo1)) {
                            boolean b = sldhFpqqlshInfoMapper.updateByFpqqlsh(requestId) > 0;
                            if (!b) {
                                log.info("百望-发票开具-已存在数据且更新状态失败，requestId:{}", requestId);
                            }
                            log.info("百望-发票开具-已存在数据且更新状态成功，requestId:{}", requestId);
                        }

                        log.info("百望-发票开具，入库请求的开票数据开始，requestId:{}", requestId);
                        SldhFpqqlshInfo sldhFpqqlshInfo = new SldhFpqqlshInfo();
                        sldhFpqqlshInfo.setSldh("");
                        sldhFpqqlshInfo.setFpqqlsh(requestId);
                        sldhFpqqlshInfo.setQdfphm(out.getInvoiceNo());
                        sldhFpqqlshInfo.setKprq(out.getInvoiceDate());
                        sldhFpqqlshInfo.setNsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                        sldhFpqqlshInfo.setIs_delete("0");
                        boolean b = sldhFpqqlshInfoMapper.insert(sldhFpqqlshInfo) > 0;
                        if (b) {
                            log.info("百望-发票开具-入库请求的开票数据失败，requestId:{}", requestId);
                        }
                        log.info("百望-发票开具-入库请求的开票数据成功，requestId:{}", requestId);
                    }
                }
            } else {
                return R.error("9999", "发票开具失败");
            }
        } catch (BWOpenException e) {
            String code = e.getCode();
            String msg = "发票开具异常：开放平台：" + e.getCode() + "--" + e.getMessage() + "; 业务系统：" + e.getSubCode() + "--" + e.getSubMessage();
            log.error("百望-发票开具，requestId:{},开票请求失败：{}", requestId, msg);
            return R.error(code, msg);
        }
        return R.ok();
    }


    /**
     * 百望 获取板式文件接口
     *
     * @param sldhFpqqlshInfo
     * @param taxpayerInfo
     * @return
     */
    public R getBswj(SldhFpqqlshInfo sldhFpqqlshInfo, TaxpayerInfo taxpayerInfo) {

        log.info("百望-获取板式文件，入参:{}", JsonUtils.getInstance().toJsonString(sldhFpqqlshInfo));
        String requestId = sldhFpqqlshInfo.getFpqqlsh();
        try {

            // 1-获取token
            String token = BWUtil.generateUrl(invoiceConfig.getBwjfInvoiceBswjUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret(), invoiceConfig.getBwjfUsername(), invoiceConfig.getBwjfPassword(), invoiceConfig.getBwjfUserSalt());
            // 2-请求报文填充
            OutputFormatQueryQdInvoiceRequest request = new OutputFormatQueryQdInvoiceRequest();
            OutputFormatQueryQdInvoiceData data = new OutputFormatQueryQdInvoiceData();
            data.setInvoiceNo(sldhFpqqlshInfo.getQdfphm());
            request.setData(data);
            request.setTaxNo(sldhFpqqlshInfo.getNsrsbh());
            log.info("百望-获取板式文件-请求报文，税号：{}， request：{}", sldhFpqqlshInfo.getNsrsbh(), JsonUtils.getInstance().toJsonString(request));
            // 3-初始化客户端
            IBWClient client = new BWRestClient(invoiceConfig.getBwjfInvoiceBswjUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret());
            log.info("百望-获取板式文件-初始化客户端完成，税号：{}", sldhFpqqlshInfo.getNsrsbh());

            // 4-发送请求
            log.info("百望-获取板式文件-client请求开始，requestId:{}", requestId);
            long startTime = System.currentTimeMillis();
            OutputFormatQueryQdInvoiceResponse response = client.outputFormat().queryQdInvoice(request, token);
            long endTime = System.currentTimeMillis();
            log.info("百望-获取板式文件-client请求完成，，requestId:{}：{}，耗时：{}，返回参数：{}", requestId, endTime - startTime, JsonUtils.getInstance().toJsonString(response));
            if (response.getSuccess()) {
                if (StringUtils.isNotBlank(response.getResponse().getOfdUrl()) || StringUtils.isNotBlank(response.getResponse().getOfdUrl()) || StringUtils.isNotBlank(response.getResponse().getOfdUrl())) {
                    sldhFpqqlshInfo.setPdfUrl(response.getResponse().getPdfUrl());
                    sldhFpqqlshInfo.setOfdUrl(response.getResponse().getOfdUrl());
                    log.info("百望-获取板式文件-返回链接更新成功，requestId:{}", requestId);
                }
                return R.ok().put("data", sldhFpqqlshInfo);
            } else {
                return R.error("9999", "获取板式文件失败");
            }

        } catch (BWOpenException e) {
            String code = e.getCode();
            String msg = "获取板式文件异常：开放平台：" + e.getCode() + "--" + e.getMessage() + "; 业务系统：" + e.getSubCode() + "--" + e.getSubMessage();
            log.error("百望-获取板式文件，requestId:{},获取板式文件异常捕获：{}", requestId, msg);
            return R.error(code, msg);
        }
    }


    private BwjfBlueInvoiceReq convert2BlueInvoiceInfo(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) throws Exception {
        BwjfBlueInvoiceReq bwjfBlueInvoiceReq = new BwjfBlueInvoiceReq();
        //  订单请求 0-普票     1-专票

        bwjfBlueInvoiceReq.setKhsh(orderInvoiceInfoEntity.getGhfNsrsbh());
        bwjfBlueInvoiceReq.setKhmc(orderInvoiceInfoEntity.getGhfMc());
        bwjfBlueInvoiceReq.setKplx("0");
        //公司代码
        bwjfBlueInvoiceReq.setGsdm(taxpayerInfo.getGsdm());
        bwjfBlueInvoiceReq.setKhdzdh(orderInvoiceInfoEntity.getGhfDz() + orderInvoiceInfoEntity.getGhfDh());
        //邮箱 电话设为假的
        bwjfBlueInvoiceReq.setGmfEmail("<EMAIL>");
        bwjfBlueInvoiceReq.setGmfMobile("18900110010");

        //订单请求  含税标志 0 不含税 1含税   接口  含税标志(1-含税    0-不含税)
        bwjfBlueInvoiceReq.setHsbz(orderInvoiceInfoEntity.getHsbz());
        //  (单据重复时)处理方式 (0-返回最后一次单据开过的发票信息 ；1-强制开发票)
        bwjfBlueInvoiceReq.setClfs("0");
        bwjfBlueInvoiceReq.setBz(orderInvoiceInfoEntity.getBz());
        bwjfBlueInvoiceReq.setSkr(orderInvoiceInfoEntity.getSkr());
        //税号绑定  需要百望九赋提供
        bwjfBlueInvoiceReq.setYhdm(taxpayerInfo.getYhdm());
        bwjfBlueInvoiceReq.setKpzddm(taxpayerInfo.getKpzddm());
        bwjfBlueInvoiceReq.setDjbh(orderInvoiceInfoEntity.getFpqqlsh());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String format = dateTimeFormatter.format(now);

        bwjfBlueInvoiceReq.setDjrq(format);
        bwjfBlueInvoiceReq.setFhr(orderInvoiceInfoEntity.getFhr());
        bwjfBlueInvoiceReq.setKpr(orderInvoiceInfoEntity.getKpr());
        //  订单请求 0-普票     1-专票
        if ("0".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            bwjfBlueInvoiceReq.setFplxdm("02");
        } else if ("1".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            bwjfBlueInvoiceReq.setFplxdm("01");
        }
        bwjfBlueInvoiceReq.setTspz("0");
        bwjfBlueInvoiceReq.setZsfs("0");

        List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
        List<BwjfInvoiceItem> invoiceItems = new ArrayList<>();
        bwjfBlueInvoiceReq.setMxxx(invoiceItems);
        for (int i = 0; i < itemEntityList.size(); i++) {
            OrderInvoiceItemEntity x = itemEntityList.get(i);

            BwjfInvoiceItem bwjfInvoiceItem = new BwjfInvoiceItem();


            bwjfInvoiceItem.setGgxh(x.getGgxh());
            // 0  使用  1  不使用
            bwjfInvoiceItem.setYhzcbs(x.getYhzcbs().equals("N") ? "1" : "0");
            String[] split = x.getXmmc().split("\\*");
            bwjfInvoiceItem.setSpfwjc(split[1]);
            bwjfInvoiceItem.setFphxz(x.getFphxz());
            if ("1".equals(x.getFphxz())) {
                bwjfInvoiceItem.setZkje(x.getJe());
            }
            // 含税标志 0 不含税 1含税
            if ("0".equals(x.getHsbz())) {
                bwjfInvoiceItem.setBhsdj(x.getDj());
                bwjfInvoiceItem.setBhsje(x.getJe());
            } else {
                bwjfInvoiceItem.setHsdj(x.getDj());
                bwjfInvoiceItem.setHsje(x.getJe());
            }
            bwjfInvoiceItem.setSpsl(x.getXmsl());
            bwjfInvoiceItem.setJldw(x.getDw());
            bwjfInvoiceItem.setZzstsgl(x.getZzstsgl());
            bwjfInvoiceItem.setSpmc(split[2]);
            bwjfInvoiceItem.setDjhh(String.valueOf(i + 1));

            if (x.getSl().contains("%")) {

                String slv = String.valueOf(NumberFormat.getPercentInstance().parse(x.getSl()));
                bwjfInvoiceItem.setTax(slv);
            } else {
                bwjfInvoiceItem.setTax(x.getSl());
            }
            bwjfInvoiceItem.setLslbs(x.getLslbs());
            bwjfInvoiceItem.setSsbm(x.getSpbm());

            invoiceItems.add(bwjfInvoiceItem);
        }

        return bwjfBlueInvoiceReq;
    }

    /**
     * 百望 专普电票接口请求报文填充
     *
     * @return
     */
    public OutputInvoiceIssueRequest convertBWIssueReq(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        OutputInvoiceIssueRequest request = new OutputInvoiceIssueRequest();
        request.setIsSplit(false);
        request.setTaxNo(orderInvoiceInfoEntity.getXhfNsrsbh());
        request.setCompletionCustom("");
        OutputInvoiceIssuePreInvoice data = new OutputInvoiceIssuePreInvoice();
//        data.setSellerTelphone(orderInvoiceInfoEntity.getXhfDh());
        data.setDiscountRate(0);
        data.setRedConfirmUuid("");
        data.setPriceTaxMark(orderInvoiceInfoEntity.getHsbz());
//        data.setBuyerBankAccount("");
//        data.setBuyerTelphone(orderInvoiceInfoEntity.getGhfDh());
//        data.setBuyerEmail("");
        data.setInvoiceTotalTax(new BigDecimal(orderInvoiceInfoEntity.getKpse()));
        data.setDiscountAmount(BigDecimal.valueOf(0.0));
//        data.setSellerAddress(orderInvoiceInfoEntity.getXhfDz());
        data.setChecker(orderInvoiceInfoEntity.getFhr());
        data.setMainGoodsName("");
//        data.setSellerAddressPhone("");
        data.setTaxationMethod("0");
        data.setRedInfoNo("");
        data.setPayee(orderInvoiceInfoEntity.getSkr());
//        data.setBuyerAddress(orderInvoiceInfoEntity.getGhfDz());
        data.setSystemName("");
//        data.setBuyerBankName(orderInvoiceInfoEntity.getGhfYh());
        data.setInvoiceType("0");
        data.setRedIssueReason("");
        data.setEmailCarbonCopy("");
        data.setDiscountType("");
//        data.setBuyerAddressPhone("");
        Map<String, Object> ext = new HashMap<String, Object>();
        data.setExt(ext);
        data.setSystemId("");
        data.setBuyerTaxNo(orderInvoiceInfoEntity.getGhfNsrsbh());
//        data.setDeductibleAmount(new BigDecimal(orderInvoiceInfoEntity.getKce()));
        data.setDrawer(orderInvoiceInfoEntity.getKpr());
        data.setInvoiceSpecialMark("00");
        data.setBuyerName(orderInvoiceInfoEntity.getGhfMc());
        data.setOriginalInvoiceNo("");
        data.setInvoiceListMark(orderInvoiceInfoEntity.getQdBz());
        data.setInvoiceTotalPriceTax(new BigDecimal(orderInvoiceInfoEntity.getJshj()));
        data.setSerialNo(orderInvoiceInfoEntity.getFpqqlsh());
//        data.setSellerBankNumber(orderInvoiceInfoEntity.getXhfZh());
//        data.setBuyerPhone("");
        data.setInvoiceTotalPrice(new BigDecimal(orderInvoiceInfoEntity.getHjbhsje()));
//        data.setBuyerBankNumber(orderInvoiceInfoEntity.getGhfZh());
//        data.setSellerBankAccount("");
//        data.setUserAccount("");
        List<OutputInvoiceIssueInvoiceDetail> invoiceDetailsList = new
                ArrayList<OutputInvoiceIssueInvoiceDetail>();
        if (orderInvoiceInfoEntity.getItemEntityList().size() > 0) {
            int i = 1;
            for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceInfoEntity.getItemEntityList()) {

                OutputInvoiceIssueInvoiceDetail outputInvoiceIssueInvoiceDetail = new
                        OutputInvoiceIssueInvoiceDetail();

                outputInvoiceIssueInvoiceDetail.setInvoiceLineNature(orderInvoiceItemEntity.getFphxz());
                outputInvoiceIssueInvoiceDetail.setGoodsTotalPrice(new BigDecimal(orderInvoiceItemEntity.getJe()));
                outputInvoiceIssueInvoiceDetail.setGoodsPersonalCode("");
                outputInvoiceIssueInvoiceDetail.setGoodsSpecification("");
                if (StringUtils.isNotBlank(orderInvoiceItemEntity.getDj())) {
                    outputInvoiceIssueInvoiceDetail.setGoodsPrice(new BigDecimal(orderInvoiceItemEntity.getDj()));
                }

                if ("免税".equals(orderInvoiceItemEntity.getSl()) || "不征税".equals(orderInvoiceItemEntity.getSl())) {
                    outputInvoiceIssueInvoiceDetail.setGoodsTaxRate(new BigDecimal("0"));
                    outputInvoiceIssueInvoiceDetail.setPreferentialMarkFlag("1");
                    outputInvoiceIssueInvoiceDetail.setVatSpecialManagement("preferentialMarkFlag=1");
                    outputInvoiceIssueInvoiceDetail.setFreeTaxMark("1");
                } else {
                    outputInvoiceIssueInvoiceDetail.setGoodsTaxRate(new BigDecimal(orderInvoiceItemEntity.getSl()));
                    outputInvoiceIssueInvoiceDetail.setPreferentialMarkFlag(orderInvoiceItemEntity.getYhzcbs());
                    outputInvoiceIssueInvoiceDetail.setVatSpecialManagement("");
                    outputInvoiceIssueInvoiceDetail.setFreeTaxMark("");
                }

                if ("1".equals(orderInvoiceItemEntity.getFphxz())) {
                    outputInvoiceIssueInvoiceDetail.setGoodsDiscountAmount(new BigDecimal(orderInvoiceItemEntity.getJe().replace("-", "")));
                }
                if (StringUtils.isNotBlank(orderInvoiceItemEntity.getXmsl())) {
                    outputInvoiceIssueInvoiceDetail.setGoodsQuantity(new BigDecimal(orderInvoiceItemEntity.getXmsl()));
                }
                outputInvoiceIssueInvoiceDetail.setGoodsUnit("");
                outputInvoiceIssueInvoiceDetail.setGoodsTotalTax(new BigDecimal(orderInvoiceItemEntity.getSe()));
                outputInvoiceIssueInvoiceDetail.setGoodsCode(orderInvoiceItemEntity.getSpbm());
                outputInvoiceIssueInvoiceDetail.setGoodsName(orderInvoiceItemEntity.getXmmc());
                outputInvoiceIssueInvoiceDetail.setGoodsLineNo(i);
                invoiceDetailsList.add(outputInvoiceIssueInvoiceDetail);
                i++;
            }
        }

        data.setInvoiceDetailsList(invoiceDetailsList);
        // order  0-普票  1--专票
        if ("0".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            data.setInvoiceTypeCode("02");
        } else if ("1".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            data.setInvoiceTypeCode("01");
        }
//        data.setSellerBankName(orderInvoiceInfoEntity.getXhfYh());
        data.setRemarks(orderInvoiceInfoEntity.getBz());
        data.setOriginalInvoiceCode("");
        request.setData(data);
        request.setOrgCode("");
        request.setInvoiceTerminalCode("");
        request.setTaxNo(orderInvoiceInfoEntity.getXhfNsrsbh());
        request.setFormatPushType(true);
        request.setTaxDiskNo("");
        request.setFormatGenerate(true);
        return request;
    }

}
