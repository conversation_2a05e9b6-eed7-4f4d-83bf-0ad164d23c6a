package com.dxhy.invoice.service.impl.huiqi;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.dao.TaxpayerInfoMapper;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.BlueInvoiceService;
import com.dxhy.invoice.util.DistributedKeyMaker;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.modules.entity.InvoiceAdditionInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.pojo.HuiqiBlueInvoiceReq;
import com.dxhy.order.pojo.HuiqiCommonRes;
import com.dxhy.order.pojo.InvoiceItem;
import com.dxhy.order.pojo.XmmxZk;
import com.dxhy.order.pojo.single.Fjys;
import com.dxhy.order.pojo.single.Fjysxx;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.HUIQI)
public class HuiqiBlueInvoiceServiceImpl implements BlueInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxpayerInfoMapper taxpayerInfoMapper;

    @Override
    public R kp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) {

        if (invoiceConfig.getYsRemark()) {
            log.info("演示环境,开票直接返回 0000");
            return R.ok();
        }

        return interfaceKp(orderInvoiceInfoEntity, taxpayerInfo);


    }

    @Override
    public R queryInvoiceInfo(JSONObject jsonObject, TaxpayerInfo taxpayerInfo) {

        return interfaceGetInvoiceInfo(jsonObject.getString("DDQQLSH"), taxpayerInfo);

    }

    private R interfaceGetInvoiceInfo(String ddqqlsh, TaxpayerInfo taxpayerInfo) {

        log.info("演示环境,发票查询接口类型：{}", invoiceConfig.getYsRemark());

        if (invoiceConfig.getYsRemark()) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("QDFPHM", invoiceConfig.getFppre() + "44200000000" + RandomUtil.randomNumbers(ConfigurerInfo.INT_7));
            jsonObj.put("KPRQ", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            jsonObj.put("OFDZJL", "https://dppt99.guangdong.chinatax.gov.cn:8443/kpfw/fpjfzz/v1/exportDzfpwjEwm?Wjgs=OFD&Jym=263D&Fphm=22442000000921770161&Kprq=20220914142427&Czsj=1663136654981");
            jsonObj.put("PDFZJL", "https://dppt99.guangdong.chinatax.gov.cn:8443/kpfw/fpjfzz/v1/exportDzfpwjEwm?Wjgs=PDF&Jym=263D&Fphm=22442000000921770161&Kprq=20220914142427&Czsj=1663136654981");

            log.info("演示环境,发票查询返回逻辑，res：{}", R.ok().put("data", jsonObj));
            return R.ok().put("data", jsonObj);
        }

        String sldh = taxpayerInfoMapper.getSldhByFpqqlsh(ddqqlsh);
        log.info("根据发票请求流水号:{},查出sldh为:{}", ddqqlsh, sldh);
        String selectBlueInvoiceInfoUrl = invoiceConfig.getSelectBlueInvoiceInfoUrl();
        String requestId = DistributedKeyMaker.generateShotKeyNew();
        String sessionId = DistributedKeyMaker.generateShotKeyNew();
        long timestamp = Instant.now().toEpochMilli();
        String reqUrl = selectBlueInvoiceInfoUrl + "?jrzh=" + taxpayerInfo.getJrzh() + "&jrmm=" + taxpayerInfo.getJrmm() + "&sjhm=" + taxpayerInfo.getSjhm() + "&sessionId=" + sessionId + "&requestId=" + requestId + "&timestamp=" + timestamp;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sldh", sldh);
        String res = HttpUtils.doPost(reqUrl, jsonObject.toJSONString());


        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(res, HuiqiCommonRes.class);

        if ("200".equals(huiqiCommonRes.getCode())) {
            //在判断result中的clzt 是否是成功

            JSONObject parse = JSONObject.parseObject(huiqiCommonRes.getResult());
            if ("EXECUTE_SUCCEED".equals(parse.getString("clzt"))) {
                //任务处理成功
                String qdfphm = parse.getString("qdfphm");
                String fpxzUrl = parse.getString("fpxzUrl");
                String pdfxzUrl = parse.getString("pdfxzUrl");
                String kprq = parse.getString("kprq");
                JSONObject data = new JSONObject();
                data.put("QDFPHM", qdfphm);
                data.put("KPRQ", kprq);
                data.put("OFDZJL", fpxzUrl);
                data.put("PDFZJL", pdfxzUrl);
                return R.ok("0000", "发票开具请求成功").put("data", data);
            } else if ("EXECUTE_FAIl".equals(parse.getString("clzt"))) {
                //任务处理失败
                return R.ok(parse.getString("errorCode"), parse.getString("errorMsg"));
            } else {
                //任务处理中 code 300111
                return R.ok("300111", "发票开具中,请稍后查询...");
            }

        } else {
            String code = huiqiCommonRes.getCode();
            String msg = huiqiCommonRes.getMessage();
            return R.ok(code, msg);
        }

    }


    private R interfaceKp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) {
        HuiqiBlueInvoiceReq huiqiBlueInvoiceReq = convert2BlueInvoiceInfo(orderInvoiceInfoEntity);
        log.info("发票请求流水号:{},转换为接口开票实体信息:{}", orderInvoiceInfoEntity.getFpqqlsh(), JSONObject.toJSONString(huiqiBlueInvoiceReq));
        String blueInvoiceIssueUrl = invoiceConfig.getBlueInvoiceIssueUrl();
        String requestId = DistributedKeyMaker.generateShotKeyNew();
        String sessionId = DistributedKeyMaker.generateShotKeyNew();
        long timestamp = Instant.now().toEpochMilli();
        String reqUrl = blueInvoiceIssueUrl + "?jrzh=" + taxpayerInfo.getJrzh() + "&jrmm=" + taxpayerInfo.getJrmm() + "&sjhm=" + taxpayerInfo.getSjhm() + "&sessionId=" + sessionId + "&requestId=" + requestId + "&timestamp=" + timestamp;
        String res = HttpUtils.doPost(reqUrl, JsonUtils.getInstance().toJsonString(huiqiBlueInvoiceReq));
        JSONObject parse = JSONObject.parseObject(res);
        Boolean success = parse.getBoolean("success");
        if (success) {
            String sldh = parse.getJSONObject("result").getString("sldh");
            log.info("发票请求流水号:{},开票请求成功返回的sldh:{}", orderInvoiceInfoEntity.getFpqqlsh(), sldh);
            //保存受理单号与发票请求流水号关联关系
            //重试时  订单模块会使用相同的流水号请求,需要把历史数据改为无效
            taxpayerInfoMapper.updateStatusByFpqqlsh(orderInvoiceInfoEntity.getFpqqlsh());
            boolean b = taxpayerInfoMapper.saveSldhAndFpqqlsh(sldh, orderInvoiceInfoEntity.getFpqqlsh()) > 0;
            log.info("发票请求流水号:{},sldh:{},保存结果:{}", orderInvoiceInfoEntity.getFpqqlsh(), sldh, b);
            return R.ok("0000", "发票开具请求成功");
        } else {
            String code = parse.getString("code");
            String msg = parse.getString("message");
            return R.ok(code, msg);
        }


    }


    private HuiqiBlueInvoiceReq convert2BlueInvoiceInfo(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        HuiqiBlueInvoiceReq huiqiBlueInvoiceReq = new HuiqiBlueInvoiceReq();
        if ("0".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            huiqiBlueInvoiceReq.setFppzDm("02");
        } else if ("1".equals(orderInvoiceInfoEntity.getFpzlDm())) {
            huiqiBlueInvoiceReq.setFppzDm("01");
        }
        huiqiBlueInvoiceReq.setGmfDz(orderInvoiceInfoEntity.getGhfDz());
        huiqiBlueInvoiceReq.setGmfKhh(orderInvoiceInfoEntity.getGhfYh());
        huiqiBlueInvoiceReq.setGmfLxdh(orderInvoiceInfoEntity.getGhfDh());
        huiqiBlueInvoiceReq.setGmfMc(orderInvoiceInfoEntity.getGhfMc());
        huiqiBlueInvoiceReq.setGmfNsrsbh(orderInvoiceInfoEntity.getGhfNsrsbh());
        huiqiBlueInvoiceReq.setGmfYhzh(orderInvoiceInfoEntity.getGhfZh());
        huiqiBlueInvoiceReq.setBz(orderInvoiceInfoEntity.getBz());
        //订单请求  含税标志 0 不含税 1含税   慧企接口  含税标志(Y-是；N-否)
        if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
            huiqiBlueInvoiceReq.setHsbz("N");
        } else {
            huiqiBlueInvoiceReq.setHsbz("Y");
        }


        List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
        // 过滤出折扣行
        List<OrderInvoiceItemEntity> collect = itemEntityList.stream().filter(x -> !x.getFphxz().equals("1")).collect(Collectors.toList());
        List<InvoiceItem> invoiceItems = new ArrayList<>();
        huiqiBlueInvoiceReq.setMxzbList(invoiceItems);
        for (int i = 0; i < collect.size(); i++) {
            OrderInvoiceItemEntity x = collect.get(i);

            InvoiceItem invoiceItem = new InvoiceItem();
            invoiceItem.setXh(i + 1);
            invoiceItem.setSphfwssflhbbm(x.getSpbm());
            invoiceItem.setHwhyslwfwmc(x.getXmmc());
            invoiceItem.setGgxh(x.getGgxh());
            invoiceItem.setDw(x.getDw());
            invoiceItem.setSpsl(x.getXmsl());
            invoiceItem.setDj(x.getDj());
            invoiceItem.setJe(x.getJe());
            if (!x.getSl().contains("%") && x.getSl().contains("0")) {
                NumberFormat num = NumberFormat.getPercentInstance();
                num.setMaximumIntegerDigits(3);
                num.setMaximumFractionDigits(2);
                invoiceItem.setSlv(num.format(Double.valueOf(x.getSl())));
            } else {
                invoiceItem.setSlv(x.getSl());
            }
            invoiceItem.setSfzdyxm("Y");

            invoiceItem.setSe(x.getSe());
            if (StringUtils.isNotBlank(x.getByzd2())) {
                XmmxZk xmmxZk = new XmmxZk();
                xmmxZk.setZkfs(String.format("%2s", x.getByzd2()).replace(" ", "0"));
//                xmmxZk.setZkdx(String.format("%2s", x.getByzd3()).replace(" ", "0"));
                xmmxZk.setZkdx(x.getByzd3());
                invoiceItem.setXmmxZk(xmmxZk);
            }
            invoiceItems.add(invoiceItem);


        }
        Fjysxx fjysxx = new Fjysxx();
        List<Fjys> fjysList = new ArrayList<>();
        fjysxx.setFjysxmzxx(fjysList);
        huiqiBlueInvoiceReq.setFjysxx(fjysxx);
        List<InvoiceAdditionInfoEntity> infoEntityList = orderInvoiceInfoEntity.getInfoEntityList();
        infoEntityList.forEach(x -> {
            Fjys fjys = new Fjys();
            fjys.setFjysxmmc(x.getFjxxmc());
            fjys.setFjysz(x.getFjxxz());
            //订单侧   1 文本型 2 数值型 3 日期型   慧企接口侧  number：数字，date：日期，string：字符 串
            if ("1".equals(x.getSjnr())) {
                fjys.setSjlx1("string");
            }
            if ("2".equals(x.getSjnr())) {
                fjys.setSjlx1("number");
            }
            if ("3".equals(x.getSjnr())) {
                fjys.setSjlx1("date");
            }
            fjysList.add(fjys);
        });


        return huiqiBlueInvoiceReq;
    }

}
