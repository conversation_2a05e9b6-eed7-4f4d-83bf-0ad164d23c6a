package com.dxhy.invoice.service.impl.bwjf;

import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.InvoiceInfoService;
import com.dxhy.order.pojo.InvoiceInfoReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Auther: admin
 * @Date: 2022/9/29 15:04
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.BWJF)
public class BwjfInvoiceInfoService implements InvoiceInfoService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Override
    public R getInvoiceInfos(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) {
        return null;
    }

    @Override
    public R getSingleInvoiceInfo(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) {
        return null;
    }
}
