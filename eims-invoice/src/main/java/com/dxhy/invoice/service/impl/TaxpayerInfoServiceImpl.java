package com.dxhy.invoice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.dao.TaxpayerInfoMapper;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.service.OpenApiService;
import com.dxhy.invoice.service.TaxpayerInfoService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.order.constant.InvoiceTypeEnum;
import com.dxhy.order.pojo.DeptInfo;
import com.dxhy.order.pojo.HuiqiCommonRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:29
 * @Description:
 */

@Service
@Slf4j
public class TaxpayerInfoServiceImpl implements TaxpayerInfoService {
    @Resource
    private TaxpayerInfoMapper taxpayerInfoMapper;
    @Resource
    private InvoiceConfig invoiceConfig;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private OpenApiService openApiService;

    @Override
    public TaxpayerInfo getTaxpayerInfo(String nsrsbh, Map<String,String> params) {
        QueryWrapper<TaxpayerInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nsrsbh", nsrsbh);
        //查询默认的通道
        TaxpayerInfo taxpayerInfo = taxpayerInfoMapper.selectOne(queryWrapper);
        // 根据参数获取符合条件的通道
        if(params != null && !params.isEmpty()){
            //业务类型默认电子发票普通和专用
            String businessType = "FPLX_012";
            //通道类型默认为空
            String channelType = "";
            if(params.containsKey("channelType") && StringUtils.isNotBlank(params.get("channelType"))){
                //通道类型优先级最高
                channelType = params.get("channelType");
            } else if(params.containsKey("gnlx") && StringUtils.isNotBlank(params.get("gnlx"))){
                businessType = params.get("gnlx");
            } else if (params.containsKey("fpzl") && StringUtils.isNotBlank(params.get("fpzl"))) {
                String fpzl = params.get("fpzl");
                if(InvoiceTypeEnum.ORDER_INVOICE_TYPE_003.getKey().equals(fpzl) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_104.getKey().equals(fpzl)){
                    businessType = "FPLX_"+fpzl;
                }else if(params.containsKey("tdyw") && StringUtils.isNotBlank(params.get("tdyw"))){
                    businessType = "TDYW_"+params.get("tdyw");
                }
            }
            String channelName = openApiService.getChannelName(nsrsbh, "1",channelType, businessType);
            if(StringUtils.isNotBlank(channelName)){
                //使用通道管理处配置的通道
                taxpayerInfo.setKpfs(channelName);
            }else {
                return null;
            }
        }
        return taxpayerInfo;
    }

    @Override
    public R addTaxpayerInfo(DeptInfo deptInfo) {
        String taxNos = invoiceConfig.getTaxNos();
        if(taxNos.contains(deptInfo.getTaxpayerCode())){
            //演示税号不更新
            log.info("演示税号:{}不更新",deptInfo.getTaxpayerCode());
            return R.ok();
        }
        TaxpayerInfo taxpayerInfo = this.getTaxpayerInfo(deptInfo.getTaxpayerCode(),null);
        if (ObjectUtils.isEmpty(taxpayerInfo)) {
            TaxpayerInfo taxpayerInfo1 = new TaxpayerInfo();
            taxpayerInfo1.setQymc(deptInfo.getName());
            taxpayerInfo1.setNsrsbh(deptInfo.getTaxpayerCode());
            taxpayerInfo1.setKpfs(deptInfo.getKpfs());
            taxpayerInfo1.setSpid(deptInfo.getSpid());
            int insert = taxpayerInfoMapper.insert(taxpayerInfo1);
            log.info("税号:{} 租户信息新增结果:{}", deptInfo.getTaxpayerCode(), insert == 1 ? true : false);
            if (insert > 0) {
                return R.ok();
            } else {
                return R.error("9999", "纳税人信息添加失败");
            }
        } else {
            taxpayerInfo.setQymc(deptInfo.getName());
            taxpayerInfo.setNsrsbh(deptInfo.getTaxpayerCode());
            taxpayerInfo.setKpfs(deptInfo.getKpfs());
            taxpayerInfo.setSpid(deptInfo.getSpid());
            int i = taxpayerInfoMapper.updateById(taxpayerInfo);
            log.info("税号:{} 租户信息更新结果:{}", deptInfo.getTaxpayerCode(), i == 1 ? true : false);
            if (i > 0) {
                return R.ok();
            } else {
                return R.error("9999", "纳税人信息更新失败");
            }

        }

    }

    @Override
    public R getQrcode(String nsrsbh) {
        try {
            String url = invoiceConfig.getGetEwmUrl() + "?jrzh=" + nsrsbh;
            String res = HttpUtils.doGet(url, "");
            HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(res, HuiqiCommonRes.class);
            if ("200".equals(huiqiCommonRes.getCode())) {
                //在判断result中的clzt 是否是成功
                JSONObject parse = JSONObject.parseObject(huiqiCommonRes.getResult());
                //任务处理成功
                String ewm = parse.getString("rwm");
                String rzid = parse.getString("rzid");
                JSONObject data = new JSONObject();
                data.put("ewm", ewm);
                data.put("rzid", rzid);
                return R.ok("0000", "获取二维码成功").put("data", data.toJSONString());
            } else {
                String code = huiqiCommonRes.getCode();
                String msg = huiqiCommonRes.getMessage();
                return R.ok(code, msg);
            }
        } catch (Exception e) {
            log.error("税号：{}，获取二维码异常：：{}", nsrsbh, e);
            return R.error("9999", "获取二维码异常，请联系管理员");
        }

    }

    @Override
    public R getQrcodeStatus(String nsrsbh, String rzid) {
        try {
            String url = invoiceConfig.getGetEwmStatusUrl() + "?jrzh=" + nsrsbh;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("rzid", rzid);
            String res = HttpUtils.doPost(url, jsonObject.toString());
            HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(res, HuiqiCommonRes.class);
            if ("200".equals(huiqiCommonRes.getCode())) {
                //在判断result中的clzt 是否是成功
                JSONObject parse = JSONObject.parseObject(huiqiCommonRes.getResult());
                //任务处理成功
                String slzt = parse.getString("slzt");
                JSONObject data = new JSONObject();
                data.put("slzt", slzt);
                return R.ok("0000", "获取二维码状态成功").put("data", data);
            } else {
                String code = huiqiCommonRes.getCode();
                String msg = huiqiCommonRes.getMessage();
                return R.ok(code, msg);
            }
        } catch (Exception e) {
            log.error("税号：{}，获取二维码状态异常：：{}", nsrsbh, e);
            return R.error("9999", "获取二维码状态异常，请联系管理员");
        }
    }
}
