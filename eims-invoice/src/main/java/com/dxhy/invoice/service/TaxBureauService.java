package com.dxhy.invoice.service;

import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.modules.entity.ApiLoginReqBO;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
public interface TaxBureauService {

    R login(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo);

    R setSms(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo);

    R getConfirmQrcode(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo);

    R getConfirmStatus(ApiLoginReqBO apiLoginReqBO, TaxpayerInfo taxpayerInfo);
}
