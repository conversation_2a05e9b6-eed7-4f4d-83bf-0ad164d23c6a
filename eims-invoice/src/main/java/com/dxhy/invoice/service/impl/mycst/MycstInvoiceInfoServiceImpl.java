package com.dxhy.invoice.service.impl.mycst;


import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.constant.MycstFplxEnum;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.InvoiceInfoService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.invoice.util.MycstUtil;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.model.MycstInvoiceInfoItem;
import com.dxhy.order.model.MycstSelectInvoiceInfoReq;
import com.dxhy.order.model.MycstSelectInvoiceInfoResp;
import com.dxhy.order.model.dxOpenPlatform.*;
import com.dxhy.order.pojo.*;
import com.dxhy.order.pojo.single.SingleInvoiceInfoResult;
import com.dxhy.order.pojo.single.SingleInvoiceItemRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: admin
 * @Date: 2022/9/29 15:04
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.SHRPA)
public class MycstInvoiceInfoServiceImpl implements InvoiceInfoService {

    @Resource
    private InvoiceConfig invoiceConfig;
    @Resource
    private MycstUtil mycstUtil;

    @Override
    public R getInvoiceInfos(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) throws Exception{
        MycstSelectInvoiceInfoReq mycstSelectInvoiceInfoReq = new MycstSelectInvoiceInfoReq();
        convertToMycstSelectInvoiceInfoReq(mycstSelectInvoiceInfoReq,invoiceInfoReq);
        //获取token
        String token = mycstUtil.getToken();
        mycstSelectInvoiceInfoReq.setToken(token);
        String url = invoiceConfig.getMycstQueryInvoiceList();
        //转换为参数拼接形式
        String param = mycstUtil.convertObjectToQueryString(mycstSelectInvoiceInfoReq);
        String res = HttpUtils.doPost(url, param);
        MycstCommonResponse response = JsonUtils.getInstance().parseObject(res, MycstCommonResponse.class);
        if(response != null){
            String resultCode = response.getResult();
            String resultMessage = response.getMessage();
            if (StringUtils.equals(resultCode, ConfigureConstant.STRING_1)) {
                InvoiceInfoRes invoiceInfoRes = new InvoiceInfoRes();
                //转换为返回给order的报文格式
                convertToInvoiceInfoRes(response.getRow(),invoiceInfoRes);
                return R.ok().put("data",JsonUtils.getInstance().toJsonString(invoiceInfoRes));
            } else {
                log.error("税航票帮手返回失败");
                return R.error("9999", "发票查询失败:"+resultMessage);
            }
        }else {
            log.error("税航票帮手查询发票列表返回数据为空");
            return R.error("9999", "发票查询失败，返回数据为空");
        }
    }

    @Override
    public R getSingleInvoiceInfo(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) {
        DXSelectInvoiceInfoReq dxInvoiceInfoReq = new DXSelectInvoiceInfoReq();
        dxInvoiceInfoReq.setNSRSBH(invoiceInfoReq.getNsrsbh());
        dxInvoiceInfoReq.setQDFPHM(invoiceInfoReq.getQdfphm());
        dxInvoiceInfoReq.setKPRQ(invoiceInfoReq.getKprq());
        String url = invoiceConfig.getMycstQueryInvoiceList();
        String res = HttpUtils.doPostToDx(url, JsonUtils.getInstance().toJsonString(dxInvoiceInfoReq));
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            DXSelectInvoiceInfoResp dxQdResponse = JsonUtils.getInstance().parseObject(dxPlatFormResponse.getContent(), DXSelectInvoiceInfoResp.class);
            SingleInvoiceInfoResult singleInvoiceInfoResult = new SingleInvoiceInfoResult();
            //转换为返回给order的报文格式
            convertToSingleInvoiceInfoResult(dxQdResponse,singleInvoiceInfoResult);
            return R.ok().put("data",JsonUtils.getInstance().toJsonString(singleInvoiceInfoResult));

        }
        return R.error(dxPlatFormResponse.getReturnStateInfo().getReturnCode(),dxPlatFormResponse.getReturnStateInfo().getReturnMessage());
    }


    private void convertToInvoiceInfoRes(List<MycstSelectInvoiceInfoResp> list, InvoiceInfoRes invoiceInfoRes) {
        invoiceInfoRes.setRecords(list.stream().map(this::convertToRecords).collect(Collectors.toList()));
    }
    private InvoiceBaseInfoRes convertToRecords(MycstSelectInvoiceInfoResp mycstSelectInvoiceInfoResp){
        InvoiceBaseInfoRes invoiceBaseInfoRes = new InvoiceBaseInfoRes();
        invoiceBaseInfoRes.setQdfphm(mycstSelectInvoiceInfoResp.getSDFPHM());
        invoiceBaseInfoRes.setXsfnsrsbh(mycstSelectInvoiceInfoResp.getXFSBH());
        invoiceBaseInfoRes.setXsfmc(mycstSelectInvoiceInfoResp.getXFMC());
        invoiceBaseInfoRes.setGmfnsrsbh(mycstSelectInvoiceInfoResp.getGFSBH());
        invoiceBaseInfoRes.setGmfmc(mycstSelectInvoiceInfoResp.getGMFMC());
        invoiceBaseInfoRes.setKprq(mycstSelectInvoiceInfoResp.getKPRQ());
        invoiceBaseInfoRes.setHjje(mycstSelectInvoiceInfoResp.getJE());
        invoiceBaseInfoRes.setHjse(mycstSelectInvoiceInfoResp.getSE());
        if(StringUtils.equals(mycstSelectInvoiceInfoResp.getFPPZ(),"数电普票")){
            invoiceBaseInfoRes.setFppzDm(MycstFplxEnum.SDP.getDxhyFplx());
        } else if (StringUtils.equals(mycstSelectInvoiceInfoResp.getFPPZ(),"数电专票")) {
            invoiceBaseInfoRes.setFppzDm(MycstFplxEnum.SDZ.getDxhyFplx());
        }
        invoiceBaseInfoRes.setFpztDm(mycstSelectInvoiceInfoResp.getFPZT());
        invoiceBaseInfoRes.setSflzfp("1");
        // 1-销项发票； 2-进项发票
        invoiceBaseInfoRes.setGjbq(mycstSelectInvoiceInfoResp.getFPPZ());
        invoiceBaseInfoRes.setItems(mycstSelectInvoiceInfoResp.getFpmx().stream().map(this::convertToInvoiceItem).collect(Collectors.toList()));
        return  invoiceBaseInfoRes;
    }

    private InvoiceItemInfoRes convertToInvoiceItem(MycstInvoiceInfoItem mycstInvoiceInfoItem){
        InvoiceItemInfoRes invoiceItemInfoRes = new InvoiceItemInfoRes();
        BeanUtils.copyProperties(mycstInvoiceInfoItem,invoiceItemInfoRes);
        return invoiceItemInfoRes;
    }
    private void convertToSingleInvoiceInfoResult(DXSelectInvoiceInfoResp dxQdResponse, SingleInvoiceInfoResult singleInvoiceInfoResult) {
        List<DxInvoiceItemInfo> mxzblist = dxQdResponse.getFPXX().getMXZBLIST();
        List<SingleInvoiceItemRes> fpmxxx = mxzblist.stream().map(this::convertToSingleInvoiceItem).collect(Collectors.toList());
        singleInvoiceInfoResult.setFpmxxx(fpmxxx);
    }

    private SingleInvoiceItemRes convertToSingleInvoiceItem(DxInvoiceItemInfo dxInvoiceItemInfo) {
        SingleInvoiceItemRes singleInvoiceItemRes = new SingleInvoiceItemRes();
        singleInvoiceItemRes.setXh(dxInvoiceItemInfo.getXH());
        singleInvoiceItemRes.setXmmc(dxInvoiceItemInfo.getXMMC());
        singleInvoiceItemRes.setGgxh(dxInvoiceItemInfo.getGGXH());
        singleInvoiceItemRes.setDw(dxInvoiceItemInfo.getDW());
        singleInvoiceItemRes.setSpsl(dxInvoiceItemInfo.getSPSL());
        singleInvoiceItemRes.setDj(dxInvoiceItemInfo.getDJ());
        singleInvoiceItemRes.setJe(dxInvoiceItemInfo.getJE());
        singleInvoiceItemRes.setSlv(dxInvoiceItemInfo.getSLV());
        singleInvoiceItemRes.setSe(dxInvoiceItemInfo.getSE());
        singleInvoiceItemRes.setSphfwssflhbbm(dxInvoiceItemInfo.getSPHFWSSFLHBBM());
        singleInvoiceItemRes.setFphxzDm(dxInvoiceItemInfo.getFPHXZDM());
        singleInvoiceItemRes.setHwhyslwfwmc(dxInvoiceItemInfo.getHWHYSLWFWMC());
        singleInvoiceItemRes.setXsyhzcbz(dxInvoiceItemInfo.getXSYHZCBZ());
        singleInvoiceItemRes.setSsyhzclxDm(dxInvoiceItemInfo.getSSYHZCLXDM());
        return singleInvoiceItemRes;
    }


    private void convertToInvoiceInfoRes(DXSelectInvoiceInfoResp dxQdResponse, InvoiceInfoRes invoiceInfoRes) {
        invoiceInfoRes.setPages(dxQdResponse.getTOTAL());
        invoiceInfoRes.setCurrent(dxQdResponse.getCXYM());
        invoiceInfoRes.setSize(dxQdResponse.getTS());
        List<DxInvoiceInfo> fplb = dxQdResponse.getFPLB();
        invoiceInfoRes.setRecords(fplb.stream().map(this::convertToRecords).collect(Collectors.toList()));
    }

    private InvoiceBaseInfoRes convertToRecords(DxInvoiceInfo dxInvoiceInfo){
        InvoiceBaseInfoRes invoiceBaseInfoRes = new InvoiceBaseInfoRes();
        invoiceBaseInfoRes.setQdfphm(dxInvoiceInfo.getQDFP_HM());
        invoiceBaseInfoRes.setXsfnsrsbh(dxInvoiceInfo.getXHF_NSRSBH());
        invoiceBaseInfoRes.setXsfmc(dxInvoiceInfo.getXHF_MC());
        invoiceBaseInfoRes.setGmfnsrsbh(dxInvoiceInfo.getGHF_NSRSBH());
        invoiceBaseInfoRes.setGmfmc(dxInvoiceInfo.getGHF_MC());
        invoiceBaseInfoRes.setKprq(dxInvoiceInfo.getKPRQ());
        invoiceBaseInfoRes.setHjje(dxInvoiceInfo.getHJJE());
        invoiceBaseInfoRes.setHjse(dxInvoiceInfo.getHJSE());
        invoiceBaseInfoRes.setFppzDm(dxInvoiceInfo.getFPZL_DM());
        invoiceBaseInfoRes.setFpztDm(dxInvoiceInfo.getFPZT_DM());
        invoiceBaseInfoRes.setSflzfp(dxInvoiceInfo.getSFLZFP());
        invoiceBaseInfoRes.setGjbq(dxInvoiceInfo.getGJBQ());
        invoiceBaseInfoRes.setFplxDm(dxInvoiceInfo.getFPLX_DM());
        return  invoiceBaseInfoRes;
    }

    private void convertToMycstSelectInvoiceInfoReq(MycstSelectInvoiceInfoReq mycstSelectInvoiceInfoReq, InvoiceInfoReq invoiceInfoReq) throws Exception{
        String pageindex = StringUtils.isBlank(invoiceInfoReq.getCurrent()) ? "1" :invoiceInfoReq.getCurrent();
        String pagesize = StringUtils.isBlank(invoiceInfoReq.getSize()) ? "100" :invoiceInfoReq.getSize();
        mycstSelectInvoiceInfoReq.setKprqq(invoiceInfoReq.getKprqq());
        mycstSelectInvoiceInfoReq.setKprqz(invoiceInfoReq.getKprqz());
        mycstSelectInvoiceInfoReq.setPageindex(pageindex);
        mycstSelectInvoiceInfoReq.setPagesize(pagesize);
        mycstSelectInvoiceInfoReq.setDfnsrsbh(invoiceInfoReq.getDfnsrsbh());
        mycstSelectInvoiceInfoReq.setSjlx(StringUtils.equals("1", invoiceInfoReq.getGjbq()) ? "2" : "1");
        mycstSelectInvoiceInfoReq.setFileDown("0");
    }
}
