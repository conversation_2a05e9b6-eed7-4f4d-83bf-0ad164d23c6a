package com.dxhy.invoice.service.impl.huiqi;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.dao.TaxpayerInfoMapper;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.InvoiceInfoService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.pojo.HuiqiCommonRes;
import com.dxhy.order.pojo.InvoiceInfoReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * @Auther: admin
 * @Date: 2022/9/29 15:04
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.HUIQI)
public class HuiqiInvoiceInfoService implements InvoiceInfoService {
    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxpayerInfoMapper taxpayerInfoMapper;
    @Override
    public R getInvoiceInfos(InvoiceInfoReq invoiceInfoReq,TaxpayerInfo taxpayerInfo) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String kprqq = simpleDateFormat.format(simpleDateFormat.parse(invoiceInfoReq.getKprqq()));
            invoiceInfoReq.setKprqq(kprqq);
            String kprqz =  simpleDateFormat.format(simpleDateFormat.parse(invoiceInfoReq.getKprqz()));
            invoiceInfoReq.setKprqz(kprqz);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        String getInvoiceInfoUrl = invoiceConfig.getInvoiceInfoUrl();

        String res = HttpUtils.doPost(getInvoiceInfoUrl, JsonUtils.getInstance().toJsonString(invoiceInfoReq), taxpayerInfo);
        return analysisResponseResult(res,"发票基础信息查询");


    }

    @Override
    public R getSingleInvoiceInfo(InvoiceInfoReq invoiceInfoReq,TaxpayerInfo taxpayerInfo) {
        String getSingleInvoiceInfoUrl = invoiceConfig.getSingleInvoiceInfoUrl();
        invoiceInfoReq.setNsrsbh(null);
        String res = HttpUtils.doPost(getSingleInvoiceInfoUrl, JsonUtils.getInstance().toJsonString(invoiceInfoReq), taxpayerInfo);
        return analysisResponseResult(res,"单张发票信息查询");
    }


    private R analysisResponseResult(String result,String logMsg){

        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(result, HuiqiCommonRes.class);
        Boolean success = huiqiCommonRes.isSuccess();
        if (success){
            R r = R.ok("0000",logMsg);


            if(null != huiqiCommonRes.getResult() && "" != huiqiCommonRes.getResult()){

                r.put("data",huiqiCommonRes.getResult());
            }
            return r;
        }else{
            String code= huiqiCommonRes.getCode();
            String msg=  huiqiCommonRes.getMessage();
            return R.ok(code,msg);
        }

    }
}
