package com.dxhy.invoice.service.impl.huiqi;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.RedInvoiceService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.pojo.*;
import com.dxhy.order.utils.DistributedKeyMaker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Auther: admin
 * @Date: 2022/9/6 09:58
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.HUIQI)
public class HuiqiRedInvoiceServiceImpl implements RedInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Override
    public R redConfirm(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {

        if (invoiceConfig.getYsRemark()) {
            log.info("演示环境,确认单录入直接返回 0000");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sldh",DistributedKeyMaker.generateShotKey());
            return R.ok().put("data", jsonObject.toJSONString());
        }

        String redConfirmUrl = invoiceConfig.getGenerateRedConfirmUrl();
        String res = HttpUtils.doPost(redConfirmUrl, JsonUtils.getInstance().toJsonString(redConfirmReq), taxpayerInfo);
        return analysisResponseResult(res, "红字确认单请求成功");

    }

    @Override
    public R getRedConfirmResult(RedConfirmReq redConfirmReqt, TaxpayerInfo taxpayerInfo) {

        if (invoiceConfig.getYsRemark()) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("hzfpxxqrdbh", RandomUtil.randomNumbers(ConfigurerInfo.INT_20));
            jsonObj.put("kdsj", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//            jsonObj.put("jshj", new BigDecimal(redConfirmReqt.getHjje()).add(new BigDecimal(redConfirmReqt.getHjse())));
//            jsonObj.put("xsfnsrsbh", redConfirmReqt.getNsrsbh());
            jsonObj.put("xsfmc", "慧企测试税号");
            jsonObj.put("hzqrxxztdm", "01");
            jsonObj.put("gmfnsrsbh", "91440300MA5EF4Q15E");
            jsonObj.put("gmfmc", "深圳标普云科技有限公司");
            log.info("演示环境,获取红字确认单结果逻辑，res:{}", R.ok().put("data", jsonObj));
            return R.ok().put("data", jsonObj.toJSONString());
        }

        String redConfirmUrl = invoiceConfig.getRedConfirmResultUrl();
        String res = HttpUtils.doPost(redConfirmUrl, redConfirmReqt.toString(), taxpayerInfo);
        return analysisResponseResultNew(res, "获取红字确认单结果请求成功");

    }

    @Override
    public R redInvoiceIssue(RedInvoiceIssueReq redInvoiceIssueReq, TaxpayerInfo taxpayerInfo) {

        if (invoiceConfig.getYsRemark()) {
            log.info("演示环境,红票开具直接返回 0000");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("sldh",DistributedKeyMaker.generateShotKey());
            return R.ok().put("data", jsonObject.toJSONString());
        }

        String redInvoiceIssueUrl = invoiceConfig.getRedInvoiceIssueUrl();
        String res = HttpUtils.doPost(redInvoiceIssueUrl, JsonUtils.getInstance().toJsonString(redInvoiceIssueReq), taxpayerInfo);
        return analysisResponseResult(res, "红字发票开具请求成功");

    }

    @Override
    public R getRedInvoiceInfo(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {

        if (invoiceConfig.getYsRemark()) {
            JSONObject jsonObj = new JSONObject();
            jsonObj.put("hzfpxxqrdbh", RandomUtil.randomNumbers(ConfigurerInfo.INT_7));
//            jsonObj.put("jshj", new BigDecimal(redConfirmReq.getHjje()).add(new BigDecimal(redConfirmReq.getHjse())));
            jsonObj.put("qdfphm", invoiceConfig.getFppre() + "44200000000" + RandomUtil.randomNumbers(ConfigurerInfo.INT_7));
            jsonObj.put("hzqrxxztdm", "01");
//            jsonObj.put("xsfnsrsbh", redConfirmReq.getNsrsbh());
            jsonObj.put("xsfmc", "慧企测试税号");
            jsonObj.put("kprq", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            jsonObj.put("pdfxzUrl", "https://dppt.guangdong.chinatax.gov.cn:8443/kpfw/fpjfzz/v1/exportDzfpwjEwm?Wjgs=PDF&Jym=EC05&Fphm=22442000000000808709&Kprq=20221010152710&Czsj=1665386806871");
            log.info("演示环境，获取红票开具结果返回逻辑,res:{}", R.ok().put("data", jsonObj));
            return R.ok().put("data", jsonObj.toJSONString());
        }

        String getRedInvoiceInfoUrl = invoiceConfig.getRedInvoiceResultUrl();
        String nsrsbh = redConfirmReq.getNsrsbh();
        String res = HttpUtils.doPost(getRedInvoiceInfoUrl, JsonUtils.getInstance().toJsonString(redConfirmReq), taxpayerInfo);
        return analysisResponseResultNew(res, "红字发票查询");

    }

    @Override
    public R redConfirmList(RedConfirmListReq redConfirmListReq, TaxpayerInfo taxpayerInfo) {
        String getRedConfirmListUrl = invoiceConfig.getRedConfirmListUrl();
        String res = HttpUtils.doPost(getRedConfirmListUrl, JsonUtils.getInstance().toJsonString(redConfirmListReq), taxpayerInfo);
        return analysisResponseResult(res, "红字确认单列表查询");
    }

    @Override
    public R getRedConfirmInfo(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        String getRedConfirmInfoUrl = invoiceConfig.getRedConfirmInfoUrl();
        String res = HttpUtils.doPost(getRedConfirmInfoUrl, JsonUtils.getInstance().toJsonString(redConfirmHandle), taxpayerInfo);
        return analysisResponseResult(res, "红字确认单单条查询");
    }

    @Override
    public R redConfirmHandle(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        String redConfirmHandleUrl = invoiceConfig.getRedConfirmHandleUrl();
        String res = HttpUtils.doPost(redConfirmHandleUrl, JsonUtils.getInstance().toJsonString(redConfirmHandle), taxpayerInfo);
        return analysisResponseResult(res, "红字确认单处理");
    }


    private R analysisResponseResult(String result, String logMsg) {

        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(result, HuiqiCommonRes.class);
        Boolean success = huiqiCommonRes.isSuccess();
        if (success) {
            R r = R.ok("0000", logMsg);


            if (null != huiqiCommonRes.getResult() && "" != huiqiCommonRes.getResult()) {

                r.put("data", huiqiCommonRes.getResult());
            }
            return r;
        } else {
            String code = huiqiCommonRes.getCode();
            String msg = huiqiCommonRes.getMessage();
            return R.ok(code, msg);
        }

    }

    /**
     * 确认单/红票结果查询需要根据code 和处理状态判断是否是成功
     *
     * @param result
     * @param logMsg
     * @return
     */
    private R analysisResponseResultNew(String result, String logMsg) {

        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(result, HuiqiCommonRes.class);

        if ("200".equals(huiqiCommonRes.getCode())) {
            //在判断result中的clzt 是否是成功

            JSONObject parse = JSONObject.parseObject(huiqiCommonRes.getResult());
            if ("EXECUTE_SUCCEED".equals(parse.getString("clzt"))) {

                return R.ok("0000", "发票开具请求成功").put("data", parse.toJSONString());
            } else if ("EXECUTE_FAIl".equals(parse.getString("clzt"))) {
                //任务处理失败
                return R.ok(parse.getString("errorCode"), parse.getString("errorMsg"));
            } else {
                //任务处理中 code 300111
                return R.ok("300111", "发票开具中,请稍后查询...");
            }

        } else {
            String code = huiqiCommonRes.getCode();
            String msg = huiqiCommonRes.getMessage();
            return R.ok(code, msg);
        }

    }
}
