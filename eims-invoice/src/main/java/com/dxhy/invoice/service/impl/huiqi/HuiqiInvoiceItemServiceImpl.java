package com.dxhy.invoice.service.impl.huiqi;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.InvoiceItemService;
import com.dxhy.invoice.util.DistributedKeyMaker;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.modules.entity.AdditionElementSaveDTO;
import com.dxhy.order.modules.entity.ItemInfoEntity;
import com.dxhy.order.pojo.HuiqiCommonRes;
import com.dxhy.order.pojo.HuiqiFjysReq;
import com.dxhy.order.pojo.HuiqiSceneTemplateDTO;
import com.dxhy.order.pojo.InvoiceItemReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;

/**
 * @Auther: admin
 * @Date: 2022/9/28 15:29
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.HUIQI)
public class HuiqiInvoiceItemServiceImpl implements InvoiceItemService {

    @Resource
    private InvoiceConfig invoiceConfig;


    @Override
    public R addInvoiceItem(ItemInfoEntity itemInfoEntity, TaxpayerInfo taxpayerInfo) throws  Exception{


        InvoiceItemReq invoiceItemReq = new InvoiceItemReq();
        //含税标识 0 否 1 是
        invoiceItemReq.setSphsbz("0".equals(itemInfoEntity.getHsbs())?"N":"Y");
        invoiceItemReq.setXsyhzcbz("0".equals(itemInfoEntity.getYhzcbs())?"N":"Y");


        if(itemInfoEntity.getSl().contains("%")){


            String slv = String.valueOf(NumberFormat.getPercentInstance().parse(itemInfoEntity.getSl()));
            invoiceItemReq.setSlv(slv);
        }else{
            invoiceItemReq.setSlv(itemInfoEntity.getSl());
        }

        invoiceItemReq.setZzstsgl(itemInfoEntity.getYhzclx());
        invoiceItemReq.setSphfwssflhbbm(itemInfoEntity.getSphssflbm());
        invoiceItemReq.setXmmc(itemInfoEntity.getXmmc());
        invoiceItemReq.setSpjm(itemInfoEntity.getJm());
        invoiceItemReq.setJydj(itemInfoEntity.getDj());
        invoiceItemReq.setGgxh(itemInfoEntity.getGgxh());
        invoiceItemReq.setJldwmc(itemInfoEntity.getDj());

        String addInvoiceItemUrl = invoiceConfig.getAddInvoiceItemUrl();
        String res = HttpUtils.doPost(addInvoiceItemUrl, JsonUtils.getInstance().toJsonString(invoiceItemReq), taxpayerInfo);
        return analysisResponseResult(res,"商品信息新增成功");

    }

    @Override
    public R selectAdditionElementList(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            return R.ok();
        }
        HuiqiFjysReq huiqiFjysReq = new HuiqiFjysReq();
        huiqiFjysReq.setCurrent(additionElementSaveDTO.getCurrent());
        huiqiFjysReq.setSize(additionElementSaveDTO.getSize());
        huiqiFjysReq.setFjysxmmc(additionElementSaveDTO.getFjxxmc());
        String selectAdditionElementListUrl = invoiceConfig.getSelectAdditionElementListUrl();
        String res = HttpUtils.doPost(selectAdditionElementListUrl, JsonUtils.getInstance().toJsonString(huiqiFjysReq), taxpayerInfo);
        return analysisResponseResult(res,"附加要素列表查询成功");
    }

    @Override
    public R addAdditionElement(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("uuid",DistributedKeyMaker.generateShotKey());
            return R.ok().put("data",jsonObject.toJSONString());
        }
        HuiqiFjysReq huiqiFjysReq = new HuiqiFjysReq();
        //数据类型 1 文本型 2 数值型 3 日期型
        if("1".equals(additionElementSaveDTO.getSjlx())){
            huiqiFjysReq.setSjlx1("string");
        }else if("2".equals(additionElementSaveDTO.getSjlx())){
            huiqiFjysReq.setSjlx1("number");
        }else if("3".equals(additionElementSaveDTO.getSjlx())){
            huiqiFjysReq.setSjlx1("date");
        }
        huiqiFjysReq.setFjysxmmc(additionElementSaveDTO.getFjxxmc());
        String addAdditionElementListUrl = invoiceConfig.getAddAdditionElementUrl();
        String res = HttpUtils.doPost(addAdditionElementListUrl, JsonUtils.getInstance().toJsonString(huiqiFjysReq), taxpayerInfo);
        return analysisResponseResult(res,"附加要素新增成功");
    }

    @Override
    public R updateAdditionElement(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            return R.ok();
        }
        HuiqiFjysReq huiqiFjysReq = new HuiqiFjysReq();
        //数据类型 1 文本型 2 数值型 3 日期型
        if("1".equals(additionElementSaveDTO.getSjlx())){
            huiqiFjysReq.setSjlx1("string");
        }else if("2".equals(additionElementSaveDTO.getSjlx())){
            huiqiFjysReq.setSjlx1("number");
        }else if("3".equals(additionElementSaveDTO.getSjlx())){
            huiqiFjysReq.setSjlx1("date");
        }
        huiqiFjysReq.setUuid(additionElementSaveDTO.getUuid());
        huiqiFjysReq.setFjysxmmc(additionElementSaveDTO.getFjxxmc());
        String updateAdditionElementListUrl = invoiceConfig.getUpdateAdditionElementUrl();
        String res = HttpUtils.doPost(updateAdditionElementListUrl, JsonUtils.getInstance().toJsonString(huiqiFjysReq), taxpayerInfo);
        return analysisResponseResult(res,"附加要素更新成功");
    }

    @Override
    public R deleteAdditionElement(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            return R.ok();
        }
        HuiqiFjysReq huiqiFjysReq = new HuiqiFjysReq();
        huiqiFjysReq.setUuidList(additionElementSaveDTO.getUuidList());
        String deleteAdditionElementListUrl = invoiceConfig.getDeleteAdditionElementUrl();
        String res = HttpUtils.doPost(deleteAdditionElementListUrl, JsonUtils.getInstance().toJsonString(huiqiFjysReq), taxpayerInfo);
        return analysisResponseResult(res,"附加要素删除成功");
    }

    @Override
    public R selectSceneTemplateList(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            return R.ok();
        }
        String selectSceneTemplateListUrl = invoiceConfig.getSelectSceneTemplateListUrl();
        String res = HttpUtils.doPost(selectSceneTemplateListUrl, JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO), taxpayerInfo);
        return analysisResponseResult(res,"场景模板列表查询成功");
    }

    @Override
    public R addSceneTemplate(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("uuid",DistributedKeyMaker.generateShotKey());
            return R.ok().put("data",jsonObject.toJSONString());
        }
        String addSceneTemplateListUrl = invoiceConfig.getAddSceneTemplateUrl();
        String res = HttpUtils.doPost(addSceneTemplateListUrl, JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO), taxpayerInfo);
        return analysisResponseResult(res,"场景模板新增成功");
    }

    @Override
    public R updateSceneTemplate(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            return R.ok();
        }
        String updateSceneTemplateListUrl = invoiceConfig.getUpdateSceneTemplateUrl();
        String res = HttpUtils.doPost(updateSceneTemplateListUrl, JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO), taxpayerInfo);
        return analysisResponseResult(res,"场景模板修改成功");
    }

    @Override
    public R deleteSceneTemplate(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo) {
        if (invoiceConfig.getYsRemark()) {
            return R.ok();
        }
        String deleteSceneTemplateListUrl = invoiceConfig.getDeleteSceneTemplateUrl();
        String res = HttpUtils.doPost(deleteSceneTemplateListUrl, JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO), taxpayerInfo);
        return analysisResponseResult(res,"场景模板删除成功");
    }

    private R analysisResponseResult(String result,String logMsg){

        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(result, HuiqiCommonRes.class);
        Boolean success = huiqiCommonRes.isSuccess();
        if (success){
            R r = R.ok("0000",logMsg);


            if(null != huiqiCommonRes.getResult() && "" != huiqiCommonRes.getResult()){

                r.put("data",huiqiCommonRes.getResult());
            }
            return r;
        }else{
            String code= huiqiCommonRes.getCode();
            String msg=  huiqiCommonRes.getMessage();
            return R.ok(code,msg);
        }

    }
}
