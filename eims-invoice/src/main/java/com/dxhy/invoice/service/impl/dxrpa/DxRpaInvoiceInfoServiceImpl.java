package com.dxhy.invoice.service.impl.dxrpa;


import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.constant.DxFplxEnum;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.InvoiceInfoService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.model.dxOpenPlatform.*;
import com.dxhy.order.pojo.InvoiceBaseInfoRes;
import com.dxhy.order.pojo.InvoiceInfoReq;
import com.dxhy.order.pojo.InvoiceInfoRes;
import com.dxhy.order.pojo.InvoiceItemInfoRes;
import com.dxhy.order.pojo.single.SingleInvoiceInfoResult;
import com.dxhy.order.pojo.single.SingleInvoiceItemRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: admin
 * @Date: 2022/9/29 15:04
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.DXRPA)
public class DxRpaInvoiceInfoServiceImpl implements InvoiceInfoService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Override
    public R getInvoiceInfos(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) throws Exception{
        DXSelectInvoiceInfoReq dxInvoiceInfoReq = new DXSelectInvoiceInfoReq();
        convertToDXSelectInvoiceInfoReq(dxInvoiceInfoReq,invoiceInfoReq);
        String selectInvoiceInfosUrl = invoiceConfig.getSelectInvoiceInfos();
        String querySingleInvoice = invoiceConfig.getQuerySingleInvoice();
        String res = HttpUtils.doPostToDx(selectInvoiceInfosUrl, JsonUtils.getInstance().toJsonString(dxInvoiceInfoReq));
        DXSelectInvoiceInfoResp dxQdResponse = JsonUtils.getInstance().parseObject(res, DXSelectInvoiceInfoResp.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxQdResponse.getZTDM())) {
            InvoiceInfoRes invoiceInfoRes = new InvoiceInfoRes();
            //转换为返回给order的报文格式
            convertToInvoiceInfoRes(dxQdResponse,invoiceInfoRes);
            invoiceInfoRes.getRecords().forEach(baseInfoRes -> {
                DXSelectInvoiceInfoReq dxInvoiceItemReq = new DXSelectInvoiceInfoReq();
                dxInvoiceItemReq.setNSRSBH(baseInfoRes.getXsfnsrsbh());
                dxInvoiceItemReq.setQDFPHM(baseInfoRes.getQdfphm());
                dxInvoiceItemReq.setKPRQ(baseInfoRes.getKprq());
                dxInvoiceItemReq.setGJBQ(baseInfoRes.getGjbq());
                //查询单条明细数据
                String itemRes = HttpUtils.doPostToDx(querySingleInvoice, JsonUtils.getInstance().toJsonString(dxInvoiceItemReq));
                DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(itemRes, DxPlatFormResponse.class);
                if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
                    DXSelectInvoiceInfoResp singleInvoiceItemResponse = JsonUtils.getInstance().parseObject(dxPlatFormResponse.getContent(), DXSelectInvoiceInfoResp.class);
                    //转换为返回给order的报文格式
                    List<InvoiceItemInfoRes> collect = singleInvoiceItemResponse.getFPXX().getMXZBLIST().stream().map(e->{
                        InvoiceItemInfoRes invoiceItemInfoRes = new InvoiceItemInfoRes();
                        BeanUtils.copyProperties(e, invoiceItemInfoRes);
                        return invoiceItemInfoRes;
                    }).collect(Collectors.toList());
                    baseInfoRes.setItems(collect);
                }
            });
            return R.ok().put("data",JsonUtils.getInstance().toJsonString(invoiceInfoRes));
        }
        return R.error(dxQdResponse.getZTDM(),dxQdResponse.getZTMS());
    }

    @Override
    public R getSingleInvoiceInfo(InvoiceInfoReq invoiceInfoReq, TaxpayerInfo taxpayerInfo) {
        DXSelectInvoiceInfoReq dxInvoiceInfoReq = new DXSelectInvoiceInfoReq();
        dxInvoiceInfoReq.setNSRSBH(invoiceInfoReq.getNsrsbh());
        dxInvoiceInfoReq.setQDFPHM(invoiceInfoReq.getQdfphm());
        dxInvoiceInfoReq.setKPRQ(invoiceInfoReq.getKprq());

        String url = invoiceConfig.getQuerySingleInvoice();
        String res = HttpUtils.doPostToDx(url, JsonUtils.getInstance().toJsonString(dxInvoiceInfoReq));
        DxPlatFormResponse dxPlatFormResponse = JsonUtils.getInstance().parseObject(res, DxPlatFormResponse.class);
        if (ConfigurerInfo.SUCCSSCODE.equals(dxPlatFormResponse.getReturnStateInfo().getReturnCode())) {
            DXSelectInvoiceInfoResp dxQdResponse = JsonUtils.getInstance().parseObject(dxPlatFormResponse.getContent(), DXSelectInvoiceInfoResp.class);
            SingleInvoiceInfoResult singleInvoiceInfoResult = new SingleInvoiceInfoResult();
            //转换为返回给order的报文格式
            convertToSingleInvoiceInfoResult(dxQdResponse,singleInvoiceInfoResult);

            return R.ok().put("data",JsonUtils.getInstance().toJsonString(singleInvoiceInfoResult));
        }
        return R.error(dxPlatFormResponse.getReturnStateInfo().getReturnCode(),dxPlatFormResponse.getReturnStateInfo().getReturnMessage());
    }

    private void convertToSingleInvoiceInfoResult(DXSelectInvoiceInfoResp dxQdResponse, SingleInvoiceInfoResult singleInvoiceInfoResult) {
        List<DxInvoiceItemInfo> mxzblist = dxQdResponse.getFPXX().getMXZBLIST();
        List<SingleInvoiceItemRes> fpmxxx = mxzblist.stream().map(this::convertToSingleInvoiceItem).collect(Collectors.toList());
        singleInvoiceInfoResult.setFpmxxx(fpmxxx);
    }

    private SingleInvoiceItemRes convertToSingleInvoiceItem(DxInvoiceItemInfo dxInvoiceItemInfo) {
        SingleInvoiceItemRes singleInvoiceItemRes = new SingleInvoiceItemRes();
        singleInvoiceItemRes.setXh(dxInvoiceItemInfo.getXH());
        singleInvoiceItemRes.setXmmc(dxInvoiceItemInfo.getXMMC());
        singleInvoiceItemRes.setGgxh(dxInvoiceItemInfo.getGGXH());
        singleInvoiceItemRes.setDw(dxInvoiceItemInfo.getDW());
        singleInvoiceItemRes.setSpsl(dxInvoiceItemInfo.getSPSL());
        singleInvoiceItemRes.setDj(dxInvoiceItemInfo.getDJ());
        singleInvoiceItemRes.setJe(dxInvoiceItemInfo.getJE());
        singleInvoiceItemRes.setSlv(dxInvoiceItemInfo.getSLV());
        singleInvoiceItemRes.setSe(dxInvoiceItemInfo.getSE());
        singleInvoiceItemRes.setSphfwssflhbbm(dxInvoiceItemInfo.getSPHFWSSFLHBBM());
        singleInvoiceItemRes.setFphxzDm(dxInvoiceItemInfo.getFPHXZDM());
        singleInvoiceItemRes.setHwhyslwfwmc(dxInvoiceItemInfo.getHWHYSLWFWMC());
        singleInvoiceItemRes.setXsyhzcbz(dxInvoiceItemInfo.getXSYHZCBZ());
        singleInvoiceItemRes.setSsyhzclxDm(dxInvoiceItemInfo.getSSYHZCLXDM());
        return singleInvoiceItemRes;
    }


    private void convertToInvoiceInfoRes(DXSelectInvoiceInfoResp dxQdResponse, InvoiceInfoRes invoiceInfoRes) {
        invoiceInfoRes.setPages(dxQdResponse.getTOTAL());
        invoiceInfoRes.setCurrent(dxQdResponse.getCXYM());
        invoiceInfoRes.setSize(dxQdResponse.getTS());
        List<DxInvoiceInfo> fplb = dxQdResponse.getFPLB();
        invoiceInfoRes.setRecords(fplb.stream().map(this::convertToRecords).collect(Collectors.toList()));
    }
    private InvoiceBaseInfoRes convertToRecords(DxInvoiceInfo dxInvoiceInfo){
        InvoiceBaseInfoRes invoiceBaseInfoRes = new InvoiceBaseInfoRes();
        invoiceBaseInfoRes.setQdfphm(dxInvoiceInfo.getQDFP_HM());
        invoiceBaseInfoRes.setXsfnsrsbh(dxInvoiceInfo.getXHF_NSRSBH());
        invoiceBaseInfoRes.setXsfmc(dxInvoiceInfo.getXHF_MC());
        invoiceBaseInfoRes.setGmfnsrsbh(dxInvoiceInfo.getGHF_NSRSBH());
        invoiceBaseInfoRes.setGmfmc(dxInvoiceInfo.getGHF_MC());
        invoiceBaseInfoRes.setKprq(dxInvoiceInfo.getKPRQ());
        invoiceBaseInfoRes.setHjje(dxInvoiceInfo.getHJJE());
        invoiceBaseInfoRes.setHjse(dxInvoiceInfo.getHJSE());
        invoiceBaseInfoRes.setFppzDm(dxInvoiceInfo.getFPZL_DM());
        invoiceBaseInfoRes.setFpztDm(dxInvoiceInfo.getFPZT_DM());
        invoiceBaseInfoRes.setSflzfp(dxInvoiceInfo.getSFLZFP());
        invoiceBaseInfoRes.setGjbq(dxInvoiceInfo.getGJBQ());
        invoiceBaseInfoRes.setFplxDm(dxInvoiceInfo.getFPLX_DM());

        return  invoiceBaseInfoRes;
    }
    private void convertToDXSelectInvoiceInfoReq(DXSelectInvoiceInfoReq dxInvoiceInfoReq, InvoiceInfoReq invoiceInfoReq) throws Exception{
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String kprqq = simpleDateFormat.format(simpleDateFormat.parse(invoiceInfoReq.getKprqq()));
        invoiceInfoReq.setKprqq(kprqq);
        String kprqz =  simpleDateFormat.format(simpleDateFormat.parse(invoiceInfoReq.getKprqz()));
        invoiceInfoReq.setKprqz(kprqz);
        dxInvoiceInfoReq.setGJBQ(invoiceInfoReq.getGjbq());
        dxInvoiceInfoReq.setFPZT_DM(Arrays.asList(invoiceInfoReq.getFpztDm()));
        dxInvoiceInfoReq.setFPLY_DM(invoiceInfoReq.getFplyDm());
        String[] fplxDms = invoiceInfoReq.getFplxDm();
        if(fplxDms != null && fplxDms.length != 0){
            for(int i = 0; i < fplxDms.length; i++){
                DxFplxEnum fplxEnum = DxFplxEnum.getCodeValue(fplxDms[i]);
                fplxDms[i] = fplxEnum.getDxFplx();
            }
        }
        dxInvoiceInfoReq.setFPLX_DM(Arrays.asList(fplxDms));
        dxInvoiceInfoReq.setKPRQQ(invoiceInfoReq.getKprqq());
        dxInvoiceInfoReq.setKPRQZ(invoiceInfoReq.getKprqz());
        dxInvoiceInfoReq.setCXYM(invoiceInfoReq.getCurrent());
        dxInvoiceInfoReq.setTS(invoiceInfoReq.getSize());
        dxInvoiceInfoReq.setNSRSBH(invoiceInfoReq.getNsrsbh());
    }
}
