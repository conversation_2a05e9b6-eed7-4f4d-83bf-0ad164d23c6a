package com.dxhy.invoice.service.impl.rpa;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.RedInvoiceService;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Auther: admin
 * @Date: 2022/9/6 09:58
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.RPA)
public class RpaRedInvoiceServiceImpl implements RedInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Override
    public R redConfirm(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {

        String redConfirmUrl = invoiceConfig.getRpaGenerateRedConfirmUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(redConfirmUrl, JsonUtils.getInstance().toJsonString(redConfirmReq));
        return analysisResponseResult(res, "红字确认单请求成功");

    }

    @Override
    public R getRedConfirmResult(RedConfirmReq redConfirmReqt, TaxpayerInfo taxpayerInfo) {
        String redConfirmUrl = invoiceConfig.getRpaRedConfirmResultUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(redConfirmUrl, JsonUtils.getInstance().toJsonString(redConfirmReqt));
        return analysisResponseResultNew(res, "确认单申请中,请稍后查询...");

    }

    @Override
    public R redInvoiceIssue(RedInvoiceIssueReq redInvoiceIssueReq, TaxpayerInfo taxpayerInfo) {
        String redInvoiceIssueUrl = invoiceConfig.getRpaRedInvoiceIssueUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(redInvoiceIssueUrl, JsonUtils.getInstance().toJsonString(redInvoiceIssueReq));
        return analysisResponseResult(res, "红字发票开具请求成功");

    }

    @Override
    public R getRedInvoiceInfo(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        String getRedInvoiceInfoUrl = invoiceConfig.getRpaRedInvoiceResultUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(getRedInvoiceInfoUrl, JsonUtils.getInstance().toJsonString(redConfirmReq));
        return analysisResponseResultNew(res, "发票开具中,请稍后查询...");

    }

    @Override
    public R redConfirmList(RedConfirmListReq redConfirmListReq, TaxpayerInfo taxpayerInfo) {
//        if (!"10".equals(redConfirmListReq.getHzqrxxztDm())) {
//            return R.error("9999","rpa只查全部，减少接口调用频率");
//        }
        String getRedConfirmListUrl = invoiceConfig.getRpaRedConfirmListUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(getRedConfirmListUrl, JsonUtils.getInstance().toJsonString(redConfirmListReq));
        return analysisResponseResult(res, "红字确认单列表查询");
    }

    @Override
    public R getRedConfirmInfo(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        String getRedConfirmInfoUrl = invoiceConfig.getRpaRedConfirmInfoUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(getRedConfirmInfoUrl, JsonUtils.getInstance().toJsonString(redConfirmHandle));
        return analysisResponseResult(res, "红字确认单单条查询");
    }

    @Override
    public R redConfirmHandle(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        String redConfirmHandleUrl = invoiceConfig.getRpaRedConfirmHandleUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(redConfirmHandleUrl, JsonUtils.getInstance().toJsonString(redConfirmHandle));
        return analysisResponseResult(res, "红字确认单处理");
    }


    private R analysisResponseResult(String result, String logMsg) {

        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(result, HuiqiCommonRes.class);
        if ("200".equals(huiqiCommonRes.getCode())) {
            R r = R.ok("0000", logMsg);


            if (null != huiqiCommonRes.getResult() && "" != huiqiCommonRes.getResult()) {

                r.put("data", huiqiCommonRes.getResult());
            }
            return r;
        } else {
            String code = huiqiCommonRes.getCode();
            String msg = huiqiCommonRes.getMessage();
            return R.ok(code, msg);
        }

    }

    /**
     * 确认单/红票结果查询需要根据code 和处理状态判断是否是成功
     *
     * @param result
     * @param logMsg
     * @return
     */
    private R analysisResponseResultNew(String result, String logMsg) {

        HuiqiCommonRes huiqiCommonRes = JSONObject.parseObject(result, HuiqiCommonRes.class);

        if ("200".equals(huiqiCommonRes.getCode())) {
            //

            JSONObject parse = JSONObject.parseObject(huiqiCommonRes.getResult());
            return R.ok("0000", "发票开具请求成功").put("data", parse.toJSONString());

            // rpa返回的所有失败都可以重试失败
        } else if ("300".equals(huiqiCommonRes.getCode())) {
            return R.ok("300111", logMsg);
        } else {
            // rpa返回的所有失败都可以重试   2022-11-09
            String code = huiqiCommonRes.getCode();
            String msg = "系统正在同步中";
            return R.ok(code, msg);
        }

    }
}
