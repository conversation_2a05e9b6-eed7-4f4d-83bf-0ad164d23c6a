package com.dxhy.invoice.service.impl.bwjf;

import com.alibaba.fastjson.JSONObject;
import com.baiwang.open.client.BWRestClient;
import com.baiwang.open.client.IBWClient;
import com.baiwang.open.entity.request.OutputRedinvoiceRedinfoapplyRequest;
import com.baiwang.open.entity.request.OutputRedinvoiceRedinfooperationRequest;
import com.baiwang.open.entity.request.OutputRedinvoiceRedinfoqueryRequest;
import com.baiwang.open.entity.request.node.OutputRedinvoiceRedinfoapplyObjectType;
import com.baiwang.open.entity.response.OutputRedinvoiceRedinfoapplyResponse;
import com.baiwang.open.entity.response.OutputRedinvoiceRedinfooperationResponse;
import com.baiwang.open.entity.response.OutputRedinvoiceRedinfoqueryResponse;
import com.baiwang.open.entity.response.node.OutputRedinvoiceRedinfoapply;
import com.baiwang.open.entity.response.node.OutputRedinvoiceRedinfoquery;
import com.baiwang.open.exception.BWOpenException;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxType;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.RedInvoiceService;
import com.dxhy.invoice.util.BWUtil;
import com.dxhy.invoice.util.DistributedKeyMaker;
import com.dxhy.invoice.util.HttpUtils;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.pojo.RedConfirmHandle;
import com.dxhy.order.pojo.RedConfirmListReq;
import com.dxhy.order.pojo.RedConfirmReq;
import com.dxhy.order.pojo.RedInvoiceIssueReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/9/6 09:58
 * @Description:
 */
@Service
@Slf4j
@TaxType(TaxTypeEnum.BWJF)
public class BwRedInvoiceServiceImpl implements RedInvoiceService {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Override
    public R redConfirm(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {

        log.info("百望-红字确认单申请，入参:{}", JsonUtils.getInstance().toJsonString(redConfirmReq));
        // 自定义请求唯一标识
        String requestId = DistributedKeyMaker.generateShotKey();
        try {
            // 1-获取token
            String token = BWUtil.generateUrl(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret(), invoiceConfig.getBwjfUsername(), invoiceConfig.getBwjfPassword(), invoiceConfig.getBwjfUserSalt());

            // 2-初始化客户端
            IBWClient client = new BWRestClient(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret());



            OutputRedinvoiceRedinfoapplyRequest request = new OutputRedinvoiceRedinfoapplyRequest();
            request.setSellerTaxNo(redConfirmReq.getXhfnsrsbh());
            request.setBuyerTaxNo(redConfirmReq.getGhfnsrsbh());
            request.setInvoiceTotalPrice(new  BigDecimal(redConfirmReq.getHjje()));
            //录入方身份（01 销方 02 购方）
            request.setEntryIdentity(redConfirmReq.getGxfxz());
            List<OutputRedinvoiceRedinfoapplyObjectType> electricInvoiceDetails = new
                    ArrayList<OutputRedinvoiceRedinfoapplyObjectType>();
            OutputRedinvoiceRedinfoapplyObjectType outputRedinvoiceRedinfoapplyObjectType = new
                    OutputRedinvoiceRedinfoapplyObjectType();
            outputRedinvoiceRedinfoapplyObjectType.setGoodsSpecification("");
            outputRedinvoiceRedinfoapplyObjectType.setGoodsTaxRate(BigDecimal.valueOf(0.00));
            outputRedinvoiceRedinfoapplyObjectType.setGoodsQuantity(BigDecimal.valueOf(0.0));
            outputRedinvoiceRedinfoapplyObjectType.setGoodsTotalTax(BigDecimal.valueOf(0));
            outputRedinvoiceRedinfoapplyObjectType.setGoodsUnit("");
            outputRedinvoiceRedinfoapplyObjectType.setGoodsCode("3049900000000000000");
            outputRedinvoiceRedinfoapplyObjectType.setGoodsName("*现代服务*其他现代服务");
            outputRedinvoiceRedinfoapplyObjectType.setGoodsLineNo(1L);
            outputRedinvoiceRedinfoapplyObjectType.setGoodsTotalPrice(BigDecimal.valueOf(-100.00));
            electricInvoiceDetails.add(outputRedinvoiceRedinfoapplyObjectType);
            request.setElectricInvoiceDetails(electricInvoiceDetails);
            request.setInvoiceTotalTax(new BigDecimal(redConfirmReq.getHjse()));
            //冲红原因（01 开票有误 02 销货退回 03 服务中止 04 销售折让）
            request.setRedInvoiceLabel(redConfirmReq.getChyymc());
            request.setOriginalInvoiceNo(redConfirmReq.getLzqdfphm());
            request.setTaxNo(redConfirmReq.getNsrsbh());
            request.setInvoiceSource("");
            request.setOriginInvoiceDate(redConfirmReq.getKprq());
            request.setInvoiceSource("2");

            //
            log.info("百望-红字确认单申请-client请求开始，requestId:{}", requestId);
            long startTime = System.currentTimeMillis();
            OutputRedinvoiceRedinfoapplyResponse response = client.outputRedinvoice().redinfoapply(request, token);
            long endTime = System.currentTimeMillis();
            log.info("百望-红字确认单申请-client请求完成，，requestId:{}：{}，耗时：{}，返回参数：{}", requestId, endTime - startTime, JsonUtils.getInstance().toJsonString(response));
            if (response.getSuccess()) {
                List<OutputRedinvoiceRedinfoapply> outputRedinvoiceRedinfoapplies = response.getResponse();
                if(outputRedinvoiceRedinfoapplies.size()==1){
                    String uuid = outputRedinvoiceRedinfoapplies.get(0).getRedConfirmUuid();
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("sldh",uuid);
                    return  R.ok().put("data",jsonObject.toJSONString());
                }else{
                    return R.error("9998", "红字确认单申请失败");
                }
            } else {
                return R.error("9999", "红字确认单申请失败");
            }
        } catch (BWOpenException e) {
            String code = e.getCode();
            String msg = "红字确认单申请：：" + e.getCode() + "--" + e.getMessage() + "; 业务系统：" + e.getSubCode() + "--" + e.getSubMessage();
            log.error("百望-红字确认单申请，requestId:{},红字确认单申请求失败：{}", requestId, msg);
            return R.error(code, msg);
        }

    }

    @Override
    public R getRedConfirmResult(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        log.info("百望-红字确认单申请结果查询，入参:{}", JsonUtils.getInstance().toJsonString(redConfirmReq));
        // 自定义请求唯一标识
        String requestId = DistributedKeyMaker.generateShotKey();
        try {
            // 1-获取token
            String token = BWUtil.generateUrl(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret(), invoiceConfig.getBwjfUsername(), invoiceConfig.getBwjfPassword(), invoiceConfig.getBwjfUserSalt());

            // 2-初始化客户端
            IBWClient client = new BWRestClient(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret());
            OutputRedinvoiceRedinfoqueryRequest request = new OutputRedinvoiceRedinfoqueryRequest();
            request.setRedConfirmUuid(redConfirmReq.getSldh());
            request.setSellerTaxNo(redConfirmReq.getXhfnsrsbh());
            request.setTaxNo(redConfirmReq.getNsrsbh());

            //
            log.info("百望-红字确认单申请结果查询-client请求开始，requestId:{}", requestId);
            long startTime = System.currentTimeMillis();
            OutputRedinvoiceRedinfoqueryResponse response = client.outputRedinvoice().redinfoquery(request, token);
            long endTime = System.currentTimeMillis();
            log.info("百望-红字确认单申请结果查询-client请求完成，，requestId:{}：{}，耗时：{}，返回参数：{}", requestId, endTime - startTime, JsonUtils.getInstance().toJsonString(response));
            if (response.getSuccess()) {

                List<OutputRedinvoiceRedinfoquery> outputRedinvoiceRedinfoapplies = response.getResponse();
                if(outputRedinvoiceRedinfoapplies.size()==1){
                    OutputRedinvoiceRedinfoquery outputRedinvoiceRedinfoquery = outputRedinvoiceRedinfoapplies.get(0);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("xsfnsrsbh",outputRedinvoiceRedinfoquery.getSellerTaxNo());
                    jsonObject.put("xsfmc",outputRedinvoiceRedinfoquery.getSellerName());
                    jsonObject.put("gmfnsrsbh",outputRedinvoiceRedinfoquery.getBuyerTaxNo());
                    jsonObject.put("gmfmc",outputRedinvoiceRedinfoquery.getBuyerName());
                    jsonObject.put("hzfpxxqrdbh",outputRedinvoiceRedinfoquery.getRedConfirmNo());
                    jsonObject.put("kdsj",outputRedinvoiceRedinfoquery.getEntryDate());
                    jsonObject.put("jshj",outputRedinvoiceRedinfoquery.getInvoiceTotalPriceTax());
                    jsonObject.put("zt",outputRedinvoiceRedinfoquery.getConfirmState());
                    return  R.ok().put("data",jsonObject.toJSONString());
                }else{
                    return R.error("9998", "红字确认单申请结果查询失败");
                }
            } else {
                return R.error("9999", "红字确认单申请结果查询失败");
            }
        } catch (BWOpenException e) {
            String code = e.getCode();
            String msg = "红字确认单申请结果查询：：" + e.getCode() + "--" + e.getMessage() + "; 业务系统：" + e.getSubCode() + "--" + e.getSubMessage();
            log.error("百望-红字确认单申请结果查询，requestId:{},请求失败：{}", requestId, msg);
            return R.error(code, msg);
        }

    }

    @Override
    public R redInvoiceIssue(RedInvoiceIssueReq redInvoiceIssueReq, TaxpayerInfo taxpayerInfo) {
        String redInvoiceIssueUrl = invoiceConfig.getRpaRedInvoiceIssueUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(redInvoiceIssueUrl, JsonUtils.getInstance().toJsonString(redInvoiceIssueReq));
        return R.ok();

    }

    @Override
    public R getRedInvoiceInfo(RedConfirmReq redConfirmReq, TaxpayerInfo taxpayerInfo) {
        String getRedInvoiceInfoUrl = invoiceConfig.getRpaRedInvoiceResultUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(getRedInvoiceInfoUrl, JsonUtils.getInstance().toJsonString(redConfirmReq));
        return R.ok();

    }

    @Override
    public R redConfirmList(RedConfirmListReq redConfirmListReq, TaxpayerInfo taxpayerInfo) {
//        if (!"10".equals(redConfirmListReq.getHzqrxxztDm())) {
//            return R.error("9999","rpa只查全部，减少接口调用频率");
//        }
        String getRedConfirmListUrl = invoiceConfig.getRpaRedConfirmListUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(getRedConfirmListUrl, JsonUtils.getInstance().toJsonString(redConfirmListReq));
        return R.ok();
    }

    @Override
    public R getRedConfirmInfo(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        String getRedConfirmInfoUrl = invoiceConfig.getRpaRedConfirmInfoUrl() + "?jrzh=" + taxpayerInfo.getNsrsbh();
        String res = HttpUtils.doPost(getRedConfirmInfoUrl, JsonUtils.getInstance().toJsonString(redConfirmHandle));
        return R.ok();
    }

    @Override
    public R redConfirmHandle(RedConfirmHandle redConfirmHandle, TaxpayerInfo taxpayerInfo) {
        log.info("百望-红字确认单处理，入参:{}", JsonUtils.getInstance().toJsonString(redConfirmHandle));
        // 自定义请求唯一标识
        String requestId = DistributedKeyMaker.generateShotKey();
        try {
            // 1-获取token
            String token = BWUtil.generateUrl(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret(), invoiceConfig.getBwjfUsername(), invoiceConfig.getBwjfPassword(), invoiceConfig.getBwjfUserSalt());

            // 2-初始化客户端
            IBWClient client = new BWRestClient(invoiceConfig.getBwjfBlueInvoiceIssueUrl(), invoiceConfig.getBwjfAppKey(), invoiceConfig.getBwjfAppSecret());

            OutputRedinvoiceRedinfooperationRequest request = new
                    OutputRedinvoiceRedinfooperationRequest();
            request.setSellerTaxNo(redConfirmHandle.getXsfnsrsbh());
            request.setRedConfirmStatus(redConfirmHandle.getCllx());
            request.setUuid(redConfirmHandle.getUuid());
            request.setTaxNo(redConfirmHandle.getNsrsbh());
            //
            log.info("百望-红字确认单处理-client请求开始，requestId:{}", requestId);
            long startTime = System.currentTimeMillis();
            OutputRedinvoiceRedinfooperationResponse response = client.outputRedinvoice().redinfooperation(request, token);
            long endTime = System.currentTimeMillis();
            log.info("百望-红字确认单处理-client请求完成，，requestId:{}：{}，耗时：{}，返回参数：{}", requestId, endTime - startTime, JsonUtils.getInstance().toJsonString(response));
            if (response.getSuccess()) {

                return  R.ok();

            } else {
                return R.error("9999", "红字确认单处理失败");
            }
        } catch (BWOpenException e) {
            String code = e.getCode();
            String msg = "红字确认单处理异常：：" + e.getCode() + "--" + e.getMessage() + "; 业务系统：" + e.getSubCode() + "--" + e.getSubMessage();
            log.error("百望-红字确认单处理异常，requestId:{},请求失败：{}", requestId, msg);
            return R.error(code, msg);
        }
    }





}
