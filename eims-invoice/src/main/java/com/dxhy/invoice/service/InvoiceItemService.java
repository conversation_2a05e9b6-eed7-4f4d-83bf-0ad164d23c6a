package com.dxhy.invoice.service;

import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.modules.entity.AdditionElementSaveDTO;
import com.dxhy.order.modules.entity.ItemInfoEntity;
import com.dxhy.order.pojo.HuiqiSceneTemplateDTO;

/**
 * @Auther: admin
 * @Date: 2022/9/28 15:28
 * @Description:
 */
public interface InvoiceItemService {
    R addInvoiceItem(ItemInfoEntity itemInfoEntity, TaxpayerInfo taxpayerInfo) throws  Exception;

    R selectAdditionElementList(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo);

    R addAdditionElement(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo);

    R updateAdditionElement(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo);

    R deleteAdditionElement(AdditionElementSaveDTO additionElementSaveDTO, TaxpayerInfo taxpayerInfo);

    R selectSceneTemplateList(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo);

    R addSceneTemplate(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo);

    R updateSceneTemplate(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo);

    R deleteSceneTemplate(HuiqiSceneTemplateDTO huiqiSceneTemplateDTO, TaxpayerInfo taxpayerInfo);
}
