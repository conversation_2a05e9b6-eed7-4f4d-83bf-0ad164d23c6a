package com.dxhy.invoice.service;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;

/**
 * @Auther: admin
 * @Date: 2022/8/29 14:35
 * @Description:
 */
public interface BlueInvoiceService {
    R kp(OrderInvoiceInfoEntity orderInvoiceInfoEntity, TaxpayerInfo taxpayerInfo) throws  Exception;

    R queryInvoiceInfo(JSONObject jsonObject,TaxpayerInfo taxpayerInfo);
}
