package com.dxhy.invoice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.service.OpenApiService;
import com.dxhy.order.utils.HttpUtils;
import com.dxhy.order.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service("openApiService")
@RefreshScope
public class OpenApiServiceImpl implements OpenApiService {
    @Value("${qst.channelNameUrl}")
    private String channelNameUrl;

    @Override
    public String getChannelName(String nsrsbh, String productName,String channelType, String businessType) {
        if (StringUtils.isEmpty(nsrsbh) || StringUtils.isEmpty(productName) || StringUtils.isEmpty(businessType)) {
            return "";
        }
        Map requestMap = new HashMap();
        requestMap.put("baseNsrsbh", nsrsbh);
        requestMap.put("productName", productName);
        requestMap.put("channelType", channelType);
        requestMap.put("businessType", businessType);
        String response = HttpUtils.doPost(channelNameUrl, JacksonUtils.toJsonString(requestMap));
        Map<String, Object> responseMap = JSONObject.parseObject(response, HashMap.class);
        if ("0000".equals(responseMap.get("code").toString())) {
            return responseMap.get("data").toString();
        }
        return "";
    }

}