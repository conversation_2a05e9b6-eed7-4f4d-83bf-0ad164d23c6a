package com.dxhy.invoice.factory;

import com.dxhy.invoice.service.*;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @program invoice
 * @description 策略工厂
 * @date 2021/04/14
 */

@Component
public class TaxInvoiceFactory implements BeanPostProcessor {

    private Map<String, Object> InvoiceMap = new ConcurrentHashMap<>();

    /**
     * 事务代理会影响bean获取其bean上的注解，此处用BeanPostProcessor 在事务代理之前 拿到bean 的注解
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        // 只对实现的类做操作
        if (bean instanceof BlueInvoiceService
                || bean instanceof RedInvoiceService
                || bean instanceof InvoiceItemService
                || bean instanceof InvoiceInfoService
                || bean instanceof TaxBureauService
                ) {
            saveInvoiceMap(bean);
        }

        return bean;
    }

    private void saveInvoiceMap(Object bean) {
        String key = bean.getClass().getInterfaces()[0].getSimpleName();
        Class<? extends Object> clazz = bean.getClass();
        TaxType annotation = clazz.getAnnotation(TaxType.class);
        this.InvoiceMap.put(annotation.value().toString().concat(key), bean);
    }

    /**
     * 寻找对应得策略处理器
     */
    public BlueInvoiceService getBlueInvoiceHandler(TaxTypeEnum type) {
        return (BlueInvoiceService) InvoiceMap.get(type.toString().concat(BlueInvoiceService.class.getSimpleName()));
    }

    public RedInvoiceService getRedInvoiceHandler(TaxTypeEnum type) {
        return (RedInvoiceService) InvoiceMap.get(type.toString().concat(RedInvoiceService.class.getSimpleName()));
    }

    public InvoiceItemService getInvoiceItemHandler(TaxTypeEnum type) {
        return (InvoiceItemService) InvoiceMap.get(type.toString().concat(InvoiceItemService.class.getSimpleName()));
    }
    public InvoiceInfoService getInvoiceInfoHandler(TaxTypeEnum type) {
        return (InvoiceInfoService) InvoiceMap.get(type.toString().concat(InvoiceInfoService.class.getSimpleName()));
    }
    public TaxBureauService getTaxBureauHandler(TaxTypeEnum type) {
        return (TaxBureauService) InvoiceMap.get(type.toString().concat(TaxBureauService.class.getSimpleName()));
    }


}
