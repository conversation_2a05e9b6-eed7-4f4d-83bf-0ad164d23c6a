package com.dxhy.invoice.factory;


/**
 * <AUTHOR>
 * @program invoice
 * @description 税控设备类型
 * @date 2021/04/14
 */
public enum TaxTypeEnum {

    /**
     * rpa
     */
    RPA,
    /**
     * 慧企
     */
    HUIQI,
    /**
     * 百望九赋
     */
    BWJF,
    /**
     * 大象RPA
     */
    DXRPA,
    /**
     * 税航RPA
     */
    SHRPA;


    public static TaxTypeEnum getTaxType(String machineType) {
        TaxTypeEnum mytype = Enum.valueOf(TaxTypeEnum.class, machineType);
        return mytype;
    }

    /**
     * 判断machineType值是否存在
     * @param machineType
     * @return
     */
    public static Boolean isDefinedCode(String machineType) {

        if (machineType == null || "" == machineType) {
            return false;
        }
        for (TaxTypeEnum bussinessNameEnums : TaxTypeEnum.values()) {
            if (bussinessNameEnums.toString().equals(machineType)) {
                return true;
            }
        }
        return false;
    }
}
