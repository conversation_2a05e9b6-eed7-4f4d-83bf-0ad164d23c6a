package com.dxhy.invoice.controller;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxInvoiceFactory;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.TaxpayerInfoService;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.pojo.RedConfirmHandle;
import com.dxhy.order.pojo.RedConfirmListReq;
import com.dxhy.order.pojo.RedConfirmReq;
import com.dxhy.order.pojo.RedInvoiceIssueReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: admin
 * @Date: 2022/9/6 09:18
 * @Description:红字发票&确认单 对接慧企接口
 */
@RestController
@RequestMapping("/redInvoice")
@Slf4j
public class RedConfirmmationInvoiceController {



    @Resource
    private TaxInvoiceFactory taxInvoiceFactory;
    @Resource
    private TaxpayerInfoService taxpayerInfoService;

    /**
     * 红字确认单申请录入
     * @param redConfirmReq
     * @return
     */
    @PostMapping("/redConfirm")
    public R redConfirm(@RequestBody RedConfirmReq redConfirmReq){
        try {
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redConfirmReq.getNsrsbh(),redConfirmReq.getFplxdm(),redConfirmReq.getTdyw());
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","红字确认单申请", JsonUtils.getInstance().toJsonString(redConfirmReq));
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).redConfirm(redConfirmReq,taxpayerInfo);



        } catch (Exception e) {
            log.error("{}--请求异常 {}","红字确认单申请",e);
            return R.error("9999","红字确认单申请异常,请联系管理员");
        }

    }

    /**
     * 获取确认单申请结果
     * @param redConfirmReq
     * @return
     */
    @PostMapping("/getRedConfirmResult")
    public R getRedConfirmResult(@RequestBody RedConfirmReq redConfirmReq){
        try {
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redConfirmReq.getNsrsbh(),redConfirmReq.getFplxdm(),redConfirmReq.getTdyw());
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","获取确认单申请结果", JsonUtils.getInstance().toJsonString(redConfirmReq.toString()));
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getRedConfirmResult(redConfirmReq,taxpayerInfo);
        } catch (Exception e) {
            log.error("{}请求异常:","获取确认单申请结果",e);
            return R.error("9999","获取确认单申请结果,请联系管理员");
        }

    }

    /**
     * 红字发票开具
     * @param redInvoiceIssueReq
     * @return
     */
    @PostMapping("/redInvoiceIssue")
    public R redInvoiceIssue(@RequestBody RedInvoiceIssueReq redInvoiceIssueReq){
        try {
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redInvoiceIssueReq.getNsrsbh(),redInvoiceIssueReq.getFpzl(),redInvoiceIssueReq.getTdyw());
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","红字发票开具", JSONObject.toJSONString(redInvoiceIssueReq));
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).redInvoiceIssue(redInvoiceIssueReq,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}请求异常:","红字发票开具",e);
            return R.error("9999","红字发票开具异常,请联系管理员");
        }

    }

    /**
     * 红字发票结果查询
     * @param redConfirmReq
     * @return
     */
    @PostMapping("/getRedInvoiceInfo")
    public R getRedInvoiceInfo(@RequestBody RedConfirmReq redConfirmReq){
        try {
            // 请求参数 {"nsrsbh":"","sldh":""}
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redConfirmReq.getNsrsbh(),redConfirmReq.getFplxdm(),redConfirmReq.getTdyw());
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","红字发票开具结果查询", redConfirmReq.toString());
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getRedInvoiceInfo(redConfirmReq,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}请求异常 ","红字发票开具结果查询",e);
            return R.error("9999","红字发票开具结果查询异常,请联系管理员");
        }

    }

    /**
     * 获取确认单列表(我发起的 我收到的)
     * @param redConfirmListReq
     * @return
     */
    @PostMapping("/getRedConfirmList")
    public R redConfirmList(@RequestBody RedConfirmListReq redConfirmListReq){
        try {
            //获取红字确认单列表通道默认使用 电子发票（普通发票）+电子发票（专用发票）
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redConfirmListReq.getNsrsbh(),"001","");
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","红字确认单列表查询", JSONObject.toJSONString(redConfirmListReq));
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).redConfirmList(redConfirmListReq,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}请求异常:","红字确认单列表查询",e);
            return R.error("9999","红字确认单列表查询异常,请联系管理员");
        }

    }

    /**
     * 获取单条 确认单详情
     * @param redConfirmHandle
     * @return
     */
    @PostMapping("/getRedConfirmInfo")
    public R getRedConfirmInfo(@RequestBody RedConfirmHandle redConfirmHandle){
        try {
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redConfirmHandle.getNsrsbh(),redConfirmHandle.getFpzl(),redConfirmHandle.getTdyw());
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","红字确认单单条查询", JSONObject.toJSONString(redConfirmHandle));
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getRedConfirmInfo(redConfirmHandle,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}请求异常:","红字确认单单条查询",e);
            return R.error("9999","红字确认单单条查询异常,请联系管理员");
        }

    }

    /**
     * 红字确认单处理   撤销 拒绝 确认
     * @param redConfirmHandle
     * @return
     */
    @PostMapping("/redConfirmHandle")
    public R redConfirmHandle(@RequestBody RedConfirmHandle redConfirmHandle){
        try {
            //
            TaxpayerInfo taxpayerInfo = getTaxpayerInfo(redConfirmHandle.getNsrsbh(),redConfirmHandle.getFpzl(),redConfirmHandle.getTdyw());
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            log.info("{}--请求报文:{}","红字确认单处理", JSONObject.toJSONString(redConfirmHandle));
            return   taxInvoiceFactory.getRedInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).redConfirmHandle(redConfirmHandle,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}请求异常:","红字确认单处理",e);
            return R.error("9999","红字确认单处理异常,请联系管理员");
        }

    }

    private TaxpayerInfo getTaxpayerInfo(String nsrsbh,String fpzl,String tdyw){
        Map<String,String> params = new HashMap<>();
        params.put("fpzl",fpzl);
        params.put("tdyw",tdyw);
        TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(nsrsbh,params);
        return  taxpayerInfo;

    }
}
