package com.dxhy.invoice.controller;

import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.service.TaxpayerInfoService;
import com.dxhy.order.pojo.DeptInfo;
import com.dxhy.order.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Auther: admin
 * @Date: 2022/11/17 14:13
 * @Description:
 */
@RestController
@RequestMapping("/taxpayerInfo")
@Slf4j
public class TaxpayerController {

    @Resource
    private TaxpayerInfoService taxpayerInfoService;
    @RequestMapping("addTaxpayerInfo")
    public R  addTaxpayerInfo(@RequestBody DeptInfo deptInfo){
        log.info("同步企业信息,请求参数为:{}", JsonUtils.getInstance().toJsonString(deptInfo));

        return taxpayerInfoService.addTaxpayerInfo(deptInfo);
    }

    /**
     * 获取实名认证二维码
     * @param nsrsbh
     * @return
     */
    @RequestMapping("/getQrcode/{nsrsbh}")
    public R  getQrcode(@PathVariable("nsrsbh") String nsrsbh){
        log.info("获取实名认证二维码,请求参数为:{}", nsrsbh);

        return taxpayerInfoService.getQrcode(nsrsbh);
    }

    /**
     * 获取二维码状态
     * @param rzid
     * @return
     */
    @RequestMapping("/getQrcodeStatus/{nsrsbh}/{rzid}")
    public R  getQrcodeStatus(@PathVariable("nsrsbh") String nsrsbh,@PathVariable("rzid") String rzid){
        log.info("获取二维码状态,请求参数为:{} {}", nsrsbh,rzid);

        return taxpayerInfoService.getQrcodeStatus(nsrsbh,rzid);
    }

}
