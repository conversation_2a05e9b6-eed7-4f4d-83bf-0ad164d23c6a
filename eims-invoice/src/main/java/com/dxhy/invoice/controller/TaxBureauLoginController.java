package com.dxhy.invoice.controller;


import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxInvoiceFactory;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.TaxpayerInfoService;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.modules.entity.ApiLoginReqBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 电子税局登录相关接口
 */

@RestController
@RequestMapping("/login")
@Slf4j
public class TaxBureauLoginController {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxInvoiceFactory taxInvoiceFactory;

    @Resource
    private TaxpayerInfoService taxpayerInfoService;

    private static final String LOG_MSG ="电子税局";
    private static final Map<String, String> params;
    static {
        params = new HashMap<>();
        params.put("channelType", "RPA");
    }

    @PostMapping("/dzswjLogin")
    public R dzswjLogin(@RequestBody ApiLoginReqBO apiLoginReqBO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"登陆",JsonUtils.getInstance().toJsonString(apiLoginReqBO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(apiLoginReqBO.getNsrsbh(),params);
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            return   taxInvoiceFactory.getTaxBureauHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).login(apiLoginReqBO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{}请求异常 {}",LOG_MSG,"登陆",e);
            return R.error("9999","电子税局登陆异常,请联系管理员");
        }

    }

    @PostMapping("/setSms")
    public R setSms(@RequestBody ApiLoginReqBO apiLoginReqBO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"登陆设置验证码",JsonUtils.getInstance().toJsonString(apiLoginReqBO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(apiLoginReqBO.getNsrsbh(),params);
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            return   taxInvoiceFactory.getTaxBureauHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).setSms(apiLoginReqBO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{}请求异常 {}",LOG_MSG,"登陆设置验证码",e);
            return R.error("9999","登陆设置验证码异常,请联系管理员");
        }

    }

    @PostMapping("/getConfirmQrcode")
    public R getConfirmQrcode(@RequestBody ApiLoginReqBO apiLoginReqBO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"获取实名认证二维码",JsonUtils.getInstance().toJsonString(apiLoginReqBO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(apiLoginReqBO.getNsrsbh(),params);
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            return   taxInvoiceFactory.getTaxBureauHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getConfirmQrcode(apiLoginReqBO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{}请求异常 {}",LOG_MSG,"获取实名认证二维码",e);
            return R.error("9999","获取实名认证二维码异常,请联系管理员");
        }

    }

    @PostMapping("/getConfirmStatus")
    public R getConfirmStatus(@RequestBody ApiLoginReqBO apiLoginReqBO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"查询实名认证状态",JsonUtils.getInstance().toJsonString(apiLoginReqBO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(apiLoginReqBO.getNsrsbh(),params);
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            return   taxInvoiceFactory.getTaxBureauHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getConfirmStatus(apiLoginReqBO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{}请求异常 {}",LOG_MSG,"查询实名认证状态",e);
            return R.error("9999","查询实名认证状态异常,请联系管理员");
        }

    }

}
