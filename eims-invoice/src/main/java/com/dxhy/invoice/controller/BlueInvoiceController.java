package com.dxhy.invoice.controller;


import com.alibaba.fastjson.JSONObject;
import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxInvoiceFactory;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.TaxpayerInfoService;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 全电发票开具
 */

@RestController
@RequestMapping("/invoice")
@Slf4j
public class BlueInvoiceController {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxInvoiceFactory taxInvoiceFactory;

    @Resource
    private TaxpayerInfoService taxpayerInfoService;

    private static final String LOG_MSG ="全电发票";

    @PostMapping("/invoiceIssue")
    public R invoiceIssue(@RequestBody OrderInvoiceInfoEntity orderInvoiceInfoEntity){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"全电发票开具", JacksonUtils.toJsonPrettyString(orderInvoiceInfoEntity));
            Map<String,String> params = new HashMap<>();
            params.put("fpzl",orderInvoiceInfoEntity.getFpzlDm());
            params.put("tdyw",orderInvoiceInfoEntity.getTdyw());
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(orderInvoiceInfoEntity.getXhfNsrsbh(),params);
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            return   taxInvoiceFactory.getBlueInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).kp(orderInvoiceInfoEntity,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 流水号:{}请求异常 {}",LOG_MSG,"全电发票开具",orderInvoiceInfoEntity.getFpqqlsh(),e);
            return R.error("9999","全电发票开具异常,请联系管理员");
        }

    }

    @PostMapping("/queryInvoiceInfo")
    public R queryInvoiceInfo(@RequestBody JSONObject jsonObject){
        try {
            //{"DDQQLSH":"","NSRSBH":""}
            log.info("{}--{} 请求报文:{}",LOG_MSG,"全电发票查询",jsonObject);
            String nsrsbh = jsonObject.getString("NSRSBH");
            String fpzl = jsonObject.getString("fpzl");
            String tdyw = jsonObject.getString("tdyw");
            Map<String,String> params = new HashMap<>();
            params.put("fpzl",fpzl);
            params.put("tdyw",tdyw);
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(nsrsbh,params);
            if(null == taxpayerInfo  || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","未匹配到合法授权通道!");
            }
            return taxInvoiceFactory.getBlueInvoiceHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).queryInvoiceInfo(jsonObject,taxpayerInfo);

        } catch (Exception e) {
            log.error("{}--{} 流水号:{} 请求异常 {}",LOG_MSG,"全电发票查询",jsonObject.getString("DDQQLSH"),e);
            return R.error("9999","发票查询异常,请联系管理员");
        }

    }





}
