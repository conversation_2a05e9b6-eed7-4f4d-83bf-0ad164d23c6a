package com.dxhy.invoice.controller;

import com.dxhy.invoice.config.InvoiceConfig;
import com.dxhy.invoice.entity.R;
import com.dxhy.invoice.entity.TaxpayerInfo;
import com.dxhy.invoice.factory.TaxInvoiceFactory;
import com.dxhy.invoice.factory.TaxTypeEnum;
import com.dxhy.invoice.service.TaxpayerInfoService;
import com.dxhy.invoice.util.JsonUtils;
import com.dxhy.order.pojo.InvoiceInfoReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: admin
 * @Date: 2022/9/7 10:45
 * @Description: 查询税局发票基础信息、单张发票查询 接口
 */
@RestController
@RequestMapping("/invoiceInfo")
@Slf4j
public class InvoiceInfoController {

    @Resource
    private InvoiceConfig invoiceConfig;

    @Resource
    private TaxInvoiceFactory taxInvoiceFactory;

    @Resource
    private TaxpayerInfoService taxpayerInfoService;

    private static final Map<String, String> params;
    static {
        params = new HashMap<>();
        params.put("gnlx", "GNLX_00");
    }

    /**
     * 批量拉取进销项发票
     *
     * @param invoiceInfoReq
     * @return
     */
    @RequestMapping("/getInvoiceInfos")
    public R getInvoiceInfos(@RequestBody InvoiceInfoReq invoiceInfoReq) {
        try{
            log.info("发票基础信息查询,请求参数:{}", JsonUtils.getInstance().toJsonString(invoiceInfoReq));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(invoiceInfoReq.getNsrsbh(),params);
            if (null == taxpayerInfo || StringUtils.isBlank(taxpayerInfo.getNsrsbh())) {
                return R.error("9999", "未匹配到合法授权通道!");
            }
            return taxInvoiceFactory.getInvoiceInfoHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getInvoiceInfos(invoiceInfoReq, taxpayerInfo);
        }catch (Exception e){
            return R.error("9999","发票基础信息查询异常");
        }
    }

    /**
     * 单张发票信息查询
     *
     * @param invoiceInfoReq
     * @return
     */
    @RequestMapping("/getSingleInvoiceInfo")
    public R getSingleInvoiceInfo(@RequestBody InvoiceInfoReq invoiceInfoReq) {
        log.info("单张发票信息查询,请求参数:{}", JsonUtils.getInstance().toJsonString(invoiceInfoReq));
        TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(invoiceInfoReq.getNsrsbh(),params);
        if (null == taxpayerInfo || StringUtils.isBlank(taxpayerInfo.getNsrsbh())) {
            return R.error("9999", "未匹配到合法授权通道!");
        }
        log.info("单张发票信息查询,获取到纳税人信息系为:{}", JsonUtils.getInstance().toJsonString(taxpayerInfo));
        return taxInvoiceFactory.getInvoiceInfoHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).getSingleInvoiceInfo(invoiceInfoReq, taxpayerInfo);
    }
}
