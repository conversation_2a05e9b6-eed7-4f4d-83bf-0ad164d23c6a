package com.dxhy.invoice.controller;


import com.dxhy.invoice.factory.TaxInvoiceFactory;
import com.dxhy.invoice.service.TaxpayerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 *   商品信息  附加要素 场景模板与底层对接接口（目前不对接通道，仅在本系统维护）
 */

@RestController
@RequestMapping("/invoiceItem")
@Slf4j
public class InvoiceItemController {



    @Resource
    private TaxInvoiceFactory taxInvoiceFactory;
    @Resource
    private TaxpayerInfoService taxpayerInfoService;

    private static final String LOG_MSG ="商品信息";

    private static final String LOG_MSG_FJYS ="附加要素";

    private static final String LOG_MSG_CJMB ="场景模板";


    /**
     * 新增商品信息
     * @param
     * @return
     */
    /*@PostMapping("/addInvoiceItem")
    public R addInvoiceItem(@RequestBody ItemInfoEntity itemInfoEntity){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"商品信息新增",JsonUtils.getInstance().toJsonString(itemInfoEntity));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(itemInfoEntity.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).addInvoiceItem(itemInfoEntity,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG,"新增",e);
            return R.error("9999","商品信息新增异常,请联系管理员");
        }

    }*/


    /**
     * 附加要素列表查询
     * @param
     * @return
     */
    /*@PostMapping("/selectAdditionElementList")
    public R selectAdditionElementList(@RequestBody AdditionElementSaveDTO additionElementSaveDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG,"附加要素列表查询",JsonUtils.getInstance().toJsonString(additionElementSaveDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(additionElementSaveDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).selectAdditionElementList(additionElementSaveDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_FJYS,"附加要素列表查询",e);
            return R.error("9999","附加要素列表查询异常,请联系管理员");
        }

    }*/


    /**
     * 新增附加要素
     * @param
     * @return
     */
    /*@PostMapping("/addAdditionElement")
    public R addAdditionElement(@RequestBody AdditionElementSaveDTO additionElementSaveDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_FJYS,"附加要素新增",JsonUtils.getInstance().toJsonString(additionElementSaveDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(additionElementSaveDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).addAdditionElement(additionElementSaveDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_FJYS,"附加要素新增",e);
            return R.error("9999","附加要素新增异常,请联系管理员");
        }

    }*/

    /**
     * 修改附加要素
     * @param
     * @return
     */
    /*@PostMapping("/updateAdditionElement")
    public R updateAdditionElement(@RequestBody AdditionElementSaveDTO additionElementSaveDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_FJYS,"附加要素修改",JsonUtils.getInstance().toJsonString(additionElementSaveDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(additionElementSaveDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).updateAdditionElement(additionElementSaveDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_FJYS,"附加要素修改",e);
            return R.error("9999","附加要素修改异常,请联系管理员");
        }

    }*/

    /**
     * 删除附加要素
     * @param
     * @return
     */
    /*@PostMapping("/deleteAdditionElement")
    public R deleteAdditionElement(@RequestBody AdditionElementSaveDTO additionElementSaveDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_FJYS,"附加要素删除",JsonUtils.getInstance().toJsonString(additionElementSaveDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(additionElementSaveDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).deleteAdditionElement(additionElementSaveDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_FJYS,"附加要素删除",e);
            return R.error("9999","附加要素删除异常,请联系管理员");
        }

    }*/

    /**
     * 场景模板列表查询
     * @param
     * @return
     */
    /*@PostMapping("/selectSceneTemplateList")
    public R selectSceneTemplateList(@RequestBody HuiqiSceneTemplateDTO huiqiSceneTemplateDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_CJMB,"场景模板列表查询",JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(huiqiSceneTemplateDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).selectSceneTemplateList(huiqiSceneTemplateDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_CJMB,"场景模板列表查询",e);
            return R.error("9999","场景模板列表查询异常,请联系管理员");
        }

    }*/


    /**
     * 新增场景模板
     * @param
     * @return
     */
    /*@PostMapping("/addSceneTemplate")
    public R addSceneTemplate(@RequestBody HuiqiSceneTemplateDTO huiqiSceneTemplateDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_CJMB,"场景模板新增",JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(huiqiSceneTemplateDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).addSceneTemplate(huiqiSceneTemplateDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_CJMB,"场景模板新增",e);
            return R.error("9999","场景模板新增异常,请联系管理员");
        }

    }*/

    /**
     * 修改场景模板
     * @param
     * @return
     */
    /*@PostMapping("/updateSceneTemplate")
    public R updateSceneTemplate(@RequestBody HuiqiSceneTemplateDTO huiqiSceneTemplateDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_CJMB,"场景模板修改",JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(huiqiSceneTemplateDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).updateSceneTemplate(huiqiSceneTemplateDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_CJMB,"场景模板修改",e);
            return R.error("9999","场景模板修改异常,请联系管理员");
        }

    }*/

    /**
     * 删除场景模板
     * @param
     * @return
     */
    /*@PostMapping("/deleteSceneTemplate")
    public R deleteSceneTemplate(@RequestBody HuiqiSceneTemplateDTO huiqiSceneTemplateDTO){
        try {
            log.info("{}--{} 请求报文:{}",LOG_MSG_CJMB,"场景模板删除",JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO));
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.getTaxpayerInfo(huiqiSceneTemplateDTO.getBaseNsrsbh());
            if(taxpayerInfo == null || StringUtils.isBlank(taxpayerInfo.getNsrsbh())){
                return R.error("9999","纳税人识别号未维护!");
            }
            return   taxInvoiceFactory.getInvoiceItemHandler(TaxTypeEnum.getTaxType(taxpayerInfo.getKpfs())).deleteSceneTemplate(huiqiSceneTemplateDTO,taxpayerInfo);


        } catch (Exception e) {
            log.error("{}--{} 处理异常 ",LOG_MSG_CJMB,"场景模板删除",e);
            return R.error("9999","场景模板删除异常,请联系管理员");
        }

    }*/

}
