package com.dxhy.invoice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
@SpringBootApplication
@EnableScheduling
public class EimsInvoiceApplication extends SpringBootServletInitializer {

	public static void main(String[] args) {
        SpringApplication.run(EimsInvoiceApplication.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(EimsInvoiceApplication.class);
	}
}