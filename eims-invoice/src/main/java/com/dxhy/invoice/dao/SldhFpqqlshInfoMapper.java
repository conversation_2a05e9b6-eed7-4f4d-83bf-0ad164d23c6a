package com.dxhy.invoice.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.invoice.entity.SldhFpqqlshInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SldhFpqqlshInfoMapper extends BaseMapper<SldhFpqqlshInfo> {

    SldhFpqqlshInfo getOneByFpqqlsh(@Param("fpqqlsh") String fpqqlsh);

    int updateByFpqqlsh(@Param("fpqqlsh") String fpqqlsh);

}
