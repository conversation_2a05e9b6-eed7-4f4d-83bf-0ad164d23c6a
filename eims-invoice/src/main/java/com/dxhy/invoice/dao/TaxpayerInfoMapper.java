package com.dxhy.invoice.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.invoice.entity.TaxpayerInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @description 针对表【commodity_code(商品编码表)】的数据库操作Mapper
 * @createDate 2022-06-07 14:16:46
 * @Entity scaninvoice.domain.CommodityCode
 */
@Mapper
public interface TaxpayerInfoMapper extends BaseMapper<TaxpayerInfo> {

    int saveSldhAndFpqqlsh(@Param("sldh") String sldh, @Param("fpqqlsh") String fpqqlsh);

    String getSldhByFpqqlsh(@Param("fpqqlsh") String fpqqlsh);

    int updateStatusByFpqqlsh(@Param("fpqqlsh") String fpqqlsh);

}




