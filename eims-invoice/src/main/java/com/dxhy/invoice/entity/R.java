package com.dxhy.invoice.entity;



import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 *@date 2022年6月23日 下午12:59:00
 * <AUTHOR>
 */
public class R extends HashMap<String, Object> {

    public R() {
        put("code", "0000");
        put("msg", "success");
    }

    public R(boolean userMessage) {
        if(userMessage) {
            put("code", "0000");
            put("msg", "success");
        }else {
            put("code", "9999");
            put("msg", "failure");
        }
    }





    public static R ok(String code, String msg) {
        R r = new R();
        r.put("code", code);
        r.put("msg", msg);
        return r;
    }
    public static R error(String code, String msg) {
        R r = new R();
        r.put("code", code);
        r.put("msg", msg);
        return r;
    }
    public static R error(String msg) {
        R r = new R();
        r.put("code", "9999");
        r.put("msg", msg);
        return r;
    }


    public static R ok(Map<String, Object> map) {
        R r = new R();
        r.putAll(map);
        return r;
    }
    

    public static R ok() {
        return new R();
    }

    @Override
    public R put(String key, Object value) {
        super.put(key, value);
        return this;
    }
}
