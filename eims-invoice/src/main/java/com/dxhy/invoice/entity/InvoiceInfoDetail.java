package com.dxhy.invoice.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: admin
 * @Date: 2023/8/21 10:37
 * @Description:
 */
@Data
public class InvoiceInfoDetail implements Serializable {
    // 项目序号
    private Integer XMXH;
    // 发票行性质
    private String FPHXZ;
    // 商品编码
    private String SPBM;
    //优惠政策标识
    private String YHZCBS;
    // 项目名称
    private String XMMC;
    // 规格型号
    private String GGXH;
    //单位
    private String DW;
    //项目数量
    private String XMSL;
    //项目单价
    private String XMDJ;
    // 项目金额
    private String XMJE;
    // 含税金额
    private String HSJE;
    // 税率
    private String SL;
    // 税额
    private String SE;
    //备用字段1
    private String BYZD1;
    //备用字段2
    private String BYZD2;
    //备用字段3
    private String BYZD3;


}
