package com.dxhy.invoice.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: admin
 * @Date: 2023/8/21 10:37
 * @Description:
 */
@Data
public class InvoiceDifferenceDetail implements Serializable {
    // 序号
    private Integer XH;
    // 票种类型
    private String PZLX;
    // 发票代码
    private String FPDM;
    // 发票号码
    private String FPHM;
    // 纸质发票号码
    private String ZZFPHM;
    // 凭证号码
    private String PZHM;
    // 开具日期
    private String KJRQ;
    // 合计金额
    private String HJJE;
    // 扣除额
    private String KCE;
    // 备注
    private String BZ;
    // 录入方式 手工录入、勾选录入、模版录入
    private String LRFS;
    //本次扣除金额
    private String BCKCJE;
    //凭证合计金额
    private String PZHJJE;
}
