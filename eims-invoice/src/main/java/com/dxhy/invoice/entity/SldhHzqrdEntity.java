package com.dxhy.invoice.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 受理单号关联红字确认单信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-24 13:51:54
 */
@Data
@TableName("sldh_hzqrd")
public class SldhHzqrdEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 
	 */
	private String sldh;
	/**
	 * 
	 */
	private String hzfpxxqrdbh;
	/**
	 * 
	 */
	private String hzqrxxztdm;
	/**
	 * 
	 */
	private String uuid;

}
