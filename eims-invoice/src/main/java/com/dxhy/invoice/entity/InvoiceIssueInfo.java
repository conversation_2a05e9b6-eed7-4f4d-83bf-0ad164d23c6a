package com.dxhy.invoice.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: admin
 * @Date: 2023/8/21 10:23
 * @Description:
 */
@Data
public class InvoiceIssueInfo implements Serializable{

    //发票请求唯一流水号
    private String FPQQLSH;
    //订单号
    private String DDH;
    //编码表版本号
    private String BMB_BBH;
    //销售方纳税人识别号
    private String XSF_NSRSBH;
    //销售方名称
    private String XSF_MC;
    //销售方地址
    private String XSF_DZ;
    //销售方电话
    private String XSF_DH;
    //销售方银行
    private String XSF_YH;
    //销售方银行账号
    private String XSF_YHZH;
    //购买方纳税人识别号
    private String GMF_NSRSBH;
    //购买方名称
    private String GMF_MC;
    //购买方地址
    private String GMF_DZ;
    //购买方电话
    private String GMF_DH;
    //购买方银行
    private String GMF_YH;
    //购买方银行账号
    private String GMF_YHZH;
    //购买方自然人标识
    private String GMF_ZRRBS;
    //发票种类代码 01专票   02普票
    private String FPZLDM;
    //开票类型 0：蓝票 1：红票
    private String KPLX;
    //差额征税类型代码
    private String CEZSLXDM;
    //增值税即征即退代码
    private String ZZSJZJTDM;
    //减按征收类型代码
    private String JAZSLXDM;
    //出口退税类型代码
    private String CKTSLXDM;
    //价税合计
    private String JSHJ;
    //合计金额
    private String HJJE;
    //合计税额
    private String HJSE;
    //含税不含税标志
    private String HSBZ;
    //扣除额
    private String KCE;
    //备注
    private String BZ;
    //备用字段1
    private String BYZD1;
    //备用字段2
    private String BYZD2;
    //备用字段3
    private String BYZD3;
    //备用字段4
    private String BYZD4;
    //备用字段5
    private String BYZD5;
    //开具理由
    private String KJLY;
    //开票账号（全电平台登录用户名）
    private String KPRZH;
    //是否开具大额发票(不确定是哪个，接口文档都有)
    private String DEKPBZ;
    //是否开具大额发票(不确定是哪个，接口文档都有)
    private String IS_DAE;
    //红字发票信息确认单编号
    private String HZFPXXQRDBH;
    //原发票号码
    private String YFP_HM;
    //原发票代码
    private String YFP_DM;
    //原开票日期
    private String YKPRQ;
    //原发票种类代码
    private String YFPZL_DM;

    //发票明细集合
    private List<InvoiceInfoDetail> INVOICE_DETAIL;
    //附加要素集合
    private List<InvoiceQdFjys> FJYSLIST;
    //差额明细
    private List<InvoiceDifferenceDetail> DIFFERENCE_DETAIL;
    //不动产租赁
    private InvoiceLeaseInfo LEASE_INFO;
    //不动产销售
    private InvoiceSaleInfo SALE_INFO;
    //旅客运输
    private List<InvoiceTravelersDetail> TRAVELERS_DETAIL;
    //建筑服务
    private InvoiceConstructInfo CONSTRUCT_INFO;
    //货物运输服务
    private List<InvoiceTransportDetail> TRANSPORT_DETAIL;
    //二手车
    private List<InvoiceOldCarSellDetail> OLDCARSELL_DETAIL;
    //机动车
    private List<InvoiceVehicleSellDetail> VEHICLESELL_DETAIL;

}
