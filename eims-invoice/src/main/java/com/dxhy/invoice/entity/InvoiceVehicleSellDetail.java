package com.dxhy.invoice.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: admin
 * @Date: 2023/8/21 10:43
 * @Description:
 */
@Data
public class InvoiceVehicleSellDetail implements Serializable {
    //产地
    private String CD;
    //车辆吨位
    private String CLDW;
    //车辆类型代码
    private String CLLX_DM;
    //车辆识别代号
    private String CLSBDH;
    //厂牌型号
    private String CPXH;
    //发动机号码
    private String FDJHM;
    //合格证号
    private String HGZH;
    //车辆识别代号uuid
    private String JDCTZCLSBDHUUID;
    //进口证明书号
    private String JKZMSH;
    //生产企业名称
    private String SCQYMC;
    //商检单号
    private String SJDH;
    //完税凭证号码
    private String WSPZHM;
    //限乘人数
    private String XCRS;
    //备用字段1
    private String BYZD1;
    //备用字段2
    private String BYZD2;
    //备用字段3
    private String BYZD3;
}
