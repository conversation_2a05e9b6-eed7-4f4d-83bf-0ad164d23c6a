package com.dxhy.invoice.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: admin
 * @Date: 2023/8/21 10:43
 * @Description:
 */
@Data
public class InvoiceOldCarSellDetail implements Serializable {
    //车牌照号
    private String CPZH;
    //登记证号
    private String DJZH;
    //车辆类型代码
    private String CLLX_DM;
    //车辆识别代号
    private String CLSBDH;
    //厂牌型号
    private String CPXH;
    //转入地车辆管理所名称
    private String ZRDCLGLSMC;
    //经营单位纳税人名称
    private String JYDWNSRMC;
    //经营单位纳税人识别号
    private String JYDWNSRSBH;
    //经营单位地址
    private String JYDWDZ;
    //经营单位开户银行
    private String JYDWKHYH;
    //经营单位银行账号
    private String JYDWYHZH;
    //经营单位电话
    private String JYDWDH;
    //拍卖单位纳税人名称
    private String PMDWNSRMC;
    //拍卖单位纳税人识别号
    private String PMDWNSRSBH;
    //拍卖单位地址
    private String PMDWDZ;
    //拍卖单位开户银行
    private String PMDWKHYH;
    //拍卖单位银行账号
    private String PMDWYHZH;
    //拍卖单位电话
    private String PMDWDH;
    //二手车市场纳税人名称
    private String ESCSCNSRMC;
    //二手车市场纳税人识别号
    private String ESCSCNSRSBH;
    //二手车市场地址
    private String ESCSCDZ;
    //二手车市场开户银行
    private String ESCSCKHYH;
    //二手车市场银行账号
    private String ESCSCYHZH;
    //二手车市场电话
    private String ESCSCDH;
}
