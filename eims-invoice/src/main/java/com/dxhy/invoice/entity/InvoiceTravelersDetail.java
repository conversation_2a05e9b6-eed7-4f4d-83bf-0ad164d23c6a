package com.dxhy.invoice.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: admin
 * @Date: 2023/8/21 10:43
 * @Description:
 */
@Data
public class InvoiceTravelersDetail implements Serializable {
    //出行人序号
    private String CXRXH;
    //出行人
    private String CXR;
    //出行人证件类型
    private String CXRZJLXDM;
    //身份证件号码
    private String SFZJHM;
    //出行日期
    private String CXRQ;
    //出发地
    private String CFD;
    //到达地
    private String DDD;
    //座位等级
    private String ZWDJ;
    //交通工具类型代码 1：飞机 2：火车 3：长途汽车 4：公共交通 5：出租车 6：汽车 7：船舶 9：其他
    private String JTGJLXDM;
}
