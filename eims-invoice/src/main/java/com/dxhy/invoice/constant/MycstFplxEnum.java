package com.dxhy.invoice.constant;

/**
 * 发票类型转换映射（全数通与税航票帮手）
 */
public enum MycstFplxEnum {
    /**
     * 数电专
     */
    SDZ("001","020"),
    /**
     * 数电普
     */
    SDP("002","021"),
    /**
     * 数电机动车
     */
    SDJDC("003","020"),
    /**
     * 数电二手车
     */
    SDESC("104","020"),
    /**
     * 数电纸专
     */
    SDZZ("085","022"),
    /**
     * 数电纸普
     */
    SDZP("086","023"),
    /**
     * 数电纸机动车
     */
    SDZJDC("087","020"),
    /**
     * 数电纸二手车
     */
    SDZESC("088","020"),
    /**
     * 增值税专
     */
    ZZSZ("004","004"),
    /**
     * 机动车销售统一发票
     */
    JDC("005","020"),
    /**
     * 二手车销售统一发票
     */
    ESC("006","020"),
    /**
     * 增值税普
     */
    ZZSP("007","007"),
    /**
     * 增值税电子普通发票
     */
    ZZSDP("026","026"),
    /**
     * 增值税电子专用发票
     */
    ZZSDZ("028","028");
    //大象慧云发票类型
    private final String dxhyFplx;
    //税航票帮手发票类型
    private final String mycstFplx;

    MycstFplxEnum(String dxhyFplx, String mycstFplx) {
        this.dxhyFplx = dxhyFplx;
        this.mycstFplx = mycstFplx;
    }
    public String getDxhyFplx() {
        return this.dxhyFplx;
    }

    public String getMycstFplx() {
        return this.mycstFplx;
    }

    public static MycstFplxEnum getCodeValue(String key) {
        for (MycstFplxEnum item : values()) {
            if (item.getDxhyFplx().equals(key)) {
                return item;
            }
        }
        return null;
    }
}
