package com.dxhy.invoice.constant;

/**
 * 交通工具类型转换映射（全数通与税航票帮手）
 */
public enum MycstJtgjlxEnum {
    /**
     * 飞机
     */
    FJ("1","飞机"),
    /**
     * 火车
     */
    HC("2","火车"),
    /**
     * 长途汽车
     */
    CTQC("3","长途汽车"),
    /**
     * 公共交通
     */
    GGJT("4","公共交通"),
    /**
     * 出租车
     */
    CZC("5","出租车"),
    /**
     * 汽车
     */
    QC("6","汽车"),
    /**
     * 船舶
     */
    CB("7","船舶"),
    /**
     * 其他
     */
    QT("9","其他");

    //大象慧云交通工具类型
    private final String dxhyJtgjlx;
    //税航票帮手发票类型
    private final String mycstJtgjlx;

    MycstJtgjlxEnum(String dxhyJtgjlx, String mycstJtgjlx) {
        this.dxhyJtgjlx = dxhyJtgjlx;
        this.mycstJtgjlx = mycstJtgjlx;
    }
    public String getDxhyJtgjlx() {
        return this.dxhyJtgjlx;
    }

    public String getMycstJtgjlx() {
        return this.mycstJtgjlx;
    }

    public static MycstJtgjlxEnum getCodeValue(String key) {
        for (MycstJtgjlxEnum item : values()) {
            if (item.getDxhyJtgjlx().equals(key)) {
                return item;
            }
        }
        return null;
    }
}
