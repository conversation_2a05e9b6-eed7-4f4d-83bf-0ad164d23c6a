package com.dxhy.invoice.constant;

/**
 * 发票类型转换映射（全数通与税航票帮手）
 */
public enum DxFplxEnum {
    /**
     * 数电专
     */
    SDZ("001","81"),
    /**
     * 数电普
     */
    SDP("002","82"),
    /**
     * 数电机动车
     */
    SDJDC("003","83"),
    /**
     * 数电二手车
     */
    SDESC("104","84"),
    /**
     * 数电纸专
     */
    SDZZ("085","85"),
    /**
     * 数电纸普
     */
    SDZP("086","86"),
    /**
     * 数电纸机动车
     */
    SDZJDC("087","87"),
    /**
     * 数电纸二手车
     */
    SDZESC("088","88"),
    /**
     * 增值税专
     */
    ZZSZ("004","01"),
    /**
     * 机动车销售统一发票
     */
    JDC("005","03"),
    /**
     * 二手车销售统一发票
     */
    ESC("006","15"),
    /**
     * 增值税普
     */
    ZZSP("007","04"),
    /**
     * 增值税电子普通发票
     */
    ZZSDP("026","10"),
    /**
     * 增值税电子专用发票
     */
    ZZSDZ("028","08");
    //大象慧云发票类型
    private final String myFplx;
    //税航票帮手发票类型
    private final String dxFplx;

    DxFplxEnum(String myFplx, String dxFplx) {
        this.myFplx = myFplx;
        this.dxFplx = dxFplx;
    }
    public String getMyFplx() {
        return this.myFplx;
    }

    public String getDxFplx() {
        return this.dxFplx;
    }

    public static DxFplxEnum getCodeValue(String key) {
        for (DxFplxEnum item : values()) {
            if (item.getMyFplx().equals(key)) {
                return item;
            }
        }
        return null;
    }
}
