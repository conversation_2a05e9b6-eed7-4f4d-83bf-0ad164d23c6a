package com.dxhy.invoice.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@RefreshScope
public class InvoiceConfig {


    /**
     * 演示环境标识
     */
    @Value("${ys.remark}")
    private Boolean ysRemark;
    /**
     * 演示环境税号
     */
    @Value("${ys.taxNos}")
    private String taxNos;
    /**
     * 演示环境 发票前缀
     */
    @Value("${ys.fppre}")
    private String fppre;


    /**
     * 机器人开票地址
     */
    @Value("${rpa.invoiceIssueUrl}")
    private String rpaInvoiceIssueUrl;
    /**
     * 机器人开票结果查询
     */
    @Value("${rpa.queryInvoiceInfoUrl}")
    private String queryInvoiceInfoUrl;
    /**
     * 机器人红字确认单申请
     */
    @Value("${rpa.generateRedConfirmUrl}")
    private String rpaGenerateRedConfirmUrl;

    /**
     * 机器人红字确认单获取结果
     */
    @Value("${rpa.redConfirmResultUrl}")
    private String rpaRedConfirmResultUrl;

    /**
     * 机器人红字确认单获取结果
     */
    @Value("${rpa.redInvoiceIssueUrl}")
    private String rpaRedInvoiceIssueUrl;

    /**
     * 机器人红字确认单获取结果
     */
    @Value("${rpa.redInvoiceResultUrl}")
    private String rpaRedInvoiceResultUrl;

    /**
     * 机器人 红字确认单列表获取
     */
    @Value("${rpa.redConfirmListUrl}")
    private String rpaRedConfirmListUrl;

    /**
     * 机器人 红字确认单 单条查询
     */
    @Value("${rpa.redConfirmInfoUrl}")
    private String rpaRedConfirmInfoUrl;

    /**
     * 机器人 红字确认单信息处理
     */
    @Value("${rpa.redConfirmHandleUrl}")
    private String rpaRedConfirmHandleUrl;
    /**
     * 机器人 发票基础信息查询
     */
    @Value("${rpa.invoiceInfoUrl}")
    private String rpaInvoiceInfoUrl;

    /**
     * 机器人 发票单张信息查询
     */
    @Value("${rpa.singleInvoiceInfoUrl}")
    private String rpaSingleInvoiceInfoUrl;

    /**
     * 机器人 附加要素列表查询
     */
    @Value("${rpa.selectAdditionElementListUrl}")
    private String rpaSelectAdditionElementListUrl;

    /**
     * 机器人 附加要素新增
     */
    @Value("${rpa.addAdditionElementUrl}")
    private String rpaAddAdditionElementUrl;

    /**
     * 机器人 附加要素修改
     */
    @Value("${rpa.updateAdditionElementUrl}")
    private String rpaUpdateAdditionElementUrl;

    /**
     * 机器人 附加要素删除
     */
    @Value("${rpa.deleteAdditionElementUrl}")
    private String rpaDeleteAdditionElementUrl;

    /**
     * 机器人 场景模板列表查询
     */
    @Value("${rpa.selectSceneTemplateListUrl}")
    private String rpaSelectSceneTemplateListUrl;

    /**
     * 机器人 场景模板新增
     */
    @Value("${rpa.addSceneTemplateUrl}")
    private String rpaAddSceneTemplateUrl;

    /**
     * 机器人 场景模板修改
     */
    @Value("${rpa.updateSceneTemplateUrl}")
    private String rpaUpdateSceneTemplateUrl;

    /**
     * 机器人 场景模板删除
     */
    @Value("${rpa.deleteSceneTemplateUrl}")
    private String rpaDeleteSceneTemplateUrl;

    /**
     * 机器人 场景模板修改
     */
    @Value("${rpa.getEwmUrl}")
    private String getEwmUrl;

    /**
     * 机器人 场景模板删除
     */
    @Value("${rpa.getEwmStatusUrl}")
    private String getEwmStatusUrl;


    /**
     * 慧电票接口开具蓝字发票
     */
    @Value("${interface.blueInvoiceIssueUrl}")
    private String blueInvoiceIssueUrl;

    /**
     * 慧电票接口获取蓝字发票信息
     */
    @Value("${interface.selectBlueInvoiceInfo}")
    private String selectBlueInvoiceInfoUrl;

    /**
     * 慧电票接口红字确认单申请
     */
    @Value("${interface.generateRedConfirmUrl}")
    private String generateRedConfirmUrl;

    /**
     * 慧电票接口红字确认单获取结果
     */
    @Value("${interface.redConfirmResultUrl}")
    private String redConfirmResultUrl;

    /**
     * 慧电票接口红字确认单获取结果
     */
    @Value("${interface.redInvoiceIssueUrl}")
    private String redInvoiceIssueUrl;

    /**
     * 慧电票接口红字确认单获取结果
     */
    @Value("${interface.redInvoiceResultUrl}")
    private String redInvoiceResultUrl;

    /**
     * 慧电票接口 红字确认单列表获取
     */
    @Value("${interface.redConfirmListUrl}")
    private String redConfirmListUrl;

    /**
     * 慧电票接口 红字确认单 单条查询
     */
    @Value("${interface.redConfirmInfoUrl}")
    private String redConfirmInfoUrl;

    /**
     * 慧电票接口 红字确认单信息处理
     */
    @Value("${interface.redConfirmHandleUrl}")
    private String redConfirmHandleUrl;

    /**
     * 慧电票接口 发票基础信息查询
     */
    @Value("${interface.invoiceInfoUrl}")
    private String invoiceInfoUrl;

    /**
     * 慧电票接口 发票单张信息查询
     */
    @Value("${interface.singleInvoiceInfoUrl}")
    private String singleInvoiceInfoUrl;

    /**
     * 慧电票接口 新增商品信息
     */
    @Value("${interface.addInvoiceItemUrl}")
    private String addInvoiceItemUrl;


    /**
     * 慧电票接口 附加要素列表查询
     */
    @Value("${interface.selectAdditionElementListUrl}")
    private String selectAdditionElementListUrl;

    /**
     * 慧电票接口 附加要素新增
     */
    @Value("${interface.addAdditionElementUrl}")
    private String addAdditionElementUrl;

    /**
     * 慧电票接口 附加要素修改
     */
    @Value("${interface.updateAdditionElementUrl}")
    private String updateAdditionElementUrl;

    /**
     * 慧电票接口 附加要素删除
     */
    @Value("${interface.deleteAdditionElementUrl}")
    private String deleteAdditionElementUrl;

    /**
     * 慧电票接口 场景模板列表查询
     */
    @Value("${interface.selectSceneTemplateListUrl}")
    private String selectSceneTemplateListUrl;

    /**
     * 慧电票接口 场景模板新增
     */
    @Value("${interface.addSceneTemplateUrl}")
    private String addSceneTemplateUrl;

    /**
     * 慧电票接口 场景模板修改
     */
    @Value("${interface.updateSceneTemplateUrl}")
    private String updateSceneTemplateUrl;

    /**
     * 慧电票接口 场景模板删除
     */
    @Value("${interface.deleteSceneTemplateUrl}")
    private String deleteSceneTemplateUrl;

    /**
     * 百望九赋接口开具蓝字发票
     */
    @Value("${bwjf.blueInvoiceIssueUrl}")
    private String bwjfBlueInvoiceIssueUrl;
    /**
     * 百望九赋接口获取板式文件
     */
    @Value("${bwjf.InvoiceBswjUrl}")
    private String bwjfInvoiceBswjUrl;

    @Value("${bwjf.appKey}")
    private String bwjfAppKey;
    @Value("${bwjf.appSecret}")
    private String bwjfAppSecret;
    @Value("${bwjf.username}")
    private String bwjfUsername;
    @Value("${bwjf.password}")
    private String bwjfPassword;
    @Value("${bwjf.userSalt}")
    private String bwjfUserSalt;
    /**
     * 大象开放平台对接
     */
    @Value("${dxKfpt.kpUrl}")
    private String lzfpKpUrl;
    @Value("${dxKfpt.hwysKpUrl}")
    private String hwysKpUrl;
    @Value("${dxKfpt.bdczlKpUrl}")
    private String bdczlKpUrl;
    @Value("${dxKfpt.bdcxsKpUrl}")
    private String bdcxsKpUrl;
    @Value("${dxKfpt.jzfwKpUrl}")
    private String jzfwKpUrl;
    @Value("${dxKfpt.ncpsgKpUrl}")
    private String ncpsgKpUrl;
    @Value("${dxKfpt.zcncpxsKpUrl}")
    private String zcncpxsKpUrl;
    @Value("${dxKfpt.lkysKpUrl}")
    private String lkysKpUrl;
    @Value("${dxKfpt.escKpUrl}")
    private String escKpUrl;
    @Value("${dxKfpt.jdcKpUrl}")
    private String jdcKpUrl;
    @Value("${dxKfpt.fileDownload}")
    private String fileDownload;
    @Value("${dxKfpt.selectInvoiceInfos}")
    private String selectInvoiceInfos;
    @Value("${dxKfpt.querySingleInvoice}")
    private String querySingleInvoice;

    @Value("${dxKfpt.redConfirmApply}")
    private String redConfirmApply;
    @Value("${dxKfpt.redInvoiceIssue}")
    private String dxRedInvoiceIssue;
    @Value("${dxKfpt.getRedConfirmList}")
    private String getRedConfirmList;
    @Value("${dxKfpt.getRedConfirmInfo}")
    private String getRedConfirmInfo;
    @Value("${dxKfpt.redConfirmHandle}")
    private String redConfirmHandle;
    @Value("${hwcloud.downloadUrl}")
    private String downloadUrl;

    /**
     * 税航票帮手获取token地址
     */
    @Value("${mycst.getTokenUrl}")
    private String mycstGetTokenUrl;
    /**
     * 税航票帮手数电发票开具地址
     */
    @Value("${mycst.fpkjUrl}")
    private String mycstFpkjUrl;
    /**
     * 税航票帮手用户编码
     */
    @Value("${mycst.userCode}")
    private String mycstUserCode;
    /**
     * 税航票帮手数电发票开具地址
     */
    @Value("${mycst.userPwd}")
    private String mycstUserPwd;


    /**
     * 税航票帮手电子税局登录
     */
    @Value("${mycst.loginUrl}")
    private String loginUrl;
    /**
     * 税航票帮手设置登录验证码
     */
    @Value("${mycst.setSmsUrl}")
    private String setSmsUrl;
    /**
     * 税航票帮手 获取实名二维码
     */
    @Value("${mycst.confirmQrcodeUrl}")
    private String confirmQrcodeUrl;
    /**
     * 税航票帮手 实名状态查询
     */
    @Value("${mycst.confirmStatusUrl}")
    private String confirmStatusUrl;

    /**
     * 税航票帮手 新增确认单
     */
    @Value("${mycst.redConfirmApply}")
    private String mycstRedConfirmApply;

    /**
     * 税航票帮手 确认单列表查询
     */
    @Value("${mycst.redConfirmList}")
    private String mycstRedConfirmList;

    /**
     * 税航票帮手 确认单明细查询
     */
    @Value("${mycst.redConfirmInfo}")
    private String mycstRedConfirmInfo;

    /**
     * 税航票帮手 确认单操作  撤销/确认 等
     */
    @Value("${mycst.redConfirmHandle}")
    private String mycstRedConfirmHandle;

    /**
     * 税航票帮手 异步获取开票结果
     */
    @Value("${mycst.getKpjgUrl}")
    private String mycstGetKpjgUrl;

    /**
     * 税航票帮手 发票列表获取
     */
    @Value("${mycst.queryInvoiceList}")
    private String mycstQueryInvoiceList;


}
