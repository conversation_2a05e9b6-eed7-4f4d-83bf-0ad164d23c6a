# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
  port: 18115
  servlet:
    context-path: /eInvoice

spring:
  profiles:
    active: @profileActive@
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true

  freemarker:
    suffix: .html
    request-context-attribute: request
  # 同一个domain里面的MBean要求name唯一 So <<
  jmx:
    default-domain: dxyun-uic
  # Nacos config
  cloud:
    nacos:
      config:
        # Nacos config 地址
        server-addr: @nacos.server.addr@
        # Nacos config 命名空间,对应配置中的名称(sims_order_namespace)
        namespace: @nacos.order.namespace@
        # Nacos config 分组
        group: eims-invoice
        # Nacos config 登录用户名
        username: @nacos.username@
        # Nacos config 登录密码
        password: @nacos.password@
        # Nacos config 配置文件前缀
        prefix: eims-invoice
        # Nacos config 配置文件后缀,拼接完URL需要对应NacosServer中的dataId对应配置,${prefix}-${spring.profiles.active}.${file-extension}
        file-extension: yaml
        extension-configs:
          #Nacos config 配置 mysql数据库,数据库只能使用一个,要么mysql,要么Oracle,要么weblogic方式
          - data-id: eims-invoice-mysql-${spring.profiles.active}.yaml
            group: eims-invoice
            refresh: true
          #Nacos config 配置 redis,redis使用方式只能用一个,要么redis,要么redis-sentinel
          - data-id: eims-invoice-redis-${spring.profiles.active}.yaml
            group: eims-invoice
            refresh: true
      # Nacos discovery 配置
      discovery:
        # Nacos discovery 服务地址
        server-addr: @nacos.server.addr@

#mybatis
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  #typeAliasesPackage: com.dxhy.invoice.digitalAccount.dao,com.dxhy.invoice.digitalAccount.entity,com.dxhy.invoice.digitalAccount.vo,com.dxhy.invoice.digitalAccount.modules.scaninvoice.domain
  typeAliasesPackage: com.dxhy.invoice.entity
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 0
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    #capital-mode: true
    # Sequence序列接口实现类配置
    #key-generator: com.baomidou.mybatisplus.incrementer.OracleKeyGenerator
    #逻辑删除配置
    logic-delete-value: -1
    logic-not-delete-value: 0
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

