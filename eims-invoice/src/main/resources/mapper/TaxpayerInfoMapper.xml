<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.invoice.dao.TaxpayerInfoMapper">

    <resultMap id="BaseResultMap" type="com.dxhy.invoice.entity.TaxpayerInfo">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="qymc" column="qymc" jdbcType="VARCHAR"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="jrzh" column="jrzh" jdbcType="VARCHAR"/>
        <result property="jrmm" column="jrmm" jdbcType="VARCHAR"/>
        <result property="sjhm" column="sjhm" jdbcType="VARCHAR"/>
        <result property="appid" column="appid" jdbcType="VARCHAR"/>
        <result property="appsecret" column="appsecret" jdbcType="VARCHAR"/>
        <result property="yhdm" column="yhdm" jdbcType="VARCHAR"/>
        <result property="gsdm" column="gsdm" jdbcType="VARCHAR"/>
        <result property="kpzddm" column="kpzddm" jdbcType="VARCHAR"/>
        <result property="kpfs" column="kpfs" jdbcType="VARCHAR"/>
    </resultMap>


    <insert id="saveSldhAndFpqqlsh">
        insert into sldh_fpqqlsh (sldh ,fpqqlsh,is_delete)
        values (#{sldh}, #{fpqqlsh}, '0')
    </insert>

    <select id="getSldhByFpqqlsh" resultType="java.lang.String">
        select sldh
        FROM sldh_fpqqlsh
        where fpqqlsh = #{fpqqlsh}
          and is_delete = '0'
    </select>

    <update id="updateStatusByFpqqlsh">
        delete
        from sldh_fpqqlsh
        where fpqqlsh = #{fpqqlsh}
          and is_delete = '0'
    </update>


</mapper>
