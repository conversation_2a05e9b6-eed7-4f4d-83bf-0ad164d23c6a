<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.invoice.dao.SldhFpqqlshInfoMapper">

    <resultMap id="BaseResultMap" type="com.dxhy.invoice.entity.SldhFpqqlshInfo">
        <result property="sldh" column="sldh" jdbcType="VARCHAR"/>
        <result property="fpqqlsh" column="fpqqlsh" jdbcType="VARCHAR"/>
        <result property="qdfphm" column="qdfphm" jdbcType="VARCHAR"/>
        <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
        <result property="kprq" column="kprq" jdbcType="DATE"/>
        <result property="is_delete" column="is_delete" jdbcType="VARCHAR"/>
    </resultMap>
    <update id="updateByFpqqlsh">
        update sldh_fpqqlsh
        set is_delete = '1'
        where fpqqlsh = #{fpqqlsh}
    </update>

    <select id="getOneByFpqqlsh" resultMap="BaseResultMap" parameterType="java.lang.String">
        select *
        FROM sldh_fpqqlsh
        where fpqqlsh = #{fpqqlsh}
          and is_delete = '0'
    </select>


</mapper>
