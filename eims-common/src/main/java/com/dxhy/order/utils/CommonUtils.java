package com.dxhy.order.utils;

import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.constant.ProvinceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;

/**
 * 订单号拆分处理
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
@Slf4j
public class CommonUtils {
    private static final String LOGGER_MSG = "(通用工具类转换)";
    
    public static final NumberFormat NF = NumberFormat.getPercentInstance();
    
    private static final String CF = "cf";
    
    private static final String DDH_SPLIT_SYMBOL = "_";
    
    
    /**
     * 税率格式化
     *
     * @param sl
     * @return
     */
    public static String formatSl(String sl) {
        DecimalFormat slFormat = new DecimalFormat("######0.00#");
        String resultSl = "";
        if (StringUtils.isNotEmpty(sl)) {
            if (sl.contains(ConfigureConstant.STRING_PERCENT)) {
                resultSl = slFormat.format(Double.parseDouble(sl.replace(ConfigureConstant.STRING_PERCENT, "")) / 100);
            } else if (ConfigureConstant.STRING_BZS.equals(sl) || ConfigureConstant.STRING_MS.equals(sl) || ConfigureConstant.STRING_0.equals(sl)) {
                resultSl = ConfigureConstant.STRING_000;
            } else {
                resultSl = slFormat.format(Double.parseDouble(sl));
            }
        }
    
        return resultSl;
    }
    
    /**
     * 税率格式化为百分比
     *
     * @param sl
     * @return
     */
    public static String formatPercentSl(String sl) {
        if (StringUtils.isNotEmpty(sl)) {
            NF.setMaximumFractionDigits(3);
            sl = NF.format(Double.valueOf(sl));
        }
        return sl;
    }
    
    /**
     * 把包含简称商品拆分出来,去除简称
     *
     * @param spmc
     * @return
     */
    public static String subSpmc(String spmc) {
        
        /**
         * 商品名称不为空,并且是以星号开头
         */
        if (StringUtils.isNotEmpty(spmc) && spmc.startsWith(ConfigureConstant.STRING_STAR)) {
            String[] split = spmc.split(ConfigureConstant.STRING_STAR_PAT);
            //根据星号进行截取,判断数组长度,如果数组长度大于2,说明包含两个星号
            if (split.length > ConfigureConstant.INT_2) {
                //设置拆分规则,保留3次
                String[] split2 = spmc.split(ConfigureConstant.STRING_STAR_PAT, ConfigureConstant.INT_3);
                String newSpmc = split2[2];
                if (StringUtils.isNotEmpty(newSpmc)) {
                    spmc = newSpmc;
                }
            }
        }
        return spmc;
    }
    
    /**
     * 红票金额格式化,返回带负号金额
     * 如果金额为0.00直接返回0.00
     * 如果金额包含负号,直接返回金额
     * 如果不包含负号,拼接负号返回
     *
     * @param je
     * @return
     */
    public static String formatRedInvoiceJe(String je) {
        if (ConfigureConstant.STRING_000.equals(je)) {
            return je;
        }
        if (je.contains(ConfigureConstant.STRING_LINE)) {
            return je;
        } else {
            return ConfigureConstant.STRING_LINE + je;
        }
        
    }
    
    /**
     * 拆分后订单号数据截取工具类
     */
    public static String dealDdh(String yddh) {
        if (yddh.length() > ConfigureConstant.INT_20) {
            int index = yddh.indexOf("cf");
            if (index < 0) {
                return yddh;
            }
            String prefixString = yddh.substring(0, index);
            String suffixString = yddh.substring(index);
            if (suffixString.length() >= ConfigureConstant.INT_20) {
                return suffixString.substring(suffixString.length() - 20);
        
            } else {
                int diff = 20 - suffixString.length();
                prefixString = prefixString.substring(0, diff);
                return prefixString + suffixString;
            }
            
        } else {
            return yddh;
        }
    }
    
    /**
     * 根据清单标志 和明细行数重置清单标志
     */
    public static String getQdbz(String qdbz, int itemLegth) {
        
        //普通发票
        if (OrderInfoEnum.QDBZ_CODE_0.getKey().equals(qdbz) || OrderInfoEnum.QDBZ_CODE_1.getKey().equals(qdbz)) {
            if (itemLegth > ConfigureConstant.SPECIAL_MAX_ITEM_LENGTH) {
                return OrderInfoEnum.QDBZ_CODE_1.getKey();
            } else {
                return OrderInfoEnum.QDBZ_CODE_0.getKey();
            }
            
        }
        //收购发票
        if (OrderInfoEnum.QDBZ_CODE_2.getKey().equals(qdbz) || OrderInfoEnum.QDBZ_CODE_3.getKey().equals(qdbz)) {
            if (itemLegth > ConfigureConstant.SPECIAL_MAX_ITEM_LENGTH) {
                return OrderInfoEnum.QDBZ_CODE_3.getKey();
            } else {
                return OrderInfoEnum.QDBZ_CODE_2.getKey();
            }
        }
    
        //成品油发票
        if (OrderInfoEnum.QDBZ_CODE_4.getKey().equals(qdbz)) {
            return OrderInfoEnum.QDBZ_CODE_4.getKey();
        }
        //机动车专票
        if (OrderInfoEnum.QDBZ_CODE_5.getKey().equals(qdbz) || OrderInfoEnum.QDBZ_CODE_6.getKey().equals(qdbz)) {
            if (itemLegth > ConfigureConstant.SPECIAL_MAX_ITEM_LENGTH) {
                return OrderInfoEnum.QDBZ_CODE_6.getKey();
            } else {
                return OrderInfoEnum.QDBZ_CODE_5.getKey();
            }
        }
        return qdbz;
    }
    
    /**
     * 根据专票特殊类型判断清单标志
     */
    public static String getQdbzBySpecialType(String specialType, int itemLegth) {
        String qdBz = OrderInfoEnum.QDBZ_CODE_0.getKey();
        if (StringUtils.isNotBlank(specialType)) {
            if (OrderInfoEnum.SPECIAL_INVOICE_TYPE_1.getKey().equals(specialType) ||
                    OrderInfoEnum.SPECIAL_INVOICE_TYPE_2.getKey().equals(specialType)) {
                qdBz = OrderInfoEnum.QDBZ_CODE_4.getKey();
            } else if (OrderInfoEnum.SPECIAL_INVOICE_TYPE_3.getKey().equals(specialType) ||
                    OrderInfoEnum.SPECIAL_INVOICE_TYPE_4.getKey().equals(specialType)) {
                if (itemLegth > ConfigureConstant.INT_8) {
                    qdBz = OrderInfoEnum.QDBZ_CODE_6.getKey();
                } else {
                    qdBz = OrderInfoEnum.QDBZ_CODE_5.getKey();
                }
                
            } else if (OrderInfoEnum.SPECIAL_INVOICE_TYPE_0.getKey().equals(specialType)) {
                if (itemLegth > ConfigureConstant.INT_8) {
                    qdBz = OrderInfoEnum.QDBZ_CODE_1.getKey();
                } else {
                    qdBz = OrderInfoEnum.QDBZ_CODE_0.getKey();
                }
            }
        }
        
        return qdBz;
    }
    
    
    /**
     * 发票类型代码转换为内部发票种类代码
     *
     * @param fplxdm
     * @return
     */
    public static String transFpzldm(String fplxdm) {
        String fpzldm;
        if (OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fplxdm) || OrderInfoEnum.ORDER_INVOICE_TYPE_026.getKey().equals(fplxdm)) {
            //电票
            fpzldm = OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fplxdm) || OrderInfoEnum.ORDER_INVOICE_TYPE_004.getKey().equals(fplxdm)) {
            //专票
            fpzldm = OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey().equals(fplxdm) || OrderInfoEnum.ORDER_INVOICE_TYPE_007.getKey().equals(fplxdm)) {
            //普票
            fpzldm = OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fplxdm) || OrderInfoEnum.ORDER_INVOICE_TYPE_005.getKey().equals(fplxdm)) {
            //机动车
            fpzldm = OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fplxdm) || OrderInfoEnum.ORDER_INVOICE_TYPE_006.getKey().equals(fplxdm)) {
            //二手车
            fpzldm = OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fplxdm) || OrderInfoEnum.ORDER_INVOICE_TYPE_028.getKey().equals(fplxdm)) {
            //电专
            fpzldm = OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey();
        } else {
            fpzldm = fplxdm;
        }
//        log.debug("{}新税控发票种类转换为旧版本发票种类,请求参数为:{},输出参数为:{}", LOGGER_MSG, fplxdm, fpzldm);
        return fpzldm;
    }
    
    /**
     * 旧版本发票种类转换为新税控发票类型代码
     *
     * @param fpzldm
     * @return
     */
    public static String transFplxdm(String fpzldm) {
        String fplxdm;
        if (OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_026.getKey().equals(fpzldm)) {
            //电票
            fplxdm = OrderInfoEnum.ORDER_INVOICE_TYPE_026.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_004.getKey().equals(fpzldm)) {
            //专票
            fplxdm = OrderInfoEnum.ORDER_INVOICE_TYPE_004.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_007.getKey().equals(fpzldm)) {
            //普票
            fplxdm = OrderInfoEnum.ORDER_INVOICE_TYPE_007.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_005.getKey().equals(fpzldm)) {
            //机动车
            fplxdm = OrderInfoEnum.ORDER_INVOICE_TYPE_005.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_006.getKey().equals(fpzldm)) {
            //二手车
            fplxdm = OrderInfoEnum.ORDER_INVOICE_TYPE_006.getKey();
        } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_028.getKey().equals(fpzldm)) {
            //电专
            fplxdm = OrderInfoEnum.ORDER_INVOICE_TYPE_028.getKey();
        } else {
            fplxdm = fpzldm;
        }
        log.debug("{}旧版本发票种类转换为新税控发票种类,请求参数为:{},输出参数为:{}", LOGGER_MSG, fpzldm, fplxdm);
        return fplxdm;
    }
    
    
    /**
     * 获取作废标志的对应的名称
     *
     * @param zfbz
     * @return
     */
    public static String getZfbzMc(String zfbz) {
        
        return OrderInfoEnum.INVALID_INVOICE_0.getKey().equals(zfbz) ? OrderInfoEnum.INVALID_INVOICE_0.getValue()
                : OrderInfoEnum.INVALID_INVOICE_1.getKey().equals(zfbz) ? OrderInfoEnum.INVALID_INVOICE_1.getValue()
                : OrderInfoEnum.INVALID_INVOICE_2.getKey().equals(zfbz) ? OrderInfoEnum.INVALID_INVOICE_2.getValue()
                : OrderInfoEnum.INVALID_INVOICE_3.getKey().equals(zfbz) ? OrderInfoEnum.INVALID_INVOICE_3.getValue()
                : OrderInfoEnum.INVALID_INVOICE_4.getKey().equals(zfbz) ? OrderInfoEnum.INVALID_INVOICE_4.getValue()
                : "";
    }
    
    
    /**
     * 获取发票类型的对应的名称
     *
     * @param kplx
     * @return 发票类型
     */
    public static String getKplxMc(String kplx) {
        
        return OrderInfoEnum.INVOICE_BILLING_TYPE_0.getKey().equals(kplx) ? OrderInfoEnum.INVOICE_BILLING_TYPE_0.getValue()
                : OrderInfoEnum.INVOICE_BILLING_TYPE_1.getKey().equals(kplx) ? OrderInfoEnum.INVOICE_BILLING_TYPE_1.getValue()
                : "";
    }
    
    
    /**
     * 获取发票种类的对应的名称
     *
     * @param fpzlDm
     * @return 发票类型
     */
    public static String getFpzlDmMc(String fpzlDm) {
        
        return OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_0.getValue()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_2.getValue()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_12.getValue()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_41.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_41.getValue()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_42.getValue()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_51.getValue()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzlDm) ? OrderInfoEnum.ORDER_INVOICE_TYPE_52.getValue() : "";
    }
    
    /**
     * 根据发票种类代码名称获取对应的发票种类
     *
     * @param fpzlDmMc
     * @return
     */
    public static String getFpzlDmByFpzlDmMc(String fpzlDmMc) {
        
        return OrderInfoEnum.ORDER_INVOICE_TYPE_0.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_2.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_12.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_41.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_41.getKey()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_42.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_51.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey()
                : OrderInfoEnum.ORDER_INVOICE_TYPE_52.getValue().equals(fpzlDmMc) ? OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey() : "";
    }
    
    /**
     * 根据税控设备类型和发票种类代码判断是否为ofd
     *
     * @param terminalCode
     * @param fpzldm
     * @return
     */
    public static boolean judgeOfd(String terminalCode, String fpzldm) {
        //如果发票种类为电票或者是电专,并且税控设备类型为新税控,方格UKey,UKey托管,U180UKey,返回都是ofd
        if (OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzldm)) {
            if (OrderInfoEnum.TAX_EQUIPMENT_NEWTAX.getKey().equals(terminalCode) || OrderInfoEnum.TAX_EQUIPMENT_FGUKEY.getKey().equals(terminalCode) || OrderInfoEnum.TAX_EQUIPMENT_UKEY.getKey().equals(terminalCode) || OrderInfoEnum.TAX_EQUIPMENT_U180_UKEY.getKey().equals(terminalCode)) {
                log.debug("{}根据发票种类代码:{},税控设备类型:{},判断为ofd", LOGGER_MSG, fpzldm, terminalCode);
                return true;
            }
        }
        return false;
    }
    
    /**
     * 根据发票种类代码判断开票类型
     *
     * @param fpzldm
     * @return
     */
    public static String getKplx(String fpzldm) {
        String invoiceType;
        if (OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzldm)) {
            invoiceType = OrderInfoEnum.INVOICE_TYPE_2.getKey();
        } else {
            invoiceType = OrderInfoEnum.INVOICE_TYPE_1.getKey();
        }
        return invoiceType;
    }
    
    public static boolean checkFpzldm(String fpzldm) {
        return StringUtils.isNotBlank(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_004.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_005.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_006.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_007.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_028.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_026.getKey().equals(fpzldm);
    }
    
    /**
     * 校验专票种类
     *
     * @param fpzldm
     * @return
     */
    public static boolean checkFpzldmForSpecial(String fpzldm) {
        return StringUtils.isNotBlank(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_004.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fpzldm)
                && !OrderInfoEnum.ORDER_INVOICE_TYPE_028.getKey().equals(fpzldm);
    }
    
    /**
     * 获取红票备注
     *
     * @param bz
     * @param terminalCode
     * @return
     * @throws UnsupportedEncodingException
     */
    public static String subBz(String bz, String terminalCode) throws UnsupportedEncodingException {
        
        /**
         * 红票备注特殊处理
         *
         * 如果税控设备为C48,A9,并且备注超长230位,截取前230位
         * 如果税控设备为方格百旺,并且备注超长184位,截取前184位
         * 如果其他,备注超长200,截取前200
         */
        
        if (OrderInfoEnum.TAX_EQUIPMENT_C48.getKey().equals(terminalCode) || OrderInfoEnum.TAX_EQUIPMENT_A9.getKey().equals(terminalCode)) {
            if (StringUtils.isNotEmpty(bz) && bz.getBytes(ConfigurerInfo.STRING_CHARSET_GBK).length > ConfigurerInfo.INT_230) {
                bz = StringUtil.substringByte(bz, ConfigurerInfo.INT_0, ConfigurerInfo.INT_230);
            }
        } else if (OrderInfoEnum.TAX_EQUIPMENT_FGBW.getKey().equals(terminalCode)) {
            if (StringUtils.isNotEmpty(bz) && bz.getBytes(ConfigurerInfo.STRING_CHARSET_GBK).length > ConfigurerInfo.INT_184) {
                bz = StringUtil.substringByte(bz, ConfigurerInfo.INT_0, ConfigurerInfo.INT_184);
            }
        } else {
            if (StringUtils.isNotEmpty(bz) && bz.getBytes(ConfigurerInfo.STRING_CHARSET_GBK).length > ConfigurerInfo.INT_200) {
                bz = StringUtil.substringByte(bz, ConfigurerInfo.INT_0, ConfigurerInfo.INT_200);
            }
        }
        
        return bz;
    }
    
    public static String getHzxxbbh(String bz) {
        String hzxxbbh = "";
        if (StringUtils.isEmpty(bz)) {
            return hzxxbbh;
        }
        if (StringUtils.isNotEmpty(bz) && bz.contains(ConfigureConstant.STRING_FORMAT_BW_HZBZ2)) {
            int index = bz.indexOf(ConfigureConstant.STRING_FORMAT_BW_HZBZ2);
            if (bz.length() >= index + ConfigureConstant.STRING_FORMAT_BW_HZBZ2.length() + ConfigureConstant.INT_16) {
                String subStr = bz.substring(index + ConfigureConstant.STRING_FORMAT_BW_HZBZ2.length(), index + ConfigureConstant.STRING_FORMAT_BW_HZBZ2.length() + ConfigureConstant.INT_16);
                if (ValidateUtil.isNumeric(subStr)) {
                    hzxxbbh = subStr;
                }
            }
        }
        return hzxxbbh;
    }
    
    /**
     * 获取订单备注数据
     *
     * @param bz
     * @param kplx
     * @param fpzldm
     * @param yfpdm
     * @param yfphm
     * @param kce
     * @param hzxxbbh
     * @return
     */
    public static String getBz(String bz, String kplx, String fpzldm, String yfpdm, String yfphm, String kce, String hzxxbbh) {
        log.debug("{}获取备注信息请求参数,原始备注:{},开票类型为:{},发票种类代码为:{},原发票代码为:{},原发票号码为:{},扣除额为:{},红字信息表编号为:{}", LOGGER_MSG, bz, kplx, fpzldm, yfpdm, yfphm, kce, hzxxbbh);
        String resultBz = bz;
        if (StringUtils.isEmpty(bz)) {
            bz = "";
        }
        //扣除额格式化为小数点后两位
        if (StringUtils.isNotEmpty(kce)) {
            kce = new BigDecimal(kce).setScale(ConfigureConstant.INT_2, RoundingMode.HALF_UP).toPlainString();
        } else {
            kce = "";
        }
        /**
         * 红字专票备注：红字发票信息表编号4403052010069521
         * 红字电专备注：红字发票信息表编号4403052010069521
         * 红字普票备注：对应正数发票代码:011002680026 号码:00155093
         * 红字电票备注：对应正数发票代码:011002680026 号码:26061014
         * 差额红字电票备注：对应正数发票代码:011002680026 号码:43237354差额征税
         * 差额红字普票备注：对应正数发票代码:011002680026 号码:43237354差额征税
         * 差额红字专票备注：红字发票信息表编号4403052010069521差额征税
         * 差额红字电专备注：红字发票信息表编号4403052010069521差额征税
         * 正数电票备注：差额征税：4.00。
         * 正数普票备注：差额征税：4.00。
         * 正数专票备注：差额征税：4.00。
         * 正数电专备注：差额征税：4.00。
         */
        //红票处理逻辑
        if (OrderInfoEnum.INVOICE_BILLING_TYPE_1.getKey().equals(kplx)) {
            //根据种类进行判断
            if (OrderInfoEnum.ORDER_INVOICE_TYPE_2.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_51.getKey().equals(fpzldm)) {
                //扣除额发票
                if (StringUtils.isNotEmpty(kce)) {
                    //差额红字普票差额发票
                    String formatBz = String.format(ConfigureConstant.STRING_FORMAT_HPBZ2, yfpdm, yfphm);
                    if (!bz.contains(formatBz) && !bz.contains(ConfigureConstant.STRING_CEZS)) {
                        resultBz = formatBz + bz;
                    }

                } else {
                    //红字普票非差额发票
                    String formatBz = String.format(ConfigureConstant.STRING_FORMAT_HPBZ1, yfpdm, yfphm);
                    if (!bz.contains(formatBz)) {
                        resultBz = formatBz + bz;
                    }
                }
            } else if (OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzldm)) {
                if (StringUtils.isEmpty(hzxxbbh)) {
                    String getHzxxbbh = CommonUtils.getHzxxbbh(bz);
                    if (StringUtils.isNotEmpty(getHzxxbbh)) {
                        hzxxbbh = getHzxxbbh;
                    }
                }

                //扣除额发票
                if (StringUtils.isNotEmpty(kce)) {
                    //红字专票(包含电专)差额票
                    String formatBz = String.format(ConfigureConstant.STRING_FORMAT_BW_HZBZ1, hzxxbbh);
                    if (!bz.contains(formatBz) && !bz.contains(ConfigureConstant.STRING_CEZS)) {
                        resultBz = formatBz + bz;
                    }
                } else {
                    //红字专票(包含电专)非差额票
                    String formatBz = String.format(ConfigureConstant.STRING_FORMAT_BW_HZBZ, hzxxbbh);
                    if (!bz.contains(formatBz)) {
                        resultBz = formatBz + bz;
                    }
                }
            }
        } else if (OrderInfoEnum.INVOICE_BILLING_TYPE_0.getKey().equals(kplx)) {

            //扣除额发票,不区分票种
            if (StringUtils.isNotEmpty(kce)) {
                //蓝字电票普票差额票
                String formatBz = String.format(ConfigureConstant.STRING_FORMAT_CEZS, kce);
                if (!bz.contains(formatBz)) {
                    resultBz = formatBz + bz;
                }
            }
        }
        log.debug("{}获取备注信息返回备注为:{}", LOGGER_MSG, resultBz);
        return resultBz;
    }
    
    
    /**
     * @param ddh
     * @param format
     * @return
     */
    public static String getSplitDdh(String ddh, String format) {
    
        if (ddh.contains(CF)) {
            //已经拆分过的订单
            ddh = ddh + DDH_SPLIT_SYMBOL + format;
        } else {
            ddh = ddh + CF + format;
        
        }
        return dealDdh(ddh);
    }
    
    /**
     * 根据税号获取省份代码
     *
     * @param nsrsbh
     * @return
     */
    public static String getProvinceByNsrsbh(String nsrsbh) {
        String sfdm = "";
        /**
         * 从第2位 截取4位和地区编码比较  有的话就有
         * 没有的话 截取两位后两位补零,再和地区枚举比较
         */
        if (StringUtils.isNotEmpty(nsrsbh) && nsrsbh.length() > ConfigurerInfo.INT_6) {
            String subStr = nsrsbh.substring(ConfigurerInfo.INT_2, ConfigurerInfo.INT_6);
            List<String> keyList = ProvinceEnum.getKeys();
            /**
             * 如果截取4位能匹配到数据,直接返回
             */
            if (keyList.contains(subStr)) {
                return keyList.get(keyList.indexOf(subStr));
            } else {
                /**
                 * 如果截取2位后补零为4位后能匹配到数据,直接返回
                 */
                subStr = nsrsbh.substring(ConfigurerInfo.INT_2, ConfigurerInfo.INT_4) + "00";
                if (keyList.contains(subStr)) {
                    return keyList.get(keyList.indexOf(subStr));
                }
            }
        }
        
        return sfdm;
    }
    
    /**
     * 转换申请原因
     * @param reason
     * @return
     */
    public static String convertReason(String reason) {
        String result;
        switch (reason) {
            case "Y":
                result = OrderInfoEnum.SPECIAL_INVOICE_REASON_1100000000.getKey();
                break;
            case "N1":
            case "N2":
            case "N3":
            case "N4":
                result = OrderInfoEnum.SPECIAL_INVOICE_REASON_1010000000.getKey();
                break;
            case "N5":
                result = OrderInfoEnum.SPECIAL_INVOICE_REASON_0000000100.getKey();
                break;
            default:
                result = reason;
        }
    
        return result;
    }
    
    /**
     * 获取字节流类型
     *
     * @param suffix
     * @return
     */
    public static String getZjlLx(String suffix) {
        String zjllx = OrderInfoEnum.FILE_TYPE_PDF.getKey();
        if (ConfigureConstant.STRING_SUFFIX_OFD.equals(suffix)) {
            zjllx = OrderInfoEnum.FILE_TYPE_OFD.getKey();
        } else if (ConfigureConstant.STRING_SUFFIX_PNG.equals(suffix)) {
            zjllx = OrderInfoEnum.FILE_TYPE_PNG.getKey();
        }
        return zjllx;
    }
    
    /**
     * 获取文件后缀
     *
     * @param fileType
     * @return
     */
    public static String getFileSuffix(String fileType) {
        String suffix = ConfigureConstant.STRING_SUFFIX_PDF;
        if (OrderInfoEnum.FILE_TYPE_OFD.getKey().equals(fileType)) {
            suffix = ConfigureConstant.STRING_SUFFIX_OFD;
        } else if (OrderInfoEnum.FILE_TYPE_PNG.getKey().equals(fileType)) {
            suffix = ConfigureConstant.STRING_SUFFIX_PNG;
        }
        return suffix;
    }
    
    /**
     * 判断税控设备是否为新版本接口
     *
     * @param terminalCode
     * @return
     */
    public static boolean judgeNewInterface(String terminalCode) {
        return OrderInfoEnum.TAX_EQUIPMENT_C48.getKey().equals(terminalCode)
                || OrderInfoEnum.TAX_EQUIPMENT_A9.getKey().equals(terminalCode)
                || OrderInfoEnum.TAX_EQUIPMENT_UKEY.getKey().equals(terminalCode)
                || OrderInfoEnum.TAX_EQUIPMENT_KYHZ_Q30.getKey().equals(terminalCode);
    
    }

    /**
     * 根据名称获取二手车企业类型代码
     * <AUTHOR>
     * @date 创建时间: 2022-06-29 09:42
     * @param escQylxMc 二手车企业类型名称
     * @return java.lang.String
     */
    public static String getEscQylxByMc(String escQylxMc){
        return OrderInfoEnum.ESC_QYLX_1.getValue().equals(escQylxMc) ? OrderInfoEnum.ESC_QYLX_1.getKey()
                : OrderInfoEnum.ESC_QYLX_2.getValue().equals(escQylxMc) ? OrderInfoEnum.ESC_QYLX_2.getKey()
                : OrderInfoEnum.ESC_QYLX_3.getValue().equals(escQylxMc) ? OrderInfoEnum.ESC_QYLX_3.getKey() : "";
    }

    /**
     * 根据名称获取机动车企业类型代码
     * <AUTHOR>
     * @date 创建时间: 2022-06-29 09:42
     * @param jdcQylxMc 机动车企业类型名称
     * @return java.lang.String
     */
    public static String getJdcQylxByMc(String jdcQylxMc){
        return OrderInfoEnum.JDCQYLX_01.getValue().equals(jdcQylxMc) ? OrderInfoEnum.JDCQYLX_01.getKey()
                : OrderInfoEnum.JDCQYLX_02.getValue().equals(jdcQylxMc) ? OrderInfoEnum.JDCQYLX_02.getKey() : "";
    }
    
    /**
     * 校验字符串是否为数字
     * <AUTHOR>
     * @date 创建时间: 2022-06-29 09:42
     * @param value 字符串
     * @return boolean true表示非数字，false表示数字
     */
    public static boolean notNumber(String value) {
        try {
            new BigDecimal(value);
            return false;
        } catch (NumberFormatException ignored) {
        }
        return true;
    }
    
    /**
     * 校验纳税人识别号
     *
     * @param nsrsbh 纳税人识别号
     * @param index  集合或数组下标索引，当校验集合或者数组中纳税人识别号时不为空
     * @return com.dxhy.order.exception.OrderReceiveException
     * <AUTHOR>
     * @date 创建时间: 2022-06-29 09:42
     */
    /*public static OrderReceiveException checkNsrsbh(String nsrsbh, Integer index) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        String successCode = OrderInfoContentEnum.SUCCESS.getKey();
        checkResultMap.put(OrderManagementConstant.ERRORCODE, successCode);
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.GET_ON_LINE_STATUS_215001,
                OrderInfoContentEnum.GET_ON_LINE_STATUS_215002,
                OrderInfoContentEnum.GET_ON_LINE_STATUS_215003,
                nsrsbh);
        String indexStr = Objects.nonNull(index) ? "第" + (index + 1) + "条信息:" : "";
        if (!successCode.equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return new OrderReceiveException(checkResultMap.get(OrderManagementConstant.ERRORCODE),
                    indexStr + checkResultMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        return null;
    }*/

    /**
     * 判断是否为方格设备类型
     * <AUTHOR>
     * @date 创建时间: 2022-06-29 09:42
     * @param terminalCode 终端代码
     * @return boolean true表示是方格税控设备，false表示不是方格税控设备
     */
    public static boolean judgeIsFgTerminalType(String terminalCode) {
        return OrderInfoEnum.TAX_EQUIPMENT_FGBW.getKey().equals(terminalCode) ||
                OrderInfoEnum.TAX_EQUIPMENT_FGHX.getKey().equals(terminalCode) ||
                OrderInfoEnum.TAX_EQUIPMENT_FGUKEY.getKey().equals(terminalCode) ||
                OrderInfoEnum.TAX_EQUIPMENT_FG51YZJ.getKey().equals(terminalCode);
    }

    /**
     * 方格：获取金税盘信息
     * <AUTHOR>
     * @date 创建时间: 2022-06-29 09:42
     * @param nsrsbh 纳税人识别号
     * @param terminalCode 终端代码
     * @return java.lang.String 金税盘类型
     */
    public static String getFgJsplx(String nsrsbh, String terminalCode) {
        String jsplx = ConfigureConstant.STRING_0;
        if (OrderInfoEnum.TAX_EQUIPMENT_FGBW.getKey().equals(terminalCode)) {
            log.info("查询税号为{}的本地{}税盘信息", nsrsbh, OrderInfoEnum.TAX_EQUIPMENT_FGBW.getValue());
            jsplx = ConfigureConstant.STRING_1;
        } else if (OrderInfoEnum.TAX_EQUIPMENT_FGHX.getKey().equals(terminalCode)) {
            log.info("查询税号为{}的本地{}税盘信息", nsrsbh, OrderInfoEnum.TAX_EQUIPMENT_FGHX.getValue());
            jsplx = ConfigureConstant.STRING_0;
        } else if (OrderInfoEnum.TAX_EQUIPMENT_FG51YZJ.getKey().equals(terminalCode)) {
            log.info("查询税号为{}的本地{}税盘信息", nsrsbh, OrderInfoEnum.TAX_EQUIPMENT_FG51YZJ.getValue());
            jsplx = ConfigureConstant.STRING_0;
        } else {
            log.info("查询税号为{}的本地{}税盘信息", nsrsbh, OrderInfoEnum.TAX_EQUIPMENT_FGUKEY.getValue());
            jsplx = ConfigureConstant.STRING_2;
        }
        return jsplx;
    }
    
    /**
     * 根据底层接口返回的信息表类型和营业税标识判断数据库type对应的值
     *
     * @param xxblx
     * @param yysbz
     * @return
     */
    public static String getSpecialTypeByXxblxAndYysbz(String xxblx, String yysbz) {
        //信息表类型:0正常；1逾期(仅销方开具)；2机动车信息表（涉及退货和开具错误等）；3机动车信息表（涉及销售折让）
        String type = OrderInfoEnum.SPECIAL_INVOICE_TYPE_0.getKey();
        if (ConfigureConstant.STRING_2.equals(xxblx)) {
            type = OrderInfoEnum.SPECIAL_INVOICE_TYPE_3.getKey();
        } else if (ConfigureConstant.STRING_3.equals(xxblx)) {
            type = OrderInfoEnum.SPECIAL_INVOICE_TYPE_4.getKey();
        }
        if (OrderInfoEnum.SPECIAL_YYSBZ_0000000090.getKey().equals(yysbz)) {
            type = OrderInfoEnum.SPECIAL_INVOICE_TYPE_1.getKey();
        }
        return type;
    }
    
    /**
     * 获取当前订单操作状态
     *
     * @param orderOperate
     * @return
     */
    public static String getOrderOperateMsg(String orderOperate) {
        return OrderInfoEnum.ORDER_OPERATE_STATUS_0.getKey().equals(orderOperate) ? OrderInfoEnum.ORDER_OPERATE_STATUS_0.getValue()
                : OrderInfoEnum.ORDER_OPERATE_STATUS_1.getKey().equals(orderOperate) ? OrderInfoEnum.ORDER_OPERATE_STATUS_1.getValue()
                : OrderInfoEnum.ORDER_OPERATE_STATUS_2.getKey().equals(orderOperate) ? OrderInfoEnum.ORDER_OPERATE_STATUS_2.getValue()
                : OrderInfoEnum.ORDER_OPERATE_STATUS_3.getKey().equals(orderOperate) ? OrderInfoEnum.ORDER_OPERATE_STATUS_3.getValue()
                : OrderInfoEnum.ORDER_OPERATE_STATUS_4.getKey().equals(orderOperate) ? OrderInfoEnum.ORDER_OPERATE_STATUS_4.getValue()
                : "";
    }
    /**
     * 去除商品名称中的空格
     * 1.去除首尾空格
     * 2.截取掉商品简称后的商品名称再次首尾去空格
     */
    public static String trimSpmc(String spmc) {
        if (StringUtils.isNotBlank(spmc)) {
            spmc = spmc.trim();

            if (spmc.startsWith("*")) {
                String[] split = spmc.split("\\*");
                //判断包含多少个*，大于2时说明包含商品简称
                if (split.length > 2) {
                    //只处理前两个*
                    String[] split2 = spmc.split("\\*", 3);
                    String newSpmc = split2[2];
                    if (StringUtils.isNotBlank(newSpmc)) {
                        spmc = "*" + split2[1] + "*" + newSpmc.trim();
                    }
                }
            }
        }
        return spmc;
    }
    
    public static void main(String[] args) {
        
        String nsrsbh = "91450100MA5NA58U11";
        System.out.println(CommonUtils.getProvinceByNsrsbh(nsrsbh));
        
        String bz = "测试数据";
        String kplx = "1";
        String fpzldm = "52";
        String yfpdm = "fpdm";
        String yfphm = "fphm";
        String kce = "";
        String hzxxbbh = "234234";
        String bz1 = CommonUtils.getBz(bz, kplx, fpzldm, yfpdm, yfphm, kce, hzxxbbh);
        System.out.println(bz1);
    
        System.out.println(CommonUtils.getHzxxbbh("adfa开具红字增值税专用发票信息表编号1234567890123457"));
        System.out.println(CommonUtils.getHzxxbbh("开具红字增值税专用发票信息表编号1234567890123457"));
        System.out.println(CommonUtils.getHzxxbbh("adfa红字发票信息表编号1234567890123457"));
        System.out.println(CommonUtils.getHzxxbbh("红字发票信息表编号1234567890123457"));
    
        bz = "asdlkjfas;kldjf;asljd;l达里看风景的撒了科几艾兰德恐惧啊;连接的拉斯咖啡机埃里克交电费我去问旁人加起来待开具撒肥料去欧文疲软期颇为如期颇为UI阿斯利康多费劲啊刘东杰放辣椒砥砺奋进卡萨丁覅偶抢卧铺饿哦阿施蒂利克就发连接订饭啦解放东路就发了空数据多方了解888878y69213470168309;laksdfja;osdiuxc;lkj;lufaporuqwpoieurpoqwieurpqowjfz;lsdkjv;klzjhvpoiaudfpwhk;fglsdhvpoiasu;fgajg";
        String s = StringUtil.substringByte(bz, ConfigurerInfo.INT_0, ConfigurerInfo.INT_230);
        System.out.println(s);
    }
}
