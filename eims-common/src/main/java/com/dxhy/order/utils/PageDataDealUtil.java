package com.dxhy.order.utils;

import cn.hutool.core.util.RandomUtil;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.model.OrderQrcodeExtendInfo;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.entity.PageQrcodeOrderInfo;
import com.dxhy.order.modules.entity.PageQrcodeOrderItemInfo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ：pageDataDealUtil
 * @Description ：前端数据展示处理
 * @date 创建时间: 2022-06-29 09:42
 */

public class PageDataDealUtil {
    
    /**
     * bean转换
     */
    public static OrderInvoiceInfoEntity pageToFpkjInfo(PageQrcodeOrderInfo pageQrcodeOrderInfo) {
        OrderInvoiceInfoEntity orderInvoiceInfo = new OrderInvoiceInfoEntity();
        orderInvoiceInfo.setBz(pageQrcodeOrderInfo.getBz());
        orderInvoiceInfo.setFhr(pageQrcodeOrderInfo.getFhr());
        orderInvoiceInfo.setGhfDh(pageQrcodeOrderInfo.getGhfDh());
        orderInvoiceInfo.setGhfDz(pageQrcodeOrderInfo.getGhfDz());
        orderInvoiceInfo.setDdly(OrderInfoEnum.ORDER_SOURCE_6.getKey());
        // 购货方邮箱
        orderInvoiceInfo.setGhfYx(pageQrcodeOrderInfo.getGhfEmail());
        orderInvoiceInfo.setGhfMc(pageQrcodeOrderInfo.getGhfMc());
        orderInvoiceInfo.setGhfNsrsbh(pageQrcodeOrderInfo.getGhfNsrsbh());
        orderInvoiceInfo.setGhfSj(pageQrcodeOrderInfo.getGhfSj());
        //  银行账号是否需要拆分开
        orderInvoiceInfo.setGhfYh(pageQrcodeOrderInfo.getGhfYh());
        orderInvoiceInfo.setGhfZh(pageQrcodeOrderInfo.getGhfZh());
        orderInvoiceInfo.setDdh(StringUtils.isBlank(pageQrcodeOrderInfo.getDdh()) ? RandomUtil.randomNumbers(12) : pageQrcodeOrderInfo.getDdh());
        orderInvoiceInfo.setDdscrq(new Date());
        orderInvoiceInfo.setCreateTime(new Date());
        orderInvoiceInfo.setUpdateTime(new Date());
        orderInvoiceInfo.setQdBz(StringUtils.isNotBlank(pageQrcodeOrderInfo.getQdbz()) ? pageQrcodeOrderInfo.getQdbz() : OrderInfoEnum.QDBZ_CODE_0.getKey());

        orderInvoiceInfo.setFpzlDm(pageQrcodeOrderInfo.getFplx());
        orderInvoiceInfo.setKpr(pageQrcodeOrderInfo.getKpy());
        orderInvoiceInfo.setSkr((pageQrcodeOrderInfo.getSky()));
        orderInvoiceInfo.setKplx(pageQrcodeOrderInfo.getKplx());
        orderInvoiceInfo.setDdh(pageQrcodeOrderInfo.getDdh());
        // 扫码开票抬头类型只有个人和企业
        orderInvoiceInfo.setGhfQylx(pageQrcodeOrderInfo.getGhfqylx());
        orderInvoiceInfo.setXhfNsrsbh(pageQrcodeOrderInfo.getXhfNsrsbh());
        orderInvoiceInfo.setXhfDz(pageQrcodeOrderInfo.getXhfdz());
        orderInvoiceInfo.setXhfDh(pageQrcodeOrderInfo.getXhfdh());
        orderInvoiceInfo.setXhfYh(pageQrcodeOrderInfo.getXhfyh());
        orderInvoiceInfo.setXhfZh(pageQrcodeOrderInfo.getXhfzh());
        orderInvoiceInfo.setXhfMc(pageQrcodeOrderInfo.getXhfmc());
        orderInvoiceInfo.setTqm(pageQrcodeOrderInfo.getTqm());
        orderInvoiceInfo.setXhfMc(pageQrcodeOrderInfo.getXhfmc());
        orderInvoiceInfo.setXhfNsrsbh(pageQrcodeOrderInfo.getXhfNsrsbh());
    
        //处理开票合计金额
        BigDecimal hjbhsje = BigDecimal.ZERO;
        BigDecimal hjse = BigDecimal.ZERO;
        List<OrderInvoiceItemEntity> orderInvoiceItemEntityList = new ArrayList<>();
        List<PageQrcodeOrderItemInfo> pageOrderItemInfoList = pageQrcodeOrderInfo.getPageOrderItemInfoList();

        for (PageQrcodeOrderItemInfo pageOrderItemInfo : pageOrderItemInfoList) {
            OrderInvoiceItemEntity orderInvoiceItem = new OrderInvoiceItemEntity();
            orderInvoiceItem.setDw(pageOrderItemInfo.getXmdw());
            orderInvoiceItem.setGgxh(pageOrderItemInfo.getGgxh());
            orderInvoiceItem.setSl("");
            orderInvoiceItem.setDj("");
            if (StringUtils.isNotBlank(pageOrderItemInfo.getSl())) {
                orderInvoiceItem.setSl(CommonUtils.formatSl(pageOrderItemInfo.getSl()));
            }
            orderInvoiceItem.setSpbm(pageOrderItemInfo.getSpbm());
            if (StringUtils.isNotBlank(pageOrderItemInfo.getXmdj())) {
                orderInvoiceItem.setDj(DecimalCalculateUtil.decimalFormatToStringWithoutZero(pageOrderItemInfo.getXmdj(), ConfigureConstant.INT_8));
            }
            if (StringUtils.isNotBlank(pageOrderItemInfo.getXmje())) {
                orderInvoiceItem.setJe(DecimalCalculateUtil.decimalFormatToString(pageOrderItemInfo.getXmje(), ConfigureConstant.INT_2));
                hjbhsje = hjbhsje.add(new BigDecimal(orderInvoiceItem.getJe()));
            }
            orderInvoiceItem.setXmmc(pageOrderItemInfo.getXmmc());
            if (StringUtils.isNotBlank(pageOrderItemInfo.getXmsl())) {
                orderInvoiceItem.setXmsl(DecimalCalculateUtil.decimalFormatToStringWithoutZero(pageOrderItemInfo.getXmsl(), ConfigureConstant.INT_8));
            }

            orderInvoiceItem.setHsbz(pageOrderItemInfo.getHsbz());
            orderInvoiceItem.setFphxz(pageOrderItemInfo.getFphxz());
            orderInvoiceItem.setYhzcbs(pageOrderItemInfo.getYhzcbs());
            orderInvoiceItem.setZzstsgl(pageOrderItemInfo.getZzstsgl());
            orderInvoiceItem.setLslbs(pageOrderItemInfo.getLslbs());
            if (StringUtils.isBlank(pageOrderItemInfo.getXmse())) {
                orderInvoiceItem.setSe(ConfigureConstant.STRING_000);
            } else {
                orderInvoiceItem.setSe(pageOrderItemInfo.getXmse());
                hjse = hjse.add(new BigDecimal(orderInvoiceItem.getSe()));
            }

            orderInvoiceItem.setKce(pageOrderItemInfo.getKce());
            orderInvoiceItemEntityList.add(orderInvoiceItem);
        }
        if (pageOrderItemInfoList.size() <= ConfigureConstant.INT_2 && StringUtils.isNotBlank(pageOrderItemInfoList.get(0).getKce())) {
            orderInvoiceInfo.setBz(ConfigureConstant.STRING_CEZS_ + pageOrderItemInfoList.get(0).getKce() + "。" + orderInvoiceInfo.getBz());
        }
        orderInvoiceInfo.setHjbhsje(DecimalCalculateUtil.decimalFormatToString(hjbhsje.toPlainString(), ConfigureConstant.INT_2));
        orderInvoiceInfo.setKpse(DecimalCalculateUtil.decimalFormatToString(hjse.toPlainString(), ConfigureConstant.INT_2));
        orderInvoiceInfo.setJshj(DecimalCalculateUtil.decimalFormatToString(hjbhsje.add(hjse).toPlainString(), ConfigureConstant.INT_2));
        orderInvoiceInfo.setItemEntityList(orderInvoiceItemEntityList);
        return orderInvoiceInfo;
    
    }

    public static OrderQrcodeExtendInfo buildOrderQrcodeInfo(OrderInvoiceInfoEntity orderInvoiceInfo) {
    
    
        Date now = new Date();
        OrderQrcodeExtendInfo orderQrcodeExtendInfo = new OrderQrcodeExtendInfo();
        orderQrcodeExtendInfo.setCreateTime(now);
        orderQrcodeExtendInfo.setFpqqlsh(orderInvoiceInfo.getFpqqlsh());
        orderQrcodeExtendInfo.setQuickResponseCodeType(OrderInfoEnum.QR_TYPE_1.getKey());
        orderQrcodeExtendInfo.setTqm(orderInvoiceInfo.getTqm());
        orderQrcodeExtendInfo.setUpdateTime(now);
        orderQrcodeExtendInfo.setXhfMc(orderInvoiceInfo.getXhfMc());
        orderQrcodeExtendInfo.setXhfNsrsbh(orderInvoiceInfo.getXhfNsrsbh());
        orderQrcodeExtendInfo.setKphjje(orderInvoiceInfo.getJshj());
        orderQrcodeExtendInfo.setDdh(orderInvoiceInfo.getDdh());
        orderQrcodeExtendInfo.setEwmzt("0");
    
        orderQrcodeExtendInfo.setFpzlDm(orderInvoiceInfo.getFpzlDm());
        orderQrcodeExtendInfo.setZfzt("0");
        orderQrcodeExtendInfo.setCardStatus("0");
        orderQrcodeExtendInfo.setDataStatus("0");
        return orderQrcodeExtendInfo;
    
    
    }
    
    
}
