package com.dxhy.order.utils;


import cn.hutool.core.util.ObjectUtil;
import com.dxhy.order.constant.ExcelReadContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表格导入通用
 *
 * <AUTHOR>
 * @date 创建时间: 2020-08-14 14:48
 */
@Slf4j
public class ExcelExportUtils {

    private static final String LOGGER_MSG = "(表格导出工具类)";

    private final ExcelReadContext excelReadContext;

    public ExcelExportUtils(ExcelReadContext context) {
        
        this.excelReadContext = context;
    }
    
    
    /**
     * excel通用导出
     *
     * @param sxssfWorkbook
     * @param <T>
     * @throws IOException
     */
    public <T> SXSSFWorkbook exportExcel(SXSSFWorkbook sxssfWorkbook, List<T> list) {

        // 创建一个工作簿
        if (ObjectUtil.isNull(sxssfWorkbook)) {
            sxssfWorkbook = new SXSSFWorkbook(1000);
        }
        
        try {
            sxssfWorkbook.setCompressTempFiles(true);
            //初始化头信息行数
            int rowIndex = excelReadContext.getRowCount();
            //初始化sheet页数
            int sheetIndex = excelReadContext.getSheetIndex();
    
            Map<Integer, Integer> colWidthMap = new HashMap<>(5);
            Map<String, String> headToPropertyMap = excelReadContext.getHeadToPropertyMap();
            Map<String, String> headerToColumnMap = excelReadContext.getHeaderToColumnMap();
            long headStart = System.currentTimeMillis();
            //创建表格头数据
            SXSSFSheet sheet = firstRow(sxssfWorkbook, excelReadContext.getSheetIndex(), excelReadContext.getHeadRow(), headToPropertyMap, colWidthMap);
            long headEnd = System.currentTimeMillis();
            log.debug("{}赋值表格头耗时:{}", LOGGER_MSG, headEnd - headStart);
            //读取表格头行数,判断是否创建头,如果创建row索引加一
            if (rowIndex == excelReadContext.getHeadRow()) {
                rowIndex++;
            }
            long headStart2 = System.currentTimeMillis();
            //生成数据
            log.info("{}list:{}", LOGGER_MSG, list);
            for (T data : list) {

                //判断当前行数属否大于单个sheet限制数,如果大于限制,重新创建sheet,行数重置为0.
                if (rowIndex > excelReadContext.getSheetLimit()) {
            
                    //处理列宽,换新sheet前,需要重置上一个sheet的列宽
                    delSheetColWidth(sheet, colWidthMap);
            
                    //创建表格头数据
                    sheetIndex++;
                    sheet = firstRow(sxssfWorkbook, sheetIndex, excelReadContext.getHeadRow(), headToPropertyMap, colWidthMap);
                    rowIndex = excelReadContext.getHeadRow();
                    excelReadContext.setSheetIndex(sheetIndex);
                    //读取表格头行数,判断是否创建头,如果创建row索引加一
                    if (rowIndex == excelReadContext.getHeadRow()) {
                        rowIndex++;
                    }
            
                }

                boolean constantItem = false;
                String itemMethodName = "";
                Method[] methods = data.getClass().getMethods();
                for (Method method : methods) {
                    if (method.getName().startsWith("get")) {
                        Object invoke = method.invoke(data);
                        if (invoke instanceof ArrayList) {
                            if (((ArrayList<?>) invoke).size() > 0) {
                                constantItem = true;
                                itemMethodName = method.getName();
                            }
        
                        }
                    }
    
                }
                if (constantItem) {
                    Method getMethod = data.getClass().getMethod(itemMethodName);
                    Object invoke = getMethod.invoke(data);
                    if (invoke instanceof ArrayList) {
                
                        for (int i = 0; i < ((ArrayList<?>) invoke).size(); i++) {
                            Row row = sheet.createRow(rowIndex);
                            Object o = ((ArrayList<?>) invoke).get(i);
                            for (Map.Entry<String, String> entry1 : headerToColumnMap.entrySet()) {
                                String property1 = entry1.getKey();
                                String getMethodName1 = "get" + property1.substring(0, 1).toUpperCase() + property1.substring(1);
                                try {
                                    Method getMethod1 = o.getClass().getMethod(getMethodName1);
                                    Object invoke1 = getMethod1.invoke(o);
                                    Cell cell = row.createCell(Integer.parseInt(entry1.getValue()));
                                    XSSFRichTextString text = new XSSFRichTextString(String.valueOf(invoke1));
                                    cell.setCellValue(text);
                                } catch (NoSuchMethodException exception) {
                                    String property2 = entry1.getKey();
                                    String getMethodName2 = "get" + property2.substring(0, 1).toUpperCase() + property2.substring(1);
                                    Method getMethod2 = data.getClass().getMethod(getMethodName2);
                                    Object invoke2 = getMethod2.invoke(data);
                                    if (!(invoke2 instanceof ArrayList)) {
                                        Cell cell = row.createCell(Integer.parseInt(entry1.getValue()));
                                        XSSFRichTextString text = new XSSFRichTextString(String.valueOf(invoke2));
                                        cell.setCellValue(text);
                                    }
    
                                }
                        
                            }
                            //自适应列宽
                            delColWidth(row, colWidthMap);
                            rowIndex++;
                        }
                
                    }
                } else {
                    Row row = sheet.createRow(rowIndex);
                    //循环map数据,获取和对象的绑定关系并放在对应excel中
                    for (Map.Entry<String, String> entry : headerToColumnMap.entrySet()) {
                
                        String property = entry.getKey();
                        String getMethodName = "get" + property.substring(0, 1).toUpperCase() + property.substring(1);
                        Method getMethod = data.getClass().getMethod(getMethodName);
                        Object invoke = getMethod.invoke(data);
    
                        Cell cell = row.createCell(Integer.parseInt(entry.getValue()));
                        XSSFRichTextString text = new XSSFRichTextString(String.valueOf(invoke));
                        cell.setCellValue(text);
    
                    }
                    //自适应列宽
                    delColWidth(row, colWidthMap);
                    rowIndex++;
                }
        
        
            }
            long headEnd2 = System.currentTimeMillis();
            log.debug("{}总耗时:{}", LOGGER_MSG, headEnd2 - headStart2);
            //重新赋值行,用户记录上次sheet结束行数
            excelReadContext.setRowCount(rowIndex);

            //处理最后一个sheet列宽
            delSheetColWidth(sheet, colWidthMap);
    
            return sxssfWorkbook;
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    
    
    private SXSSFSheet firstRow(SXSSFWorkbook sxssfWorkbook, int sheetIndex, int rowIndex, Map<String, String> headToPropertyMap, Map<Integer, Integer> colWidthMap) throws Exception {
        // 创建一个表格
        int numberOfSheets = sxssfWorkbook.getNumberOfSheets();
    
        SXSSFSheet sheet;
        if (numberOfSheets == 0 || numberOfSheets <= sheetIndex) {
            sheet = sxssfWorkbook.createSheet();
            //冻结首行
            sheet.createFreezePane(0, rowIndex + 1, 0, rowIndex + 1);

            //生成表头
            if (headToPropertyMap != null) {
                // 创建一行
                Row row = sheet.createRow(rowIndex);
                // 生成一个样式
                CellStyle style = buildHeadStyle(sxssfWorkbook);
            
                for (Map.Entry<String, String> entry : headToPropertyMap.entrySet()) {
                
                    if (excelReadContext.getHeaderToColumnMap() == null) {
                        throw new Exception("excel填充参数错误!");
                    }
                
                    String columnIndex = excelReadContext.getHeaderToColumnMap().get(entry.getKey());
                    if (columnIndex == null) {
                        throw new Exception("excel填充参数错误!");
                    }
                    Cell cell = row.createCell(Integer.parseInt(columnIndex));
                    cell.setCellStyle(style);
                    XSSFRichTextString text = new XSSFRichTextString(entry.getValue());
                    cell.setCellValue(text);
                }
                //自适应列宽
                delColWidth(row, colWidthMap);
            }
        } else {
            sheet = sxssfWorkbook.getSheetAt(sheetIndex);
        }
        return sheet;
    }
    
    
    /**
     * 处理列宽
     *
     * @param tableRow
     * @param colWidthMap
     */
    private void delColWidth(Row tableRow, Map<Integer, Integer> colWidthMap) {
        for (Cell cell : tableRow) {
            int columnIndex = cell.getColumnIndex();
            Integer maxColumIndex = colWidthMap.get(columnIndex);
            if (maxColumIndex == null) {
                String stringCellValue = cell.getStringCellValue();
                if (StringUtils.isNotBlank(stringCellValue)) {
                    int length = cell.getStringCellValue().getBytes().length;
                    colWidthMap.put(columnIndex, length * 256);
                } else {
                    colWidthMap.put(columnIndex, 0);
                }
            } else {
                String stringCellValue = cell.getStringCellValue();
                if (StringUtils.isNotBlank(stringCellValue)) {
                    int length = cell.getStringCellValue().getBytes().length;
                    length = length * 256;
                    if (length > maxColumIndex) {
                        colWidthMap.put(columnIndex, length);
                    }
                }
            }
        }
    
    }
    
    /**
     * 处理sheet列宽
     *
     * @param sheet
     * @param colWidthMap
     */
    private void delSheetColWidth(SXSSFSheet sheet, Map<Integer, Integer> colWidthMap) {
        for (Map.Entry<Integer, Integer> entry : colWidthMap.entrySet()) {
            Integer key = entry.getKey();
            Integer value = entry.getValue();
            if (value < 255 * 256) {
                sheet.setColumnWidth(key, value);
            } else {
                sheet.setColumnWidth(key, 6000);
            }
            
        }
        
    }
    
    /**
     * 创建表头格式
     *
     * @param workBook
     * @return
     */
    private CellStyle buildHeadStyle(SXSSFWorkbook workBook) {
        CellStyle style = workBook.createCellStyle();
        style.setFillForegroundColor(HSSFColor.HSSFColorPredefined.PALE_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }


}
