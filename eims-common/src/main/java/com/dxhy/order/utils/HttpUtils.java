package com.dxhy.order.utils;

import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.KeyManager;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
@Slf4j
public class HttpUtils {

    private static final String LOGGER_MSG = "(请求http访问)";

    /**
     * 执行post请求
     *
     * @param url
     * @param paramMap
     * @return
     * @throws IOException
     */
    public static String doPost(String url, Map<String, ?> paramMap) {
        Map<String, Object> requestMap = new HashMap<>(paramMap);
        long startTime = System.currentTimeMillis();
        log.debug("{}以Map调用post请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.post(url).form(requestMap).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Map调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }

    public static String doPost(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},参数:{}", LOGGER_MSG, url, request);
        String body = HttpRequest.post(url).body(request).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},耗时:{},返回参数:{}", LOGGER_MSG, url, endTime - startTime, body);
        return body;
    }

    public static String doPostWithHeader(String url, String data, Map<String, String> header) {
        long startTime = System.currentTimeMillis();
        log.debug("{}带head调用post请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.post(url).addHeaders(header).body(data).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}带head调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }

    public static String doPostFormWithHeader(String url, Map<String, ?> paramMap, Map<String, String> header) {
        Map<String, Object> requestMap = new HashMap<>(paramMap);
        long startTime = System.currentTimeMillis();
        log.debug("{}带head和form调用post请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.post(url).addHeaders(header).form(requestMap).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}带head和form调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }

    public static String doGetWithHeader(String url, Map<String, String> header) {
        long startTime = System.currentTimeMillis();
        log.debug("{}带head调用get请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.get(url).addHeaders(header).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}带head调用get请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }

    public static String doGet(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{}以字符串调用get请求url:{}", LOGGER_MSG, url);
        String body = HttpRequest.get(url).body(request).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以字符串调用get请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }

    public static String doPostDx(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},参数:{}", LOGGER_MSG, url, request);
        String body = HttpRequest.post(url).body(request).timeout(600000).header("Accept-Encoding", "", true).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},耗时:{},返回参数:{}", LOGGER_MSG, url, endTime - startTime, body);
        return body;
    }

    /**
     * http 主要针对百望pdf下载的http方式，可通用
     */
    public static byte[] doGet(String uri) throws IOException {
        HttpURLConnection httpConn = getHttpsURLConnection(uri, "GET");
        return getBytesFromStream(httpConn.getInputStream());
    }

    private static HttpURLConnection getHttpsURLConnection(String uri, String method) throws IOException {
        SSLContext ctx = null;
        try {
            ctx = SSLContext.getInstance("TLS");
            ctx.init(new KeyManager[0], new TrustManager[]{new DefaultTrustManager()}, new SecureRandom());
        } catch (KeyManagementException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        URL url = new URL(uri);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setConnectTimeout(3000);//设置连接主机超时（单位：毫秒）
        httpConn.setReadTimeout(30000);//设置从主机读取数据超时（单位：毫秒）
        httpConn.setRequestMethod(method);
        httpConn.setDoInput(true);
        httpConn.setDoOutput(true);
        httpConn.setRequestProperty("Content-Type", "application/json");
        return httpConn;
    }

    private static byte[] getBytesFromStream(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] kb = new byte[1024];
        int len;
        while ((len = is.read(kb)) != -1) {
            baos.write(kb, 0, len);
        }
        byte[] bytes = baos.toByteArray();
        baos.close();
        is.close();
        return bytes;
    }

    private static final class DefaultTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }
    }
}
