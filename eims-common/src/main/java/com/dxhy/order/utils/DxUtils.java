package com.dxhy.order.utils;

import cn.hutool.core.util.RandomUtil;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.modules.entity.AccessTokeReq;
import com.dxhy.order.modules.entity.AccessTokenBean;
import com.dxhy.order.modules.entity.GlobalInfo;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DxUtils {

    private static final String LOGGER_MSG = "(DX接口请求)";

    /**
     * 查询token
     *
     * @param appKey
     * @param appSecret
     * @param tokenUrl
     * @return token
     */
    public static String getDxToken(String appKey, String appSecret, String tokenUrl) {
        long startTime = System.currentTimeMillis();
        AccessTokeReq access_token = new AccessTokeReq();
        access_token.setAppKey(appKey);
        access_token.setAppSecret(appSecret);
        log.debug("{购方名称模糊查询},请求token,请求参数为:{}", JsonUtils.getInstance().toJsonString(access_token));
        String doPost = HttpUtils.doPost(tokenUrl, JsonUtils.getInstance().toJsonString(access_token));
        log.debug("{},返回参数为:{}", LOGGER_MSG, doPost);
        long endTime = System.currentTimeMillis();
        log.debug("{},请求token,请求url:{},耗时:{}返回参数:{}", LOGGER_MSG, tokenUrl, endTime - startTime, doPost);
        AccessTokenBean accessTokenBean = JsonUtils.getInstance().parseObject(doPost, AccessTokenBean.class);
        String token = accessTokenBean.getAccess_token();
        return token;
    }

    /**
     * 开放平台通用外层信息包装
     *
     * @param content
     * @param entCode
     * @return requestJson
     */
    public static String buildGlobalInfoCheck(String content, String entCode) {
        GlobalInfo globalInfo = new GlobalInfo();
        globalInfo.setZipCode("0");
        globalInfo.setEncryptCode("0");
        globalInfo.setDataExchangeId(RandomUtil.randomNumbers(ConfigurerInfo.INT_32));
        globalInfo.setEntCode(entCode);
        globalInfo.setContent(Base64Encoding.encode(content));
        String requestJson = JsonUtils.getInstance().toJsonString(globalInfo);
        return requestJson;
    }
}
