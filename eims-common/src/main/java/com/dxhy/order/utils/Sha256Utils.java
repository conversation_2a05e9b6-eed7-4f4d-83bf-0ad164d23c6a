package com.dxhy.order.utils;

/**
 * @Auther: admin
 * @Date: 2025/2/7 17:21
 * @Description:
 */
public class Sha256Utils {


    public static String sha256(String str){
        return sha256(str, "UTF-8");
    }
    public static String sha256(String str, String charset){
        try {
            return sha256(str.getBytes(charset));
        } catch (Exception e) {
            e.printStackTrace();

        }
        return null;
    }
    public static String sha256(byte[] bytes) {
        try {
            java.security.MessageDigest md =
                    java.security.MessageDigest.getInstance("SHA-256");
            byte[] digest = md.digest(bytes);
            return byte2hex(digest);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    public static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }
}
