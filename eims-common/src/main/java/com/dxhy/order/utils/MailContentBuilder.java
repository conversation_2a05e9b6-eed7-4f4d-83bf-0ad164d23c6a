package com.dxhy.order.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 邮件内容构造器
 * 根据模板动态生成邮件正文
 *
 * @author: <a href="<EMAIL>">ya<PERSON>ug<PERSON></a>
 * @createDate: Created in 2021-12-13
 */
@Component
public class MailContentBuilder {
    
    /**
     * 异常发票推送 邮件模板CODE
     */
    private static final String ABNORMAL_INVOICE_PUSH_EMAIL_TEMPLATE_CODE = "53";
    
    /**
     * 余票预警 邮件模板CODE
     */
    private static final String REMAINING_INVOICE_WARNING_EMAIL_TEMPLATE_CODE = "65";
    
    /**
     * 异常订单通知 邮件模板CODE
     */
    private static final String ABNORMAL_ORDER_NOTICE_EMAIL_TEMPLATE_CODE = "55";
    
    public String getMailContent(String templateId, String[] contentElements) {
        String mainContent = "";
        if (ArrayUtil.isNotEmpty(contentElements)) {
            if (StringUtils.isNotBlank(templateId)) {
                Map<String, String> elementMap = getFreeMarkerParamMap(templateId, contentElements);
                if (CollectionUtil.isNotEmpty(elementMap)) {
                    String templateName = templateId + ".ftl";
//                    FreeMarkerUtil freeMarkerUtil = SpringUtil.getBean(FreeMarkerUtil.class);
                    FreeMarkerUtil freeMarkerUtil =new FreeMarkerUtil();
                    mainContent = freeMarkerUtil.processTemplate(templateName, elementMap);
                }
            }
        }
        return mainContent;
    }
    
    private Map<String, String> getFreeMarkerParamMap(String templateId, String[] contentElements) {
        Map<String, String> paramMap = new HashMap<>(10);
        if (ABNORMAL_INVOICE_PUSH_EMAIL_TEMPLATE_CODE.equals(templateId)) {
            //异常发票推送
            paramMap.put("year", contentElements[0]);
            paramMap.put("month", contentElements[1]);
            paramMap.put("day", contentElements[2]);
            paramMap.put("fphm", contentElements[3]);
            paramMap.put("xhfmc", contentElements[4]);
            paramMap.put("ghfmc", contentElements[5]);
            paramMap.put("kphjje", contentElements[6]);
        } else if (REMAINING_INVOICE_WARNING_EMAIL_TEMPLATE_CODE.equals(templateId)) {
            //余票预警
            paramMap.put("sbmc", contentElements[0]);
            paramMap.put("sbbh", contentElements[1]);
            paramMap.put("mc", contentElements[2]);
            paramMap.put("dqfpfs", contentElements[3]);
        } else if (ABNORMAL_ORDER_NOTICE_EMAIL_TEMPLATE_CODE.equals(templateId)) {
            //异常订单通知
            paramMap.put("ddh", contentElements[0]);
            paramMap.put("statusRemark", contentElements[1]);
            paramMap.put("kphjje", contentElements[2]);
            paramMap.put("createTime", contentElements[3]);
            paramMap.put("fpzldm", contentElements[4]);
            paramMap.put("sbyy", contentElements[5]);
        }
        else if ("mailContent".equals(templateId)) {
            //
            paramMap.put("ddrq", contentElements[0]);
            paramMap.put("fphm", contentElements[2]);
            paramMap.put("kprq", contentElements[3]);
            paramMap.put("gfmc", contentElements[4]);
            paramMap.put("kphjje", contentElements[5]);

        }
        return paramMap;
    }
}
