package com.dxhy.order.utils;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 分页
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 11:39:24
 */
@ApiModel("分页相关参数")
public class BasePage {

    @TableField(exist = false)
    @ApiModelProperty(name = "页码")
    private int currPage = 1;

    @TableField(exist = false)
    @ApiModelProperty(name = "每页数量")
    private int pageSize = 10;

    public int getCurrPage() {
        return currPage;
    }

    public void setCurrPage(int currPage) {
        this.currPage = currPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
