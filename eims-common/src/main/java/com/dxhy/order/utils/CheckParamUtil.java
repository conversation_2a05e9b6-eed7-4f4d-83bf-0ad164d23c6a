package com.dxhy.order.utils;


import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.model.OrderInfoContentEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;


/**
 * 非空长度校验工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CheckParamUtil {

    private static final String LOGGER_MSG = "(非空长度校验)";


    /**
     * 校验默认的长度
     *
     * @param content
     * @param param
     * @return
     */
    public static Map<String, String> checkParam(OrderInfoContentEnum content, String param) {
        return checkParam(content, param, -1);
    }

    /**
     * 校验默认的长度返回明细行书
     *
     * @param content
     * @param param
     * @param num
     * @return
     */
    public static Map<String, String> checkParam(OrderInfoContentEnum content, String param, int num) {

        return checkParam(content, param, "", num);
    }

    /**
     * 最原子方法, 返回明细
     *
     * @param content
     * @param param
     * @param num
     * @return
     */
    public static Map<String, String> checkParam(OrderInfoContentEnum content, String param, String terminalCode, int num) {

        String replyMsg;
        String numMsg = "";
        if (-1 != num) {
            numMsg = "第" + (num + 1) + "行,";
        }
        Map<String, String> map = new HashMap<>(5);
        map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.SUCCESS.getKey());
        //校验是否为空
        if (content.getCheckNull()) {
            if (StringUtils.isBlank(param)) {
                replyMsg = numMsg + content.getMessage() + "不能为空!";
                map.put(OrderManagementConstant.ERRORCODE, content.getKey());
                map.put(OrderManagementConstant.ERRORMESSAGE, replyMsg);
            }
        }

        //校验长度
        int strLength = 0;
        if (StringUtils.isNotBlank(param) && content.getCheckLength()) {
            try {
                strLength = param.getBytes("GBK").length;
            } catch (UnsupportedEncodingException e) {
                replyMsg = numMsg + content.getMessage() + "获取长度异常!";
                map.put(OrderManagementConstant.ERRORCODE, content.getKey());
                map.put(OrderManagementConstant.ERRORMESSAGE, replyMsg);
            }
            if (content.getMinLength() != 0) {
                if (content.getMaxLength() == content.getMinLength() && (strLength != content.getMinLength())) {
                    replyMsg = numMsg + content.getMessage() + "数据不合法,长度应等于" + content.getMinLength() + "!";
                    map.put(OrderManagementConstant.ERRORCODE, content.getKey());
                    map.put(OrderManagementConstant.ERRORMESSAGE, replyMsg);
                } else if (strLength > content.getMaxLength() || strLength < content.getMinLength()) {
                    replyMsg = numMsg + content.getMessage() + "数据不合法,长度应大于" + content.getMinLength() + "小于" + content.getMaxLength() + "!";
                    map.put(OrderManagementConstant.ERRORCODE, content.getKey());
                    map.put(OrderManagementConstant.ERRORMESSAGE, replyMsg);
                }
            }

        }

        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(map.get(OrderManagementConstant.ERRORCODE))) {
            log.error("{}数据:{},失败code为:{},失败原因为:{}", LOGGER_MSG, param, map.get(OrderManagementConstant.ERRORCODE), map.get(OrderManagementConstant.ERRORMESSAGE));
        }
        return map;
    }

    /**
     * 校验税号规则
     *
     * @param nsrsbh
     * @return
     */
    public static Map<String, String> checkNsrsbhParam(OrderInfoContentEnum contentEnum,
                                                       OrderInfoContentEnum contentEnum1, OrderInfoContentEnum contentEnum2, String nsrsbh) {

        Map<String, String> checkResultMap = new HashMap<>(10);
        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.SUCCESS.getKey());

        checkResultMap = CheckParamUtil.checkParam(contentEnum, nsrsbh);
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (StringUtils.isNotEmpty(nsrsbh)) {
            // 是否包含空格
            if (nsrsbh.contains(ConfigureConstant.STRING_SPACE)) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE,
                        OrderInfoContentEnum.CHECK_ISS7PRI_107164.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE,
                        OrderInfoContentEnum.CHECK_ISS7PRI_107164.getMessage());
                return checkResultMap;
            }
            // 判断税号长度合法性问题,长度必须14,15,16,17,18,20位
            if (ConfigureConstant.INT_6 > ValidateUtil.getStrBytesLength(nsrsbh)
                    && ConfigureConstant.INT_20 < ValidateUtil.getStrBytesLength(nsrsbh)) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE, contentEnum1.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, contentEnum1.getMessage());
                return checkResultMap;
            }
            // 纳税人识别号需要全部大写
            if (!ValidateUtil.isAcronym(nsrsbh)) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE, contentEnum2.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, contentEnum2.getMessage());
                return checkResultMap;
            }
        }

        return checkResultMap;
    }

    /**
     * 异常返回统用工具
     *
     * @param orderInfoContentEnum
     * @return
     */
    public static Map<String, String> generateErrorMap(OrderInfoContentEnum orderInfoContentEnum) {
        Map<String, String> errorMap = new HashMap<>(2);
        errorMap.put(OrderManagementConstant.ERRORCODE, orderInfoContentEnum.getKey());
        errorMap.put(OrderManagementConstant.ERRORMESSAGE, orderInfoContentEnum.getMessage());
        log.error("数据校验结果码为:{},校验结果信息为:{}", orderInfoContentEnum.getKey(), orderInfoContentEnum.getMessage());
        return errorMap;
    }


    /**
     * 组装返回信息
     *
     * @param fpqqlsh
     * @param errorMsg
     * @param orderInfoContentEnum
     * @return
     */
    public static Map<String, String> generateErrorMap(String fpqqlsh, String errorMsg, OrderInfoContentEnum orderInfoContentEnum) {
        StringBuilder stringBuilder = new StringBuilder();

        if (StringUtils.isBlank(fpqqlsh)) {
            stringBuilder.append(errorMsg).append(orderInfoContentEnum.getMessage());
        } else {
            stringBuilder.append("请求流水号:").append(fpqqlsh).append(",").append(errorMsg).append(orderInfoContentEnum.getMessage());
        }
        Map<String, String> errorMap = new HashMap<>(10);
        errorMap.put(OrderManagementConstant.ERRORCODE, orderInfoContentEnum.getKey());
        errorMap.put(OrderManagementConstant.ERRORMESSAGE, stringBuilder.toString());
        log.error("{}数据校验结果码为:{},校验结果信息为:{}", LOGGER_MSG, orderInfoContentEnum.getKey(), stringBuilder.toString());
        return errorMap;
    }
}
