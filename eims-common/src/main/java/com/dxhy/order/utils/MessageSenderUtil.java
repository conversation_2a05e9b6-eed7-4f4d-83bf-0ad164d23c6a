package com.dxhy.order.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.order.modules.entity.AttachmentVo;
import com.dxhy.order.modules.entity.MailContentVo;
import com.dxhy.order.modules.entity.SendMailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: wangyang
 * @date: 2020/4/26 11:38
 * @description
 */
@Component
@Slf4j
public class MessageSenderUtil {
    public static final String DEFAULT_ENCODING = "UTF-8";
    
    
    private final ExecutorService executorService = new ThreadPoolExecutor(5, 20,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024), new ThreadFactoryBuilder()
            .setNamePrefix("messageSendPool-%d").build(), new ThreadPoolExecutor.AbortPolicy());
    
    private JavaMailSender mailSender;
    
    private static SendMailVo sendMailVo;
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile("[;,:\"<>]");
    
    /**
     * QQ邮箱后缀
     */
    private static final String QQ_EMAIL_SUFFIX = ".qq.com";
    
    
    //加载发件人配置信息
    static {
        Map<String, String> map = PropertiesUtil.properties2Map();
        String params = JSONObject.toJSONString(map);
        sendMailVo = JSONObject.parseObject(params, SendMailVo.class);
    }
    
    public JavaMailSender getMailSender() {
        return mailSender;
    }
    
    public void setMailSender(JavaMailSender mailSender) {
        this.mailSender = mailSender;
    }
    
    private boolean createMailConfig() {
        log.info("发件人邮箱配置信息:{}", sendMailVo.toString());
        
        if (sendMailVo == null) {
            log.error("发件人邮件配置信息为空");
            return false;
        }
        
        boolean result = StringUtils.isBlank(sendMailVo.getSendAddress())
                || StringUtils.isBlank(sendMailVo.getSmtpServer())
                || ("true".equals(sendMailVo.getSmtpAuth()) && StringUtils.isBlank(sendMailVo.getAuthPassword()));
        if (result) {
            log.error("发件人邮件配置信息为空或配置有误");
            return false;
        }
        
        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        javaMailSender.setHost(sendMailVo.getSmtpServer());
        javaMailSender.setPort(Integer.parseInt(sendMailVo.getPort()));
        javaMailSender.setUsername(sendMailVo.getSendName());
        javaMailSender.setPassword(sendMailVo.getAuthPassword());
        javaMailSender.setDefaultEncoding(DEFAULT_ENCODING);
        
        //设置Session中的信息,保证邮件认证的一致性
        Properties props = new Properties();
        props.put("mail.smtp.host", sendMailVo.getSmtpServer());
        props.put("mail.smtp.port", sendMailVo.getPort());
        props.put("mail.smtp.auth", sendMailVo.getSmtpAuth());
        props.put("mail.transport.protocol", "smtp");
        //如果是qq.com启用ssl
        if (sendMailVo.getSmtpServer().toLowerCase().endsWith(QQ_EMAIL_SUFFIX)) {
            props.put("mail.smtp.starttls.enable", "true");
        }
        
        Authenticator authentic = null;
        if (Boolean.TRUE.toString().equals(sendMailVo.getSmtpAuth())) {
            authentic = new MyAuthenticator(sendMailVo.getSendAddress(), sendMailVo.getAuthPassword());
        }
        Session session = Session.getInstance(props, authentic);
        javaMailSender.setSession(session);
        
        this.setMailSender(javaMailSender);
        return true;
    }
    
    private static class MyAuthenticator extends Authenticator {
        String userName = null;
        String password = null;
        
        public MyAuthenticator(String username, String password) {
            this.userName = username;
            this.password = password;
        }
        
        @Override
        protected PasswordAuthentication getPasswordAuthentication() {
            return new PasswordAuthentication(userName, password);
        }
    }
    
    /**
     * @Description: 发送邮件业务放入线程池处理
     * @Author: wangyang
     * @Date:2020/5/5 11:37
     * @Param:
     * @return:
     */
    public String sendMailExecutor(MailContentVo mailContent, SendMailVo sendVo) throws Exception {
        SendMailWork sendMailWork = new SendMailWork(mailContent, sendVo);
        Future<String> future = executorService.submit(sendMailWork);
        return future.get();
    }
    
    private class SendMailWork implements Callable<String> {
        
        private final MailContentVo mailContent;
        
        private final SendMailVo paramSendVo;
        
        public SendMailWork(MailContentVo mailContent, SendMailVo paramSendVo) {
            this.mailContent = mailContent;
            this.paramSendVo = paramSendVo;
            
            if (paramSendVo != null && StringUtils.isNotBlank(paramSendVo.getSendAddress())
                    && StringUtils.isNotBlank(paramSendVo.getAuthPassword())
                    && StringUtils.isNotBlank(paramSendVo.getSendName())
                    && StringUtils.isNotBlank(paramSendVo.getSmtpServer())) {
                sendMailVo = paramSendVo;
            }
        }
        
        @Override
        public String call() {
            log.info("线程ID-->{} start...", Thread.currentThread().getId());
            String msg = "";
            try {
                sendMailWithFile(mailContent);
            } catch (Exception e) {
                log.error("发送邮件失败", e);
                msg = e.getMessage();
            }
            log.info("线程ID-->{} end...", Thread.currentThread().getId());
            return msg;
        }
    }
    
    /**
     * @Description: 发送带附件的邮件
     * @Author: wangyang
     * @Date:2020/5/5 11:27
     * @Param:
     * @return:
     */
    public void sendMailWithFile(MailContentVo mailContent) {
        //初始化邮件配置信息
        boolean configFlag = createMailConfig();
        if (!configFlag) {
            return;
        }
        
        log.info("线程ID-->{} start replace mail template...", Thread.currentThread().getId());
//        MailContentBuilder mailContentBuilder = SpringUtil.getBean(MailContentBuilder.class);
        MailContentBuilder mailContentBuilder =  new MailContentBuilder();
        String content = mailContentBuilder.getMailContent(mailContent.getTemplateId(), mailContent.getContent());
        log.info("线程ID-->{} end replace mail template...", Thread.currentThread().getId());
        
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        try {
            Address fromAddress = getFrom(sendMailVo.getSendName(), sendMailVo.getSendAddress());
            //发件人 支持带昵称
            mimeMessage.setFrom(fromAddress);
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage);
            //收件人
            messageHelper.setTo(mailContent.getTo());
            //抄送人
            if (mailContent.getCc() != null) {
                messageHelper.setCc(mailContent.getCc());
            }
            //邮件标题
            String[] subjects = mailContent.getSubject();
            if (ArrayUtil.isNotEmpty(subjects)) {
                messageHelper.setSubject(subjects[0]);
            }
            
            //内容附件对象
            Multipart multipart = new MimeMultipart();
            //文本内容
            BodyPart txt = new MimeBodyPart();
            //设置内容，格式
            txt.setContent(content, "text/html;charset=utf-8");
            //把文本内容添加到part中
            multipart.addBodyPart(txt);
            
            List<AttachmentVo> attachmentVos = mailContent.getAttachmentVos();
            if (CollectionUtil.isNotEmpty(attachmentVos)) {
                for (AttachmentVo attachmentVo : attachmentVos) {
                    //附件
                    MimeBodyPart addpendix = new MimeBodyPart();
                    //数据源
                    DataSource ds = new ByteArrayDataSource(new ByteArrayInputStream(
                            attachmentVo.getFileBytes()), attachmentVo.getType());
                    //添加附件
                    addpendix.setDataHandler(new DataHandler(ds));
                    //设置附件的名称
                    addpendix.setFileName(MimeUtility.encodeText(attachmentVo.getName()));
                    //解决乱码
                    //把附件添加到part中
                    multipart.addBodyPart(addpendix);
                }
            }
            mimeMessage.setContent(multipart);
            log.info("线程ID-->{} JavaMailSender send start.....", Thread.currentThread().getId());
            mailSender.send(mimeMessage);
            log.info("线程ID-->{} JavaMailSender send success....", Thread.currentThread().getId());
        } catch (Exception e) {
            log.error("发送邮件异常", e);
            throw new RuntimeException("发送邮件异常：" + e.getMessage());
        }
    }
    
    /**
     * @Description: 获取含昵称的发件人
     * @Author: wangyang
     * @Date:2020/5/5 11:27
     * @Param:
     * @return:
     */
    private Address getFrom(String nickename, String from) {
        String nick = "";
        try {
            Matcher matcher = EMAIL_PATTERN.matcher(nickename);
            nickename = matcher.replaceAll("").trim();
            nick = javax.mail.internet.MimeUtility.encodeText(nickename, "utf-8", "B");
            return new InternetAddress(nick + " <" + from + ">");
        } catch (Exception e) {
            log.error("昵称处理异常", e);
            return null;
        }
    }
}
