package com.dxhy.order.utils;

import cn.hutool.core.util.ObjectUtil;
import com.dxhy.order.constant.ConfigureConstant;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @ClassName ：StringUtils
 * @Description ：字符串工具类
 * @date 创建时间: 2022-06-29 09:42
 */
public class StringUtil {
    
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("0000000");

    private static final Pattern CHAR_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    private static final Pattern REPLACE_STR = Pattern.compile("\\s*|\t|\r|\n");

    /**
     * 默认替换\r\n\t
     * fankunfeng
     * @param param
     * @return
     */
    public static String replaceStr(String param) {
        return replaceStr(param, true);
    }

    /**
     * 对param进行特殊处理
     * 1.替换 中文（ 为 英文(
     * 2.替换特殊字符为空格
     * 3. \r \t \n 替换为空格（备注除外）
     * fankunfeng
     */
    public static String replaceStr(String param, boolean flag) {

        if (StringUtils.isNotBlank(param)) {
            // 括号处理
//            param = param.replace("（", "(");
//            param = param.replace("）", ")");
            // \t \r \n处理
            if (flag) {
                param = param.replace("\r", "");
                param = param.replace("\t", "");
                param = param.replace("\n", "");
            }
            param = GbkUtil.replaceX(param);
        }
        return param;
    }
    
    /**
     * 根据字节长度截取字符串
     *
     * @param orignal
     * @param start
     * @param count
     * @return
     */
    public static String substringByte(String orignal, int start, int count) {
        
        //如果目标字符串为空，则直接返回，不进入截取逻辑；
        if (orignal == null || "".equals(orignal)) {
            return orignal;
        }
        
        //截取Byte长度必须>0
        if (count <= 0) {
            return orignal;
        }

        //截取的起始字节数必须比
        if (start < 0) {
            start = 0;
        }

        //目标char Pull buff缓存区间；
        StringBuffer buff = new StringBuffer();
        try {
            //截取字节起始字节位置大于目标String的Byte的length则返回空值
            if (start >= getStringByteLenths(orignal)) {
                return null;
            }
            // int[] arrlen=getByteLenArrays(orignal);
            int len = 0;
            char c;

            //遍历String的每一个Char字符，计算当前总长度
            //如果到当前Char的的字节长度大于要截取的字符总长度，则跳出循环返回截取的字符串。
            for (int i = 0; i < orignal.toCharArray().length; i++) {
                c = orignal.charAt(i);
                //当起始位置为0时候
                if (start == 0) {
                    len += String.valueOf(c).getBytes("gbk").length;
                    if (len <= count) {
                        buff.append(c);
                    } else {
                        break;
                    }
                } else {
                    //截取字符串从非0位置开始
                    len += String.valueOf(c).getBytes("gbk").length;
                    if (len >= start && len <= start + count) {
                        buff.append(c);
                    }
                    if (len > start + count) {
                        break;
                    }
                }
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //返回最终截取的字符结果;
        //创建String对象，传入目标char Buff对象
        return new String(buff);
    }

    /**
     * 根据gbk获取byte数组获取String字符串的长度
     *
     * @param args
     * @return
     * @throws UnsupportedEncodingException
     */
    public static int getStringByteLenths(String args) throws UnsupportedEncodingException {
        return StringUtils.isNotBlank(args) ? args.getBytes("gbk").length : 0;
    }
    
    public static String subStringByByte(String str, int len) throws IOException {
        byte[] buf = str.getBytes("gbk");
        int count = 0;
        for (int x = len - 1; x >= 0; x--) {
            if (buf[x] < 0) {
                count++;
            } else {
                break;
            }
        }
        if (count % ConfigureConstant.INT_2 == 0) {
            return new String(buf, 0, len, "gbk");
        } else {
            return new String(buf, 0, len - 1, "gbk");
        }
    }

    public static String slFormat(String args) {
        DecimalFormat df1 = new DecimalFormat("0.########");
        if (StringUtils.isBlank(args)) {
            return args;
        }
        return df1.format(Double.parseDouble(args));
    }

    /**
     * 格式化两位小数
     * @param
     * @return java.lang.String
     * <AUTHOR>
     **/
    public static String numberFormat2(String args) {
        if(StringUtils.isEmpty(args)){
            return "";
        }
        BigDecimal bd = new BigDecimal(args);
        return bd.setScale(2, RoundingMode.HALF_UP).toString();
    }
    
    /***
     * 匹配是否需要补全商品简码
     * ture为需要补全
     * false为不需要补全
     * @param spmc
     * @return
     */
    public static boolean checkStr(String spmc, String spjc) {
    
        /**
         * 1.判断商品是否以星号开头
         * 2.判断商品名称中是否包含2个以上星号
         * 3.截取前两个星号中间的简称
         * 4.判断简称为汉字
         * 5.判断简称超过4个字节.简称最小为2个汉字
         * 6.都符合条件返回为true,已经包含商品简称,不需要补全
         */
        //默认不需要补填
        boolean bl = true;
        if (spmc.indexOf(ConfigureConstant.STRING_STAR) == 0) {
            String[] split = spmc.split("\\*");
            if (split.length > ConfigureConstant.INT_2) {
                String jm = split[1];
            
            
                //判断简码是否为汉字
                if (checkName(jm)) {
                    try {
                        if (jm.getBytes(ConfigureConstant.STRING_CHARSET_GBK).length >= ConfigureConstant.INT_2) {
                            /**
                             * 判断简码和商品简称是否一致,如果一致不需要补全简称,如果不一致需要补全简称
                             */
                            if (StringUtils.isNotBlank(spjc) && !jm.equals(spjc)) {
        
                            } else {
                                bl = false;
                            }
    
                        }
                    } catch (UnsupportedEncodingException ignored) {
    
                    }
                }
            }
        }
        return bl;
    }

    /**
     * 校验是否为汉字,
     * true为汉字
     * false为非汉字
     *
     * @param name
     * @return
     */
    public static boolean checkName(String name) {
        Matcher m = CHAR_PATTERN.matcher(name);
        return m.find();
    }

    /**
     * @param @param  spbm
     * @param @return
     * @return String
     * @throws
     * @Title : fillZero
     * @Description ：末位补零
     */
    public static String fillZero(String spbm,int length) {
    
        if (StringUtils.isNotEmpty(spbm)) {
            spbm = StringUtils.rightPad(spbm, length, ConfigureConstant.STRING_0);
        }
        return spbm;
    }
    
    public static String subBz(String bz) {
        //校验长度
        int strLength = 0;
        try {
            strLength = bz.getBytes(ConfigureConstant.STRING_CHARSET_GBK).length;
        } catch (UnsupportedEncodingException e) {
            // TODO 后期考虑异常情况
        }
    
        if (strLength > ConfigureConstant.INT_150) {
            bz = StringUtil.substringByte(bz, 0, ConfigureConstant.INT_150);
            
        }
        if (bz.endsWith(ConfigureConstant.STRING_SEMICOLON)) {
            bz = bz.substring(0, bz.length() - 1);
        }
        return bz;
    }
    
    public static String replaceBlank(String str) {
        String dest = "";
        if (str != null) {
            Matcher m = REPLACE_STR.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }
    
    
    /**
     * 判断时间格式
     *
     * @param date
     * @return
     */

    public static String checkDateFormat(String date) {
    
        date = date.trim();
        //yyyyMMddHHmmss
        String a1 = "[0-9]{4}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{2}";
        //yyyyMMdd
        String a2 = "[0-9]{4}[0-9]{2}[0-9]{2}";
        //yyyy-MM-dd HH:mm:ss
        String a3 = "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}";
        //yyyy-MM-dd
        String a4 = "[0-9]{4}-[0-9]{2}-[0-9]{2}";
        //yyyy-MM-dd  HH:mm
        String a5 = "[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}";
    
        boolean date1 = Pattern.compile(a1).matcher(date).matches();
    
        if (date1) {
            return ConfigureConstant.DATE_FORMAT_DATE_YMDHMS;
        }
        boolean date2 = Pattern.compile(a2).matcher(date).matches();
        if (date2) {
            return ConfigureConstant.DATE_FORMAT_DATE_YMD;
        }
        boolean date3 = Pattern.compile(a3).matcher(date).matches();
        if (date3) {
            return ConfigureConstant.DATE_FORMAT_DATE_Y_M_DH_M_S;
        }
    
        boolean date4 = Pattern.compile(a4).matcher(date).matches();
        if (date4) {
            return ConfigureConstant.DATE_FORMAT_DATE;
        }
        boolean date5 = Pattern.compile(a5).matcher(date).matches();
        if (date5) {
            return ConfigureConstant.DATE_FORMAT_DATE_Y_M_DH_M;
        }
        return "";
    
    }
    
    /**
     * 多个字符串拼接,去除空字符串
     *
     * @param param
     * @return
     */
    public static String strAdd(String... param) {
        StringBuilder stringBuilder = new StringBuilder();
        if (ObjectUtil.isEmpty(param)) {
            return "";
        }
        for (String s : param) {
            if (StringUtils.isNotEmpty(s)) {
                stringBuilder.append(s);
            }
        }
        return stringBuilder.toString();
    }
}
