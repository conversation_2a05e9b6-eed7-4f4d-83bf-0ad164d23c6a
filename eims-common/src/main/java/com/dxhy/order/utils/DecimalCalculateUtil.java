package com.dxhy.order.utils;


import com.dxhy.order.constant.InvoiceTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 数值格式化工具类
 *
 * <AUTHOR>
 */
public class DecimalCalculateUtil {
    /**
     * 由于Java的简单类型不能够精确的对浮点数进行运算，这个工具类提供精
     * 确的浮点数运算，包括加减乘除和四舍五入。
     */
    /**
     * 默认除法运算精度
     */
    private static final int DEF_DIV_SCALE = 10;

    /**
     * 这个类不能实例化
     */
    private DecimalCalculateUtil() {
    }

    /**
     * 提供精确的加法运算。
     *
     * @param v1 被加数
     * @param v2 加数
     * @return 两个参数的和
     */
    public static double add(double v1, double v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.add(b2).doubleValue();
    }
    public static String bigDecimalAddNew(String s1, String s2, int scale) {
        BigDecimal b1 = new BigDecimal(s1);
        BigDecimal b2 = new BigDecimal(s2);
        return b1.add(b2).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }
    public static String decimalSeFormat(BigDecimal se) {
        return se.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
    public static boolean stringIsZero(String v1) {
        if (StringUtils.isBlank(v1)) {
            return false;
        } else {
            BigDecimal b1 = new BigDecimal(v1);
            return BigDecimal.ZERO.compareTo(b1.abs()) == 0;
        }
    }
    public static boolean stringIsEquals(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b2.abs().compareTo(b1.abs()) == 0;
    }
    public static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            new BigDecimal(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static String dynamicDecimalFormatToStringWithoutZero(String parameterStr, String fpzlDm, String qdfwlx) {
        int xmslAfterPointLength = getAfterPointDynamicLength(fpzlDm, qdfwlx, parameterStr);
        return decimalFormatToStringWithoutZero(parameterStr, xmslAfterPointLength);
    }
    public static int getAfterPointDynamicLength(String fpzlDm, String qdfwlx, String numberStr) {
        int afterPointLength;
        if (judgeSdPz(fpzlDm) && "2".equals(qdfwlx)) { //RPA开票
            int integerDigits = getStringDigits(numberStr, "INTEGER");
            if (integerDigits == 0) {
                afterPointLength = getAfterPointLengthNew(fpzlDm, qdfwlx);
            } else if (integerDigits >= 1 && integerDigits <= 3) {
                afterPointLength = 13;
            } else if (integerDigits >= 4 && integerDigits <= 12) {
                afterPointLength = 16 - integerDigits;
            } else {
                afterPointLength = 4;
            }
        } else { //乐企开票
            afterPointLength = getAfterPointLengthNew(fpzlDm, qdfwlx);
        }
        return afterPointLength;
    }
    public static int getStringDigits(String numberStr, String type) {
        int digits = 0;
        if (StringUtils.isNotBlank(numberStr)) {
            String regex = "^-?\\d+\\.\\d+$";
            if (numberStr.matches(regex)) {
                String[] parts = numberStr.split("\\.");
                if ("INTEGER".equals(type)) {
                    digits = parts[0].length();
                } else if ("DECIMAL".equals(type) && parts.length > 1 && !parts[1].isEmpty()) {
                    digits = parts[1].length();
                }
            }
        }
        return digits;
    }
    @Deprecated
    public static int getAfterPointLengthNew(String fpzlDm, String qdfwlx) {
        int afterPointLength = 8;
        if (judgeSdPz(fpzlDm) && StringUtils.isNotBlank(qdfwlx)) {
            afterPointLength = 15;
            if ("2".equals(qdfwlx)) { //RPA开票
                afterPointLength = 13;
            }
        }
        return afterPointLength;
    }
    public static boolean judgeSdPz(String fpzldm) {
        return StringUtils.isNotBlank(fpzldm) && (InvoiceTypeEnum.ORDER_INVOICE_TYPE_001.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_002.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_003.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_104.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_085.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_086.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_087.getKey().equals(fpzldm) || InvoiceTypeEnum.ORDER_INVOICE_TYPE_088.getKey().equals(fpzldm))  ;
    }
    /**
     * bigDecimal数据相加
     *
     * @param s1
     * @param s2
     * @return
     */
    public static String bigDecimalAdd(String s1, String s2) {
        BigDecimal b1 = new BigDecimal(s1);
        BigDecimal b2 = new BigDecimal(s2);
        return b1.add(b2).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
    
    
    /**
     * bigDecimal数据相加
     *
     * @param s1
     * @param s2
     * @return
     */
    public static String bigDecimalAdd(String s1, String s2, int scale) {
        BigDecimal b1 = new BigDecimal(s1);
        BigDecimal b2 = new BigDecimal(s2);
        return b1.add(b2).setScale(scale, RoundingMode.HALF_UP).toPlainString();
    }
    
    /**
     * 提供精确的减法运算。
     *
     * @param v1 被减数
     * @param v2 减数
     * @return 两个参数的差
     */
    public static double sub(double v1, double v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.subtract(b2).doubleValue();
    }
    
    /**
     * bigDecimal 减法运算
     *
     * @param s1
     * @param s2
     * @return
     */
    public static String bigDecimalSub(String s1, String s2) {
        BigDecimal b1 = new BigDecimal(s1);
        BigDecimal b2 = new BigDecimal(s2);
        return b1.subtract(b2).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
    
    
    /**
     * bigDecimal 减法运算
     *
     * @param s1
     * @param s2
     * @return
     */
    public static String bigDecimalSub(String s1, String s2, int scale) {
        BigDecimal b1 = new BigDecimal(s1);
        BigDecimal b2 = new BigDecimal(s2);
        return b1.subtract(b2).setScale(scale, RoundingMode.HALF_UP).toPlainString();
    }
    
    
    /**
     * bigDecimal 减法运算
     *
     * @param s1
     * @param s2
     * @return
     */
    public static String bigDecimalSubAbs(String s1, String s2) {
        BigDecimal b1 = new BigDecimal(s1);
        BigDecimal b2 = new BigDecimal(s2);
        return b1.abs().subtract(b2.abs()).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
    
    /**
     * 提供精确的乘法运算。
     *
     * @param v1 被乘数
     * @param v2 乘数
     * @return 两个参数的积
     */
    public static double mul(double v1, double v2) {
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.multiply(b2).doubleValue();
    }
    
    public static String mul(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.multiply(b2).toPlainString();
    }
    
    /**
     * 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到
     * 小数点以后10位，以后的数字四舍五入。
     *
     * @param v1 被除数
     * @param v2 除数
     * @return 两个参数的商
     */
    public static double div(double v1, double v2) {
        return div(v1, v2, DEF_DIV_SCALE);
    }

    /**
     * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指
     * 定精度，以后的数字四舍五入。
     *
     * @param v1    被除数
     * @param v2    除数
     * @param scale 表示表示需要精确到小数点以后几位。
     * @return 两个参数的商
     */
    public static double div(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = BigDecimal.valueOf(v1);
        BigDecimal b2 = BigDecimal.valueOf(v2);
        return b1.divide(b2, scale, RoundingMode.HALF_UP).doubleValue();
    }
    
    /**
     * 两个字符串相除
     *
     * @param v1
     * @param v2
     * @param scale
     * @return
     */
    public static String div(String v1, String v2, int scale) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.divide(b2, scale, RoundingMode.HALF_UP).toPlainString();
    }
    
    /**
     * 按照四舍六入五看偶方式计算
     *
     * @param v1
     * @param v2
     * @param scale
     * @return
     */
    public static String divNew(String v1, String v2, int scale) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.divide(b2, scale, RoundingMode.HALF_EVEN).toPlainString();
    }
    public static String mulNew(String v1, String v2, int scale) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.multiply(b2).setScale(scale, RoundingMode.HALF_EVEN).toPlainString();
    }

    /**
     * 提供精确的小数位四舍五入处理。
     *
     * @param v     需要四舍五入的数字
     * @param scale 小数点后保留几位
     * @return 四舍五入后的结果
     */
    public static double round(double v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b = BigDecimal.valueOf(v);
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 提供精确的类型转换(Float)
     *
     * @param v 需要被转换的数字
     * @return 返回转换结果
     */
    public static float convertsToFloat(double v) {
        BigDecimal b = new BigDecimal(v);
        return b.floatValue();
    }

    /**
     * 提供精确的类型转换(Int)不进行四舍五入
     *
     * @param v 需要被转换的数字
     * @return 返回转换结果
     */
    public static int convertsToInt(double v) {
        BigDecimal b = new BigDecimal(v);
        return b.intValue();
    }

    /**
     * 提供精确的类型转换(Long)
     *
     * @param v 需要被转换的数字
     * @return 返回转换结果
     */
    public static long convertsToLong(double v) {
        BigDecimal b = new BigDecimal(v);
        return b.longValue();
    }

    /**
     * 返回两个数中大的一个值
     *
     * @param v1 需要被对比的第一个数
     * @param v2 需要被对比的第二个数
     * @return 返回两个数中大的一个值
     */
    public static double returnMax(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.max(b2).doubleValue();
    }

    /**
     * 返回两个数中小的一个值
     *
     * @param v1 需要被对比的第一个数
     * @param v2 需要被对比的第二个数
     * @return 返回两个数中小的一个值
     */
    public static double returnMin(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.min(b2).doubleValue();
    }
    
    /**
     * 精确对比两个数字
     *
     * @param v1 需要被对比的第一个数
     * @param v2 需要被对比的第二个数
     * @return 如果两个数一样则返回0，如果第一个数比第二个数大则返回1，反之返回-1
     */
    public static int stringCompare(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.compareTo(b2);
    }
    
    /**
     * 精确对比两个数字
     *
     * @param v1 需要被对比的第一个数
     * @param v2 需要被对比的第二个数
     * @return 如果两个数一样则返回0，如果第一个数比第二个数大则返回1，反之返回-1
     */
    public static int stringCompareAbs(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.abs().compareTo(b2.abs());
    }
    
    /**
     * <p>Double类型的格式化</p>
     *
     * @param parameterStr
     * @param length
     * @return String
     * @author: 张双超
     * @date: Created on Sep 10, 2015 3:18:51 PM
     */
    public static String decimalFormat(double parameterStr, int length) {
    
        BigDecimal bigDecimal = BigDecimal.valueOf(parameterStr);
        return bigDecimal.setScale(length, RoundingMode.HALF_UP).toPlainString();
    }
    
    /**
     * 字符串格式化为double类型,
     *
     * @param parameterStr
     * @param length
     * @return
     */
    public static double decimalFormatToDouble(String parameterStr, int length) {
    
        BigDecimal bigDecimal = new BigDecimal(parameterStr);
        return bigDecimal.setScale(length, RoundingMode.HALF_UP).doubleValue();
    }
    
    /**
     * 字符串格式化为字符串类型,
     *
     * @param parameterStr
     * @param length
     * @return
     */
    public static String decimalFormatToString(String parameterStr, int length) {
    
        BigDecimal bigDecimal = new BigDecimal(parameterStr);
        return bigDecimal.setScale(length, RoundingMode.HALF_UP).toPlainString();
    }
    
    /**
     * 字符串格式化为字符串类型,使用四舍六入五成双
     *
     * @param parameterStr
     * @param length
     * @return
     */
    public static String decimalFormatToStringNew(String parameterStr, int length) {
    
        BigDecimal bigDecimal = new BigDecimal(parameterStr);
        return bigDecimal.setScale(length, RoundingMode.HALF_EVEN).toPlainString();
    }
    
    
    /**
     * 字符串格式化为字符串类型,使用四舍六入五成双方式,小数点后保留非零位
     *
     * @param parameterStr
     * @param length
     * @return
     */
    public static String decimalFormatToStringWithoutZero(String parameterStr, int length) {
        
        BigDecimal bigDecimal = new BigDecimal(parameterStr);
        String plainString = bigDecimal.setScale(length, RoundingMode.HALF_EVEN).toPlainString();
        DecimalFormat decimalFormat = new DecimalFormat("0.########");
        return decimalFormat.format(Double.parseDouble(plainString));
    }
    
}
