package com.dxhy.order.utils;

import com.dxhy.order.constant.OrderInfoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 发票种类各形式转换工具类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-07-04 14:46
 */
@Slf4j
public class FpzldmUtils {

    private static final String LOGGER_MSG = "(发票种类代码转换)";

    /**
     *         发票票类型代码转换
     * 	增值税专用发票： 	  004       0
     * 	机动车销售统一发票：  005 	    12
     * 	二手车销售统一发票：  006 	    42
     * 	增值税普通发票：     007  	    2
     * 	增值税电子普通发票：  026 	    51
     * 	增值税电子专用发票：  028	    52
     * 	（全电）电普        0
     * 	（全电）电专        1
     * */

    /**
     * 发票种类代码 转 发票种类名称
     */
    public static String FpzldmToMc(String fpzldm) {

        String fpzdmc = "";
        if(StringUtils.isNotBlank(fpzldm)){
            switch (fpzldm) {
                case "0":
                    fpzdmc = OrderInfoEnum.ORDER_QDINVOICE_TYPE_0.getValue();
                    break;
                case "1":
                    fpzdmc = OrderInfoEnum.ORDER_QDINVOICE_TYPE_1.getValue();
                    break;
                case "004":
                    fpzdmc = OrderInfoEnum.ORDER_INVOICE_TYPE_004.getValue();
                    break;
                case "005":
                    fpzdmc = OrderInfoEnum.ORDER_INVOICE_TYPE_005.getValue();
                    break;
                case "006":
                    fpzdmc = OrderInfoEnum.ORDER_INVOICE_TYPE_006.getValue();
                    break;
                case "007":
                    fpzdmc = OrderInfoEnum.ORDER_INVOICE_TYPE_007.getValue();
                    break;
                case "026":
                    fpzdmc = OrderInfoEnum.ORDER_INVOICE_TYPE_026.getValue();
                    break;
                case "028":
                    fpzdmc = OrderInfoEnum.ORDER_INVOICE_TYPE_028.getValue();
                    break;
                default:
                    fpzdmc = fpzldm;
                    log.info("{}，未维护发票种类代码：{}", LOGGER_MSG, fpzldm);
                    break;
            }
        } else {
            log.info("{}，发票种类代码为空", LOGGER_MSG);
        }
        log.info("{}，发票种类代码：{}，转换发票种类名称：{}", LOGGER_MSG, fpzldm, fpzdmc);
        return fpzdmc;
    }
}
