package com.dxhy.order.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.tools.imageio.ImageIOUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FileConversion {

    private final static String LOGGER_MSG = "(PDF转图片)";

    private static final String SUFFIX_PDF = ".pdf";

    /**
     * 本地图片转换成base64字符串
     * @param file
     * @return
     */
    public static List<String> pngToBase64(File[] file) {
        List<String> dataList = new ArrayList<>();
        InputStream in = null;
        byte[] data = null;
        if(file!=null){
            for(File f :file){
                // 读取图片字节数组
                try {
                    in = new FileInputStream(f);
                    data = new byte[in.available()];
                    in.read(data);
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                // 对字节数组Base64编码
                String base64 = new String(Base64.encodeBase64(data), StandardCharsets.UTF_8);
                // 返回Base64编码过的字节数组字符串
                dataList.add("data:pdf/png;base64," + base64);
            }
        }
        return dataList;
    }

    /**
     * Description: 将pdf的base64转换为png的文件
     * @param  base64Content base64编码内容，文件的存储路径（含文件名）
     */
    /*public static File[] base64StringToPng(String base64Content,String pdfName,String pdfPath) {
        // PDF本地临时文件
        File tepmPdfFile = null;
        // PDF转换后的PNG文件
        File[] pngFiles = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;
        new File(pdfPath).mkdirs();
        try {
            //base64编码内容转换为字节数组
            byte[] streams = Base64Encoding.decode(base64Content);
            tepmPdfFile = new File(pdfPath + "项目单价" + pdfName + SUFFIX_PDF);
            if (tepmPdfFile.exists()) {
                tepmPdfFile.createNewFile();
            }
            FileUtils.writeByteArrayToFile(tepmPdfFile, streams);
            pngFiles = pdfToPngFile(tepmPdfFile.getAbsolutePath());
            return pngFiles;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }*/

    /**
     * @Description pdf 转png图片
     * <AUTHOR>
     * @Date 15:12 2018-08-13
     */
    public static File[] pdfToPngFile(String pdfPath) {
        {
            File[] files = null;

            PDDocument document = null;
            try {
                document = PDDocument.load(new File(pdfPath));
                PDFRenderer pdfRenderer = new PDFRenderer(document);
                int pageCounter = 0;
                files = new File[document.getNumberOfPages()];
                String imgPath = null;
                for (PDPage page : document.getPages()) {
                    BufferedImage bim = pdfRenderer.renderImageWithDPI(pageCounter, 150, ImageType.RGB);
                    imgPath = pdfPath.substring(0, pdfPath.lastIndexOf(".")) +"-"+ (pageCounter) + ".png";
                    ImageIOUtil.writeImage(bim, imgPath,150);
                    files[pageCounter] = new File(imgPath);
                    pageCounter++;
                }
            }
            catch (IOException e)  {
                files = null;
                log.error(e.getMessage(), e);
            }finally{
                try {
                    if (document != null) {
                        document.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    document = null;
                }
            }
            return files;
        }
    }

    public static byte[][] pdfByteToPngByte(byte[] pdfByte, String formatName) {
        byte[][] pngBytes = null;

        try {
            PDDocument document = PDDocument.load(pdfByte);
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            pngBytes = new byte[document.getNumberOfPages()][];
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(i, 200);
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ImageIO.write(bim, formatName, out);
                byte[] pngByte = out.toByteArray();
                pngBytes[i] = pngByte;
                out.close();
            }
            document.close();
        } catch (IOException e) {
            log.error("{}pdf流转换为图片流出现问题:{}", LOGGER_MSG, e);
        }
        return pngBytes;
    }
}
