package com.dxhy.order.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;

import java.net.Inet4Address;
import java.net.NetworkInterface;
import java.net.UnknownHostException;


/**
 * 分布式主键生成器
 *
 * <AUTHOR>
 */
@Slf4j
public final class DistributedKeyMaker {
    
    /**
     * 开始时间截 (2015-01-01)
     */
    private final long twepoch = 1489111610226L;
    
    /**
     * 机器id所占的位数
     */
    private final long workerIdBits = 5L;
    
    /**
     * 数据标识id所占的位数
     */
    private final long dataCenterIdBits = 5L;
    
    /**
     * 支持的最大机器id，结果是31 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
     */
    private final long maxWorkerId = ~(-1L << workerIdBits);
    
    /**
     * 支持的最大数据标识id，结果是31
     */
    private final long maxDataCenterId = ~(-1L << dataCenterIdBits);
    
    /**
     * 序列在id中占的位数
     */
    private final long sequenceBits = 12L;
    
    /**
     * 机器ID向左移12位
     */
    private final long workerIdShift = sequenceBits;
    
    /**
     * 数据标识id向左移17位(12+5)
     */
    private final long dataCenterIdShift = sequenceBits + workerIdBits;
    
    /**
     * 时间截向左移22位(5+5+12)
     */
    private final long timestampLeftShift = sequenceBits + workerIdBits + dataCenterIdBits;
    
    /**
     * 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)
     */
    private final long sequenceMask = ~(-1L << sequenceBits);
    
    /**
     * 工作机器ID(0~31)
     */
    private final long workerId;
    
    /**
     * 数据中心ID(0~31)
     */
    private final long dataCenterId;
    
    /**
     * 毫秒内序列(0~4095)
     */
    private long sequence = 0L;
    
    /**
     * 上次生成ID的时间截
     */
    private long lastTimestamp = -1L;
    
    private static final DistributedKeyMaker ID_WORKER;
    
    static {
        ID_WORKER = new DistributedKeyMaker(getWorkId(), getDataCenterId());
    }
    
    //==============================Constructors=====================================
    
    /**
     * 构造函数
     *
     * @param workerId     工作ID (0~31)
     * @param dataCenterId 数据中心ID (0~31)
     */
    public DistributedKeyMaker(long workerId, long dataCenterId) {
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format("workerId can't be greater than %d or less than 0", maxWorkerId));
        }
        if (dataCenterId > maxDataCenterId || dataCenterId < 0) {
            throw new IllegalArgumentException(String.format("dataCenterId can't be greater than %d or less than 0", maxDataCenterId));
        }
        this.workerId = workerId;
        this.dataCenterId = dataCenterId;
    }
    
    // ==============================Methods==========================================
    
    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * @return SnowflakeId
     */
    private synchronized long nextId() {
        long timestamp = timeGen();
        
        //如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                    String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }
        
        //如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            //毫秒内序列溢出
            if (sequence == 0) {
                //阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        }
        //时间戳改变，毫秒内序列重置
        else {
            sequence = 0L;
        }
        
        //上次生成ID的时间截
        lastTimestamp = timestamp;
        
        //移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - twepoch) << timestampLeftShift)
                | (dataCenterId << dataCenterIdShift)
                | (workerId << workerIdShift)
                | sequence;
    }
    
    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }
    
    /**
     * 返回以毫秒为单位的当前时间
     *
     * @return 当前时间(毫秒)
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }
    
    /**
     * workId使用服务器hostName生成
     *
     * @return
     */
    private static Long getWorkId() {
        try {
            String hostAddress = Inet4Address.getLocalHost().getHostAddress();
            log.info("获取到的系统地址:{}", hostAddress);
            if ("127.0.0.1".equals(hostAddress)) {
                hostAddress = getMacAddress();
                log.info("重新获取到的系统地址:{}", hostAddress);
            }
            int[] ints = StringUtils.toCodePoints(hostAddress);
            log.info("转换成十六进制的int数组:{}", ints);
            int sums = 0;
            for (int b : ints) {
                sums += b;
            }
            return (long) (sums % 32);
        } catch (UnknownHostException e) {
            // 如果获取失败，则使用随机数备用
            return RandomUtils.nextLong(0, 31);
        }
    }
    
    /**
     * dataCenterId使用IP生成
     *
     * @return
     */
    private static Long getDataCenterId() {
        String hostName = SystemUtils.getHostName();
        log.info("获取到的主机地址:{}", hostName);
        if (StringUtils.isBlank(hostName)) {
            hostName = SystemUtils.USER_NAME;
            log.info("默认获取到的主机地址:{}", hostName);
        }
        int[] ints = StringUtils.toCodePoints(hostName);
        log.info("获取到的十六进制主机地址:{}", ints);
    
        int sums = 0;
        for (int i : ints) {
            sums += i;
        }
        return (long) (sums % 32);
    }
    
    /**
     * 获取mac地址
     *
     * @return
     */
    private static String getMacAddress() {
        String macAddress = "";
        try {
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(Inet4Address.getLocalHost());
            byte[] hardwareAddress = networkInterface.getHardwareAddress();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < hardwareAddress.length; i++) {
                sb.append(String.format("%02X%s", hardwareAddress[i], (i < hardwareAddress.length - 1) ? "-" : ""));
            }
            macAddress = sb.toString();
        } catch (Exception e) {
            macAddress = SystemUtils.getUserName();
        }
        log.info("获取到的主机地址:{}", macAddress);
        return macAddress;
    }
    
    /**
     * 生成主键
     */
    public static String generateShotKey() {
        return String.valueOf(ID_WORKER.nextId());
    }
    
}
