package com.dxhy.order.utils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.ASN1ObjectIdentifier;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECNamedDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Base64;

import static java.util.Objects.isNull;

/**
 * @Auther: admin
 * @Date: 2025/2/7 17:23
 * @Description:
 */
@Slf4j
public class SM2Utils {

    private static final String EC = "EC";
    private static final Base64.Encoder BASE64_ENCODER = Base64.getEncoder();
    private static final Base64.Decoder BASE64_DECODER = Base64.getDecoder();
    private static final BouncyCastleProvider PROVIDER = new BouncyCastleProvider();
    static {
        if (isNull(Security.getProvider(BouncyCastleProvider.PROVIDER_NAME))) {

            Security.addProvider(PROVIDER);
        }
    }
    /**
     * SM2 加密算法
     *
     * @param publicKey 公钥
     * @param data 待加密的数据
     * @param mode 密文排列方式
     * @return 密文，BC 库产生的密文带由 04 标识符，与非 BC 库对接时需要去掉开头的 04
     */
    @SneakyThrows
    public static byte[] encrypt(byte[] publicKey, byte[] data, SM2Engine.Mode mode) {
        final ASN1ObjectIdentifier sm2p256v1 = GMObjectIdentifiers.sm2p256v1;
// 获取一条 SM2 曲线参数
        X9ECParameters parameters = GMNamedCurves.getByOID(sm2p256v1);
// 构造 ECC 算法参数，曲线方程、椭圆曲线 G 点、大整数 N
        ECNamedDomainParameters namedDomainParameters = new ECNamedDomainParameters(
                sm2p256v1, parameters.getCurve(), parameters.getG(), parameters.getN());
//提取公钥点
        ECPoint pukPoint = parameters.getCurve().decodePoint(publicKey);
// 公钥前面的 02 或者 03 表示是压缩公钥，04 表示未压缩公钥, 04 的时候，可以去掉前面的 04
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint,
                namedDomainParameters);
        SM2Engine sm2Engine = new SM2Engine(mode);
        SecureRandom secureRandom = new SecureRandom();
// 设置 sm2 为加密模式
        sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, secureRandom));
        final byte[] encrypt = sm2Engine.processBlock(data, 0, data.length);
// if (encrypt[0] == 0x04) {
// return Arrays.copyOfRange(encrypt, 1, encrypt.length);
// }
        return encrypt;
    }
    /**
     * SM2 加密算法,加密使用这个方法
     * @param publicKey
     * @param data
     * @return
     */
    public static String encryptBase64(String publicKey, String data) {
        return encryptBase64(publicKey, data, SM2Engine.Mode.C1C3C2);
    }
    /**
     * SM2 加密算法
     * @param publicKey
     * @param data
     * @param mode
     * @return
     */
    public static String encryptBase64(String publicKey, String data, SM2Engine.Mode mode) {

        final byte[] key = BASE64_DECODER.decode(publicKey);
        byte[] bytes = data.getBytes(StandardCharsets.UTF_8);
        final byte[] encrypt = encrypt(key, bytes, mode);
        return BASE64_ENCODER.encodeToString(encrypt);
    }
    /**
     * SM2 解密算法
     *
     * @param privateKey 私钥
     * @param cipherData 密文数据
     * @param mode 密文排列方式
     * @return
     */
    @SneakyThrows
    public static byte[] decrypt(BigInteger privateKey, byte[] cipherData, SM2Engine.Mode mode)
    {
        final ASN1ObjectIdentifier sm2p256v1 = GMObjectIdentifiers.sm2p256v1;
//获取一条 SM2 曲线参数
        X9ECParameters parameters = GMNamedCurves.getByOID(sm2p256v1);
// 构造 ECC 算法参数，曲线方程、椭圆曲线 G 点、大整数 N
        ECNamedDomainParameters namedDomainParameters = new ECNamedDomainParameters(
                sm2p256v1, parameters.getCurve(), parameters.getG(), parameters.getN());
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKey,
                namedDomainParameters);
        SM2Engine sm2Engine = new SM2Engine(mode);
// 设置 sm2 为解密模式
        sm2Engine.init(false, privateKeyParameters);
// 使用 BC 库加解密时密文以 04 开头，传入的密文前面没有 04 则补上
        if (cipherData[0] == 0x04) {
            return sm2Engine.processBlock(cipherData, 0, cipherData.length);
        } else {
            byte[] bytes = new byte[cipherData.length + 1];
            bytes[0] = 0x04;
            System.arraycopy(cipherData, 0, bytes, 1, cipherData.length);
            return sm2Engine.processBlock(bytes, 0, bytes.length);
        }
    }
    /**
     * SM2 解密算法,解密使用这个方法
     * @param privateKey
     * @param cipherData
     * @return
     */
    public static String decryptBase64(String privateKey, String cipherData) {
        return decryptBase64(privateKey, cipherData, SM2Engine.Mode.C1C3C2);
    }
    /**
     * SM2 解密算法
     * @param privateKey
     * @param cipherData
     * @param mode
    18
    19
     * @return
     */
    public static String decryptBase64(String privateKey, String cipherData, SM2Engine.Mode mode)
    {
        final BigInteger key = new BigInteger(BASE64_DECODER.decode(privateKey));
        final byte[] decrypt = decrypt(key,
                BASE64_DECODER.decode(cipherData.getBytes(StandardCharsets.UTF_8)), mode);
        return new String(decrypt, StandardCharsets.UTF_8);
    }
}
