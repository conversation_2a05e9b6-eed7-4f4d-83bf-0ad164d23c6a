package com.dxhy.order.model.dxOpenPlatform;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2023/8/22 17:59
 * @Description:
 */
@Data
public class DxInvoiceInfo {

    private String GJBQ;
    private String QDFP_HM;
    private String ZZFPHM;
    private String ZZFP_DM;
    private String FPLY_DM;
    private String FPLX_DM;
    private String FPZL_DM;
    private String TDYSLX_DM;
    private String FPZT_DM;
    private String SFLZFP;
    private String KJHZFPDYDLZFPHM;
    private String KPFNSRSBH;
    private String XHF_NSRSBH;
    private String XHF_MC;
    private String GHF_NSRSBH;
    private String GHF_MC;
    private String KPRQ;
    private String HJJE;
    private String HJSE;

    private List<DxInvoiceItemInfo> MXZBLIST;

}
