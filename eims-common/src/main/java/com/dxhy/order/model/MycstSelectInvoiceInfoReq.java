package com.dxhy.order.model;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2025/3/13 14:20
 * @Description:
 */
@Data
public class MycstSelectInvoiceInfoReq {
    //固定传data
    private String lx = "data";
    //1 进项 2 销项
    private String sjlx;
    //开票日期起
    private String kprqq;
    //开票日期止
    private String kprqz;
    //发票号码
    private String fphm;
    //页码 从1开始
    private String pageindex;
    //每页记录数
    private String pagesize;
    //是否需要下载文件 0不需要 1需要
    private String fileDown;
    //授权token
    private String token;
    //固定值用2
    private String ver = "2";
    //纳税人识别号
    private String dfnsrsbh;
    //日期类型 0 开票日期 1更新日期
    private String rqlx = "0";



}
