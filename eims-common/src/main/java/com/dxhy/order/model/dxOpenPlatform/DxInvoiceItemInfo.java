package com.dxhy.order.model.dxOpenPlatform;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2023/8/23 09:45
 * @Description:
 */
@Data
public class DxInvoiceItemInfo {


    private String DJ;
    private String DW;
    private String GGXH;
    private String HWHYSLWFWMC;
    private String JE;
    private String SE;
    private String SLV;
    private String SPHFWSSFLHBBM;
    private String SPSL;
    private String XH;
    private String DYLZFPXH;
    private String XMMC;
    private String FPHXZDM;
    private String SSYHZCLXDM;//享受优惠政策类型代码
    private String TDZSFSDM;//特定征收方式代码 01 不征税  02 普通 0%   03 差额  04 免税（农产品收购、自产农产品、光伏）05 简易征收、按 3%简易征收、按 5%简易征收 06 减按、按 5%简易征收减按 1.5%计征
    private String XSYHZCBZ;//Y：享受优惠政策  N：不享受优惠政策



}
