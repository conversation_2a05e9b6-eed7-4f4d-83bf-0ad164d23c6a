package com.dxhy.order.model;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2025/3/13 14:20
 * @Description:
 */
@Data
public class MycstSelectInvoiceInfoResp {
    // 发票号码
    private String FPHM;
    //  数电发票代码
    private String SDFPHM;
    //销方税号
    private String XFSBH;
    //销方名称
    private String XFMC;
    //购方税号
    private String GFSBH;
    //购方名称
    private String GMFMC;
    //开票日期
    private String KPRQ;
    //总不含税金额
    private String JE;
    //总税额
    private String SE;
    //发票来源
    private String FPLY;
    //发票种类
    private String FPPZ;
    //发票状态
    private String FPZT;
    //发票风险等级
    private String FPFXDJ;
    //开票人
    private String KPR;
    //备注
    private String BZ;
    //税局pdf url
    private String SJURL;
    //购方地址电话
    private String GMFDZDH;
    //购方银行账号
    private String GMFYHZH;
    //销方地址电话
    private String XFDZDH;
    //销方银行账号
    private String XFYHZH;
    //校验码
    private String JYM;
    //认证所属期
    private String SSQ;
    //勾选时间
    private String GXSJ;
    //勾选状态代码
    private String GXZTDM;
    //有效抵扣税额
    private String YXDKSE;
    //特定要素名称
    private String TDYSLXMC;
    //明细列表
    private List<MycstInvoiceInfoItem> fpmx;

}
