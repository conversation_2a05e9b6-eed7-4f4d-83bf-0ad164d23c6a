package com.dxhy.order.model.dxOpenPlatform;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2023/8/22 17:29
 * @Description:
 */
@Data
public class DXSelectInvoiceInfoReq {
    /**
     * 纳税人识别号
     */
    private String NSRSBH;
    /**
     * 归集标签 1销项 2 进项
     */
    private String GJBQ;
    /**
     * 发票状态代码
     */
    private List<String> FPZT_DM;
    /**
     * 发票来源代码
     */
    private String FPLY_DM;
    /**
     * 发票类型代码
     */
    private List<String> FPLX_DM;
    /**
     * 开票日期起
     */
    private String KPRQQ;
    /**
     * 开票日期止
     */
    private String KPRQZ;
    /**
     * 填发日期起
     */
    private String TFRQQ;
    /**
     * 填发日期止
     */
    private String TFRQZ;
    /**
     * 全电发票号码
     */
    private String QDFP_HM;
    /**
     * 全电发票号码(查询单张发票接口使用)
     */
    private String QDFPHM;
    /**
     * 开票日期（查询单张发票接口使用）
     */
    private String KPRQ;
    /**
     * 纸质发票代码
     */
    private String ZZFPDM;
    /**
     * 纸质发票号码
     */
    private String ZZFPHM;
    /**
     * 对方纳税人识别号
     */
    private String DFNSRSBH;
    /**
     * 对方纳税人名称
     */
    private String DFNSRMC;
    /**
     * 购方纳税人识别号
     */
    private String GHF_NSRSBH;
    /**
     * 购方纳税人名称
     */
    private String GHF_MC;
    /**
     * 查询页码
     */
    private String CXYM;
    /**
     * 每页记录数
     */
    private String TS;

}
