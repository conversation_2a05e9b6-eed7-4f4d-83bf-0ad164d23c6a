package com.dxhy.order.model;

import com.dxhy.order.pojo.MycstCommonRequest;
import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2023/8/23 16:36
 * @Description:
 */
@Data
public class MycstRedConfirmListReq  extends MycstCommonRequest{

    private String qsrq;//信息表填开起始日期(YYYYMMDD)
    private String jzrq;//信息表填开终止日期(YYYYMMDD)
    private String pageindex;//分页页码
    private String pagesize;//分页大小
    private String sqly;//0表示购方 1表示销方  购方/销方申请标记
    private String djzt;//djzt01无需确认 02销方方录入待购确认 03购方录入待销方确认 04购销双方已确认 05 作废（销方录入购入否认）
    // 06 作废（购方录入销方否认） 07 作废（超72小时未确认）
    // 08 作废（发起方撤消） 09 作废（确认后撤消） 10 作废
    // （异常凭证） 11作废（纳税人状态异常阻断）12作废(自然人拒收)
    private String kpzt;//开票状态 0未开具 1已开具 不填 全部
    private String dfmc;//对方名称

    //终端ID
    private String spid;
    //token
    private String token;









}
