package com.dxhy.order.model.dxOpenPlatform;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2023/8/23 16:36
 * @Description:
 */
@Data
public class DXRedInvoiceReq {

    private RedInvoice REDINVOICE;
    @Data
    public static class RedInvoice {
        //发票请求唯一流水号
        private String FPQQLSH;
        //订单号
        private String DDH;
        //纳税人识别号
        private String NSRSBH;
        //开票人
        private String KPR;
        //红字确认单编号-全电使用
        private String HZQRXXDBH;
        //扣除额
        private String KCE;
        //备注
        private String BZ;
        //开票账号（全电平台登录用户名）
        private String YHM;
    }
}
