package com.dxhy.order.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 静态码明细
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
@Setter
@Getter
public class QuickResponseCodeItemInfo implements Serializable{
    
    private String id;
    
    
    private String quickResponseCodeInfoId;
    
    
    private String sphxh;
    
    private String xmmc;
    
    private String xmdw;
    
    private String ggxh;
    
    private String xmsl;
    
    private String hsbz;
    
    private String xmdj;
    
    private String fphxz;
    
    private String spbm;
    
    private String zxbm;
    
    private String yhzcbs;
    
    private String lslbs;
    
    private String zzstsgl;
    
    private String kce;
    
    private String xmje;
    
    private String sl;
    
    private String se;
    
    private String wcje;
    
    private String byzd1;
    
    private String byzd2;
    
    private String byzd3;
    
    private String byzd4;
    
    private String byzd5;
    
    private Date createTime;
    
    private String xhfNsrsbh;
}
