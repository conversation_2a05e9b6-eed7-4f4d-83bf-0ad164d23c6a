package com.dxhy.order.model.dxOpenPlatform;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2023/8/22 17:29
 * @Description:  发票列表查询和单张发票查询共用实体
 */
@Data
public class DXSelectInvoiceInfoResp {

    /**
     * 状态代码
     */
    private String ZTDM;
    /**
     * 状态描述
     */
    private String ZTMS;
    /**
     * 当前页码
     */
    private String CXYM;
    /**
     * 总页数
     */
    private String TS;
    /**
     * 总页数
     */
    private String TOTAL;
    /**
     * 发票列表
     */
    private List<DxInvoiceInfo> FPLB;
    /**
     * 发票详情（查询单张发票时使用）
     */
    private DxInvoiceInfo FPXX;

}
