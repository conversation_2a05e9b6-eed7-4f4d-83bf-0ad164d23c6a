package com.dxhy.order.model.page;

import lombok.Getter;
import lombok.Setter;

/**
 *
 * @ClassName ：Query
 * @Description ：前端查询对象
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 *
 *
 */
@Getter
@Setter
public class Query {
	
    private String kpzt;
    private String pushStatus;
    private String startTime;
    private String endTime;
    private String zfbz;
    private String xhfNsrsbh;
    private String ddh;
    private String fphm;
    private String fpdm;
    private String gmfmc;
    private String fplx;
    private String kplx;
    private String mdh;
    private String sld;
    private String xhfmc;
    private String minhjje;
    private String maxhjje;
    private String ddly;
    
    /**
     * 冲红标志
     */
    private String chbz;
    /**
     * 业务类型
     */
    private String ywlxId;
    
}
