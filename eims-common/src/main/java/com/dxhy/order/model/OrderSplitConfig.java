package com.dxhy.order.model;

import lombok.Data;

import java.util.List;
@Data
public class OrderSplitConfig {
    private String splitType;
    private String splitRule;
    private String errorCorrectionType;
    private String isReSeparation;
    private String hsbz;
    private String page;
    private String limitJe;
    private Integer limitRang;
    private List<String> jeList;
    private List<String> slList;
    private List<Integer> lineList;
    private String isEquivalent;
    private String terminalCode;
    private String qdfwlx;
    private String fpzlDm;
    private String countUpperLimit;
}
