package com.dxhy.order.permit.tenant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dxhy.order.modules.dao.DbTenantDao;
import com.dxhy.order.modules.dao.NsrsbhTenantRelationDao;
import com.dxhy.order.modules.entity.DbTenantEntity;
import com.dxhy.order.modules.entity.NsrsbhTenantRelationEntity;
import com.dxhy.order.permit.tenant.DataSourceConstant;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.permit.tenant.RdsConfig;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: admin
 * @Date: 2022/6/19 15:50
 * @Description:
 */
@Service
@Slf4j
public class TenantRdsServiceImpl implements TenantRdsService {

    @Resource
    private DbTenantDao dbTenantDao;

    @Resource
    private NsrsbhTenantRelationDao  nsrsbhTenantRelationDao;

    /**
     * 根据租户代码切换rds连接，同一个线程内rds配置只会查一次
     * @param tenantCode
     * @date 2021/8/28 13:16
     **/
    @Override
    public String switchRds(String tenantCode) {
        // 如果当前已是这个租户rds则直接返回
        if (tenantCode.equals(DynamicDataSource.getDataSourceKey())) {
            return "";
        }
        // 如果本地已有则不查了 改rds需要重启服务
        if (null == DynamicDataSource.getDataSourceMap(tenantCode)) {
            // 如果当前不是配置库则先切回配置库
            if (!DataSourceConstant.DATA_SOURCE_MASTER.equals(DynamicDataSource.getDataSourceKey())) {
                DynamicDataSource.setDataSourceDefault();
            }
            // 获取rds配置
            RdsConfig rdsConfig = getRdsConfig(tenantCode);
            if(rdsConfig == null){
                return "租户数据异常";
            }
            DynamicDataSource.setDataSourceMap(rdsConfig);
        }
        // 切换到业务库
        DynamicDataSource.setDataSource(tenantCode);
        return "";
    }

    @Override
    public String switchRdsBytaxNo(String entId) {
        DynamicDataSource.setDataSourceDefault();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("nsrsbh",entId);
        NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity = nsrsbhTenantRelationDao.selectOne(queryWrapper);
        String tenantCode = nsrsbhTenantRelationEntity.getTenantCode();
        // 如果当前已是这个租户rds则直接返回
        if (nsrsbhTenantRelationEntity.getTenantCode().equals(DynamicDataSource.getDataSourceKey())) {
            return "";
        }
        // 如果本地已有则不查了 改rds需要重启服务
        if (null == DynamicDataSource.getDataSourceMap(tenantCode)) {
            // 如果当前不是配置库则先切回配置库
            if (!DataSourceConstant.DATA_SOURCE_MASTER.equals(DynamicDataSource.getDataSourceKey())) {
                DynamicDataSource.setDataSourceDefault();
            }
            // 获取rds配置
            RdsConfig rdsConfig = getRdsConfig(tenantCode);
            if(rdsConfig == null){
                return "租户数据异常";
            }
            DynamicDataSource.setDataSourceMap(rdsConfig);
        }
        // 切换到业务库
        DynamicDataSource.setDataSource(tenantCode);
        return "";
    }

    private RdsConfig getRdsConfig(String tenantCode) {
        // 根据租户代码取租户数据源配置表
        DbTenantEntity dbTenantEntity = dbTenantDao.selectById(tenantCode);
        if (null == dbTenantEntity) {
            return null;
        }
        // 转换为rds配置
        RdsConfig rdsConfig = new RdsConfig();
        rdsConfig.setTenantCode(tenantCode);
        rdsConfig.setDbUrl(dbTenantEntity.getDbUrl());
        rdsConfig.setDbPort(dbTenantEntity.getDbPort());
        rdsConfig.setDbName(dbTenantEntity.getDbName());
        rdsConfig.setDbAccount(dbTenantEntity.getDbAccount());
        rdsConfig.setDbPassword(dbTenantEntity.getDbPassword());
        return rdsConfig;
    }

}
