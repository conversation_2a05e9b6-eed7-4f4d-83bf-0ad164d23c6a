package com.dxhy.order.permit.tenant;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @Date 2022/7/6 18:02
 * @Version 1.0
 **/
@Component
public class SpringContextUtil implements ApplicationContextAware{
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringContextUtil.applicationContext = applicationContext;
    }
    public static <T> T getBean(String name){
        return (T)applicationContext.getBean(name);
    }
}
