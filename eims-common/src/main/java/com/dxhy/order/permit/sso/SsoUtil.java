package com.dxhy.order.permit.sso;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: zhangjinjing
 * @Date: 2022/7/12 10:52
 * @Version 1.0
 */
@Component
@Slf4j
public class SsoUtil {

    /**
     * 获取票税系统权限信息的URL
     **/
    @Value("${sso.getDeptInfoUrl}")
    private String getDeptInfoUrl;


    public String getUserName(){
        String userName = "";
        JSONObject deptInfo = getDeptInfo();
        if(deptInfo == null){
            return userName;
        }
        userName = deptInfo.getJSONObject("userInfo").getString("username");
        return userName;
    }

    public JSONObject getDeptInfo(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String auth = request.getHeader("Authorization");
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", auth);
        String getDeptInfoResponseBodyStr = "";
        try{
            ResponseEntity<String> getDeptInfoResponseEntity = restTemplate.exchange(
                    this.getDeptInfoUrl,
                    HttpMethod.GET,
                    new HttpEntity<>(httpHeaders),
                    String.class);
            getDeptInfoResponseBodyStr = getDeptInfoResponseEntity.getBody();
            JSONObject getDeptInfoResponseBody = JSONObject.parseObject(getDeptInfoResponseBodyStr);
            JSONObject dataJsonObj = getDeptInfoResponseBody.getJSONObject("data");
            return dataJsonObj;
        }catch (Exception e){
            log.error("getDeptInfo error: {}", getDeptInfoResponseBodyStr, e);
            return null;
        }
    }



}
