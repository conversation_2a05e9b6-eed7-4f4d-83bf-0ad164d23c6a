package com.dxhy.order.permit.context;

import lombok.Data;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

@Data
public class RequestContext {
    private RequestAttributes requestAttributes;

    // ThreadLocal存储
    private static final ThreadLocal<RequestContext> CONTEXT = new ThreadLocal<>();

    public static RequestContext getCurrent() {
        return CONTEXT.get();
    }

    public static void setCurrent(RequestContext context) {
        CONTEXT.set(context);
    }

    public static void clear() {
        CONTEXT.remove();
    }
    // 新增方法
    public static void initFromCurrentRequest() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            RequestContext context = getCurrent();
            if (context == null) {
                context = new RequestContext();
                setCurrent(context);
            }
            context.setRequestAttributes(attributes);
        }
    }

}
