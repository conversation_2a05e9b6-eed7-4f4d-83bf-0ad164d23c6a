package com.dxhy.order.permit.context;

import com.dxhy.order.permit.tenant.DynamicDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskDecorator;
import org.springframework.web.context.request.RequestContextHolder;

public class ContextAwareTaskDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        // 捕获当前上下文
        String dataSourceKey = DynamicDataSource.getDataSourceKey();
        RequestContext context = RequestContext.getCurrent();
        return () -> {
            try {
                if (context != null && context.getRequestAttributes() != null) {
                    RequestContextHolder.setRequestAttributes(context.getRequestAttributes());
                }
                // 设置数据源
                if (StringUtils.isNotBlank(dataSourceKey)) {
                    DynamicDataSource.setDataSource(dataSourceKey);
                }
                runnable.run();
            } finally {
                RequestContextHolder.resetRequestAttributes();
                DynamicDataSource.remove();
                RequestContext.clear();
            }
        };
    }
}
