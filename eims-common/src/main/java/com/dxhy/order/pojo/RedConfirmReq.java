package com.dxhy.order.pojo;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/9/6 09:54
 * @Description:
 */
@Data
public class RedConfirmReq {

    private String xhfnsrsbh;

    private String ghfnsrsbh;

    private String xhfmc;

    private String ghfmc;

    /**
     * 购销方选择 0 销售方，1 购买方
     */
    private String gxfxz;

    private String lzqdfphm;
    //发票代码
    private String fpdm;
    //发票号码
    private String fphm;

    /**
     * 开票有误、销货退回、服务中止、销售折让
     */
    private String chyymc;

    private String kprq;

    private String nsrsbh;

    private String sldh;

    private String fplxdm;
    /**
     * 特定业务（选择通道时使用）
     */
    private String tdyw;

    //红冲金额(不含税金额)
    private String hjje;

    private String hjse;
    //价税合计
    private String jshj;
    private List<Hzmx> hzmx;
    //红字信息表编号
    private String hzxxbbh;
    //红字信息表状态
    private String hzxxbzt;
}
