package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: hudesheng
 * @Date: 2024/12/28 15:17
 * @Description: 对接税航获取开票结果响应
 */
@Data
public class MycstGetKpjgResponse {
    //结果
    private String Result;
    //消息
    private String Message;
    //编码
    private String Code;
    //单据号
    private String XTLSH;
    //发票种类
    private String FPZL;
    //发票号码
    private String FPHM;
    //开票日期
    private String KPRQ;
    //时间
    private String TIME;
    //密文
    private String MW;
    //校验码
    private String XYM;
    //机器编号
    private String JQBH;
    //开票流水号
    private String KPLSH;
    //作废标志
    private String ZFBZ;
    //原订单号
    private String YDDH;
    //pdf下载链接
    private String URL;
    //发票状态 :-1 调用异常或数据不存在，0 发票退回到待审中，1发票已经开具成功 2发票还在进行中
    private String FPZT;
    //实际开票不含税金额
    private String SKBHSJE;
    //实际开票税额
    private String SKSE;
    //企业税号
    private String QYSH;
    //税局pdf下载链接
    private String SJURL_PDF;
    //税局ofd下载链接
    private String SJURL_OFD;
    //税局xml下载链接
    private String SJURL_XML;


}
