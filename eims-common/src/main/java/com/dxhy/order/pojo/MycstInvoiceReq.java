package com.dxhy.order.pojo;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 蓝字发票请求实体 税航票帮手
 */
@Data
public class MycstInvoiceReq {
    //单据号
    private String XTLSH;
    //客户名称
    private String KHMC;
    //税号（专票必填，普票选填）
    private String KHSH;
    //地址,数电发票时，如需备注显示地址电话，需要，地址+空格+电话格式传值
    private String KHDZ;
    //客户银行账号,数电发票时，如需备注显示银行账号，需要 银行+空格+账号格式传值
    private String KHKHYHZH;
    //发票种类： 004 纸质专票 007 纸质普票 026 电子普票 028 电子专票 020 数电专票 021 数电普票 022 数电纸质专票 023 数电纸质普票，（机动车发票使用020）
    private String FPZL;
    //备注
    private String BZ;
    //开票人,乐企对接时，必需是电局授权的开票人
    private String KPR;
    //收款人
    private String SKR;
    //复核人
    private String FHR;
    //企业开户银行账号,数电发票时，如需备注显示银行账号，需要 银行+空格+账号格式传值
    private String QYKHYHZH;
    //企业地址电话,数电发票时，如需备注显示地址电话，需要 地址+空格+电话格式传值
    private String QYDZDH;
    //红冲原发票号,开具红字发票时必需，数电发票时，无需开具信息表，只需要传此值，自动产生信息表并自动产生红字发票
    private String YFPHM;
    //信息表编号,开具纸质专用发票以及电子专用发票时时必需
    private String XXBBH;
    //(只有税盘才有效)清单标志,0普通发票 1强制升级清单发票
    private String QDBZ;
    //客户邮件,邮件发给多个人时，用;（分号）分隔
    private String KHYJ;
    //客户手机号，需要订购手机短信套餐才会自动发送短信
    private String KHSJ;
    //机动车标识,开具机动车专用发票时传1，开具机动车发票时传2 开具二手车发票时传3
    private String JDC;
    //代办退税标识,航天税盘有效，开具代办税退时传1
    private String DBTS;
    //特定业务标识：SGFP 农产品收购 LCPXS 自产农产品销售，XT 稀土 FWHS 报废产品收购
    private String ZSFS;
    //发票明细列表
    private List<MycstInvoiceItem> ITEM;
    //特定业务列表
    private List<MycstInvoiceTdyw> TDYW;
    //发票状态 0将数据保存到监时待处理中，2将数据保存到进行中并立即开票，不传默认为2
    private String FPZT;
    //数电附加信息列表
    private List<MycstInvoiceFjxx> FJXX;
    //自然人标识,数电票才有用，0 非自然人 1自然人 默认0
    private String ZRRBZ;
    //显示销方银行信息 0 不显示 1显示，数电票有效
    private String XSXFXX;
    //显示购方银行信息 0 不显示 1显示,数电票有效
    private String XSGFXX;
    //取消优惠说明 数电票有效
    private String QXYHSM;
    //原票开票日期，红字发票时可传入，需要带上时分秒
    private String LPKPRQ;
    //原票发票种类，红冲时可传入
    private String LPFPZL;
    //差额明细列表
    private List<MycstInvoiceCemx> CEMX;
    //冲红原因01开票有误,02销货退回,03服务中止, 04销售折让
    private String CHYY;
    //二手车信息列表
    private List<MycstInvoiceEscxx> ESCXX;
    //机动车信息列表
    private List<MycstInvoiceJdcxx> JDCXX;
    //显示销方地址电话信息 0 不显示 1显示,数电票有效
    private String XSXFDZ;
    //显示购方地址电话信息 0 不显示 1显示,数电票有效
    private String XSGFDZ;
    //显示收款人，复核人 0 不显示 1显示,数电票有效
    private String XSRRXX;
}
