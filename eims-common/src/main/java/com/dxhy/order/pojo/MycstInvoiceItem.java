package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 发票明细 税航票帮手
 */
@Data
public class MycstInvoiceItem {
    //产品名称,可需带分类编码简称，也可不带简称,机动车发票时，此字段是车辆类型
    private String CPMC;
    //产品型号，折扣时，此值为空或不传,机动车发票这里传 车架号
    private String CPXH;
    //产品单位，折扣时，此值为空或不传
    private String CPDW;
    //税率,可以传13，也可以传0.13还可以传13%,可直接传免税和不征税
    private String SL;
    //产品数量，折扣时，此值为空或不传,红字发票时传负数
    private String CPSL;
    //不含税金额,红字发票时或折扣行时传负数
    private String BHSJE;
    //税额,通常的计算方式：不含税金额*税率,红字发票或折扣行时传负数
    private String SE;
    //分类编码，可以用19位，也可以用没补全19位的
    private String FLBM;
    //税率优惠,0 没有税收优惠 1有税收优惠
    private String XSYH;
    //只有在税率是0时，以及税收优惠是1时有效，零税率标识,1免税 2不征税
    private String LSLBZ;
    //优惠说明,免税就填免税，不征税就填不征税
    private String YHSM;
    //扣除金额(差额征税时有用)
    private String KCJE;
    //产品不含税单价，可以不用传，不传时，系统自动计算，传时系统按传入的值送税务局，折扣时，此值为空或不传,蓝票和红票都传正数
    private String ZDYCPDJ;
    //产品含税单价，可以不用传，不传时，系统自动计算，此字段仅仅保留在平台上，折扣时，此值为空或不传，蓝票和红票都传正数
    private String ZDYHSDJ;
    //含税金额（在不传BHSJE和SE节点的情况下，系统会自动计算 BHSJE和SE),如果有传BHSJE和SE节点的情况下，此功能节点无效,红字发票或折扣行时传负数
    private String HSJE;
    //即征即退类型
    private String JZJTLX;
}
