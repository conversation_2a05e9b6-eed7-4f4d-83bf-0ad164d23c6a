package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 发票特定业务 税航票帮手
 */
@Data
public class MycstInvoiceTdyw {
    //不动产租赁地区，列表详见 开发指南 特定业务里的 地区
    private String BDC_SF;
    //不动产租赁详细地址
    private String BDC_XXDZ;
    //不动产租赁起始日期
    private String BDC_QSRQ;
    //不动产租赁截止日期
    private String BDC_JZRQ;
    //不动产租赁跨市标志
    private String BDC_KSBZ;
    //不动产租赁产权证号
    private String BDC_CQJH;
    //不动产租赁面积单位
    private String BDC_PJDW;
    //不动产销售产品证号
    private String BDCXS_CQZH;
    //不动产销售所在省份地区
    private String BDCXS_DQDZ;
    //不动产销售详细地址
    private String BDCXS_XXDZ;
    //不动产销售合同编号
    private String BDCXS_HTBH;
    //不动产销售项目编号
    private String BDCXS_XMBH;
    //不动产销售计税价格
    private String BDCXS_JSJG;
    //不动产销售成交价格
    private String BDCXS_CJJE;
    //不动产销售跨区标志
    private String BDCXS_KQBZ;
    //不动产销售面积单位
    private String BDCXS_MJDW;
    //旅客运输出行人
    private String LKYS_CXR;
    //旅客运输证件类型
    private String LKYS_CXRZJLX;
    //旅客运输证件号码
    private String LKYS_CXRZJHM;
    //旅客运输出行日期
    private String LKYS_CXRQ;
    //旅客运输出发地
    private String LKYS_CFD;
    //旅客运输到达地
    private String LKYS_DDD;
    //旅客运输交通工具类型
    private String LKYS_JTGJLX;
    //旅客运输等级
    private String LKYS_DJ;
    //货物运输起运地
    private String HWYS_QYD;
    //货物运输到达地
    private String HWYS_DDD;
    //货物运输运输工具
    private String HWYS_YSGJ;
    //货物运输运输工具牌号
    private String HWYS_YSGJTH;
    //货物运输货物名称
    private String HWYS_YSHWMC;
    //建筑服务发生地
    private String GZFW_FSD;
    //发生地详细地址
    private String GZFW_XXDZ;
    //建筑项目名称
    private String GZFW_XMMC;
    //土地增值税项目编号
    private String GZFW_XMBH;
    //跨地市标志
    private String GZFW_KSBZ;
    //跨区域涉税事项报验管理编号
    private String GZFW_KQYBYBH;

}
