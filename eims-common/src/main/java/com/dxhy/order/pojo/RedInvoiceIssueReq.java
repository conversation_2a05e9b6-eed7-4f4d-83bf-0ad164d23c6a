package com.dxhy.order.pojo;

import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/9/6 15:00
 * @Description:
 */
@Data
public class RedInvoiceIssueReq {
    private String requestId;
    //纳税人识别号
    private String nsrsbh;
    //蓝字数电发票号码
    private String lzqdfphm;
    //开票日期
    private String kprq;
    //对方纳税人名称
    private String dfnsrmc;
    //对方纳税人识别号
    private String dfnsrsbh;
    //红字发票信息确认单编号
    private String hzfpxxqrdbh;
    //红字确认信息状态代码
    private String hzqrxxztDm;
    //冲红原因
    private String chyy;
    //明细集合
    private List<OrderInvoiceItemEntity> itemEntityList;
    //发票种类
    private String fpzl;
    //特定业务
    private String tdyw;

}
