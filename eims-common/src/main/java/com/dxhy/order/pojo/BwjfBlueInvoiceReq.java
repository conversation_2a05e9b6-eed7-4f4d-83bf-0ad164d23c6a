package com.dxhy.order.pojo;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/10/10 11:13
 * @Description:
 */
@Data
public class BwjfBlueInvoiceReq {
    private String khsh;

    private String khmc;

    private String kplx;

    private String gsdm;

    private String khdzdh;

    private String zdr;

    private String kce;

    private String gmfMobile;

    private String gmfEmail;

    private String tdyslxDm;

    private String cezslxDm;

    private String jazslxDm;

    private String ssyhzclxDm;

    private String sgfpbq;

    private String cktslxDm;

    private String kjly;

    private String sjly;

    private String khyhzh;

    private String hsbz;

    private String zkje;

    private String sjlx;

    private String clfs;

    private String bz;

    private String skr;

    private String yhdm;

    private String kpzddm;

    private String kz2;

    private String djbh;

    private String kz1;

    private String kz3;

    private String tax;

    private String djrq;

    private String kpr;

    private String fhr;

    private String fplxdm;

    private String hsje;

    private String bmdm;

    private String tspz;

    private String zsfs;

    private List<BwjfInvoiceItem> mxxx;





}
