package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2022/9/13 15:20
 * @Description:  发票信息查询请求实体
 */
@Data
public class InvoiceInfoReq {
    // 查询类型 1销项 2 进项
    private String gjbq;
    //发票状态代码  01 正常  02  已作废  03 已冲红-全额 04 已冲红-部分
    private String[] fpztDm;
    //发票来源代码 0-全部   1 -增值税发票管理系统  2 电子发票服务平台
    private String fplyDm;
    //发票类型代码  电子发票服务平台： 81-全电发票（增值税专用发票）； 82-全电发票（普通发票）； 83-机动车销售电子统一发票； 84-二手车销售电子统一发票； 85-纸质发票（增值税专用发票）； 86-纸质发票（普通发票）； 87-机动车销售统一发票； 88-二手车销售统一发票
    // 增值税发票管理系统： 01- 增值税专用发票 03- 机动车销售统一发票 04- 增值税普通发票 08- 增值税电子专用发票 10- 增值税电子普通发票 15- 二手车销售统一发票
    private String[] fplxDm;
    //全电发票号码
    private String qdfphm;
    //发票代码
    private String fpdm;
    //发票号码
    private String fphm;
    //对方纳税人识别号
    private String dfnsrsbh;
    //对方名称
    private String dfmc;
    //开票日期起
    private String kprqq;
    //开票日期止
    private String kprqz;
    //第几页
    private String current;
    //每页几条记录
    private String size;

    private String nsrsbh;

    private String kprq;


}
