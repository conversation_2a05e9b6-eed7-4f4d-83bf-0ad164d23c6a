package com.dxhy.order.pojo;

import com.dxhy.order.model.MycstSelectInvoiceInfoResp;
import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 对接税航最外层响应
 */
@Data
public class MycstCommonResponse {
    //结果
    private String Result;
    //是否已保存
    private String HaveSave;
    //消息
    private String Message;
    //失败列表
    private List<MycstInvoiceError> ErrList;
    //成功列表(根据情况，部份节点不一定会输出)
    private List<MycstInvoiceSuccess> SucessList;
    //是否需要验证码
    private String ISSMS;
    //实名认证二维码
    private String Qrcode;
    //实名认证状态
    private String sfsl;
    //查询抽取平台全量发票数据总数
    private String total;
    //查询抽取平台全量发票数据
    private List<MycstSelectInvoiceInfoResp> Row;
}
