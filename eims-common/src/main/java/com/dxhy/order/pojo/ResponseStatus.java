package com.dxhy.order.pojo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 接口返回外层
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ResponseStatus implements Serializable {


    private String code;
    private String message;

    public ResponseStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResponseStatus() {
    }
}
