package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2022/9/28 19:08
 * @Description:  商品信息实体类
 */
@Data
public class InvoiceItemReq {

    /**
     *含税标志(Y/N)
     */
    private String sphsbz;
    /**
     *税收优惠政策标志(Y/N)
     */
    private String xsyhzcbz;
    /**
     *税率  0.13
     */
    private String slv;
    /**
     *优惠政策及简易计税类型。xsyhzcbz 为 Y 时 必填
     */
    private String zzstsgl;
    /**
     *  税编
     */
    private String sphfwssflhbbm;
    /**
     *项目名称
     */
    private String xmmc;
    /**
     * 商品简码
     */
    private String spjm;
    /**
     *  单价
     */
    private String jydj;
    /**
     * 规格型号
     */
    private String ggxh;
    /**
     * 单位
     */
    private String jldwmc;












}
