package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 对接税航最外层请求
 */
@Data
public class MycstCommonRequest {
    //发票报文json的base64，同时需要对base64用[]括号起来
    private String data;
    //终端ID
    private String spid;
    //token
    private String token;
    //dzswjyh
    private String dzswjyh;

    //dzswjmm
    private String dzswjmm;

    //dlfs
    private String dlfs;

    //isshare
    private String isshare;

    //qysh
    private String qysh;

    //sflx
    private String sflx;

    //yzm
    private String yzm;
    //发票种类
    private String fpzl;
    //系统单据号
    private String xtlsh;
    //固定用2
    private String ver = "2";

}
