package com.dxhy.order.pojo;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 响应成功列表(根据情况，部份节点不一定会输出) 税航票帮手
 */
@Data
public class MycstInvoiceSuccess {
    //系统流水号
    private String xtlsh;
    //发票种类
    private String fpzl;
    //发票代码
    private String fpdm;
    //发票号码
    private String fphm;
    //开票日期
    private String kprq;
    //开票时间
    private String time;
    //密文
    private String skm;
    //代码
    private String code;
    //用户
    private String user;
    //校验码
    private String jym;
    //机器编号
    private String jqbh;
    //二维码
    private String ewm;
    //PDF下载链接
    private String url;
    //开票结果
    private String kpjg;
    //开票反馈
    private String kpfk;
    //实际开票不含税金额
    private String skbhsje;
    //实际开票税额
    private String skse;
    //税局PDF下载网址
    private String sjurl_pdf;
    //税局OFD下载网址
    private String sjurl_ofd;
    //税局XML下载网址
    private String sjurl_xml;
    //PDF信息
    private String pdfxx;
}
