package com.dxhy.order.pojo;

import com.dxhy.order.model.MycstRedConfirmItem;
import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2024/12/30 11:42
 * @Description:
 */
@Data
public class MycstRedConfirmInfo {



    /**
     * 红冲原因代码'01': '开票有误', '02': '销货退回', '03': '服务中止', '04': '销售折让',
     */
    private String chyyDm;


    /**
     *
     */
    private String uuid;


    /**
     *
     */
    private String fpdm;

    /**
     *
     */
    private String fphm;
    /**
     * 发票开具方式代码
     */
    private String fpkjfsDm;
    /**
     * 发票认证状态代码
     */
    private String fprzztDm;
    /**
     * 购买方名称
     */
    private String gfmc;

    /**
     * 购买方名称
     */
    private String gfsh;

    /**
     * 原票合计金额（不含税）
     */
    private String hjje;

    /**
     * 原票合计税额
     */
    private String hjse;

    /**
     * 红字发票号
     */
    private String hzfphm;

    /**
     * 红字信息确认单号
     */
    private String hzfpxxqrdbh;
    /**
     * 红字开票日期
     */
    private String hzkprq;
    /**
     * 红字确认单明细行数
     */
    private String hzqrdmxsl;
    /**
     * 红字确认单信息状态
     */
    private String hzqrxxztDm;
    /**
     * 录入日期
     */
    private String lrrq;
    /**
     * 已开具红字发票标记
     */
    private String ykjhzfpbz;


    private List<MycstRedConfirmItem> fyxm;


}
