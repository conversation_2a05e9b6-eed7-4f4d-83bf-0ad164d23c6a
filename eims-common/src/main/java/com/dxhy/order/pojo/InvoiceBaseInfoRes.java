package com.dxhy.order.pojo;

import lombok.Data;

import java.util.List;

@Data
public class InvoiceBaseInfoRes {
    private String fpdm;
    private String fphm;
    private String qdfphm;
    private String xsfnsrsbh;
    private String xsfmc;
    private String gmfnsrsbh;
    private String gmfmc;
    private String kprq;
    private String hjje;
    private String hjse;
    private String fplyDm;
    private String fplxDm;
    private String fpztDm;
    private String tdyslxDm;
    private String sflzfp;
    private String kpfnsrsbh;
    private String fppzDm;
    private String bz;
    private String ewm;
    private String cezslxDm;
    private String kce;
    private String kpr;
    private String gjbq;
    private List<InvoiceItemInfoRes> items;
}
