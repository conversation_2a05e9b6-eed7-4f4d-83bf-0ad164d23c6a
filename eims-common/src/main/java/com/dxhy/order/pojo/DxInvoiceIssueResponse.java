package com.dxhy.order.pojo;

import lombok.Data;

import java.util.List;

/**
 * @Auther: admin
 * @Date: 2022/8/29 15:17
 * @Description: 对接税航最外层响应
 */
@Data
public class DxInvoiceIssueResponse {
    //失败列表
    private List<InvoiceIssueErrorRes> errorList;
    //成功列表
    private List<InvoiceIssueSuccessRes> successList;
    /**
     * 发票开具成功列表对象
     */
    @Data
    public static class InvoiceIssueSuccessRes {
        //订单号
        private String ddh;
        //受理单号
        private String sldh;
    }

    /**
     * 发票开具失败列表对象
     */
    @Data
    public static class InvoiceIssueErrorRes {
        //订单号
        private String ddh;
        //失败代码
        private String errorCode;
        //失败原因
        private String errorMsg;
    }
}
