package com.dxhy.order.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName ：OpenApiConfig
 * @Description ：外部api接口配置
 * @date 创建时间: 2022-06-29 09:42
 */
@Configuration
@Getter
@Setter
@RefreshScope
public class OpenApiConfig {
    
    /**
     * 当前服务器地址
     */
    @Value("${qrorder.url.domain}")
    private String orderApiDomain;
    
    /**
     * 我的发票服务器地址
     */
//    @Value("${order.url.myinvoiceUrl}")
    private String myInvoiceUrl;
    
    /**
     * 扫码开票前端地址
     */
    @Value("${qrorder.url.frontUrl}")
    private String frontUrl;
    
    /**
     * 公众号appid
     */
//    @Value("${order.gzh.appid}")
    private String appId;
    
    /**
     * 大象渠道
     */
//    @Value("${order.system.eleDistributorId}")
    private String eleDistributorId;
    
    /**
     * 调用底层异常原因列表,包含以下原因的会放入重试队列,错误信息以,割开
     */
//    @Value("${order.system.invoiceErrorMsgList}")
    private String invoiceErrorMsgList;

    /**
     * 轮询开票，开票数据放入延时队列重试次数阈值
     */
//    @Value("${order.system.sendDelayMqRetryThreshold}")
    private Integer sendDelayMqRetryThreshold;
    
    /**
     * 京东宙斯URL
     */
//    @Value("${order.jdJos.url}")
    private String jdJosUrl;
    
    /**
     * 京东宙斯appKey
     */
//    @Value("${order.jdJos.appKey}")
    private String jdJosAppKey;
    
    /**
     * 京东宙斯appSecret
     */
//    @Value("${order.jdJos.appSecret}")
    private String jdJosAppSecret;
    
    /**
     * 扫码开票推送公众号secretId
     */
//    @Value("${order.push.myinvoice.secretId}")
    private String pushMyInvoiceSecretId;
    
    /**
     * 扫码开票推送公众号secretKey
     */
//    @Value("${order.push.myinvoice.secretKey}")
    private String pushMyInvoiceSecretKey;
    
    /**
     * 票池推送状态
     */
//    @Value("${order.push.ticketPool.push}")
    private String pushTicketPoolStatus;
    
    /**
     * 票池推送地址
     */
//    @Value("${order.url.ticketPoolUrl}")
    private String pushTicketPoolUrl;
    
    /**
     * 票池推送secretId
     */
//    @Value("${order.push.ticketPool.secretId}")
    private String pushTicketPoolSecretId;
    
    /**
     * 票池推送secretKey
     */
//    @Value("${order.push.ticketPool.secretKey}")
    private String pushTicketPoolSecretKey;
    
    /**
     * 销项后台AES加密key
     */
//    @Value("${order.aes.simsback}")
    private String aesSimsBack;
    
    /**
     * 调用接口限制时间内失败次数,超过次数后直接返回失败不执行业务
     */
//    @Value("${order.interfaceCheck.checkLimitCount}")
    private String checkLimitCount;
    
    /**
     * 限制时间,单位为分钟
     */
//    @Value("${order.interfaceCheck.checkLimitTime}")
    private String checkLimitTime;
    
    /**
     * 限制访问时间,超过次数限制后限制访问时间,单位为分钟
     */
//    @Value("${order.interfaceCheck.checkLimitExpireTime}")
    private String checkLimitExpireTime;
    /**
     * 等金额、等数量拆分后条数上限
     */
    @Value("${order.split.countUpperLimit:200}")
    private Integer countUpperLimit;
    
    /**
     * 对外二维码短码
     *
     * @return
     */
    public String configQrCodeShortUrl() {
        return orderApiDomain + "/api/v3/%s";
    }
    
    /**
     * 静态码领票跳转地址
     *
     * @return
     */
    public String configQrCodeScanUrl() {
        return frontUrl + "/html/wxscaninvoice/transferPage.html?tqm=%s&nsrsbh=%s&type=%s";
    }
    
    /**
     * 获取授权url的接口
     *
     * @return
     */
    public String configGetAuthUrl() {
        return myInvoiceUrl + "/wxservice/api/getAuthUrl";
    }
    
    /**
     * 获取授权状态的接口
     *
     * @return
     */
    public String configGetAuthStatus() {
        return myInvoiceUrl + "/wxservice/api/getAuthData";
    }
    
    /**
     * 前端预览地址
     *
     * @return
     */
    public String configFrontUrl() {
        return frontUrl + "/html/wxscaninvoice/invoicePreview.html?tqm=%s";
    }
    
    /**
     * 扫码开票推送公众号secretId
     *
     * @return
     */
    public String configPushMyInvoiceSecretId() {
        return pushMyInvoiceSecretId;
    }
    
    /**
     * 扫码开票推送公众号secretKey
     *
     * @return
     */
    public String configPushMyInvoiceSecretKey() {
        return pushMyInvoiceSecretKey;
    }
    
    /**
     * 推送票池地址
     *
     * @return
     */
    public String configPushTicketPoolUrl() {
        return pushTicketPoolUrl + "/invoice/salsePush";
    }
    
    /**
     * 推送票池地址
     *
     * @return
     */
    public String configPushTicketPoolStatus() {
        return pushTicketPoolStatus;
    }
    
    /**
     * 推送票池secretId
     *
     * @return
     */
    public String configPushTicketPoolSecretId() {
        return pushTicketPoolSecretId;
    }
    
    /**
     * 推送票池secretKey
     *
     * @return
     */
    public String configPushTicketPoolSecretKey() {
        return pushTicketPoolSecretKey;
    }
    
    /**
     * 我的发票公众号appid
     *
     * @return
     */
    public String configMyInvoiceAppId() {
        return appId;
    }
    
    /**
     * 京东宙斯URL
     *
     * @return
     */
    public String configEleDistributorId() {
        return eleDistributorId;
    }
    
    /**
     * 开票异常信息
     *
     * @return
     */
    public String configInvoiceErrorMsgList() {
        return invoiceErrorMsgList;
    }
    
    /**
     * 京东宙斯URL
     *
     * @return
     */
    public String configJdJosUrl() {
        return jdJosUrl;
    }
    
    /**
     * 京东宙斯appKey
     *
     * @return
     */
    public String configJdJosAppKey() {
        return jdJosAppKey;
    }
    
    /**
     * 京东宙斯appSecret
     *
     * @return
     */
    public String configJdJosAppSecret() {
        return jdJosAppSecret;
    }


}
