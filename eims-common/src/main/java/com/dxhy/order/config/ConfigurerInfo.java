package com.dxhy.order.config;

/**
 * 常量值
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
public class ConfigurerInfo {
    /**
     * 常量定值
     */
    public static final String STRING_CHARSET_GBK = "GBK";

    public static final int INT_0 = 0;
    public static final int INT_2 = 2;
    public static final int INT_4 = 4;
    public static final int INT_6 = 6;
    public static final int INT_7 = 7;
    public static final int INT_20 = 20;
    public static final int INT_32 = 32;


    public static final int PASSWORD_SIZE = 24;

    public static final int INT_184 = 184;

    public static final int INT_200 = 200;

    public static final int INT_230 = 230;

    /**
     * 常量变值配置
     */

    public static final String ENCRYPTCODE = "encryptCode";
    public static final String ZIPCODE = "zipCode";
    public static final String CONTENT = "content";

    public static final String TIMESTAMP = "Timestamp";

    public static final String NONCE = "Nonce";

    public static final String SECRETID = "SecretId";
    public static final String SECRETKEY = "SecretKey";
    public static final String SIGNATURE = "Signature";

    public static final String RESPONSESTATUS = "responseStatus";

    public static final String RESPONSEDATA = "responseData";

    public static final String SUCCSSCODE = "0000";

    public static final String ERROR_CODE = "9999";
    public static final String COMMONTHREADPOOL = "eimsCommonThreadPool";

    /**
     * 接口相关
     */

    /**
     * 对外接口版本号--v1
     */
    public static final String INTERFACE_VERSION_V1 = "v1";

    /**
     * 对外接口版本号--v2
     */
    public static final String INTERFACE_VERSION_V2 = "v2";

    /**
     * 最新的对外接口文档
     * 对外接口版本号--v3
     */
    public static final String INTERFACE_VERSION_V3 = "v3";

    /**
     * 统一的对外接口文档
     * 对外接口版本号--v4
     */
    public static final String INTERFACE_VERSION_V4 = "v4";


    /**
     * 自动开票接口
     */
    public static final String ALLOCATEINVOICES = "AllocateInvoices";
    /**
     * 请求执行状态查询接口
     */
    public static final String GETALLOCATEINVOICESSTATUS = "GetAllocateInvoicesStatus";
    /**
     * 开具发票结果获取接口
     */
    public static final String GETALLOCATEDINVOICES = "GetAllocatedInvoices";
    /**
     * 发票作废接口
     */
    public static final String DEPRECATEINVOICES = "DeprecateInvoices";
    /**
     * 获取电子发票接口
     */
    public static final String GETINVOICEPDFFILES = "GetInvoicePdfFiles";
    /**
     * 发票打印状态接口
     */
    public static final String PRINTINVOICES = "PrintInvoices";
    /**
     * 发票自定义打印接口
     */
    public static final String GETPRINTINVOICESSTATUS = "GetPrintInvoicesStatus";
    /**
     * 企业数据自动导入接口
     */
    public static final String IMPORTORDERS = "ImportOrders";
    /**
     * 开票点上下票列表管理接口
     */
    public static final String QUERYINVOICEROLLPLOLIST = "Queryinvoicerollplolist";
    /**
     * 开票点上票接口接口
     */
    public static final String ACCESSPOINTUPINVOICE = "AccessPointUpInvoice";
    /**
     * 开票点下票接口接口
     */
    public static final String ACCESSPOINTDOWNINVOICE = "AccessPointDownInvoice";
    /**
     * 开票点列表接口接口
     */
    public static final String QUERYSLD = "QuerySld";
    /**
     * 根据订单号获取订单数据以及发票数据接口
     */
    public static final String GETORDERINFOANDINVOICEINFO = "GetOrderInfoAndInvoiceInfo";
    /**
     * 获取发票数据接口
     */
    public static final String GET_INVOICE_INFO = "GetInvoiceInfo";
    /**
     * 红字发票申请单上传接口
     */
    public static final String ALLOCATEREDINVOICEAPPLICATION = "AllocateRedInvoiceApplication";
    /**
     * 红字发票申请单审核结果下载接口
     */
    public static final String DOWNLOADREDINVOICEAPPLICATIONRESULT = "DownloadRedInvoiceApplicationResult";
    /**
     * 根据提取码获取订单
     */
    public static final String GETORDERINFOBYTQM = "GetOrderInfoByTqm";
    /**
     * 成品油库存局端可下载库存查询接口
     */
    public static final String QUERYCPYJDKC = "QueryCpyJdKc";
    /**
     * 成品油已下载库存查询接口
     */
    public static final String QUERYCPYYXZKC = "QueryCpyYxzKc";
    /**
     * 成品油库存下载接口
     */
    public static final String DOWNLOADCPYKC = "DownloadCpyKc";
    /**
     * 成品油库存退回接口
     */
    public static final String BACKCPYKC = "BackCpyKc";
    /**
     * 成品油库存同步接口
     */
    public static final String SYNCCPYKC = "SyncCpyKc";
    /**
     * 获取动态二维码的接口
     */
    public static final String GENERATEDYNAMICCODE = "generateDynamicCode";

    /**
     * 历史数据导入接口
     */
    public static final String IMPORTINVOICEINFO = "importInvoiceInfo";

    /**
     * 商品信息查询接口
     */
    public static final String QUERYCOMMODITYINFO = "QueryCommodityInfo";

    /**
     * 商品信息同步接口
     */
    public static final String SYNCCOMMODITYINFO = "SyncCommodityInfo";

    /**
     * 购买方信息查询接口
     */
    public static final String QUERYBUYERINFO = "queryBuyerInfo";

    /**
     * 购买方信息同步接口
     */
    public static final String SYNCBUYERINFO = "SyncBuyerInfo";

    /**
     * 税控设备信息同步接口
     */
    public static final String SYNCTAXEQUIPMENTINFO = "SyncTaxEquipmentInfo";

    /**
     * 发票余量同步接口
     */
    public static final String QUERYINVOICESTORE = "QueryInvoiceStore";

    /**
     * UKey企业初始化信息查询接口
     */
    public static final String GET_ENTERPRISE_STATUS = "GetEnterpriseStatus";

    /**
     * UKey清卡状态查询接口
     */
    public static final String GET_CLEAN_CARD_STATUS = "GetCleanCardStatus";

    /**
     * UKey设备在线状态查询接口
     */
    public static final String GET_ON_LINE_STATUS = "GetOnLineStatus";


    /**
     * 开票
     */
    /**
     * 待开发票数据接口
     */
    public static final String FANG_GE_GETINVOICES = "getInvoices";
    /**
     * 接收待开订单数据状态接口
     */
    public static final String FANG_GE_GETINVOICESTATUS = "getInvoiceStatus";
    /**
     * 接收开票完成订单数据接口
     */
    public static final String FANG_GE_UPDATEINVOICES = "updateInvoices";


    /**
     * 红字申请单上传
     */
    /**
     * 获取红字申请单待上传数据接口
     */
    public static final String FANG_GE_GETUPLOADREDINVOICE = "getUploadRedInvoice";
    /**
     * 接收红字申请单待上传数据状态接口
     */
    public static final String FANG_GE_GETUPLOADREDINVOICESTATUS = "getUploadRedInvoiceStatus";
    /**
     * 接收红字申请单上传数据接口
     */
    public static final String FANG_GE_UPDATEUPLOADREDINVOICE = "updateUploadRedInvoice";

    /**
     * 红字申请单下载
     */
    /**
     * 获取红字申请单待下载数据接口
     */
    public static final String FANG_GE_GETDOWNLOADREDINVOICE = "getDownloadRedInvoice";
    /**
     * 接收红字申请单待下载数据状态接口
     */
    public static final String FANG_GE_GETDOWNLOADREDINVOICESTATUS = "getDownloadRedInvoiceStatus";
    /**
     * 接收红字申请单下载数据接口
     */
    public static final String FANG_GE_UPDATEDOWNLOADREDINVOICE = "updateDownloadRedInvoice";

    /**
     * 发票作废
     */
    /**
     * 获取待作废数据
     */
    public static final String FANG_GE_GETDEPRECATEINVOICES = "getDeprecateInvoices";
    /**
     * 接收待作废发票状态
     */
    public static final String FANG_GE_GETDEPRECATEINVOICESSTATUS = "getDeprecateInvoicesStatus";
    /**
     * 接收作废完成订单数据接口
     */
    public static final String FANG_GE_UPDATEDEPRECATEINVOICES = "updateDeprecateInvoices";

    /**
     * 发票打印
     */
    /**
     * 获取待打印的接口数据
     */
    public static final String FANG_GE_GETPRINTINVOICES = "getPrintInvoices";
    /**
     * 接收待打印订单状态接口
     */
    public static final String FANG_GE_GETPRINTINVOICESSTATUS = "getPrintInvoicesStatus";
    /**
     * 接收打印结果接口
     */
    public static final String FANG_GE_UPDATEPRINTINVOICES = "updatePrintInvoices";
    /**
     * 税盘数据同步
     */
    public static final String FANG_GE_UPDATETAXDISKINFO = "updateTaxDiskInfo";
    /**
     * 税盘注册
     */
    public static final String FANG_GE_REGISTTAXDISK = "registTaxDisk";

    /**
     * 待上传发票种类接口
     */
    public static final String FANG_GE_GETUPLOADFPZLDMS = "getUploadFpzldms";

    /**
     * 更新待上传发票接口
     */
    public static final String FANG_GE_UPDATEINVOICEUPLOADSTATUS = "updateInvoiceUploadStatus";

    /**
     * 获取企业状态接口
     */
    public static final String FANG_GE_QUERY_ENTERPRISE = "queryEnterprise";

    /**
     * 抄报税发票种类接口
     */
    public static final String FANG_GE_GET_TAX_FPZLDMS = "getTaxFpzldms";

    /**
     * 抄报税更新结果接口
     */
    public static final String FANG_GE_UPDATE_TAX_STATUS = "updateTaxStatus";

    /**
     * 发票申领结果接收(好活专用)
     */
    public static final String RECEIVEINVOICEAPPLICATION = "ReceiveInvoiceApplication";

    /**
     * 税盘下架接口（好活专用）
     */
    public static final String ENTERPRISEOFFLINE = "EnterpriseOffline";

    /**
     * 设备信息查询接口
     */
    public static final String QUERYTAXPAYERINFO = "QueryTaxpayerInfo";
    /**
     * 发票打印接口
     */
    public static final String PRINTINVOICE = "PrintInvoice";
    /**
     * 打印机查询接口
     */
    public static final String QUERYPRINTER = "QueryPrinter";
    /**
     * 接收云组件发票上传状态接口
     */
    public static final String FANG_GE_UPDATEUPLOADSTATUS = "updateUploadStatus";
    /**
     * 企业基本信息查询接口
     */
    public static final String QUERYTAXPAYERGENERALINFO = "QueryTaxpayerGeneralInfo";
    /**
     * 申领准备信息查询接口
     */
    public static final String QUERYAPPLICATIONPREPARATION = "QueryApplicationPreparation";
    /**
     * 发票申领接口
     */
    public static final String INVOICEAPPLY = "InvoiceApply";
    /**
     * 申领状态查询接口
     */
    public static final String QUERYINVOICEAPPLYSTATUS = "QueryInvoiceApplyStatus";
    /**
     * 纸票接收确认接口
     */
    public static final String CONFIRMINVOICEAPPLY = "ConfirmInvoiceApply";
    /**
     * 撤销申领接口
     */
    public static final String CANCELINVOICEAPPLY = "CancelInvoiceApply";
    /**
     * 抄报状态查询接口
     */
    public static final String QUERYDECLARETAXSTATUS = "QueryDeclareTaxStatus";
    /**
     * 汇总抄报接口
     */
    public static final String DECLARETAXUPLOAD = "DeclareTaxUpload";

    /**
     * 启用zip压缩
     */
    public static final String ZIPCODE_1 = "1";

    /**
     * 启用base64加密
     */
    public static final String ENCRYPTCODE_0 = "0";

    /**
     * 启用3DES加密
     */
    public static final String ENCRYPTCODE_1 = "1";

    /**
     * 方格接口 签名参数 固定
     */
    public static final String FG_QMCS = "0000004282000000";


    /**
     * 3.1 蓝字发票开具接口
     */
    public static final String INVOICEISSUE = "invoiceIssue";

    /**
     * 3.2 蓝字发票结果查询接口
     */
    public static final String QUERYINVOICEINFO = "queryInvoiceInfo";

    /**
     * 3.3 发票基础信息查询
     */
    public static final String GETINVOICEBASEINFO = "getInvoiceBaseInfo";

    /**
     * 3.4 单张发票信息查询
     */
    public static final String GETSINGLEINVOICEINFO = "getSingleInvoiceInfo";

    /**
     * 3.5 红字开票确认信息录入
     */
    public static final String REDINVOICECONFIRMINFOENTER = "redInvoiceConfirmInfoEnter";

    /**
     * 3.6 红字开票确认信息受理结果查询
     */
    public static final String GETREDINVOICECONFIRMINFO = "getRedInvoiceConfirmInfo";

    /**
     * 3.7 红字发票确认信息处理
     */
    public static final String GETREDINVOICECONFIRMINFODEAL = "getRedInvoiceConfirmInfoDeal";

    /**
     * 3.8 红字发票确认信息列表查询
     */
    public static final String GETREDINVOICECONFIRMINFOLIST = "getRedInvoiceConfirmInfoList";

    /**
     * 3.9 红字发票确认明细信息查询
     */
    public static final String GETREDINVOICECONFIRMINFODETAIL = "getRedInvoiceConfirmInfoDetail";

    /**
     * 3.10 红字发票开具受理
     */
    public static final String REDINVOICEISSUE = "redInvoiceIssue";

    /**
     * 3.11 红字发票开具受理结果查询
     */
    public static final String GETREDINVOICERESULT = "getRedInvoiceResult";

    /**
     * 电子税局登录
     */
    public static final String DZSWJLOGIN = "dzswjLogin";

    /**
     * 登录设置验证码
     */
    public static final String SETSMS = "setSms";

    /**
     * 获取实名认证二维码
     */
    public static final String GETCONFIRMQRCODE = "getConfirmQrcode";

    /**
     * 实名认证信息查询
     */
    public static final String GETCONFIRMSTATUS = "getConfirmStatus";
    /**
     * 获取电局登录session信息
     */
    public static final String GETSESSIONINFO = "getSessionInfo";




}

