package com.dxhy.order.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@RefreshScope
public class OrderConfig {

    /**
     * 商品新增地址
     */
    @Value("${order.encrypt.addInvoiceItemUrl}")
    private String addInvoiceItemUrl;

    /**
     * 纳税人信息新增地址
     */
    @Value("${order.encrypt.addDeptUrl}")
    private String addDeptUrl;

    /**
     * 数据库地址
     */
    @Value("${order.dbUrl}")
    private String dbUrl;

    /**
     * 数据库端口
     */
    @Value("${order.dbPort}")
    private String dbPort;

    /**
     * 库名
     */
    @Value("${order.dbName}")
    private String dbName;

    /**
     * 用户名
     */
    @Value("${order.dbAccount}")
    private String dbAccount;

    /**
     * 密码
     */
    @Value("${order.dbPassword}")
    private String dbPassword;

    /**
     * 获取实名认证二维码
     */
    @Value("${order.encrypt.getQrcodeUrl}")
    private String getQrcodeUrl;

    /**
     * 获取实名认证二维码状态
     */
    @Value("${order.encrypt.getQrcodeStatusUrl}")
    private String getQrcodeStatusUrl;

    /**
     * 根据税号获取集团公司下的税号
     */
    @Value("${sso.queryAllTaxListByTaxNo}")
    private String queryAllTaxListByTaxNo;

    @Value("${order.dx.appkey}")
    private String appkey;
    @Value("${order.dx.appSecret}")
    private String appSecret;
    @Value("${order.dx.entcode}")
    private String entcode;
    @Value("${order.dx.tokenUrl}")
    private String tokenUrl;
    @Value("${order.dx.activeUrl}")
    private String activeUrl;
    @Value("${order.dx.loginUrl}")
    private String loginUrl;
    @Value("${order.dx.getAuthQrcodeUrl}")
    private String authQrcodeUrl;
    @Value("${order.dx.getAuthStatusUrl}")
    private String authStatusUrl;




    /**
     * 电子税局登录
     */
    @Value("${order.encrypt.loginUrl}")
    private String taxBureauLoginUrl;

    /**
     * 设置登录二维码
     */
    @Value("${order.encrypt.setSmsUrl}")
    private String setSmsUrl;

    /**
     * 获取实名二维码
     */
    @Value("${order.encrypt.confirmQrcodeUrl}")
    private String confirmQrcodeUrl;

    /**
     * 获取实名二维码状态
     */
    @Value("${order.encrypt.confirmStatusUrl}")
    private String confirmStatusUrl;


    /**
     * 获取电局登录信息URL
     */
    @Value("${order.hbhx.sessionInfoUrl}")
    private String sessionInfoUrl;
    /**
     * 系统标识
     */
    @Value("${order.hbhx.xtbs}")
    private String xtbs;
    /**
     * 公钥
     */
    @Value("${order.hbhx.gy}")
    private String gy;
    /**
     * 签名秘钥
     */
    @Value("${order.hbhx.qmmy}")
    private String qmmy;







}
