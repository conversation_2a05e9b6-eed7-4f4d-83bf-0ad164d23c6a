package com.dxhy.order.config;

import org.redisson.Redisson;
import org.redisson.RedissonRedLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: Redisson锁
 * @date 2023/1/30 18:25
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private String redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;


    @Bean
    public RedissonRedLock redissonRedLock(){
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + redisHost + ":" + redisPort).setPassword(redisPassword).setDatabase(10)
                .setDnsMonitoringInterval(5 * 60 * 1000);

        RedissonClient redissonClient3 = Redisson.create(config);

        final RLock lock2 = redissonClient3.getLock("LOCK");

        RedissonRedLock redissonRedLock = new RedissonRedLock(lock2);

        return redissonRedLock;
    }



}
