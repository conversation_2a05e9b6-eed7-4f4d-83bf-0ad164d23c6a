package com.dxhy.order.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 基础服务配置文件信息获取
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
@Configuration
@Getter
@Setter
@RefreshScope
public class BaseServiceConfig {
    
    
    /**
     * 当前服务器地址
     */
//    @Value("${order.url.domain}")
    private String localDomain;
    
    
    /**
     * 销项后台URL
     */
//    @Value("${order.url.simsBackUrl}")
    private String simsBackUrl;
    
    /**
     * 编码表版本号
     */
//    @Value("${order.system.bmbbbh}")
    private String bmbbbh;
    
    /**
     * 敏感词信息
     */
//    @Value("${order.sensitive}")
    private String sensitive;
    
    /**
     * 判断不是方式是不是weblogic
     */
//    @Value("${order.weblogic.webServerType}")
    private String webServerType;
    
    /**
     * weblogic方式部署,读取模板地址
     */
//    @Value("${order.weblogic.downloadFileUrl}")
    private String downloadFileUrl;
    
    /**
     * 购方信息查询是否使用大数据接口
     */
//    @Value("${order.system.useBigDataProcessBuyer}")
    private String useBigDataProcessBuyer;
    
    
    /**
     * 大数据地址
     */
//    @Value("${order.url.bigDataUrl}")
    private String bigDataUrl;
    
    /**
     * 大数据企业信息模糊查询鉴权id
     */
//    @Value("${order.bigData.authId}")
    private String bigDataAuthId;
    
    /**
     * 大数据企业信息模糊查询平台code
     */
//    @Value("${order.bigData.ptCode}")
    private String bigDataPtCode;
    
    
//    @Value("${order.openapi.OPENAPI_EMAIL_NOTE}")
    private String openApiEmailNote;
//    @Value("${order.openapi.OPENAPI_TOKEN}")
    private String openApiToken;
//    @Value("${order.openapi.TOKEN_CLIENT_ID}")
    private String tokenClientId;
//    @Value("${order.openapi.TOKEN_CLIENT_SECRET}")
    private String tokenClientSecret;
//    @Value("${order.openapi.TOKEN_GRANT_TYPE}")
    private String tokenGrantType;
//    @Value("${order.openapi.TOKEN_SCOPE}")
    private String tokenScope;
    
    
    /**
     * 部署web容器类型
     *
     * @return
     */
    public String configWebServerType() {
        return webServerType;
    }
    
    /**
     * weblogic部署时文件存放路径
     *
     * @return
     */
    public String configDownloadFileUrl() {
        return downloadFileUrl;
    }
    
    /**
     * 调用销项后台获取销项后台销方信息
     *
     * @return
     */
    public String configQuerySimsBackTaxpayerList() {
        return simsBackUrl + "/sims-management-service/companyCustomer/web/queryCompanyCustomerList";
    }
    
    /**
     * 调用销项后台获取税控设备列表
     *
     * @return
     */
    public String configQueryTaxEquipmentList() {
        return simsBackUrl + "/sims-management-service/taxManager/queryTaxEquipment";
    }
    
    /**
     * 根据税号调用销项后台获取税控设备
     *
     * @return
     */
    public String configQueryTaxEquipment() {
        return simsBackUrl + "/sims-management-service/taxManager/queryTaxEquipmentByNsrsbh";
    }
    
    /**
     * 调用销项后台新增税控设备
     *
     * @return
     */
    public String configAddTaxEquipment() {
        return simsBackUrl + "/sims-management-service/taxManager/addTaxEquipment";
    }
    
    /**
     * 调用销项后台企业下架接口
     */
    public String configTakeDownTaxDisk(){
        return simsBackUrl + "/sims-management-service/invoiceCount/updateInvoiceCountForOffLine";
    }

    /**
     * 调用销项后台发票申领结果接收接口
     */
    public String configReceiveInvoiceApplication(){
        return simsBackUrl + "/sims-management-service/backService/receiveFpslJg";
    }
    
    /**
     * 调用大数据获取购方信息
     *
     * @return
     */
    public String configBigDataQueryEnterprise() {
        return bigDataUrl + "/enterprise/platform/fuzzyQuery";
    }
    
    /**
     * 大数据精确获取购方信息URL
     *
     * @return
     */
    public String configBigDataExactQueryEnterprise() {
        return bigDataUrl + "/enterprise/platform/exactQuery";
    }
    
    /**
     * 发送邮件地址
     *
     * @return
     */
    public String configOpenapiEmailNote() {
        return openApiEmailNote + "emailSend";
    }
    
    /**
     * 插卡地址
     *
     * @return
     */
    public String configInsertCardUrl() {
        return openApiEmailNote + "insertCard";
    }
    
    /**
     * 短信发送接口地址
     *
     * @return
     */
    public String configSendMessageUrl() {
        return openApiEmailNote + "smsSend";
    }
    
    /**
     * 获取token
     *
     * @return
     */
    public String configOpenapiToken() {
        return openApiToken;
    }
    
    /**
     * token获取配置参数
     *
     * @return
     */
    public String configTokenClientId() {
        return tokenClientId;
    }
    
    /**
     * token获取配置参数
     *
     * @return
     */
    public String configTokenClientSecret() {
        return tokenClientSecret;
    }
    
    /**
     * token获取配置参数
     *
     * @return
     */
    public String configTokenGrantType() {
        return tokenGrantType;
    }
    
    /**
     * token获取配置参数
     *
     * @return
     */
    public String configTokenScope() {
        return tokenScope;
    }
    
    /**
     * rabbitMq重启队列
     *
     * @return
     */
    public String configRabbitmqRestartListen() {
        return localDomain + "/rabbitmq/restartListen?nsrsbh=%s&queueName=%s";
    }
    
    /**
     * 大数据企业信息模糊查询鉴权id
     *
     * @return
     */
    public String configBigDataAuthId() {
        return bigDataAuthId;
    }
    
    /**
     * 大数据企业信息模糊查询平台code
     *
     * @return
     */
    public String configBigDataPtCode() {
        return bigDataPtCode;
    }
    
    /**
     * 购方信息查询是否使用大数据接口
     *
     * @return
     */
    public String configUseBigDataProcessBuyer() {
        return useBigDataProcessBuyer;
    }
    
    /**
     * 编码表版本号配置
     *
     * @return
     */
    public String configBmbbbh() {
        return bmbbbh;
    }
    
    /**
     * 敏感词配置
     *
     * @return
     */
    public String configSensitive() {
        return sensitive;
    }

    
    /**
     * 方格订单更新状态接口
     *
     * @return
     */
    public String configUpdateInvoiceStatus() {
        return localDomain + "/api/updateOrderInvoice";
    }

    /**
     * 订单更新状态接口
     *
     * @return
     */
    public String configUpdateOrderAndInvoiceStatus() {
        return localDomain + "/api/updateOrderAndInvoice";
    }

    
}
