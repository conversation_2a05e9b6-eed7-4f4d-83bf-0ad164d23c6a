package com.dxhy.order.config;

import com.dxhy.order.permit.context.ContextAwareTaskDecorator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfig {

    private Executor getInstance(String threadNamePrefix, Integer corePoolSize, Integer maxPoolSize, Integer queueCapacity) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 设置最大线程数，获取运行实际线程数 Runtime.getRuntime().availableProcessors()
        executor.setMaxPoolSize(maxPoolSize);
        // 线程池所使用的缓冲队列
        executor.setQueueCapacity(queueCapacity);
        // 等待任务在关机时完成--表明等待所有线程执行完
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间 （默认为0，此时立即停止），并没等待xx秒后强制停止
        executor.setAwaitTerminationSeconds(60);
        // 线程名称前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        //设置拒绝策略，默认直接抛出异常：AbortPolicy【直接抛出异常】，CallerRunsPolicy【由调用线程处理该任务】，DiscardPolicy【直接丢弃】，DiscardOldestPolicy【丢弃队列中最老的任务】
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 设置线程池中任务的装饰器，用于在任务执行之前和之后对任务进行装饰
        executor.setTaskDecorator(new ContextAwareTaskDecorator());
        //初始化线程
        executor.initialize();
        return executor;
    }

    @Bean(ConfigurerInfo.COMMONTHREADPOOL)
    public Executor getCommonAsyncExecutor() {
        return getInstance("eims-common-", 8, 8, 300);
    }

}
