package com.dxhy.order.constant;
/**
 * 发票状态枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum RedInvoiceReasonEnum {

    /**
     * 0:开票有误;1:销售退回;2:销售折让;
     */
    RED_INVOICE_REASON_ENUM_0("0", "开票有误"),
    RED_INVOICE_REASON_ENUM_1("1", "销售退回"),
    RED_INVOICE_REASON_ENUM_2("2", "销售折让");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static RedInvoiceReasonEnum getCodeValue(String key) {
        for (RedInvoiceReasonEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    RedInvoiceReasonEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
