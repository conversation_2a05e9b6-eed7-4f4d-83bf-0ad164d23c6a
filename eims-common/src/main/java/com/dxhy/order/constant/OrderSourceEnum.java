package com.dxhy.order.constant;
/**
 * 订单来源枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum OrderSourceEnum {

    /**
     * 0：扫码开票；1：批量导入，2外部接口，3手动填开
     */
    ORDER_SOURCE_ENUM_0("0", "扫码开票"),
    ORDER_SOURCE_ENUM_1("1", "批量导入"),
    ORDER_SOURCE_ENUM_2("2", "外部接口"),
    ORDER_SOURCE_ENUM_3("3", "手动填开");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static OrderSourceEnum getCodeValue(String key) {
        for (OrderSourceEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    OrderSourceEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
