package com.dxhy.order.constant;

public enum OrderSplitEnum {
    ORDER_SPLIT_TYPE_1("1", "按金额超限额拆分"),
    ORDER_SPLIT_TYPE_2("2", "按金额拆分"),
    ORDER_SPLIT_TYPE_3("3", "按数量拆分"),
    ORDER_SPLIT_TYPE_4("4", "按限制的明细行拆分"),
    ORDER_SPLIT_TYPE_5("5", "按明细行拆分"),
    ORDER_SPLIT_RULE_1("1", "拆分后保证数量不变"),
    ORDER_SPLIT_RULE_2("2", "拆分后保证单价不变"),
    ORDER_SPLIT_RULE_3("3", "拆分后保证总数量，总金额，单价不变，平衡金额误差"),
    ORDER_HSBZ_0("0", "不含税拆分"),
    ORDER_HSBZ_1("1", "含税拆分"),
    ORDER_RESPERATION_0("0", "不重新价税分离"),
    ORDER_RESPERATION_1("1", "重新价税分离"),
    ORDER_SPLIT_OVERLIMIT_FIXED_QTY("4", "超限额固定数量拆分"),;

    private final String key;
    private final String value;

    private OrderSplitEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static OrderSplitEnum getCodeValue(String key) {
        OrderSplitEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            OrderSplitEnum item = var1[var3];
            if (item.getKey().equals(key)) {
                return item;
            }
        }

        return null;
    }

    public String getKey() {
        return this.key;
    }

    public String getValue() {
        return this.value;
    }
}
