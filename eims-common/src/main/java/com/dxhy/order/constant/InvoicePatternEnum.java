package com.dxhy.order.constant;
/**
 * 发票状态枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum InvoicePatternEnum {

    /**
     * 0 自动开票  1 手动开票 2 扫码开票
     */
    INVOICE_PATTERN_ENUM_0("0", "自动开票"),
    INVOICE_PATTERN_ENUM_1("1", "手动开票"),
    INVOICE_PATTERN_ENUM_2("2", "扫码开票");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static InvoicePatternEnum getCodeValue(String key) {
        for (InvoicePatternEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    InvoicePatternEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
