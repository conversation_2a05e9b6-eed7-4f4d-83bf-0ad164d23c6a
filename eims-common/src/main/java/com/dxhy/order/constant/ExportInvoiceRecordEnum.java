package com.dxhy.order.constant;


import java.util.ArrayList;
import java.util.List;

/**
 * Excel导出 - 开票记录
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 12:50
 */
public enum ExportInvoiceRecordEnum {

    /**
     * Excel导出开票记录
     * 必须按照顺序存放,否则会异常
     */
    EXCEL_EXPORT_INVOICE_FPZLDM("0", "fpzldm", "发票票种"),
    EXCEL_EXPORT_INVOICE_GHFMC("1", "ghfmc", "购方名称"),
    EXCEL_EXPORT_INVOICE_QDFPHM("2", "qdfphm", "全电发票号码"),
    EXCEL_EXPORT_INVOICE_KPRQ("3", "kprq", "开票日期"),
    EXCEL_EXPORT_INVOICE_FPJE("4", "kpje", "开票金额（元）"),
    EXCEL_EXPORT_INVOICE_FPSE("5", "kpse", "开票税额（元）"),
    EXCEL_EXPORT_INVOICE_KPLX("6", "kplx", "开票类型");

    /**
     * key
     */
    private final String key;

    /**
     * 值
     */
    private final String value;


    /**
     * 表格头名称
     */
    private final String cellName;


    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getCellName() {
        return cellName;
    }

    ExportInvoiceRecordEnum(String key, String value, String cellName) {
        this.key = key;
        this.value = value;
        this.cellName = cellName;
    }

    public static ExportInvoiceRecordEnum getCodeValue(String key) {

        for (ExportInvoiceRecordEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }

    public static List<String> getValues() {

        List<String> resultList = new ArrayList<>();
        for (ExportInvoiceRecordEnum item : values()) {
            resultList.add(item.getValue());
        }
        return resultList;
    }
}
