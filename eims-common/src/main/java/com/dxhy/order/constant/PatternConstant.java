package com.dxhy.order.constant;

/**
 * 正则常量值
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
public class PatternConstant {
    
    /**
     * 项目单价正则表达式
     */
    public static final String PATTERN_XMDJ = "\\d+?[.]?\\d{0,8}";
    
    /**
     * 项目数量正则表达式
     */
    public static final String PATTERN_XMSL = "-?\\d+?[.]?\\d{0,8}";
    
    /**
     * 项目金额正则表达式
     */
    public static final String PATTERN_XMJE = "-?\\d+?[.]?\\d{0,2}";
    
    /**
     * 项目税率正则表达式
     */
    public static final String PATTERN_SL = "\\d+?[.]?\\d{0,3}";
    
    /**
     * http格式校验
     */
    public static final String PATTERN_HTTP = "^([hH][tT]{2}[pP]://|[hH][tT]{2}[pP][sS]://)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~/])+$";
    
    
}
