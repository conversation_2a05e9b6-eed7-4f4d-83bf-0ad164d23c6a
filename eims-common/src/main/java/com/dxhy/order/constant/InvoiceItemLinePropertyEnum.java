package com.dxhy.order.constant;
/**
 * 发票行性质枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum InvoiceItemLinePropertyEnum {

    /**
     * 0正常商品行  1折扣行  2被折扣行
     */
    INVOICE_ITEM_LINE_PROPERTY_ENUM_0("0", "正常商品行"),
    INVOICE_ITEM_LINE_PROPERTY_ENUM_1("1", "折扣行"),
    INVOICE_ITEM_LINE_PROPERTY_ENUM_2("2", "被折扣行");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static InvoiceItemLinePropertyEnum getCodeValue(String key) {
        for (InvoiceItemLinePropertyEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    InvoiceItemLinePropertyEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
