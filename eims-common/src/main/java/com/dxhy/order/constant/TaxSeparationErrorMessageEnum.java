package com.dxhy.order.constant;

/**
 * <AUTHOR>
 * @ClassName ：TaxSeparationErrorMessageEnum
 * @Description ：价税分离错误信息枚举类
 * @date 创建时间: 2022-06-29 09:42
 */

public enum TaxSeparationErrorMessageEnum {
    
    /**
     * 价税分离异常类
     */


    TAXSEPARATION_SE_WC_TOTAL("1000", "税额累计误差大于1.27"),
    
    TAXSEPARATION_KCE_FORMAT_ERROR("1001", "扣除额格式错误"),
    
    TAXSEPARATION_SE_WC_ERROR("1002", "单条明细税额误差大于0.06"),
    
    TAXSEPARATION_BHSJE_NULL_ERROR("1003", "不含税订单，不含税金额不能为空"),
    
    TAXSEPARATION_HSJE_NULL_ERROR("1004", "含税订单，含税金额不能为空"),
    
    TAXSEPARATION_NULL("1005", "请求外层数据为空"),
    TAXSEPARATION_NULL1("1006", "请求明细列表数据为空"),
    TAXSEPARATION_NULL2("1007", "请求明细数据为空"),
    TAXSEPARATION_NULL3("1007", "请求订单主数据为空"),
    TAXSEPARATION_NULL4("1007", "请求订单主数据开票合计金额为空"),
    TAXSEPARATION_NULL5("1007", "请求订单主数据开票合计不含税金额为空"),
    TAXSEPARATION_ITEM_SL_NULL("1008", "明细行税率为空"),
    TAXSEPARATION_ITEM_JE_NULL("1009", "明细行金额为空"),
    TAXSEPARATION_ITEM_HSBZ_ERROR("1010", "明细行含税标志只能为0或1"),
    TAXSEPARATION_ITEM_FPHXZ_ERROR("1011", "明细行发票行性质只能为0,1,2,6"),
    TAXSEPARATION_ITEM_KCE_ERROR("1013", "正数发票扣除额不能大于金额"),
    
    
    TAXSEPARATION_ERROR("9999", "价税分离异常"),
    ;
    /**
     * key
     */
    private final String key;
    
    /**
     * 值
     */
    private final String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    TaxSeparationErrorMessageEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }
    
    public static TaxSeparationErrorMessageEnum getCodeValue(String key) {

        for (TaxSeparationErrorMessageEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }

}
