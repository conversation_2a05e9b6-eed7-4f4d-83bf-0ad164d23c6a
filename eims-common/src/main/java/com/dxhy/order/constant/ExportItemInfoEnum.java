package com.dxhy.order.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * excel导出 项目信息
 * <AUTHOR>
 * @Date 2022/7/13 18:05
 * @Version 1.0
 **/
public enum ExportItemInfoEnum {

    /**
     * Excel导出开票记录
     * 必须按照顺序存放,否则会异常
     */
    EXCEL_EXPORT_ITEMINFO_XMMC("0", "xmmc", "项目名称"),
    EXCEL_EXPORT_ITEMINFO_SPHSSFLJC("1", "sphssfljc", "商品和服务分类简称"),
    EXCEL_EXPORT_ITEMINFO_SPHSSFLBM("2", "sphssflbm", "商品和服务税收分类编码"),
    EXCEL_EXPORT_ITEMINFO_DJ("3", "dj", "单价"),
    EXCEL_EXPORT_ITEMINFO_HSBS("4", "hsbs", "含税标志"),
    EXCEL_EXPORT_ITEMINFO_GGXH("5", "ggxh", "规格型号"),
    EXCEL_EXPORT_ITEMINFO_SL("6", "sl", "税率/征收率"),
    EXCEL_EXPORT_ITEMINFO_DW("7", "dw", "单位"),
    EXCEL_EXPORT_ITEMINFO_JM("8", "jm", "简码"),
    EXCEL_EXPORT_ITEMINFO_YHZCBS("9", "yhzcbs", "是否使用优惠政策标识"),
    EXCEL_EXPORT_ITEMINFO_YHZCLX("10", "yhzclx", "优惠政策类型"),
    EXCEL_EXPORT_ITEMINFO_ITEMGROUPNAME("11", "itemGroupName", "项目分类名称"),
    EXCEL_EXPORT_ITEMINFO_ITEMGROUPCODE("12", "itemGroupCode", "项目分类编码");

    /**
     * key
     */
    private final String key;

    /**
     * 值
     */
    private final String value;


    /**
     * 表格头名称
     */
    private final String cellName;


    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getCellName() {
        return cellName;
    }

    ExportItemInfoEnum(String key, String value, String cellName) {
        this.key = key;
        this.value = value;
        this.cellName = cellName;
    }

    public static ExportItemInfoEnum getCodeValue(String key) {

        for (ExportItemInfoEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }

    public static List<String> getValues() {

        List<String> resultList = new ArrayList<>();
        for (ExportItemInfoEnum item : values()) {
            resultList.add(item.getValue());
        }
        return resultList;
    }
}
