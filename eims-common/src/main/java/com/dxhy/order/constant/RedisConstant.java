package com.dxhy.order.constant;

/**
 * redis常量值
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
public class RedisConstant {


    /**
     * redis key
     */

    public static final int REDIS_EXPIRE_TIME_DEFAULT = 30 * 24 * 60 * 60;

    public static final int REDIS_EXPIRE_TIME_25DAYS = 25 * 24 * 60 * 60;
    public static final int REDIS_EXPIRE_TIME_1DAYS = 1 * 24 * 60 * 60;

    public static final int REDIS_EXPIRE_TIME_24HOUR = 24 * 60 * 60;

    public static final int REDIS_EXPIRE_TIME_10HOUR = 10 * 60 * 60;

    public static final int REDIS_EXPIRE_TIME_2HOUR = 2 * 60 * 60;

    public static final int REDIS_EXPIRE_TIME_30MIN = 30 * 60;

    public static final int REDIS_EXPIRE_TIME_1MIN = 60;

    public static final String REDIS_INVOICE_SPLIT_PREFIX = "xxfp:split:fpqqlsh:%s";

    /**
     * 认证状态
     */
    public static final String REDIS_QRCODE_STATES_PREFIX = "kp:sm:slzt:%s";

    /**
     * 获取当前税号对应的可信账号
     */
    public static final String REDIS_DZSWJ_ZH = "HBHX:TAX_NAME:%s";

    /**
     * 静态码rediskey
     */
    public static final String REDIS_EWM_STATIC = "xxfp:ewm:static:%s";

    /**
     * 二维码开票接收微信推送redis同步锁前缀
     */
    public static final String REDIS_EWM_SYN_LOCK = "xxfp:ewm:receive:lock:%s";


    /**
     * 动态码Rediskey
     */
    public static final String REDIS_EWM_DYNAMIC = "dynamic";

    /**
     * 订单接收数据key
     */
    public static final String REDIS_INTERFACE_RECEIVE = "xxfp:order:receive:%s";

    /**
     * 订单接收数据,验证是否超过限制次数
     */
    public static final String REDIS_INTERFACE_CHECK_LIMIT = "xxfp:cache:check:limit:%s";

    /**
     * 订单接收底层开票数据状态
     */
    public static final String REDIS_ORDER_RECEIVE_STATUS = "xxfp:cache:order:receive:status:%s";

    /**
     * 订单操作记录缓存
     */
    public static final String REDIS_ORDER_OPERATE_STATUS = "xxfp:cache:order:operate:%s";

    /**
     * 订单缓存企业限额
     */
    public static final String REDIS_ORDER_XE = "xxfp:cache:order:xe:%s:%s";

    /**
     * 缓存用户抬头信息的前缀
     */
    public static final String REDIS_EWM_TITLE = "xxfp:ewm:title:%s";
    /**
     * 用户引导缓存redis前缀
     */
    public static final String REDIS_USER_GUIDER = "xxfp:guider:%s";

    /**
     * 开票流水号与销方税号对应关系
     */
    public static final String REDIS_KPLSH = "xxfp:cache:kplsh:%s";

    /**
     * 发票开具同步锁
     */
    public static final String REDIS_SYNC_FPQQLSH = "xxfp:cache:sync:fpqqlsh:%s";

    /**
     * 发票批次号与销方税号对应关系
     */
    public static final String REDIS_FPQQPCH = "xxfp:cache:fpqqpch:%s";

    /**
     * 发票批次号与销方税号对应关系
     */
    public static final String REDIS_FPDMHM = "xxfp:cache:fpdmhm:%s";

    /**
     * 提取码与销方税号对应关系
     */
    public static final String REDIS_TQM = "xxfp:cache:tqm:%s";

    /**
     * 异常邮箱预警
     */
    public static final String REDIS_SALE_WARNING = "xxfp:cache:salewarning:%s";

    /**
     * 验签失败发票可开票阈值(销方税号)
     */
    public static final String REDIS_CHECK_FAIL_COUNT = "xxfp:checkfail:%s";

    /**
     * 提取码与销方税号对应关系
     */
    public static final String REDIS_AUTHID = "xxfp:cache:authid:%s";

    /**
     * 接口鉴权id
     */
    public static final String REDIS_AUTH_SECRET_ID = "xxfp:cache:auth:%s";

    /**
     * 订单查询日期范围
     */
    public static final String REDIS_QUERY_INVOICE_TIME = "xxfp:cache:queryInvoiceTime:%s";

    public static final String REDIS_NOTES = "xxfp:cache:notes:%s";


    /**
     * 方格存放税盘信息的  redis key
     */
    public static final String FG_TAX_DISK_INFO = "fa_tax_disk_info";
    /**
     * 存放注册码的 redis key
     */
    public static final String REDIS_FG_TAX_DISK_INFO = "xxfp:cache:" + FG_TAX_DISK_INFO + ":%s";
    /**
     * 存放税盘数据的 redis key
     */
    public static final String REDIS_FG_TAX_DISK_DATA = "xxfp:spxx:nsrsbh:%s" + ConfigureConstant.STRING_UNDERLINE + "fjh:%s";

    /**
     * 方格存放消息队列  redis key
     */
    public static final String REDIS_FG_MQTT_MSG = "xxfp:cache:fg_mqtt_msg:%s:%s";

    public static final String FG_MQTT_MSG_FLAG = "fg_mqtt_msg_flag";
    /**
     * 方格存放消息标识是否可取消息  redis key
     */
    public static final String REDIS_FG_MQTT_MSG_FLAG = "xxfp:cache:" + FG_MQTT_MSG_FLAG + ":%s:%s";

    /**
     * 方格缓存抄报税结果状态数据
     */
    public static final String REDIS_FG_TAX_STATUS = "xxfp:cache:tax_status:%s";


    /**
     * 方格发送消息topic
     */
    public static final String FG_MQTT_TOPIC_PUB_FANGGE = "invoice/fangge/%s/%s";

    public static final String ERROR_MESSAGE_ORDER = "【税号:%s,发票请求唯一流水号:%s,订单号:%s】异常信息:%s";

    public static final String ERROR_MESSAGE_INVOICE = "【税号:%s,分机号:%s】剩余%s张%s发票，已低于预警值:%s;";

    /**
     * 设备信息查询缓存 redis key
     */
    public static final String QUERY_TAXPAYERINFO_FG = "xxfp:cache:fg_taxpayer_info:%s:%s";
    public static final String QUERY_TAXPAYERINFO_NEW = "xxfp:cache:new_taxpayer_info:%s";
    public static final String QUERY_TAXPAYERINFO_OTHER = "xxfp:cache:other_taxpayer_info:%s:%s";

    /**
     * 轮询开票，放入延时队列重试次数缓存
     */
    public static final String POOL_SENDDEALYMQ_RETRY_COUNT = "xxfp:cache:pool_send_dealyMq_retry_count:%s";

    /**
     * 列配置
     */
    public static final String COLUMNCONFIG = "lpz:%s:%s";
    /**
     * 税航票帮手token存储key
     */
    public static String MYCST_TOKEN_CODE_KEY = "heaven_code:mycst_token:";
}
