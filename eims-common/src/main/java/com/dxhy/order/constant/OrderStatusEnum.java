package com.dxhy.order.constant;
/**
 * 订单状态枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum OrderStatusEnum {

    /**
     * 0: 正常 ,1: 合并, 2: 退回,3:无效
     */
    ORDER_STATUS_ENUM_0("0", "正常"),
    ORDER_STATUS_ENUM_1("1", "合并"),
    ORDER_STATUS_ENUM_2("2", "退回"),
    ORDER_STATUS_ENUM_3("3", "无效"),
    ORDER_STATUS_ENUM_4("4", "拆分");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static OrderStatusEnum getCodeValue(String key) {
        for (OrderStatusEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    OrderStatusEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
