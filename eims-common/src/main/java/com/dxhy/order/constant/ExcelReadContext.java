package com.dxhy.order.constant;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * Excel表格导入头信息
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 13:09
 */
@Setter
@Getter
public class ExcelReadContext {
    
    
    /**
     * 需要转换成的bean
     */
    private Class<?> cla;
    
    /**
     * 表格头信息与协议bean对应关系
     */
    private Map<String, String> headToPropertyMap;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 文件前缀
     */
    private String filePrefix;
    
    /**
     * sheet页数
     */
    private int sheetIndex;
    
    /**
     * sheet单页限制大小
     */
    private int sheetLimit;
    
    /**
     * 表格头行数
     */
    private int headRow;
    
    /**
     * 行数
     */
    private int rowCount;
    
    /**
     * 是否需要表格头
     */
    private boolean needRowIndex;

    /**
     * 是否需要sheet名称
     */
    private boolean needSheetName;
    
    /**
     * 表格头对应的excel的列数
     * 表格导出需要该数据
     */
    private Map<String, String> headerToColumnMap;
    
    
    public ExcelReadContext(Class<?> t, Map<String, String> map, boolean isNeedRowIndex) {
        
        this.cla = t;
        this.headToPropertyMap = map;
        this.filePrefix = ".xlsx";
        this.sheetIndex = 0;
        this.headRow = 1;
        this.needRowIndex = isNeedRowIndex;
    }
    
    public ExcelReadContext(Map<String, String> map, boolean isNeedRowIndex, Map<String, String> headerToColumnMap) {
        
        this.headToPropertyMap = map;
        this.filePrefix = ".xlsx";
        this.sheetIndex = 0;
        this.headRow = 1;
        this.needRowIndex = isNeedRowIndex;
        this.headerToColumnMap = headerToColumnMap;
    }
    
    
}
