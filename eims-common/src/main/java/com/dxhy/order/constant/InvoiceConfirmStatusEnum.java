package com.dxhy.order.constant;
/**
 * 发票状态枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum InvoiceConfirmStatusEnum {


    INVOICE_CONFIRM_STATUS_ENUM_0("0", "无需确认"),
    INVOICE_CONFIRM_STATUS_ENUM_1("1", "销方录入待确认"),
    INVOICE_CONFIRM_STATUS_ENUM_2("2", "购方录入待确认"),
    INVOICE_CONFIRM_STATUS_ENUM_3("3", "购销方已确认"),
    INVOICE_CONFIRM_STATUS_ENUM_4("4", "作废(销方录入购方否认)"),
    INVOICE_CONFIRM_STATUS_ENUM_5("5", "作废(购方录入销方否认)"),
    INVOICE_CONFIRM_STATUS_ENUM_6("6", "作废(超72小时未确认)"),
    INVOICE_CONFIRM_STATUS_ENUM_7("7", "作废(发起方撤销)"),
    INVOICE_CONFIRM_STATUS_ENUM_8("8", "作废(异常凭证)");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static InvoiceConfirmStatusEnum getCodeValue(String key) {
        for (InvoiceConfirmStatusEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    InvoiceConfirmStatusEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
