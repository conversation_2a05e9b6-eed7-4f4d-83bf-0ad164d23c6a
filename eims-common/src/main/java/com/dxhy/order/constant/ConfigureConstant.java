package com.dxhy.order.constant;

/**
 * 配置类静态常量
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
public class ConfigureConstant {
    
    /**
     * 静态常量基础数值
     */

    public static final String CODE = "code";
    
    public static final String STRING_RESULT = "result";
    
    public static final String STRING_CONTENT = "content";
    
    public static final String STRING_QUEUE_DEFAULT = "config";
    
    public static final String STRING_QUEUE_QD = "qd";
    
    public static final String STRING_1_ = "-1";
    
    public static final String STRING_0 = "0";
    
    public static final String STRING_000 = "0.00";
    
    public static final String STRING_000_ = "-0.00";
    
    public static final String STRING_001 = "0.01";
    
    public static final String STRING_003 = "0.03";
    
    public static final String STRING_004 = "0.04";
    
    public static final String STRING_005 = "0.05";
    
    public static final String STRING_006 = "0.06";
    
    public static final String STRING_0015 = "0.015";
    
    public static final String STRING_0127 = "1.27";
    
    public static final String STRING_01 = "01";
    
    public static final String STRING_02 = "02";
    
    public static final String STRING_1 = "1";
    
    public static final String STRING_2 = "2";
    
    public static final String STRING_3 = "3";
    
    public static final String STRING_4 = "4";
    
    public static final String STRING_5 = "5";
    
    public static final String STRING_6 = "6";
    
    public static final String STRING_60 = "60";
    
    public static final String STRING_10 = "10";
    
    public static final String STRING_99_01 = "99.01";
    
    public static final String STRING_100 = "100";
    
    public static final String STRING_910 = "910";
    
    public static final String STRING_0000 = "0000";
    
    public static final String STRING_0001 = "0001";
    
    public static final String STRING_050000 = "050000";
    
    public static final String STRING_E1111 = "^E1111^";
    
    public static final String STRING_000000 = "000000";
    
    public static final String STRING_2000 = "2000";
    
    public static final String STRING_9999 = "9999";
    public static final String STRING_9000 = "9000";
    public static final String STRING_9001 = "9001";
    public static final String STRING_9002 = "9002";
    public static final String STRING_9003 = "9003";
    public static final String STRING_9004 = "9004";
    
    public static final String STRING_40618 = "40618";
    
    public static final String STRING_201014 = "201014";
    
    public static final String STRING_009999 = "009999";
    
    public static final String STRING_1090305010800000000 = "1090305010800000000";
    
    public static final String STRING_1300000 = "1300000";
    
    public static final String STRING_POINT = ".";
    
    public static final String STRING_POINT1 = "、";
    
    public static final String STRING_POINT2 = ",";
    
    public static final String STRING_CHAR = "\\^";
    
    public static final String STRING_PERCENT = "%";
    
    public static final String STRING_CHARSET_1 = "[";
    /**
     * 空格
     */
    public static final String STRING_SPACE = " ";
    
    /**
     * 错误百分号匹配
     */
    public static final String STRING_ERROR_PERCENT = "％";
    
    public static final String STRING_SEMICOLON = ";";
    
    /**
     * 差额征税句号
     */
    public static final String STRING_JH = "。";
    
    public static final String STRING_COLON = ":";
    
    public static final String STRING_UNDERLINE = "_";
    
    public static final String STRING_LINE = "-";
    
    public static final String STRING_SLASH_LINE = "/";
    
    public static final String STRING_ZKH = "zkh_";
    
    public static final String STRING_SHI = "是";
    
    public static final String STRING_FOU = "否";
    
    public static final String STRING_CEZS = "差额征税";
    
    public static final String STRING_CEZS_ = "差额征税：";
    
    public static final String STRING_KSL = "空税率";
    
    public static final String STRING_MS = "免税";
    
    public static final String STRING_BZS = "不征税";
    
    public static final String STRING_CKLS = "出口零税";
    
    public static final String STRING_JYZS = "简易征收";
    
    public static final String STRING_JYZS5 = "按5%简易征收";
    
    public static final String STRING_JYZS3 = "按3%简易征收";
    
    public static final String STRING_JYZS5_1 = "按5%简易征收减按1.5%计征";
    
    public static final String STRING_SHENG = "升";
    
    public static final String STRING_DUN = "吨";
    
    public static final String STRING_LINAG = "辆";
    
    public static final String STRING_HPBZ = "对应正数发票代码";
    
    public static final String STRING_FORMAT_HPBZ = "对应正数发票代码:%s号码:%s";
    
    public static final String STRING_FORMAT_HPBZ1 = "对应正数发票代码:%s 号码:%s";
    
    public static final String STRING_FORMAT_HPBZ2 = "对应正数发票代码:%s 号码:%s差额征税";
    
    public static final String STRING_FORMAT_HPBZ3 = "差额征税。对应正数发票代码:%s号码:%s";
    
    public static final String STRING_HZBZ = "开具红字增值税专用发票信息表编号";
    
    public static final String STRING_FORMAT_HZBZ = "开具红字增值税专用发票信息表编号%s";
    
    public static final String STRING_FORMAT_HZBZ1 = "差额征税。开具红字增值税专用发票信息表编号%s";
    
    public static final String STRING_FORMAT_CEZS = "差额征税：%s。";
    
    public static final String STRING_FORMAT_BW_HZBZ = "红字发票信息表编号%s";
    
    public static final String STRING_FORMAT_BW_HZBZ1 = "红字发票信息表编号%s差额征税";
    
    public static final String STRING_FORMAT_BW_HZBZ2 = "红字发票信息表编号";
    
    public static final String STRING_DSL = "多税率";
    
    public static final String STRING_SYSTEM = "系统";
    
    public static final String STRING_XXGL = "XXGL";
    
    /**
     * 优惠政策标识excel中的描述
     */
    public static final String STRING_YHZCBS_S = "是";
    
    public static final String STRING_YHZCBS_F = "否";
    
    /**
     * 含税标志excel中的描述
     */
    public static final String STRING_HSBZ_HS = "是";
    
    public static final String STRING_HSBZ_BHS = "否";
    
    public static final String STRING_EXCEL_ZKH = "折扣行";
    
    /**
     * 订单合并提示信息标识
     */
    public static final String STRING_ORDER_MERGE_TS = "tsxx";
    
    
    public static final String STRING_GHF_QYLX_QY = "企业";
    public static final String STRING_GHF_QYLX_GR = "个人/其他";
    public static final String STRING_GHF_QYLX_QT = "其它";
    
    
    public static final String STRING_QT = "其他";
    
    public static final String STRING_TAXPAYER_CODE = "taxpayerCode";
    
    public static final String STRING_TAXPAYER_NAME = "taxpayerName";
    
    public static final String STRING_NAME = "name";
    
    public static final String STRING_PAGE_SIZE = "pageSize";
    
    public static final String STRING_CURR_PAGE = "currPage";
    
    public static final String STRING_HB = "hb";
    
    public static final String STRING_FP = "fp";
    
    public static final String STRING_DXHY = "dxyun";
    
    public static final String STRING_FGBW = "fgbw";
    
    public static final String STRING_FGUKEY = "fgUkey";
    
    public static final String STRING_ORDER_BUSINESS = "orderBusiness";
    
    /**
     * 字符串
     * 清单或者渠道
     */
    public static final String STRING_QD = "qd";
    
    
    public static final String STRING_STAR = "*";
    
    public static final String STRING_STAR_PAT = "\\*";
    
    public static final String STRING_STAR2 = "**";
    
    public static final String STRING_CHARSET_GBK = "GBK";
    
    public static final String EMAIL_ADDRESS = "emailAddress";
    
    public static final String INVOICE_ID = "orderInvoiceId";
    
    public static final String ORDERINFO_ID = "orderInfoId";
    
    public static final String FPQQLSH = "fpqqlsh";
    
    public static final String ORDER_ID = "orderId";
    
    public static final String SLD_ID = "sldid";
    
    public static final String SLD_MC = "sldmc";
    
    public static final String KPJH = "kpjh";
    
    public static final String SHLIST = "shList";
    
    public static final String STRING_PUSH_TYPE = "pushType";
    
    public static final String CLIENT_PREFIX = "order-fg";
    
    public static final String EXIST = "exist";
    
    public static final String FPKJ = "fpkj";
    
    public static final String TOKEN = "token";
    
    public static final String FZYY_TOKEN = "fzyy_token";
    
    public static final String DXHY_SSO_SESSION_ID = "cookieId";
    
    public static final String AUTHORIZATION = "Authorization";
    
    public static final String BEARER = "Bearer";
    
    public static final String BASIC = "Basic";
    
    public static final String CELL_NAME = "cellName";
    
    public static final String TAB_CODE = "tabCode";
    
    public static final String STRING_SL = "sl";
    
    public static final String STRING_ERRCODE = "errcode";
    
    public static final String STRING_IS_FLAG = "isFlag";
    
    public static final String STRING_IS_PASS = "isPass";
    
    public static final String STRING_TERMINAL_CODE = "terminalCode";
    
    public static final String STRING_DICTIONARY_FGUPLOAD_URL = "UploadUrl";
    
    public static final String STRING_DICTIONARY_FGOFDDOWNLOAD_URL = "OfdDownloadUrl";
    
    public static final String STRING_OPEN_API_ACCESS_TOKEN = "?access_token=";
    
    public static final String STRING_NULL = "null";
    
    /**
     * C48字典表可编辑状态
     */
    public static final String STRING_DICTIONARY_C48_EDIT_STATUS = "C48EditStatus";
    
    /**
     * A9字典表可编辑状态
     */
    public static final String STRING_DICTIONARY_A9_EDIT_STATUS = "A9EditStatus";
    
    /**
     * 字典表京东渠道id
     */
    public static final String STRING_DICTIONARY_JD_JOS = "JdJosQdId";
    
    /**
     * 方格pdf生成返回结果
     */
    public static final String PDF_GEN_R = "r";
    public static final String PDF_GEN_O = "o";
    
    /**
     * 用户中心项目名称
     */
    public static final String USER_CENTER_URL = "itax-base-api";
    
    /**
     * 业务请求参数
     */
    public static final String REQUEST_PARAM_FPQQLSH = "fpqqlsh";
    
    public static final String REQUEST_PARAM_NSRSBH = "nsrsbh";
    
    public static final String REQUEST_PARAM_TQM = "tqm";
    
    public static final String REQUEST_PARAM_DDH = "ddh";
    
    public static final String REQUEST_PARAM_DDZT = "ddzt";
    
    public static final String REQUEST_PARAM_KPZT = "kpztList";
    
    public static final String REQUEST_PARAM_ORDER_STATUS = "orderStatus";
    
    public static final String REQUEST_PARAM_START_TIME = "startTime";
    
    public static final String REQUEST_PARAM_END_TIME = "endTime";
    
    
    public static final String MSG = "msg";
    
    public static final String INVOICEING_CONTENT = "对应税盘正在开具";
    
    /**
     * pdf
     */
    public static final String STRING_SUFFIX_PDF = ".pdf";
    
    public static final String STRING_SUFFIX_OFD = ".ofd";
    
    public static final String STRING_SUFFIX_PNG = ".png";
    
    public static final String STRING_APPLICATION_PDF = "application/pdf";
    
    public static final String STRING_APPLICATION_OFD = "application/ofd";
    
    public static final String STRING_APPLICATION_PNG = "application/png";
    
    public static final String STRING_APPLICATION_JSON = "application/json";
    
    /**
     *
     */
    public static final String STRING_Y = "Y";
    
    public static final String STRING_N = "N";
    
    /**
     * 时间
     */
    public final static String DATE_FORMAT_DATE = "yyyy-MM-dd";
    public final static String DATE_FORMAT_DATE_Y_M = "yyyy-MM";
    public final static String DATE_FORMAT_DATE_YM = "yyyyMM";
    public final static String DATE_FORMAT_DATE_YMD = "yyyyMMdd";
    public final static String DATE_FORMAT_DATE_YMDHMS = "yyyyMMddHHmmss";
    public final static String DATE_FORMAT_DATE_Y_M_DH_M_S = "yyyy-MM-dd HH:mm:ss";
    public final static String DATE_FORMAT_DATE_Y_M_DH_M = "yyyy-MM-dd HH:mm";
    
    
    /**
     * 详见销货清单
     */
    public final static String XJXHQD = "（详见销货清单）";
    
    /**
     * 详见对应正数发票及清单
     */
    public final static String XJZSXHQD = "详见对应正数发票及清单";
    
    /**
     * 数值类型
     */
    
    public static final int INT_0 = 0;
    
    public static final int INT_1 = 1;
    
    public static final int INT_2 = 2;
    
    public static final int INT_3 = 3;
    
    public static final int INT_4 = 4;
    
    public static final int INT_5 = 5;
    
    public static final int INT_6 = 6;
    
    public static final int INT_8 = 8;
    
    public static final int INT_10 = 10;
    
    public static final int INT_11 = 11;
    
    public static final int INT_12 = 12;
    
    public static final int INT_14 = 14;
    
    public static final int INT_15 = 15;
    
    public static final int INT_16 = 16;
    
    public static final int INT_17 = 17;
    
    public static final int INT_18 = 18;
    
    public static final int INT_19 = 19;
    
    public static final int INT_20 = 20;
    
    public static final int INT_23 = 23;
    
    public static final int INT_24 = 24;
    
    public static final int INT_25 = 25;
    
    public static final int INT_30 = 30;
    
    public static final int INT_32 = 32;
    
    public static final int INT_36 = 36;
    
    public static final int INT_40 = 40;
    
    public static final int INT_50 = 50;
    
    public static final int INT_60 = 60;
    
    public static final int INT_70 = 70;
    
    public static final int INT_80 = 80;
    
    public static final int INT_90 = 90;
    
    public static final int INT_100 = 100;
    
    public static final int INT_150 = 150;
    
    public static final int INT_180 = 180;
    
    public static final int INT_200 = 200;
    
    public static final int INT_201 = 201;
    
    public static final int INT_210 = 210;
    
    public static final int INT_230 = 230;
    
    public static final int INT_300 = 300;
    
    public static final int INT_330 = 330;
    
    public static final int INT_399 = 399;
    
    public static final int INT_401 = 401;
    
    public static final int INT_443 = 443;
    
    public static final int INT_500 = 500;
    
    public static final int INT_600 = 600;
    
    public static final int INT_630 = 630;
    
    public static final int INT_1000 = 1000;
    
    public static final int INT_65536 = 65536;
    
    public static final long LONG_10000 = 10000L;
    
    /**
     * 批次插入数据长度
     */
    public static final int BATCH_INSERT = 1000;
    
    
    /**
     * 批次允许最大开票数据长度
     */
    public static final int PC_MAX_ITEM_LENGTH = 1000;
    
    /**
     * 单张发票允许最大开票数据长度
     */
    public static final int MAX_ITEM_LENGTH = 2000;
    
    /**
     * 专票允许最大开票数据长度
     */
    public static final int SPECIAL_MAX_ITEM_LENGTH = 8;
    
    /**
     * 扣除额商品明细数量
     */
    public static final int KCE_MAX_ITEM_LENGTH = 1;
    
    
    public static final double DOUBLE_PENNY_ZERO = 0.00;
    
    public static final double DOUBLE_PENNY = 0.01;
    
    public static final double DOUBLE_PENNY_3 = 0.03;
    
    public static final double DOUBLE_PENNY_4 = 0.04;
    
    public static final double DOUBLE_PENNY_5 = 0.05;
    
    public static final double DOUBLE_PENNY_SIX = 0.06;
    
    public static final double DOUBLE_PENNY_127 = 1.27;
    
    
}
