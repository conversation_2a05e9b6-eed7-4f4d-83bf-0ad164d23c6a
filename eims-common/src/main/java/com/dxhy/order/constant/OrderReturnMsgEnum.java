package com.dxhy.order.constant;

/**
 * 对外开票接口返回枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum OrderReturnMsgEnum {

    ORDER_RETURN_MSG_ENUM_001("001", "订单头信息传入不能为空"),
    ORDER_RETURN_MSG_ENUM_002("002", "批次信息不能为空"),
    ORDER_RETURN_MSG_ENUM_003("003", "发票信息不能为空"),
    ORDER_RETURN_MSG_ENUM_004("004", "发票明细行不能为空"),
    ORDER_RETURN_MSG_ENUM_005("005", "销货方或购货方识别号不能为空"),
    ORDER_RETURN_MSG_ENUM_006("006", "销货方或购货方订单主体信息中纳税人识别号长度只能大于等于6位,小于等于20位，其他长度不合法"),
    ORDER_RETURN_MSG_ENUM_007("007", "销货方或购货方纳税人识别号需要全部大写"),
    ORDER_RETURN_MSG_ENUM_008("008", "销货方信息填写不全"),
    ORDER_RETURN_MSG_ENUM_009("009", "购买方识别号不能为空"),
    ORDER_RETURN_MSG_ENUM_010("010", "购货方信息填写不全"),
    ORDER_RETURN_MSG_ENUM_011("011", "价税合计不能为空"),
    ORDER_RETURN_MSG_ENUM_012("012", "订单主体信息中价税合计不能为0且保证小数点后两位小数"),
    ORDER_RETURN_MSG_ENUM_013("013", "合计金额不能为空"),
    ORDER_RETURN_MSG_ENUM_014("014", "订单主体信息中合计不含税金额不为0时，小数点位数须为2位小数"),
    ORDER_RETURN_MSG_ENUM_015("015", "合计税额不能为空"),
    ORDER_RETURN_MSG_ENUM_016("016", "订单主体信息中合计税额不为0时，小数点位数须为2位小数"),
    ORDER_RETURN_MSG_ENUM_017("017", "订单明细信息中项目单价必须为正数且小数点后数值不能大于8位"),
    ORDER_RETURN_MSG_ENUM_018("018", "合计税额不能为空"),
    ORDER_RETURN_MSG_ENUM_019("019", "项目名称不能为空"),
    ORDER_RETURN_MSG_ENUM_020("020", "规格型号不能为空"),
    ORDER_RETURN_MSG_ENUM_021("021", "单位不能为空"),
    ORDER_RETURN_MSG_ENUM_022("022", "含税标志不能为空"),
    ORDER_RETURN_MSG_ENUM_023("023", "发票行性质不能为空"),
    ORDER_RETURN_MSG_ENUM_024("024", "折扣行项目名称不能为空"),
    ORDER_RETURN_MSG_ENUM_025("025", "订单号不能为空"),
    ORDER_RETURN_MSG_ENUM_026("026", "订单请求流水号不能为空"),
    ORDER_RETURN_MSG_ENUM_027("027", "批次号不能为空"),
    ORDER_RETURN_MSG_ENUM_0028("028", "销货方或购货方识别号不能包含空格"),
    ORDER_RETURN_MSG_ENUM_0029("029", "发票请求流水号已存在");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static OrderReturnMsgEnum getCodeValue(String key) {
        for (OrderReturnMsgEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    OrderReturnMsgEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
