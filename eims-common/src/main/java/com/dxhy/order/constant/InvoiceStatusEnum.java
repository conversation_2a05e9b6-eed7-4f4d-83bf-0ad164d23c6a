package com.dxhy.order.constant;
/**
 * 发票状态枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum InvoiceStatusEnum {

    /**
     * 0:待开;1:开票中;2:开票成功;3:开票失败
     */
    INVOICE_STATUS_0("0", "待开"),
    INVOICE_STATUS_1("1", "开票中"),
    INVOICE_STATUS_2("2", "开票成功"),
    INVOICE_STATUS_3("3", "开票失败");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static InvoiceStatusEnum getCodeValue(String key) {
        for (InvoiceStatusEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    InvoiceStatusEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
