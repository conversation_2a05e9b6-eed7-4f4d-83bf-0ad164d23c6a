package com.dxhy.order.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * 省份枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
public enum ProvinceEnum {
    
    /**
     * 北京
     */
    PROVINCE_BEIJING("1100", "北京"),
    /**
     * 天津
     */
    PROVINCE_TIANJIN("1200", "天津"),
    /**
     * 河北
     */
    PROVINCE_HEBEI("1300", "河北"),
    /**
     * 山西
     */
    PROVINCE_SHANXI("1400", "山西"),
    /**
     * 内蒙古
     */
    PROVINCE_NEIMENGGU("1500", "内蒙古"),
    /**
     * 辽宁
     */
    PROVINCE_LIAONING("2100", "辽宁"),
    /**
     * 大连
     */
    PROVINCE_DALIAN("2102", "大连"),
    /**
     * 吉林
     */
    PROVINCE_JILIN("2200", "吉林"),
    /**
     * 黑龙江
     */
    PROVINCE_HEILONGJIANG("2300", "黑龙江"),
    /**
     * 上海
     */
    PROVINCE_SHANGHAI("3100", "上海"),
    /**
     * 江苏
     */
    PROVINCE_JIANGSU("3200", "江苏"),
    /**
     * 浙江
     */
    PROVINCE_ZHEJIANG("3300", "浙江"),
    /**
     * 宁波
     */
    PROVINCE_NINGBO("3302", "宁波"),
    /**
     * 安徽
     */
    PROVINCE_ANHUI("3400", "安徽"),
    /**
     * 福建
     */
    PROVINCE_FUJIAN("3500", "福建"),
    /**
     * 厦门
     */
    PROVINCE_XIAMEN("3502", "厦门"),
    /**
     * 江西
     */
    PROVINCE_JIANGXI("3600", "江西"),
    /**
     * 山东
     */
    PROVINCE_SHANDONG("3700", "山东"),
    /**
     * 青岛
     */
    PROVINCE_QINGDAO("3702", "青岛"),
    /**
     * 河南
     */
    PROVINCE_HENAN("4100", "河南"),
    /**
     * 湖北
     */
    PROVINCE_HUBEI("4200", "湖北"),
    /**
     * 湖南
     */
    PROVINCE_HUNAN("4300", "湖南"),
    /**
     * 广东
     */
    PROVINCE_GUANGDONG("4400", "广东"),
    /**
     * 深圳
     */
    PROVINCE_SHENZHEN("4403", "深圳"),
    /**
     * 广西
     */
    PROVINCE_GUANGXI("4500", "广西"),
    /**
     * 海南
     */
    PROVINCE_HAINAN("4600", "海南"),
    /**
     * 重庆
     */
    PROVINCE_CHONGQING("5000", "重庆"),
    /**
     * 四川
     */
    PROVINCE_SICHUAN("5100", "四川"),
    /**
     * 贵州
     */
    PROVINCE_GUIZHOU("5200", "贵州"),
    /**
     * 云南
     */
    PROVINCE_YUNAN("5300", "云南"),
    /**
     * 西藏
     */
    PROVINCE_XIZANG("5400", "西藏"),
    /**
     * 陕西
     */
    PROVINCE_SHANXI1("6100", "陕西"),
    /**
     * 甘肃
     */
    PROVINCE_GANSU("6200", "甘肃"),
    /**
     * 青海
     */
    PROVINCE_QINGHAI("6300", "青海"),
    /**
     * 宁夏
     */
    PROVINCE_NINGXIA("6400", "宁夏"),
    /**
     * 新疆
     */
    PROVINCE_XINJIANG("6500", "新疆");
    
    private final String key;
    private final String value;
    
    ProvinceEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }
    
    
    public String getKey() {
        return this.key;
    }
    
    public String getValue() {
        return this.value;
    }
    
    /**
     * 根据key获取value值
     *
     * @param key
     * @return
     */
    public static String getValue(String key) {
        
        for (ProvinceEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item.getValue();
            }
        }
        return "";
    }
    
    /**
     * 获取所有的key
     *
     * @return
     */
    public static List<String> getKeys() {
        List<String> keysList = new ArrayList<>();
        for (ProvinceEnum item : values()) {
            keysList.add(item.getKey());
        }
        return keysList;
    }
    
}
