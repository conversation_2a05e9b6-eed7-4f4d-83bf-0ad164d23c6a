package com.dxhy.order.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * excel导出 Excel导入的错误客户信息
 * <AUTHOR>
 * @Date 2022/7/13 18:05
 * @Version 1.0
 **/
public enum ExportExcelCustomerInfoEnum {

    /**
     * Excel导出
     * 必须按照顺序存放,否则会异常
     */
    EXCEL_EXPORT_CUSTOMER_KHFLMC("0", "khflmc", "客户分类名称"),
    EXCEL_EXPORT_CUSTOMER_KHMC("1", "khmc", "客户名称"),
    EXCEL_EXPORT_CUSTOMER_NSRSBH("2", "nsrsbh", "统一社会信用代码/纳税人识别号"),
    EXCEL_EXPORT_CUSTOMER_JM("3", "jm", "简码"),
    EXCEL_EXPORT_CUSTOMER_DZ("4", "dz", "地址"),
    EXCEL_EXPORT_CUSTOMER_DH("5", "dh", "电话"),
    EXCEL_EXPORT_CUSTOMER_KHHMC("6", "khhmc", "开户行名称"),
    EXCEL_EXPORT_CUSTOMER_YHZH("7", "yhzh", "银行账号"),
    EXCEL_EXPORT_CUSTOMER_LXYX("8", "lxyx", "联系邮箱"),
    EXCEL_EXPORT_CUSTOMER_SFMRDZ("9", "sfmrdz", "是否默认地址");

    /**
     * key
     */
    private final String key;

    /**
     * 值
     */
    private final String value;


    /**
     * 表格头名称
     */
    private final String cellName;


    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getCellName() {
        return cellName;
    }

    ExportExcelCustomerInfoEnum(String key, String value, String cellName) {
        this.key = key;
        this.value = value;
        this.cellName = cellName;
    }

    public static ExportExcelCustomerInfoEnum getCodeValue(String key) {

        for (ExportExcelCustomerInfoEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }

    public static List<String> getValues() {

        List<String> resultList = new ArrayList<>();
        for (ExportExcelCustomerInfoEnum item : values()) {
            resultList.add(item.getValue());
        }
        return resultList;
    }
}
