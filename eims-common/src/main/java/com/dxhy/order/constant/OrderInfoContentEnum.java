package com.dxhy.order.constant;

/**
 * 订单接口枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum OrderInfoContentEnum {

    /**
     * 统配适应
     */

    SUCCESS("0000", "请求成功"),
    READY_OPEN_KPR_ERROR("9623", "订单开票人不能为空"),
    READY_OPEN_XFXX_ERROR("9624", "销方信息不能为空"),
    COMMON_ERROR("9999", "企业未配置税控设备"),
    TAXCODE_ISNULL("20001", "当前请求的税号为空！"),
    INVOICE_PARAM_ERROR("10005", "请求参数为空！"),
    CHECK_ISS7PRI_107164("107164", "纳税人识别号不能包含空格"),


    ERROR("9999", "系统异常"),
    RECEIVE_FAILD("9999", "订单处理失败"),
    ISEX("3333", "该发票不存在"),
    BACK("3334", "存在未合并发票，不允许退回"),
    NORECORD("3335", "退回失败,没有操作记录"),
    MERGE("3336", "订单发票合并失败"),
    MERGE_DDLY("3337", "订单来源不一致"),
    MERGE_GHFXX("3338", "购方信息不一致"),
    MERGE_XHFXX("3339", "销方信息不一致"),
    MERGE_FPLX("3340", "发票类型不一致"),
    ORDER_TEMPLETE("3341", "表格填写不能为空"),
    ORDER_XHFXX("3342", "销方信息传入不全"),
    ORDER_ISSUE("3343", "勾选订单不能为空"),
    ORDER_IMPORT("3344", "订单信息传入不能为空"),
    ORDER_HSBZ("3345", "含税标志不一致"),
    MERGE_SUCCESS("3346", "订单发票合并成功"),
    ISSUE_INVOICE("3347", "底层开具接口调用异常"),
    ISSUE_ORDER_INVOICE("3348", "订单开票底层开具接口调用异常"),
    ORDER_INVOICE_CHECK("3349", "请勿连续点击开票按钮"),
    ORDER_INVOICE_EXIST("3350", "存在开票中的发票,请勿重复操作"),
    MERGE_ADDCONDITION("3351", "含有附加信息,不允许合并"),
    ORDER_IMPORT_REPE("3352", "订单已存在不允许导入"),
    INVOICE_QDP("3353", "全电普票无需申请红字信息表"),
    MERGE_KPZT("3354", "存在开票中或者开票完成状态,不允许合并"),
    BACK_KPZT("3355", "存在开票中或者开票完成状态,不允许退回"),
    MERGE_KPLX("3356", "存在红票票种,不允许合并"),
    MERGE_JEERROR("335601", "数据错误，负数行金额大于正数行,不允许合并"),
    ORDER_INVOICE_SUC("3357", "存在开票成功发票,请去查询统计查看发票"),
    ORDER_INVOICE_SPFZRRBS("3358", "受票方自然人标识不一致"),
    ORDER_INVOICE_BZ("3359", "备注不一致"),
    ORDER_INVOICE_SJ("3360", "交付手机号不一致"),
    ORDER_INVOICE_YX("3361", "交付邮箱地址不一致"),
    ORDER_INVOICE_JBRXM("3362", "购买方经办人姓名不一致"),
    ORDER_INVOICE_JBRLX("3363", "购买方经办人类型不一致"),
    ORDER_INVOICE_JBRZJHM("3364", "购买方经办人证件号码不一致"),


    /**
     * 导出Excel文件名称
     */
    EXPORT_EXCEL_INVOICERECORD("0", "开票记录");


    /**
     * 特殊符号
     */

    public static final String STRING_SLASH_LINE = "/";


    /**
     * 常量定值
     */
    public static final String CHARSET_UTF8 = "UTF-8";


    /**
     * 日期格式
     */
    public final static String DATE_FORMAT_DATE_Y = "yyyy";
    public final static String DATE_FORMAT_DATE = "yyyy-MM-dd";
    public final static String DATE_FORMAT_DATE_Y_M = "yyyy-MM";
    public final static String DATE_FORMAT_DATE_YM = "yyyyMM";
    public final static String DATE_FORMAT_DATE_YMD = "yyyyMMdd";
    public final static String DATE_FORMAT_DATE_YMDHMS = "yyyyMMddHHmmss";
    public final static String DATE_FORMAT_DATE_Y_M_DH_M_S = "yyyy-MM-dd HH:mm:ss";
    public final static String DATE_FORMAT_DATE_Y_M_DH_M = "yyyy-MM-dd HH:mm";
    public final static String DATE_FORMAT_DATE_CHINESE = "yyyy年MM月dd日";

    /**
     * 数值类型
     */
    public static final int INT_0 = 0;

    public static final int INT_1 = 1;

    public static final int INT_2 = 2;

    public static final int INT_3 = 3;

    public static final int INT_4 = 4;

    public static final int INT_5 = 5;

    public static final int INT_6 = 6;

    public static final int INT_8 = 8;

    public static final int INT_10 = 10;

    public static final int INT_11 = 11;

    public static final int INT_12 = 12;

    public static final int INT_14 = 14;

    public static final int INT_15 = 15;

    public static final int INT_20 = 20;


    /**
     * key值
     */
    private final String key;

    /**
     * 对应Message
     */
    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static OrderInfoContentEnum getCodeValue(String key) {
        for (OrderInfoContentEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    OrderInfoContentEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

    /**
     * 非空判断
     */
    private boolean checkNull = false;

    /**
     * 长度判断
     */
    private boolean checkLength = false;

    /**
     * 最小长度
     */
    private int minLength;

    /**
     * 最大长度
     */
    private int maxLength;

    /**
     * 新税控最大长度
     */
    private int maxLengthNewTax;

    public boolean getCheckNull() {
        return this.checkNull;
    }

    public boolean getCheckLength() {
        return this.checkLength;
    }

    public int getMinLength() {
        return minLength;
    }

    public int getMaxLength() {
        return maxLength;
    }

    public int getMaxLengthNewTax() {
        return maxLengthNewTax;
    }
}
