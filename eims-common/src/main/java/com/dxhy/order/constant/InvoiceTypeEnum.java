package com.dxhy.order.constant;
/**
 * 订单接口枚举类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-23 18:18
 */
public enum InvoiceTypeEnum {

    /**
     * 统一发票类型代码
     * 增值税专用发票： 004
     * 机动车销售统一发票：005
     * 二手车销售统一发票：006
     * 增值税普通发票： 007
     * 增值税电子普通发票： 026
     * 增值税电子专用发票：028
     */
    ORDER_INVOICE_TYPE_001("001", "数电专用发票"),
    ORDER_INVOICE_TYPE_002("002", "数电普通发票"),
    ORDER_INVOICE_TYPE_003("003", "数电机动车销售统一发票"),
    ORDER_INVOICE_TYPE_004("004", "增值税专用发票"),
    ORDER_INVOICE_TYPE_005("005", "机动车销售统一发票"),
    ORDER_INVOICE_TYPE_006("006", "二手车销售统一发票"),
    ORDER_INVOICE_TYPE_007("007", "增值税普通发票"),
    ORDER_INVOICE_TYPE_026("026", "增值税电子普通发票"),
    ORDER_INVOICE_TYPE_028("028", "增值税电子专用发票"),
    ORDER_INVOICE_TYPE_104("104", "数电二手车销售统一发票"),
    ORDER_INVOICE_TYPE_085("085", "数电纸票专用发票"),
    ORDER_INVOICE_TYPE_086("086", "数电纸票普通发票"),
    ORDER_INVOICE_TYPE_087("087", "数电纸票机动车销售统一发票"),
    ORDER_INVOICE_TYPE_088("088", "数电纸票二手车销售统一发票");

    private final String key;

    private final String message;


    public String getKey() {
        return this.key;
    }

    public String getMessage() {
        return this.message;
    }

    public static InvoiceTypeEnum getCodeValue(String key) {
        for (InvoiceTypeEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        return null;
    }


    InvoiceTypeEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

}
