package com.dxhy.order.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

public interface InvoiceInfoReason {

    @Getter
    @AllArgsConstructor
    enum GiveUpReasonType implements CodeEnum {
        REASON_1("1", "前期已开具发票，发生销售折让、中止或者退回等情形需要开具红字发票，或者开票有误需要重新开具"),
        REASON_2("2", "因为实际经营业务需要，放弃享受减按1%征收率征收增值税政策。");
        String code;
        String desc;
        public static GiveUpReasonType getTypeByCode(String code) {
            for (GiveUpReasonType type : GiveUpReasonType.values()) {
                if (Objects.equals(code, type.getCode())) {
                    return type;
                }
            }
            return null;
        }

        public static GiveUpReasonType getCodeByType(String type) {
            for (GiveUpReasonType reasonType : GiveUpReasonType.values()) {
                if (Objects.equals(type, reasonType.getDesc())) {
                    return reasonType;
                }
            }
            return null;
        }
    }
}
