package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.RedInvoiceConfirmEntity;
import com.dxhy.order.modules.entity.RedInvoiceConfirmSldh;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.pojo.RedConfirmHandle;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;

/**
 * 附加要素表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 11:39:24
 */
public interface RedInvoiceConfirmService extends IService<RedInvoiceConfirmEntity> {

    PageUtils queryPage(RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    R redInvoiceConfirmChoose(IdDTO idDTO);

    R redInvoiceConfirmSubmit(OrderInvoiceInfoEntity orderInvoiceInfoEntity) throws Exception;

    R redInvoiceConfirmIssueQuery(String id);

    R redInvoiceConfirmIssue(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    PageUtils selectRedInvoiceConfirm(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R redInvoiceConfirmDetail(String id);

    R redInvoiceConfirmView(String id);

    R redInvoiceConfirmBackOut(String yqdfpdm);

    PageUtils selectRedInvoiceRecord(RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    R selectRedInvoiceRecordView(String id);

    R redInvoiceConfirmStatistics();

    R getHadInvoiceInfos(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R getHadInvoiceDetail(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R getReceiveInvoiceInfos(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R getReceiveInvoiceDetail(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R redInvoiceIssue(RedInvoiceConfirmSldh redInvoiceConfirmSldh);

    PageUtils listRedInvoiceConfirm(RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    R hzqrxxUpdate(RedConfirmHandle redConfirmHandle);

    PageUtils getListRedInvoiceConfirm(RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    R getInvoiceInfosAndItemsResult(String param);

    R pullRedInvoiceConfirmSldhResult();

    R queryHzqrxxResult(String param);

    R pullRedInvoiceResultInfoResult();

    R queryHzqrxxStatusResult();

    R getRedInvoiceConfirmInfo(String id);

    R queryHzqrxxOutResult(String param);

    R getComfirmSldStatusResult();

    R getComfirmJgcxUUidResult();

    R getOneYearInvoiceInfosAndItems(String nsrsbh, String startTime, String endTime);

    R retryRedInvoiceConfirmSldh(String id);

}

