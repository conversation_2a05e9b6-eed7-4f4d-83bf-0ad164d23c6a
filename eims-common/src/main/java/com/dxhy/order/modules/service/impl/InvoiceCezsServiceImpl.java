package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceCezsDao;
import com.dxhy.order.modules.entity.InvoiceCezsEntity;
import com.dxhy.order.modules.service.InvoiceCezsService;
import com.dxhy.order.utils.DistributedKeyMaker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 差额征税的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceCezsService")
@Slf4j
@RefreshScope
public class InvoiceCezsServiceImpl extends ServiceImpl<InvoiceCezsDao, InvoiceCezsEntity>
        implements InvoiceCezsService {

    @Override
    public void saveBatch(List<InvoiceCezsEntity> list,String invoiceId) {
        list.forEach(cezsEntity -> {
            cezsEntity.setId(DistributedKeyMaker.generateShotKey());
            cezsEntity.setOrderInvoiceInfoId(invoiceId);
        });
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId) {
        LambdaUpdateWrapper<InvoiceCezsEntity> cezsWrapper = Wrappers.lambdaUpdate();
        cezsWrapper.set(InvoiceCezsEntity::getIsDelete, "1");
        cezsWrapper.eq(InvoiceCezsEntity::getOrderInvoiceInfoId, invoiceId);
        this.update(cezsWrapper);
    }
}




