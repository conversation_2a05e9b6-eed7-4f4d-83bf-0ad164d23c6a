package com.dxhy.order.modules.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrderInvoiceImportReqEntity {

    @JsonProperty("XHFMC")
    private String xhfMc;
    @JsonProperty("XHFSBH")
    private String xhfNsrsbh;
    @JsonProperty("XHFDZ")
    private String xhfDz;
    @JsonProperty("XHFDH")
    private String xhfDh;
    @JsonProperty("XHFYH")
    private String xhfYh;
    @JsonProperty("XHFZH")
    private String xhfZh;
    List<OrderInvoiceImportExcelEntity> orderInvoiceImportExcelEntities;
}
