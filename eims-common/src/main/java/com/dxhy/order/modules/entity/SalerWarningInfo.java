package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票预警表
 * @TableName saler_warning
 */
@TableName("saler_warning")
@Data
public class SalerWarningInfo extends BasePage implements Serializable {

    /**
     * 预警id
     */
    private String id;

    /**
     * 销货方税号
     */
    private String xhfNsrsbh;

    /**
     * 预警邮箱（多个邮箱以逗号分隔）
     */
    private String warnEmails;

    /**
     * 提醒手机号码（多个手机以逗号分隔）
     */
    private String warnPhones;

    /**
     * 预警开关（0,关闭；1，开启）
     */
    private String warnFlag;

    /**
     * 月度金额上限
     */
    private String monthUpper;

    /**
     * 季度金额上限
     */
    private String quarterUpper;

    /**
     * 年度金额上限
     */
    private String yearUpper;

    /**
     * 客户信息是否自动保存
     */
    private String saveInfo;

    /**
     * 冲红是否带入原蓝票备注
     */
    private String autoBz;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;



  }