package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.RedInvoiceConfirmEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 附加要素表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 11:39:24
 */

@Mapper
public interface RedInvoiceConfirmDao extends BaseMapper<RedInvoiceConfirmEntity> {
    List<RedInvoiceConfirmEntity> selectList(Page page, @Param("redInvoiceConfirmEntity") RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    RedInvoiceConfirmEntity selectRedConfirmDaoByHztzdbh(@Param("hzxxbbh") String hzxxbbh);

    List<RedInvoiceConfirmEntity> selectRedConfirmDaoListByHztzdbh(@Param("hzxxbbh") String hzxxbbh);

    List<RedInvoiceConfirmEntity> taxGenerateRedTableResult();

    List<RedInvoiceConfirmEntity> selectRedInvoiceRecord(Page page, @Param("redInvoiceConfirmEntity") RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    List<RedInvoiceConfirmEntity> selectRedInvoiceConfirmEntityList(@Param("thisMonthFirstTime") String thisMonthFirstTime, @Param("thisMonthEndTime") String thisMonthEndTime);

    List<RedInvoiceConfirmEntity> selectAll();

    List<RedInvoiceConfirmEntity> selectRedInsvoiceConfirmSlzt();

    RedInvoiceConfirmEntity selectRedConfirmDaoByQdfphm(@Param("qdfphm") String qdfphm);

    List<RedInvoiceConfirmEntity> selectRedConfirmIsNotFailByQdfphm(@Param("qdfphm") String qdfphm);

    RedInvoiceConfirmEntity selectRedConfirmDaoOneByQdfphm(@Param("qdfphm") String qdfphm, @Param("gxsf") String gxsf);

    List<RedInvoiceConfirmEntity> selectRedConfirmsDaoByQdfphm(@Param("qdfphm") String qdfphm);

    List<RedInvoiceConfirmEntity> selectRedConfirmsDaoByQdfphmSpe(@Param("qdfphm") String qdfphm);

    List<RedInvoiceConfirmEntity> selectByQdfphm(@Param("qdfphm") String qdfphm);

    List<RedInvoiceConfirmEntity> selectRedInsvoiceConfirmKjzt();

    List<RedInvoiceConfirmEntity> listRedInvoiceConfirm(Page page, @Param("redInvoiceConfirmEntity") RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    List<RedInvoiceConfirmEntity> getListRedInvoiceConfirm(Page page, @Param("redInvoiceConfirmEntity") RedInvoiceConfirmEntity redInvoiceConfirmEntity);

    List<RedInvoiceConfirmEntity> queryHzqrxxStatus();

    RedInvoiceConfirmEntity queryHzqrxxStatusByUuid(@Param("uuid") String uuid);

    List<RedInvoiceConfirmEntity> queryHzqrxxStatusByUuidAndNsrsbh(@Param("uuid") String uuid, @Param("nsrsbh") String nsrsbh);

    List<RedInvoiceConfirmEntity> getComfirmJgcxUUid();

    List<RedInvoiceConfirmEntity> getComfirmSldStatus();

    RedInvoiceConfirmEntity getComfirmJgcxUUidByHzqrdbh(@Param("hztzdbh") String hztzdbh);

    List<RedInvoiceConfirmEntity> selectRedInvoiceConfirmNsrsbh();

    RedInvoiceConfirmEntity selectBySldh(@Param("sldh") String sldh, @Param("nsrsbh") String nsrsbh);

    List<RedInvoiceConfirmEntity> selectListBySldh(@Param("sldh") String sldh);

    RedInvoiceConfirmEntity selectByKpjgsldh(@Param("kpjgsldh") String kpjgsldh);

    RedInvoiceConfirmEntity selectRedConfirmDaoByQrdbh(@Param("qrdbh") String hzfpxxqrdbh, @Param("fpfqr") String fpfqr);

    List<RedInvoiceConfirmEntity> queryHzqrxxStatusListByUuid(@Param("uuid") String uuid);

    List<RedInvoiceConfirmEntity> queryHzqrxxStatusListByHzqrdbh(@Param("hztzdbh") String hztzdbh);

    int updateKjztByHzqrdbh(@Param("hztzdbh") String hztzdbh, @Param("qdfphm") String qdfphm);

    int updateKjzt1ByHzqrdbh(@Param("hztzdbh") String hztzdbh, @Param("ztdm") String ztdmm, @Param("kpjgsldh") String kpjgsldh);

}
