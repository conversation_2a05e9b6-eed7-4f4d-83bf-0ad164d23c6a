package com.dxhy.order.modules.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dxhy.order.config.OpenApiConfig;
import com.dxhy.order.constant.*;
import com.dxhy.order.exception.OrderSeparationException;
import com.dxhy.order.exception.OrderSplitException;
import com.dxhy.order.model.OrderSplitConfig;
import com.dxhy.order.modules.dao.OrderInvoiceInfoDao;
import com.dxhy.order.modules.dao.OrderInvoiceItemDao;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.pojo.dto.SplitByItemReq;
import com.dxhy.order.modules.service.OrderSplitService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.pojo.SplitInvoiceInfo;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderSplitServiceImpl implements OrderSplitService {
    private static final String LOGGER_MSG = "(订单拆分业务类)";
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#0000");
    private static final String CF = "cf";
    @Autowired
    OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    OrderInvoiceItemDao orderInvoiceItemDao;
    @Autowired
    OpenApiConfig orderConfig;
    @Resource
    private SsoUtil ssoUtil;

    /**
     * 按明细行拆分并保存
     * @param splitInvoiceInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R splitByInvoiceItem(SplitInvoiceInfo splitInvoiceInfo) {
        log.info("按明细行拆分 入参: {}", JsonUtils.getInstance().toJsonString(splitInvoiceInfo));

        String invoiceId = splitInvoiceInfo.getInvoiceList().get(0).getItemInfos().get(0).getOrderInvoiceId();
        OrderInvoiceInfoEntity oldOrderInvoiceInfo = orderInvoiceInfoDao.selectById(invoiceId);
        //  orderInvoiceInfoEntities  有且只有一条才可以   第一条数据为元数据
        for (SplitByItemReq splitByItemReq : splitInvoiceInfo.getInvoiceList()) {
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
            BeanUtils.copyProperties(oldOrderInvoiceInfo, orderInvoiceInfoEntity);
            String orderId = DistributedKeyMaker.generateShotKey();
            String fpqqlsh = DistributedKeyMaker.generateShotKey();
            String pch = DistributedKeyMaker.generateShotKey();
            orderInvoiceInfoEntity.setIsDelete("0");
            orderInvoiceInfoEntity.setDdh(splitByItemReq.getDdh());
            orderInvoiceInfoEntity.setId(orderId);
            orderInvoiceInfoEntity.setFpqqlsh(fpqqlsh);
            orderInvoiceInfoEntity.setPch(pch);
            orderInvoiceInfoEntity.setYddid(invoiceId);
            orderInvoiceInfoEntity.setDdzt("4");
            if (!StringUtils.isEmpty(splitByItemReq.getYysdh())) {
                orderInvoiceInfoEntity.setYsdh(splitByItemReq.getYsdh());
                orderInvoiceInfoEntity.setYysdh(splitByItemReq.getYysdh());
            }
            if (!StringUtils.isEmpty(splitByItemReq.getYddh())) {
                orderInvoiceInfoEntity.setYddh(splitByItemReq.getYddh());
            }
            orderInvoiceInfoEntity.setCreateTime(new Date());
            orderInvoiceInfoEntity.setDdscrq(new Date());
            BigDecimal je = new BigDecimal(0.00);
            BigDecimal se = new BigDecimal(0.00);
            String flag = "N";
            for (OrderInvoiceItemEntity orderInvoiceItemEntity : splitByItemReq.getItemInfos()) {
                orderInvoiceItemEntity.setOrderInvoiceId(orderId);
                orderInvoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                je = je.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                if (!"***".equals(orderInvoiceItemEntity.getSe())) {
                    se = se.add(new BigDecimal(orderInvoiceItemEntity.getSe()));
                } else {
                    orderInvoiceItemEntity.setSe("0.00");
                }
                if ("0.03".equals(orderInvoiceItemEntity.getSl()) || "3%".equals(orderInvoiceItemEntity.getSl())) {
                    log.info("按明细拆分 订单号:{} 含有3%", splitByItemReq.getDdh());
                    flag = "Y";
                }
                int i = orderInvoiceItemDao.insert(orderInvoiceItemEntity);
                if (i == 0) {
                    log.error("按明细拆分 orderInvoiceId:{} orderInvoiceItemId:{}保存明细行失败", orderId, orderInvoiceItemEntity.getId());
                    throw new RuntimeException("按明细拆分 保存明细行失败");
                }
            }

            if ("N".equals(flag)) {
                log.info("按明细拆分 订单号:{} 不含3%", splitByItemReq.getDdh());
                orderInvoiceInfoEntity.setGiveUpReason("");
            } else {
                if (StringUtils.isEmpty(orderInvoiceInfoEntity.getGiveUpReason())) {
                    log.info("按明细拆分 订单号:{} 含有3%但原因为空", splitByItemReq.getDdh());
                    orderInvoiceInfoEntity.setGiveUpReason("2");
                }
            }
            //计算价税合计 hsbz 0不含税   1含税
            if ("0".equals(splitByItemReq.getHsbz())) {
                orderInvoiceInfoEntity.setHjbhsje(je.toPlainString());
                orderInvoiceInfoEntity.setKpse(new DecimalFormat("0.00").format(se));
                orderInvoiceInfoEntity.setJshj(je.add(se).toPlainString());
            } else if ("1".equals(splitByItemReq.getHsbz())) {
                orderInvoiceInfoEntity.setKpse(new DecimalFormat("0.00").format(se));
                orderInvoiceInfoEntity.setJshj(je.toPlainString());
                orderInvoiceInfoEntity.setKpse(new DecimalFormat("0.00").format(se));
                orderInvoiceInfoEntity.setHjbhsje(je.subtract(se).toPlainString());
            } else {
                log.error("按明细拆分 订单号:{} 对应的含税标志不合法", splitByItemReq.getDdh());
                throw new RuntimeException("按明细拆分 含税标志不合法");
            }
            int k = orderInvoiceInfoDao.insert(orderInvoiceInfoEntity);
            if (k == 0) {
                log.error("按明细拆分 orderInvoiceId:{} 保存订单失败", orderId);
                throw new RuntimeException("按明细拆分 保存订单失败");
            }
        }
        oldOrderInvoiceInfo.setIsDelete("1");
        orderInvoiceInfoDao.updateById(oldOrderInvoiceInfo);
        return R.ok();
    }

    /**
     * 订单拆分后还原
     * @param orderInvoiceId
     * @return
     */
    @Override
    public R splitBack(String orderInvoiceId) {
        log.info("按订单拆分后,退回至原订单 入参: {}", orderInvoiceId);
        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectById(orderInvoiceId);
        QueryWrapper queryWrapper = new QueryWrapper();
        String yddid = orderInvoiceInfoEntity.getYddid();
        queryWrapper.eq("yddid", yddid);
        List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectList(queryWrapper);

        String msg = "";
        for (OrderInvoiceInfoEntity invoiceInfoEntity : orderInvoiceInfoEntities) {
            //如果不是待开票的状态提示不能退回
            if (!"0".equals(invoiceInfoEntity.getKpzt())) {
                msg = msg + invoiceInfoEntity.getDdh() + " ";
            }
        }
        if (StringUtils.isEmpty(msg)) {
            //可以退回
            for (OrderInvoiceInfoEntity invoiceInfoEntity : orderInvoiceInfoEntities) {
                //已拆分订单设置为无效
                invoiceInfoEntity.setIsDelete("1");
                orderInvoiceInfoDao.updateById(invoiceInfoEntity);
            }
            //原订单设置为有效
            OrderInvoiceInfoEntity invoiceInfoEntityOld = new OrderInvoiceInfoEntity();
            invoiceInfoEntityOld.setId(orderInvoiceInfoEntities.get(0).getYddid());
            invoiceInfoEntityOld.setIsDelete("0");
            orderInvoiceInfoDao.updateById(invoiceInfoEntityOld);
            return R.ok();
        } else {
            return R.error("订单" + msg + "的状态为非待开票状态，请检查订单发票状态！");
        }
    }

    /**
     * 按金额/数量拆分订单
     * @param orderId 原订单id
     * @param shList  税号集合
     * @param orderSplitConfig  本次页面配置
     * @return
     */
    @Override
    public List<OrderInvoiceInfoEntity> splitOrder(String orderId, List<String> shList, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        //获取原始订单信息
        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectById(orderId);
        //获取原始订单明细信息
        List<OrderInvoiceItemEntity> orderInvoiceItemEntityList = orderInvoiceItemDao.selectItemListById(orderId);
        //设置明细信息
        orderInvoiceInfoEntity.setItemEntityList(orderInvoiceItemEntityList);
        if (ObjectUtil.isNotNull(orderInvoiceInfoEntity)) {
            if (OrderStatusEnum.ORDER_STATUS_ENUM_3.getKey().equals(orderInvoiceInfoEntity.getDdzt()) || "1".equals(orderInvoiceInfoEntity.getIsDelete())) {
                log.error("{}当前订单为无效状态,请查询后再进行操作!流水号为:{}", LOGGER_MSG, orderInvoiceInfoEntity.getFpqqlsh());
                throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ERROR2.getKey(), OrderSplitErrorMessageEnum.ORDER_SPLIT_ERROR2.getValue());
            }
        } else {
            log.error("{}当前订单不存在,请确认后再进行操作!订单id为:{}", LOGGER_MSG, orderId);
            throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_NULL_ERROR.getKey(), OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_NULL_ERROR.getValue());
        }

        //按金额拆分 添加校验
        List<String> jeList = orderSplitConfig.getJeList();
        if (StringUtils.isNotBlank(orderSplitConfig.getHsbz()) && ObjectUtil.isNotEmpty(jeList)) {
            for (String je : jeList) {
                //不含税
                if (OrderInfoEnum.HSBZ_0.getKey().equals(orderSplitConfig.getHsbz())) {
                    if (DecimalCalculateUtil.stringCompare(je, orderInvoiceInfoEntity.getHjbhsje()) > 0) {
                        log.error("{}当前订单录入的金额大于原订单的合计不含税金额，不允许拆分!订单id为:{}", LOGGER_MSG, orderId);
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_ERROR.getKey(), OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_ERROR.getValue());
                    }
                } else {
                    if (DecimalCalculateUtil.stringCompare(je, orderInvoiceInfoEntity.getJshj()) > 0) {
                        log.error("{}当前订单录入的拆分总金额大于原订单的价税合计金额，不允许拆分!订单id为:{}", LOGGER_MSG, orderId);
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_ERROR1.getKey(), OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_ERROR1.getValue());
                    }
                }
            }
        }

        //按数量拆分 添加校验
        OrderInvoiceItemEntity orderItemInfo = orderInvoiceItemEntityList.get(0);
        List<String> slList = orderSplitConfig.getSlList();
        if (CollUtil.isNotEmpty(slList)) {
            for (String sl : slList) {
                if (DecimalCalculateUtil.stringCompare(sl, orderItemInfo.getXmsl()) > 0) {
                    log.error("{}当前订单录入的拆分数量大于原订单的明细数量!，不允许拆分!订单id为:{}", LOGGER_MSG, orderId);
                    throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_OVER_MXSL_ERROR.getKey(), OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_OVER_MXSL_ERROR.getValue());
                }
            }
        }

        //判断是否为等金额、等数量拆分
        if (StringUtils.equals(ConfigureConstant.STRING_1, orderSplitConfig.getIsEquivalent())) {
            if (Objects.nonNull(orderInvoiceInfoEntity) && CollUtil.isNotEmpty(orderInvoiceItemEntityList)) {
                //等金额拆分，重新计算拆分金额
                if (StringUtils.equals(OrderSplitEnum.ORDER_SPLIT_TYPE_2.getKey(), orderSplitConfig.getSplitType())) {
                    List<String> jeListNew = new ArrayList<>();
                    if (StringUtils.isNotBlank(orderSplitConfig.getHsbz()) && ObjectUtil.isNotEmpty(jeList)) {
                        String equivalentJe = jeList.get(0);
                        String je;
                        //不含税
                        if (OrderInfoEnum.HSBZ_0.getKey().equals(orderSplitConfig.getHsbz())) {
                            je = orderInvoiceInfoEntity.getHjbhsje();
                        } else {
                            je = orderInvoiceInfoEntity.getJshj();
                        }
                        BigDecimal equivalentCount = new BigDecimal(je).divide(new BigDecimal(equivalentJe), 0, RoundingMode.DOWN);
                        //等金额拆分后订单条数
                        int splitAfterOrderSize = equivalentCount.intValue();
                        //剩余的拆分金额
                        BigDecimal residueJe = new BigDecimal(je).subtract(new BigDecimal(equivalentJe).multiply(equivalentCount));
                        //如果等数量拆分无法整除，将余数添加到拆分数量集合中，拆分后订单条数 +1
                        if (residueJe.compareTo(BigDecimal.ZERO) > 0) {
                            jeListNew.add(residueJe.toPlainString());
                            splitAfterOrderSize = splitAfterOrderSize + 1;
                        }
                        int countUpperLimit;
                        if (Objects.nonNull(orderConfig.getCountUpperLimit())) {
                            countUpperLimit = orderConfig.getCountUpperLimit();
                        } else {
                            countUpperLimit = 200;
                        }
                        if (splitAfterOrderSize > countUpperLimit) {
                            String errorMsg = "等金额拆分最大支持拆分为" + countUpperLimit + "个子订单，当前录入的拆分金额过小，请修改拆分金额后再进行拆分。";
                            log.error("拆分参数校验失败：{}", errorMsg);
                            throw new OrderSplitException(ConfigureConstant.STRING_9999, errorMsg);
                        }
                        for (int i = 0; i < equivalentCount.intValue(); i++) {
                            jeListNew.add(equivalentJe);
                        }
                        orderSplitConfig.setJeList(jeListNew);
                    }
                }
                //等数量拆分，重新计算拆分数量
                if (StringUtils.equals(OrderSplitEnum.ORDER_SPLIT_TYPE_3.getKey(), orderSplitConfig.getSplitType())) {
                    //总数量
                    String xmsl = orderItemInfo.getXmsl();
                    //计算后的拆分数量
                    List<String> slListNew = new ArrayList<>();
                    if (CollUtil.isNotEmpty(slList)) {
                        String equivalentSl = slList.get(0);
                        BigDecimal equivalentCount = new BigDecimal(xmsl).divide(new BigDecimal(equivalentSl), 0, RoundingMode.DOWN);
                        //等数量拆分后订单条数
                        int splitAfterOrderSize = equivalentCount.intValue();
                        //剩余的拆分数量
                        BigDecimal residueCount = new BigDecimal(xmsl).subtract(new BigDecimal(equivalentSl).multiply(equivalentCount));
                        //如果等数量拆分无法整除，将余数添加到拆分数量集合中，拆分后订单条数 +1
                        if (residueCount.compareTo(BigDecimal.ZERO) > 0) {
                            slListNew.add(residueCount.toPlainString());
                            splitAfterOrderSize = splitAfterOrderSize + 1;
                        }

                        //校验按数量拆分后的条数，不能大于配置文件阈值
                        int countUpperLimit;
                        if (Objects.nonNull(orderConfig.getCountUpperLimit())) {
                            countUpperLimit = orderConfig.getCountUpperLimit();
                        } else {
                            countUpperLimit = 200;
                        }
                        if (splitAfterOrderSize > countUpperLimit) {
                            String errorMsg = "等数量拆分最大支持拆分为" + countUpperLimit + "个子订单，当前录入的拆分数量过小，请修改拆分数量后再进行拆分。";
                            log.error("拆分参数校验失败：{}", errorMsg);
                            throw new OrderSplitException(ConfigureConstant.STRING_9999, errorMsg);
                        }
                        for (int i = 0; i < equivalentCount.intValue(); i++) {
                            slListNew.add(0, equivalentSl);
                        }
                        orderSplitConfig.setSlList(slListNew);
                    }
                }
            }
        }

        List<OrderInvoiceInfoEntity> orderSplit = orderSplit(orderInvoiceInfoEntity, orderSplitConfig);
        //重新设置拆分后的订单号
        int i = 1;
        for (OrderInvoiceInfoEntity com : orderSplit) {
            //设置原订单信息
            com.setYysdh(com.getYsdh());
            com.setYddh(com.getDdh());
            com.setYddid(com.getId());
            //设置新的订单信息
            com.setId(DistributedKeyMaker.generateShotKey());
            String cfDdh = com.getDdh() + CF + DECIMAL_FORMAT.format(i);
            String cfYsdh = com.getYsdh() + CF + DECIMAL_FORMAT.format(i);
            String cfPch = com.getPch() + CF + DECIMAL_FORMAT.format(i);
            com.setDdh(CommonUtils.dealDdh(cfDdh));
            com.setYsdh(CommonUtils.dealDdh(cfYsdh));
            com.setPch(CommonUtils.dealDdh(cfPch));
            com.setFpqqlsh(DistributedKeyMaker.generateShotKey());
            //开票状态 待开
            com.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_0.getKey());
            com.setDdly(OrderInfoEnum.ORDER_SOURCE_4.getKey());
            com.setKprq(null);
            com.setQdfphm("");
            com.setFphm("");
            com.setFpdm("");
            com.setSfcf("1");
            com.setByzd1("");
            com.setByzd2("");
            //订单状态 拆分
            com.setDdzt("4");
            i++;
        }
        
        // 如果是按金额拆分，确保价税合计准确无误
        if (StringUtils.equals(OrderSplitEnum.ORDER_SPLIT_TYPE_2.getKey(), orderSplitConfig.getSplitType()) 
            && StringUtils.equals(OrderSplitEnum.ORDER_HSBZ_1.getKey(), orderSplitConfig.getHsbz())
            && CollUtil.isNotEmpty(jeList) && orderSplit.size() <= jeList.size()) {
            
            // 重要：在返回前直接应用用户输入的精确金额值
            for (int j = 0; j < orderSplit.size() && j < jeList.size(); j++) {
                OrderInvoiceInfoEntity entity = orderSplit.get(j);
                String exactAmount = jeList.get(j);
                
                // 计算不含税金额和税额
                BigDecimal jshjBD = new BigDecimal(exactAmount);
                BigDecimal kpseBD = new BigDecimal(entity.getKpse());
                BigDecimal hjbhsjeBD = jshjBD.subtract(kpseBD).setScale(2, RoundingMode.HALF_UP);
                
                // 重要：直接使用传入的拆分金额值作为价税合计
                entity.setJshj(exactAmount);
                entity.setHjbhsje(hjbhsjeBD.toPlainString());
                
                // 标记为精确计算值
                entity.setByzd1("EXACT_AMOUNT");
                
                log.info("{}重新设置订单拆分金额，保证精确值：拆分金额={}, 税额={}, 不含税金额={}", 
                         LOGGER_MSG, exactAmount, entity.getKpse(), entity.getHjbhsje());
            }
        }
        
        //拆分后数据校验，支持某些特定业务拆分后需要进行数据合法性验证
        checkSdSplitAfterData(orderSplit, orderInvoiceInfoEntity.getFpqqlsh(), orderInvoiceInfoEntity.getDdh());
        return orderSplit;
    }

    public void checkSdSplitAfterData(List<OrderInvoiceInfoEntity> orderSplit, String originFpqqlsh, String originDdh) throws OrderSplitException {
        if (CollUtil.isNotEmpty(orderSplit)) {
            //校验拆分后数电拖拉机业务的数量是否为整数
            OrderInvoiceInfoEntity commonOrderInfo = orderSplit.get(0);
            if (Objects.nonNull(commonOrderInfo)) {
                if (OrderInfoEnum.ORDER_QD_TDYS_13.getKey().equals(commonOrderInfo.getTdyw())) {
                    //获取拆分后的明细信息
                    List<OrderInvoiceItemEntity> splitOrderItemInfoList = new ArrayList<>();
                    orderSplit.forEach(order -> splitOrderItemInfoList.addAll(order.getItemEntityList()));

                    List<String> xmslList = splitOrderItemInfoList.stream().map(OrderInvoiceItemEntity::getXmsl).collect(Collectors.toList());
                    for (String xmsl : xmslList) {
                        if (StringUtils.isNotBlank(xmsl)) {
                            xmsl = new BigDecimal(xmsl).stripTrailingZeros().toPlainString();
                            if (!NumberUtil.isInteger(xmsl)) {
                                throw new OrderSplitException(ConfigureConstant.STRING_9999, "发票请求流水号:" + originFpqqlsh + ",订单号:" + originDdh + ",数电拖拉机和联合收割机订单拆分后的数量必须为整数");
                            }
                        }
                    }
                }
            }
        }
    }
    private String getDdh() {
        StringBuilder dd = new StringBuilder("DD");
        String date = System.currentTimeMillis() + "";
        int i = (int) ((Math.random() * 9 + 1) * 1000);
        StringBuilder append = dd.append(date).append(i);
        return append.toString();
    }
    /**
     * 拆分预校验
     */
    @Override
    public R checkPreSpilt(List<String> orderIds, List<String> shList) {
        //校验订单来源是否为扫码开票
        boolean isQrCodeMakeOutOnInvoice = false;
        //校验是否为数电特定业务订单（建筑服务发票、货物运输服务发票、不动产销售服务发票、不动产租赁服务发票、旅客运输服务发票）
        boolean isQdTdyw = false;
        //是否为数电差额征税发票
        boolean isQdCezs = false;
        //是否为数电医疗服务发票
        boolean isQdYlfw = false;
        //是否为数电代收车船税发票
        boolean isQdDsccs = false;
        //是否为数电机动车销售统一发票
        boolean isQdJdcxstyfp = false;
        //是否为数电二手车销售统一发票
        boolean isQdEscxstyfp = false;
        //数电拖拉机和联合收割机(发票用于办理登记时不允许合并)
        boolean isQdTractor = false;

        for (String orderId : orderIds) {
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectById(orderId);
            if (!ObjectUtils.isEmpty(orderInvoiceInfoEntity)) {

                if (OrderInfoEnum.ORDER_SOURCE_5.getKey().equals(orderInvoiceInfoEntity.getDdly()) || OrderInfoEnum.ORDER_SOURCE_6.getKey().equals(orderInvoiceInfoEntity.getDdly())) {
                    isQrCodeMakeOutOnInvoice = true;
                    break;
                }
                if (OrderInfoEnum.ORDER_QD_TDYS_06.getKey().equals(orderInvoiceInfoEntity.getTdyw())
                        || OrderInfoEnum.ORDER_QD_TDYS_05.getKey().equals(orderInvoiceInfoEntity.getTdyw())
                        || OrderInfoEnum.ORDER_QD_TDYS_04.getKey().equals(orderInvoiceInfoEntity.getTdyw())
                        || OrderInfoEnum.ORDER_QD_TDYS_03.getKey().equals(orderInvoiceInfoEntity.getTdyw())
                        || OrderInfoEnum.ORDER_QD_TDYS_09.getKey().equals(orderInvoiceInfoEntity.getTdyw())) {
                    isQdTdyw = true;
                    break;
                }

                if (OrderInfoEnum.ORDER_QD_TDYS_10.getKey().equals(orderInvoiceInfoEntity.getTdyw())
                        || OrderInfoEnum.ORDER_QD_TDYS_11.getKey().equals(orderInvoiceInfoEntity.getTdyw())) {
                    isQdYlfw = true;
                    break;
                }

                if (OrderInfoEnum.ORDER_QD_TDYS_07.getKey().equals(orderInvoiceInfoEntity.getTdyw())) {
                    isQdDsccs = true;
                    break;
                }

                if (InvoiceTypeEnum.ORDER_INVOICE_TYPE_003.getKey().equals(orderInvoiceInfoEntity.getFpzlDm())
                        || InvoiceTypeEnum.ORDER_INVOICE_TYPE_087.getKey().equals(orderInvoiceInfoEntity.getFpzlDm())) {
                    isQdJdcxstyfp = true;
                    break;
                }

                if (InvoiceTypeEnum.ORDER_INVOICE_TYPE_104.getKey().equals(orderInvoiceInfoEntity.getFpzlDm())
                        || InvoiceTypeEnum.ORDER_INVOICE_TYPE_088.getKey().equals(orderInvoiceInfoEntity.getFpzlDm())) {
                    isQdEscxstyfp = true;
                    break;
                }

                if (OrderInfoEnum.ORDER_QD_CEZSLXDM_01.getKey().equals(orderInvoiceInfoEntity.getCezslxdm())
                        || OrderInfoEnum.ORDER_QD_CEZSLXDM_02.getKey().equals(orderInvoiceInfoEntity.getCezslxdm())) {
                    isQdCezs = true;
                    break;
                }
            }
        }
        if (isQrCodeMakeOutOnInvoice) {
            return R.error("扫码开票的订单不能拆分");
        }

        if (isQdTdyw) {
            return R.error("数电特定业务订单不允许拆分");
        }

        if (isQdYlfw) {
            return R.error("数电医疗服务订单不允许拆分");
        }

        if (isQdCezs) {
            return R.error("数电差额征税订单不允许拆分");
        }

        if (isQdDsccs) {
            return R.error("数电代收车船税订单不允许拆分");
        }

        if (isQdJdcxstyfp) {
            return R.error("数电机动车销售统一发票订单不支持拆分");
        }

        if (isQdEscxstyfp) {
            return R.error("数电二手车销售统一发票订单不支持拆分");
        }

        if (isQdTractor) {
            return R.error("数电拖拉机和联合收割机发票用于办理登记时订单不支持拆分");
        }
        return R.ok();
    }

    private static List<OrderInvoiceInfoEntity> orderSplit(OrderInvoiceInfoEntity orderInvoiceInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        String orderJson = JsonUtils.getInstance().toJsonString(orderInvoiceInfo);
        String configJson = JsonUtils.getInstance().toJsonString(orderSplitConfig);
        log.debug("订单拆分接口，入参,订单信息:{},配置参数信息:{}", orderJson, configJson);

        String kphjjeStr = orderInvoiceInfo.getJshj();
        if (StringUtils.isBlank(kphjjeStr)) {
            log.error("订单拆分失败：开票合计金额为空");
            throw new OrderSplitException("9999", "开票合计金额为空");
        }

        List<OrderInvoiceInfoEntity> resultList = new ArrayList<>();

        if (StringUtils.isBlank(orderInvoiceInfo.getFpzlDm())) {
            orderInvoiceInfo.setFpzlDm(InvoiceTypeEnum.ORDER_INVOICE_TYPE_002.getKey());
            orderSplitConfig.setFpzlDm(InvoiceTypeEnum.ORDER_INVOICE_TYPE_002.getKey());
        } else {
            orderSplitConfig.setFpzlDm(orderInvoiceInfo.getFpzlDm());
        }

        if (OrderSplitEnum.ORDER_SPLIT_TYPE_2.getKey().equals(orderSplitConfig.getSplitType())) {
            resultList = orderSplitByJeArray(orderInvoiceInfo, orderSplitConfig);
        } else if (OrderSplitEnum.ORDER_SPLIT_TYPE_3.getKey().equals(orderSplitConfig.getSplitType())) {
            resultList = orderSplitBySlArray(orderInvoiceInfo, orderSplitConfig);
        }

        BigDecimal cfKphjje = BigDecimal.ZERO;

        for (OrderInvoiceInfoEntity entity : resultList) {
            finalRebuildCommonOrderInfo(entity, orderSplitConfig);
            cfKphjje = cfKphjje.add(new BigDecimal(entity.getJshj()));
        }

        if (!DecimalCalculateUtil.stringIsEquals(cfKphjje.toPlainString(), kphjjeStr)) {
            String errorMsg = String.format("拆分后的开票合计金额与拆分前的合计金额不一致! 原金额=%s, 拆分后总和=%s", kphjjeStr, cfKphjje.toPlainString());
            log.error(errorMsg + ", 订单信息={}, 拆分结果={}", orderJson, JsonUtils.getInstance().toJsonString(resultList));
            throw new OrderSplitException(
                OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getKey(),
                OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getValue() + ": " + errorMsg
            );
        } else {
            log.debug("拆分接口出参:{}", JsonUtils.getInstance().toJsonString(resultList));
            return resultList;
        }
    }

    public static void finalRebuildCommonOrderInfo(OrderInvoiceInfoEntity commonOrderInfo, OrderSplitConfig orderSplitConfig) {
        // 如果是精确计算的金额，不做任何处理，直接返回
        if ("EXACT_AMOUNT".equals(commonOrderInfo.getByzd1())) {
            log.info("检测到EXACT_AMOUNT标记，保留原始价税合计：{}, 不含税金额：{}, 税额：{}", 
                     commonOrderInfo.getJshj(), commonOrderInfo.getHjbhsje(), commonOrderInfo.getKpse());
            
            // 检查所有明细的税额之和是否等于订单税额
            BigDecimal itemsTaxSum = BigDecimal.ZERO;
            for (OrderInvoiceItemEntity item : commonOrderInfo.getItemEntityList()) {
                if (StringUtils.isNotBlank(item.getSe())) {
                    itemsTaxSum = itemsTaxSum.add(new BigDecimal(item.getSe()));
                }
            }
            
            // 如果明细税额之和与订单税额不一致，调整最后一个明细项的税额
            if (itemsTaxSum.compareTo(new BigDecimal(commonOrderInfo.getKpse())) != 0) {
                log.info("EXACT_AMOUNT订单的明细税额之和({})与订单税额({})不一致，进行调整", 
                         itemsTaxSum.toPlainString(), commonOrderInfo.getKpse());
                
                // 查找最后一个带税额的明细项
                for (int i = commonOrderInfo.getItemEntityList().size() - 1; i >= 0; i--) {
                    OrderInvoiceItemEntity item = commonOrderInfo.getItemEntityList().get(i);
                    if (StringUtils.isNotBlank(item.getSe())) {
                        // 计算需要的调整额
                        BigDecimal adjustment = new BigDecimal(commonOrderInfo.getKpse()).subtract(itemsTaxSum);
                        BigDecimal newItemSe = new BigDecimal(item.getSe()).add(adjustment);
                        
                        log.info("调整明细项[{}]的税额: {} -> {}, 调整量: {}", 
                                i, item.getSe(), newItemSe.toPlainString(), adjustment.toPlainString());
                        
                        // 设置调整后的税额
                        item.setSe(newItemSe.toPlainString());
                        break;
                    }
                }
            }
            return;
        }
        
        String fpzlDm = orderSplitConfig.getFpzlDm();
        BigDecimal hjje = BigDecimal.ZERO;
        BigDecimal hjse = BigDecimal.ZERO;
        BigDecimal jshj = BigDecimal.ZERO;

        for(int i = 0; i < commonOrderInfo.getItemEntityList().size(); ++i) {
            OrderInvoiceItemEntity orderItemInfo = commonOrderInfo.getItemEntityList().get(i);
            if (OrderInfoEnum.HSBZ_0.getKey().equals(orderItemInfo.getHsbz())) {
                hjje = hjje.add(new BigDecimal(orderItemInfo.getJe()));
                hjse = hjse.add(new BigDecimal(orderItemInfo.getSe()));
            } else {
                jshj = jshj.add(new BigDecimal(orderItemInfo.getJe()));
            }

            if (StringUtils.isNotBlank(orderItemInfo.getDj())) {
                orderItemInfo.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(orderItemInfo.getDj(), fpzlDm, orderSplitConfig.getQdfwlx()));
            }

            if (StringUtils.isNotBlank(orderItemInfo.getXmsl())) {
                orderItemInfo.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(orderItemInfo.getXmsl(), fpzlDm, orderSplitConfig.getQdfwlx()));
            }

            String xmsl = StringUtils.isBlank(orderItemInfo.getXmsl()) ? "0" : orderItemInfo.getXmsl();
            if (DecimalCalculateUtil.stringIsZero(xmsl)) {
                orderItemInfo.setXmsl("");
                orderItemInfo.setDj("");
            }

            orderItemInfo.setXh(String.valueOf(i + 1));
        }

        if (OrderInfoEnum.HSBZ_0.getKey().equals((commonOrderInfo.getItemEntityList().get(0)).getHsbz())) {
            jshj = hjje.add(hjse);
            commonOrderInfo.setHjbhsje(DecimalCalculateUtil.decimalFormatToStringNew(hjje.toPlainString(), 2));
            commonOrderInfo.setJshj(DecimalCalculateUtil.decimalFormatToStringNew(jshj.toPlainString(), 2));
            commonOrderInfo.setKpse(DecimalCalculateUtil.decimalFormatToStringNew(hjse.toPlainString(), 2));
        } else {
            commonOrderInfo.setHjbhsje(DecimalCalculateUtil.decimalFormatToStringNew(jshj.toPlainString(), 2));
        }
    }

    private static List<OrderInvoiceInfoEntity> orderSplitBySlArray(OrderInvoiceInfoEntity commonOrderInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        boolean isDiscountRang = OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(commonOrderInfo.getItemEntityList().get(0).getFphxz());
        return dealSplitSl(commonOrderInfo, commonOrderInfo.getItemEntityList(), isDiscountRang, orderSplitConfig);
    }
    private static List<OrderInvoiceInfoEntity> dealSplitSl(OrderInvoiceInfoEntity orderInfo, List<OrderInvoiceItemEntity> selectByOrderId,
        boolean isDiscountRang, OrderSplitConfig orderSplitConfig) throws OrderSplitException {

        if (selectByOrderId == null || selectByOrderId.isEmpty()) {
            throw new OrderSplitException("9999","订单项列表为空");
        }

        OrderInvoiceItemEntity orderItemInfo = selectByOrderId.get(0);
        String fpzlDm = orderInfo.getFpzlDm();
        resetDjSl(orderItemInfo, fpzlDm, orderSplitConfig);

        OrderInvoiceItemEntity secondItem = selectByOrderId.size() > 1 ? selectByOrderId.get(1) : null;

        List<OrderInvoiceInfoEntity> invoiceEntityList;
        String hsbz = orderItemInfo.getHsbz();

        boolean isHs = OrderInfoEnum.HSBZ_1.getKey().equals(hsbz);
        if (isHs) {
            if (isDiscountRang) {
                invoiceEntityList = dealSplitSlHs(orderInfo, orderItemInfo, secondItem, orderSplitConfig);
            } else {
                invoiceEntityList = dealSplitSlHs(orderInfo, orderItemInfo, null, orderSplitConfig);
            }
        } else if (isDiscountRang) {
            invoiceEntityList = dealSplitSlBhs(orderInfo, orderItemInfo, secondItem, orderSplitConfig);
        } else {
            invoiceEntityList = dealSplitSlBhs(orderInfo, orderItemInfo, null, orderSplitConfig);
        }

        List<OrderInvoiceInfoEntity> finalInvoiceList = new ArrayList<>();
        for (OrderInvoiceInfoEntity comm : invoiceEntityList) {
            OrderInvoiceInfoEntity rebuilt = rebuildCommonOrderInfo(comm, comm.getItemEntityList());
            finalInvoiceList.add(rebuilt);
        }

        return finalInvoiceList;
}

    private static List<OrderInvoiceInfoEntity> dealSplitSlBhs(OrderInvoiceInfoEntity orderInfo, OrderInvoiceItemEntity orderItemInfo, OrderInvoiceItemEntity discountOrderItemInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
    List<OrderInvoiceInfoEntity> resultList = new ArrayList<>();
    String hjje = "0.00";
    String hjse = "0.00";
    String hjzkje = "0.00";
    String hjzkse = "0.00";
    String hjsl = "0.00";
    String fpzlDm = orderSplitConfig.getFpzlDm();
    String qdfwlx = orderSplitConfig.getQdfwlx();

    for (String sl : orderSplitConfig.getSlList()) {
        sl = checkSlIsZero(sl, fpzlDm, qdfwlx);
        hjsl = DecimalCalculateUtil.bigDecimalAdd(hjsl, sl, 20);

        OrderInvoiceInfoEntity copyOrderInfo = new OrderInvoiceInfoEntity();
        OrderInvoiceItemEntity copyOrderItemInfo = new OrderInvoiceItemEntity();
        List<OrderInvoiceItemEntity> itemList = new ArrayList<>();

        BeanUtil.copyProperties(orderInfo, copyOrderInfo, new String[0]);
        BeanUtil.copyProperties(orderItemInfo, copyOrderItemInfo, new String[0]);

        copyOrderItemInfo.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(sl, fpzlDm, qdfwlx));

        BigDecimal xmje;
        if (new BigDecimal(hjsl).compareTo(new BigDecimal(orderItemInfo.getXmsl())) == 0) {
            xmje = new BigDecimal(orderItemInfo.getJe()).subtract(new BigDecimal(hjje));
            copyOrderItemInfo.setJe(DecimalCalculateUtil.decimalFormatToStringNew(xmje.toPlainString(), 2));

            BigDecimal jeDiff = new BigDecimal(copyOrderItemInfo.getXmsl())
                    .multiply(new BigDecimal(copyOrderItemInfo.getDj()))
                    .setScale(2, RoundingMode.HALF_UP)
                    .subtract(new BigDecimal(copyOrderItemInfo.getJe()));

            if (DecimalCalculateUtil.stringCompareAbs(jeDiff.abs().toPlainString(), "0.01") > 0) {
                String zkje = DecimalCalculateUtil.divNew(copyOrderItemInfo.getJe(), copyOrderItemInfo.getXmsl(), 20);
                copyOrderItemInfo.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(zkje, fpzlDm, qdfwlx));
            }

            if (StringUtils.isNotBlank(orderItemInfo.getSe())) {
                String seDiff = DecimalCalculateUtil.bigDecimalSub(orderItemInfo.getSe(), hjse);
                copyOrderItemInfo.setSe(seDiff);
                hjse = DecimalCalculateUtil.bigDecimalAdd(hjse, seDiff);
            }
        } else {
            xmje = new BigDecimal(sl).multiply(new BigDecimal(orderItemInfo.getDj()));
            String zkje = checkJeIsZero(xmje.toPlainString());
            copyOrderItemInfo.setJe(zkje);

            if (StringUtils.isNotBlank(orderItemInfo.getSe())) {
                String xmse = xmje.divide(new BigDecimal(orderItemInfo.getJe()), 10, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(orderItemInfo.getSe()))
                        .setScale(2, RoundingMode.HALF_UP)
                        .toPlainString();
                copyOrderItemInfo.setSe(DecimalCalculateUtil.decimalFormatToString(xmse, 2));
                hjse = DecimalCalculateUtil.bigDecimalAdd(hjse, xmse);
            }
        }

        hjje = DecimalCalculateUtil.bigDecimalAdd(hjje, xmje.toPlainString());
        itemList.add(copyOrderItemInfo);

        if (discountOrderItemInfo != null) {
            OrderInvoiceItemEntity copyDiscountOrderItem = new OrderInvoiceItemEntity();
            BeanUtil.copyProperties(discountOrderItemInfo, copyDiscountOrderItem, new String[0]);

            String zkje;
            String xmse;
            if (new BigDecimal(hjsl).compareTo(new BigDecimal(orderItemInfo.getXmsl())) == 0) {
                zkje = DecimalCalculateUtil.bigDecimalSub(discountOrderItemInfo.getJe(), hjzkje);
                xmse = DecimalCalculateUtil.bigDecimalSub(discountOrderItemInfo.getSe(), hjzkse);
                copyDiscountOrderItem.setSe(xmse);
            } else {
                zkje = new BigDecimal(discountOrderItemInfo.getJe())
                        .multiply(new BigDecimal(copyOrderItemInfo.getJe())
                                .divide(new BigDecimal(orderItemInfo.getJe()), 8, RoundingMode.HALF_UP))
                        .setScale(2, RoundingMode.HALF_UP)
                        .toPlainString();
                xmse = new BigDecimal(zkje)
                        .divide(new BigDecimal(discountOrderItemInfo.getJe()), 8, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(discountOrderItemInfo.getSe()))
                        .setScale(2, RoundingMode.HALF_UP)
                        .toPlainString();
                copyDiscountOrderItem.setSe(DecimalCalculateUtil.decimalFormatToString(xmse, 2));
                hjzkse = DecimalCalculateUtil.bigDecimalAdd(hjzkse, xmse);
            }

            copyDiscountOrderItem.setJe(DecimalCalculateUtil.decimalFormatToString(zkje, 2));
            itemList.add(copyDiscountOrderItem);
            hjzkje = DecimalCalculateUtil.bigDecimalAdd(hjzkje, zkje);
        }

        // 修复：应添加 copyOrderInfo 而不是原始 orderInfo
        copyOrderInfo.setItemEntityList(itemList);
        resultList.add(copyOrderInfo);
    }

    String leaveSl = DecimalCalculateUtil.bigDecimalSub(orderItemInfo.getXmsl(), hjsl, 20);
    if (!DecimalCalculateUtil.stringIsZero(leaveSl)) {
        BigDecimal leaveJe = new BigDecimal(orderItemInfo.getJe()).subtract(new BigDecimal(hjje));
        OrderInvoiceInfoEntity copyOrderInfo = new OrderInvoiceInfoEntity();
        OrderInvoiceItemEntity copyOrderItemInfo = new OrderInvoiceItemEntity();
        List<OrderInvoiceItemEntity> itemList = new ArrayList<>();

        BeanUtil.copyProperties(orderInfo, copyOrderInfo, new String[0]);
        BeanUtil.copyProperties(orderItemInfo, copyOrderItemInfo, new String[0]);

        copyOrderItemInfo.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(leaveSl, fpzlDm, qdfwlx));
        copyOrderItemInfo.setJe(leaveJe.setScale(2, RoundingMode.HALF_UP).toPlainString());

        String jeWc = DecimalCalculateUtil.bigDecimalSub(leaveJe.toPlainString(), DecimalCalculateUtil.mulNew(orderItemInfo.getDj(), leaveSl, 2));
        if (DecimalCalculateUtil.stringCompareAbs(jeWc, "0.01") > 0) {
            String zkje = DecimalCalculateUtil.divNew(leaveJe.toPlainString(), leaveSl, 20);
            copyOrderItemInfo.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(zkje, fpzlDm, qdfwlx));
        }

        if (StringUtils.isNotBlank(orderItemInfo.getSe())) {
            String seDiff = DecimalCalculateUtil.bigDecimalSub(orderItemInfo.getSe(), hjse);
            copyOrderItemInfo.setSe(seDiff);
        }

        itemList.add(copyOrderItemInfo);

        if (discountOrderItemInfo != null) {
            String zkje = DecimalCalculateUtil.bigDecimalSub(discountOrderItemInfo.getJe(), hjzkje);
            OrderInvoiceItemEntity copyDiscountOrderItem = new OrderInvoiceItemEntity();
            BeanUtil.copyProperties(discountOrderItemInfo, copyDiscountOrderItem);
            copyDiscountOrderItem.setJe(zkje);

            if (StringUtils.isNotBlank(discountOrderItemInfo.getSe())) {
                String xmse = DecimalCalculateUtil.bigDecimalSub(discountOrderItemInfo.getSe(), hjzkse);
                copyDiscountOrderItem.setSe(xmse);
            }

            itemList.add(copyDiscountOrderItem);
        }

        copyOrderInfo.setItemEntityList(itemList);
        resultList.add(copyOrderInfo);
    }

    dealSeWc(resultList, orderSplitConfig);
    return resultList;
}

    private static List<OrderInvoiceInfoEntity> dealSplitSlHs(OrderInvoiceInfoEntity orderInfo, OrderInvoiceItemEntity orderItemInfo, OrderInvoiceItemEntity discountOrderItemInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
    if (orderInfo == null || orderItemInfo == null || orderSplitConfig == null || orderSplitConfig.getSlList() == null) {
        throw new OrderSplitException("9999","参数不能为空");
    }

    List<OrderInvoiceInfoEntity> resultList = new ArrayList<>();
    String jshj = "0.00";
    String hjzkje = "0.00";
    String hjsl = "0.00";
    String fpzlDm = orderSplitConfig.getFpzlDm();
    String qdfwlx = orderSplitConfig.getQdfwlx();

    for (String taxRateStr : orderSplitConfig.getSlList()) {
        taxRateStr = checkSlIsZero(taxRateStr, fpzlDm, qdfwlx);
        if (taxRateStr == null || !DecimalCalculateUtil.isNumeric(taxRateStr)) {
            throw new OrderSplitException("9999","税率配置异常：" + taxRateStr);
        }
        hjsl = DecimalCalculateUtil.bigDecimalAdd(hjsl, taxRateStr, 20);

        OrderInvoiceInfoEntity copyOrderInfo = new OrderInvoiceInfoEntity();
        OrderInvoiceItemEntity copyOrderItemInfo = new OrderInvoiceItemEntity();
        BeanUtil.copyProperties(copyOrderInfo, orderInfo);
        BeanUtil.copyProperties(copyOrderItemInfo, orderItemInfo);

        List<OrderInvoiceItemEntity> itemList = new ArrayList<>();
        String slFormatted = DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(taxRateStr, fpzlDm, qdfwlx);
        copyOrderItemInfo.setXmsl(slFormatted);

        String xmje;
        if (new BigDecimal(hjsl).compareTo(new BigDecimal(orderItemInfo.getXmsl())) == 0) {
            xmje = DecimalCalculateUtil.bigDecimalSub(orderItemInfo.getJe(), jshj);
            copyOrderItemInfo.setJe(xmje);

            String zkje = DecimalCalculateUtil.bigDecimalSub(
                DecimalCalculateUtil.mulNew(copyOrderItemInfo.getDj(), copyOrderItemInfo.getXmsl(), 2),
                copyOrderItemInfo.getJe(), 2
            );

            if (DecimalCalculateUtil.stringCompareAbs(zkje, "0.01") > 0) {
                String newXmdj = DecimalCalculateUtil.divNew(copyOrderItemInfo.getJe(), copyOrderItemInfo.getXmsl(), 20);
                copyOrderItemInfo.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
            }
        } else {
            xmje = DecimalCalculateUtil.mul(taxRateStr, orderItemInfo.getDj());
            String zkje = checkJeIsZero(xmje);
            copyOrderItemInfo.setJe(zkje);
        }

        jshj = DecimalCalculateUtil.bigDecimalAdd(jshj, xmje);
        itemList.add(copyOrderItemInfo);

        if (discountOrderItemInfo != null) {
            String zkje;
            if (new BigDecimal(hjsl).compareTo(new BigDecimal(orderItemInfo.getXmsl())) == 0) {
                zkje = DecimalCalculateUtil.bigDecimalSub(discountOrderItemInfo.getJe(), hjzkje);
            } else {
                BigDecimal discountRate = new BigDecimal(copyOrderItemInfo.getJe())
                    .divide(new BigDecimal(orderItemInfo.getJe()), 10, RoundingMode.HALF_UP);
                zkje = new BigDecimal(discountOrderItemInfo.getJe())
                    .multiply(discountRate)
                    .setScale(2, RoundingMode.HALF_UP)
                    .toPlainString();
            }

            OrderInvoiceItemEntity copyDiscountOrderItem = new OrderInvoiceItemEntity();
            BeanUtil.copyProperties(copyDiscountOrderItem, discountOrderItemInfo);
            copyDiscountOrderItem.setJe(DecimalCalculateUtil.decimalFormatToString(zkje, 2));
            itemList.add(copyDiscountOrderItem);
            hjzkje = DecimalCalculateUtil.bigDecimalAdd(hjzkje, zkje);
        }

        // 深拷贝防止引用污染
        OrderInvoiceInfoEntity deepCopyOrderInfo = new OrderInvoiceInfoEntity();
        BeanUtil.copyProperties(deepCopyOrderInfo, copyOrderInfo);
        deepCopyOrderInfo.setItemEntityList(itemList);
        resultList.add(deepCopyOrderInfo);
    }

    BigDecimal totalSl = new BigDecimal(orderItemInfo.getXmsl()).subtract(new BigDecimal(hjsl)).setScale(10, RoundingMode.HALF_UP);
    if (totalSl.compareTo(BigDecimal.ZERO) != 0) {
        BigDecimal totalJe = new BigDecimal(orderItemInfo.getJe()).subtract(new BigDecimal(jshj));

        OrderInvoiceInfoEntity copyOrderInfo = new OrderInvoiceInfoEntity();
        OrderInvoiceItemEntity copyOrderItemInfo = new OrderInvoiceItemEntity();
        BeanUtil.copyProperties(copyOrderInfo, orderInfo);
        BeanUtil.copyProperties(copyOrderItemInfo, orderItemInfo);

        List<OrderInvoiceItemEntity> itemList = new ArrayList<>();
        int xmslAfterPointLength = DecimalCalculateUtil.getAfterPointDynamicLength(fpzlDm, qdfwlx, totalSl.toPlainString());
        copyOrderItemInfo.setXmsl(totalSl.setScale(xmslAfterPointLength, RoundingMode.HALF_UP).toPlainString());
        copyOrderItemInfo.setJe(totalJe.setScale(2, RoundingMode.HALF_UP).toPlainString());

        BigDecimal expectedJe = totalSl.multiply(new BigDecimal(orderItemInfo.getDj())).setScale(2, RoundingMode.HALF_UP);
        if (totalJe.subtract(expectedJe).abs().compareTo(new BigDecimal("0.01")) > 0) {
            BigDecimal xmdj = totalJe.divide(totalSl, 20, RoundingMode.HALF_UP);
            int xmdjAfterPointLength = DecimalCalculateUtil.getAfterPointDynamicLength(fpzlDm, qdfwlx, xmdj.toPlainString());
            copyOrderItemInfo.setDj(xmdj.setScale(xmdjAfterPointLength, RoundingMode.HALF_UP).toPlainString());
        }

        itemList.add(copyOrderItemInfo);

        if (discountOrderItemInfo != null) {
            String remainingDiscount = DecimalCalculateUtil.bigDecimalSub(discountOrderItemInfo.getJe(), hjzkje);
            OrderInvoiceItemEntity copyDiscountOrderItem = new OrderInvoiceItemEntity();
            BeanUtil.copyProperties(copyDiscountOrderItem, discountOrderItemInfo);
            copyDiscountOrderItem.setJe(remainingDiscount);
            itemList.add(copyDiscountOrderItem);
        }

        copyOrderInfo.setItemEntityList(itemList);
        resultList.add(copyOrderInfo);
    }

    dealSeWc(resultList, orderSplitConfig);
    return resultList;
}

    public static void dealSeWc(List<OrderInvoiceInfoEntity> orderSplit, OrderSplitConfig orderSplitConfig) {
        if (OrderInfoEnum.FPHXZ_CODE_0.getKey().equals((orderSplit.get(0).getItemEntityList().get(0)).getFphxz())) {
            OrderInvoiceInfoEntity commonOrderInfo = orderSplit.get(orderSplit.size() - 1);
            OrderInvoiceItemEntity orderItemInfo = commonOrderInfo.getItemEntityList().get(0);
            String qdfwlx = orderSplitConfig.getQdfwlx();
            String wcje;
            if (!"0".equals(qdfwlx) && !"2".equals(qdfwlx)) {
                wcje = DecimalCalculateUtil.bigDecimalSub(DecimalCalculateUtil.mulNew(orderItemInfo.getJe(), orderItemInfo.getSl(), 2), orderItemInfo.getSe());
            } else {
                wcje = DecimalCalculateUtil.bigDecimalSub(DecimalCalculateUtil.mulNew(orderItemInfo.getJe(), orderItemInfo.getSl(), 3), orderItemInfo.getSe());
            }

            if (DecimalCalculateUtil.stringCompareAbs(wcje, "0.06") > 0) {
                log.info("拆分后最后一张发票的税额误差大于0.06 需要重新分配税额，orderItemId:{}", orderItemInfo.getId());
                BigDecimal leaveJe;
                BigDecimal syJe;
                if (DecimalCalculateUtil.stringCompareAbs(wcje, "0.00") > 0) {
                    leaveJe = new BigDecimal(DecimalCalculateUtil.bigDecimalSub(wcje, "0.05", 2));
                    syJe = leaveJe;
                    String finalse = DecimalCalculateUtil.bigDecimalAdd(orderItemInfo.getSe(), leaveJe.toPlainString());
                    orderItemInfo.setSe(finalse);
                    int devide = orderSplit.size() - 1;
                    BigDecimal realAvgSe = leaveJe.divide(new BigDecimal(devide), 10, RoundingMode.HALF_UP);
                    if (DecimalCalculateUtil.stringCompare(realAvgSe.toPlainString(), "0.01") < 0) {
                        realAvgSe = new BigDecimal("0.01");
                    }

                    for(int i = 0; i < devide; ++i) {
                        BigDecimal avgSe = BigDecimal.ZERO;
                        if (realAvgSe.doubleValue() == 0.01) {
                            if (devide - 1 - i != 0) {
                                avgSe = new BigDecimal("0.01");
                            }
                        } else {
                            BigDecimal currentAvgJe;
                            if (devide - 1 - i == 0) {
                                currentAvgJe = syJe;
                            } else {
                                currentAvgJe = syJe.divide(new BigDecimal(devide - i), 10, RoundingMode.HALF_UP);
                            }

                            if (currentAvgJe.compareTo(realAvgSe) > 0) {
                                avgSe = leaveJe.divide(new BigDecimal(devide), 2, RoundingMode.UP);
                            } else {
                                avgSe = leaveJe.divide(new BigDecimal(devide), 2, RoundingMode.DOWN);
                            }
                        }

                        syJe = syJe.subtract(avgSe).setScale(2, RoundingMode.HALF_UP);
                        String se1;
                        String realSe;
                        if (syJe.doubleValue() < 0.0) {
                            se1 = avgSe.add(syJe).setScale(2, RoundingMode.HALF_UP).toPlainString();
                            realSe = DecimalCalculateUtil.bigDecimalSub(((orderSplit.get(i)).getItemEntityList().get(0)).getSe(), se1);
                            (orderSplit.get(i).getItemEntityList().get(0)).setSe(realSe);
                            break;
                        }

                        if (i == devide - 1) {
                            se1 = syJe.add(avgSe).setScale(2, RoundingMode.HALF_UP).toPlainString();
                            realSe = DecimalCalculateUtil.bigDecimalSub(((orderSplit.get(i)).getItemEntityList().get(0)).getSe(), se1);
                            ((orderSplit.get(i)).getItemEntityList().get(0)).setSe(realSe);
                        } else {
                            se1 = DecimalCalculateUtil.bigDecimalSub(((orderSplit.get(i)).getItemEntityList().get(0)).getSe(), avgSe.toPlainString());
                            ((orderSplit.get(i)).getItemEntityList().get(0)).setSe(se1);
                        }
                    }
                } else {
                    leaveJe = (new BigDecimal(wcje)).abs();
                    syJe = leaveJe.subtract(new BigDecimal("0.05")).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal finalse = (new BigDecimal(orderItemInfo.getSe())).subtract(syJe).setScale(2, RoundingMode.HALF_UP);
                    orderItemInfo.setSe(finalse.toPlainString());
                    int devide = orderSplit.size() - 1;
                    BigDecimal realAvgJe = syJe.divide(new BigDecimal(devide), 10, RoundingMode.HALF_UP);
                    if (DecimalCalculateUtil.stringCompare(realAvgJe.toPlainString(), "0.01") < 0) {
                        realAvgJe = new BigDecimal("0.01");
                    }

                    for(int i = 0; i < devide; ++i) {
                        String avgSe = "0.00";
                        if (realAvgJe.setScale(2, RoundingMode.HALF_UP).doubleValue() == 0.01) {
                            if (devide - 1 - i != 0) {
                                avgSe = "0.01";
                            }
                        } else {
                            BigDecimal currentAvgJe;
                            if (devide - 1 - i == 0) {
                                currentAvgJe = syJe;
                            } else {
                                currentAvgJe = syJe.divide(new BigDecimal(devide - i), 10, RoundingMode.HALF_UP);
                            }

                            if (currentAvgJe.compareTo(realAvgJe) > 0) {
                                avgSe = syJe.divide(new BigDecimal(devide), 2, RoundingMode.UP).toPlainString();
                            } else {
                                avgSe = syJe.divide(new BigDecimal(devide), 2, RoundingMode.DOWN).toPlainString();
                            }
                        }

                        syJe = syJe.subtract(new BigDecimal(avgSe)).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = syJe.add(new BigDecimal(avgSe)).setScale(2, RoundingMode.HALF_UP);
                        String se1;
                        String realSe;
                        if (syJe.doubleValue() < 0.0) {
                            se1 = bigDecimal.toPlainString();
                            realSe = DecimalCalculateUtil.bigDecimalAdd(((orderSplit.get(i)).getItemEntityList().get(0)).getSe(), se1);
                            (orderSplit.get(i).getItemEntityList().get(0)).setSe(realSe);
                            break;
                        }

                        if (i == devide - 1) {
                            se1 = bigDecimal.toPlainString();
                            realSe = DecimalCalculateUtil.bigDecimalAdd(((orderSplit.get(i)).getItemEntityList().get(0)).getSe(), se1);
                            ((orderSplit.get(i)).getItemEntityList().get(0)).setSe(realSe);
                        } else {
                            se1 = DecimalCalculateUtil.bigDecimalAdd(((orderSplit.get(i)).getItemEntityList().get(0)).getSe(), avgSe);
                            (orderSplit.get(i).getItemEntityList().get(0)).setSe(se1);
                        }
                    }
                }
            }
        }

    }
    private static String checkJeIsZero(String xmje) throws OrderSplitException {
        String xmjeDecimalFormat = (new BigDecimal(xmje)).setScale(2, RoundingMode.HALF_UP).toPlainString();
        if (DecimalCalculateUtil.stringIsZero(xmjeDecimalFormat)) {
            throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_SL_ERROR5);
        } else {
            return xmjeDecimalFormat;
        }
    }
    private static String checkSlIsZero(String sl, String fpzlDm, String qdfwlx) throws OrderSplitException {
        int afterPointLength = DecimalCalculateUtil.getAfterPointDynamicLength(fpzlDm, qdfwlx, sl);
        String slDecimalFormat = (new BigDecimal(sl)).setScale(afterPointLength, RoundingMode.HALF_UP).toPlainString();
        if (DecimalCalculateUtil.stringIsZero(slDecimalFormat)) {
            throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_SL_ERROR6);
        } else {
            return slDecimalFormat;
        }
    }
    private static void resetDjSl(OrderInvoiceItemEntity orderItemInfo, String fpzlDm, OrderSplitConfig orderSplitConfig) {
        String qdfwlx = orderSplitConfig.getQdfwlx();
        String newXmsl;
        if (StringUtils.isBlank(orderItemInfo.getDj()) && StringUtils.isNotBlank(orderItemInfo.getXmsl())) {
            newXmsl = DecimalCalculateUtil.divNew(orderItemInfo.getJe(), orderItemInfo.getXmsl(), 20);
            orderItemInfo.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
        } else if (StringUtils.isNotBlank(orderItemInfo.getDj()) && StringUtils.isBlank(orderItemInfo.getXmsl())) {
            newXmsl = DecimalCalculateUtil.divNew(orderItemInfo.getJe(), orderItemInfo.getDj(), 20);
            orderItemInfo.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
        }

    }

    private static List<OrderInvoiceInfoEntity> orderSplitByJeArray(OrderInvoiceInfoEntity commonOrderInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        List<OrderInvoiceInfoEntity> commonList = new ArrayList<>();
        if (orderSplitConfig == null || commonOrderInfo == null) {
            return commonList;
        }
        List<String> jeList = orderSplitConfig.getJeList();
        if (jeList == null || jeList.isEmpty()) {
            return commonList;
        }
        
        // 如果是含税拆分，使用专门的含税拆分逻辑
        if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz())) {
            return orderSplitByJeArrayWithTax(commonOrderInfo, orderSplitConfig);
        }
        
        // 不含税拆分原有逻辑
        try {
            int index = 0;
            for (String je : jeList) {
                List<OrderInvoiceInfoEntity> resultList = dealSplitJeBhs(je, commonOrderInfo,
                    commonOrderInfo.getItemEntityList() != null ? commonOrderInfo.getItemEntityList() : Collections.emptyList(), orderSplitConfig);
                if (resultList.isEmpty()) {
                    commonList.addAll(resultList);
                    break;
                }
                commonList.addAll(resultList);
                if (index == jeList.size() - 1 && !commonOrderInfo.getItemEntityList().isEmpty()) {
                    convertAndAdd(commonOrderInfo, commonOrderInfo.getItemEntityList(), commonList);
                }
                index++;
            }
        } catch (OrderSeparationException e) {
            throw new OrderSplitException(e.getCode(), e.getMessage(), e);
        }
        return commonList;
    }
    
    /**
     * 含税拆分专用方法
     */
    private static List<OrderInvoiceInfoEntity> orderSplitByJeArrayWithTax(OrderInvoiceInfoEntity commonOrderInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        List<OrderInvoiceInfoEntity> resultList = new ArrayList<>();
        List<String> jeList = orderSplitConfig.getJeList();
        
        // 记录原始的订单信息和明细项，用于后续参考
        log.info("原始订单价税合计:{}, 合计不含税金额:{}, 税额:{}", commonOrderInfo.getJshj(), commonOrderInfo.getHjbhsje(), commonOrderInfo.getKpse());
        
        // 详细记录传入的金额列表
        log.info("拆分金额列表数据类型: {}", jeList.getClass().getName());
        for (int i = 0; i < jeList.size(); i++) {
            log.info("金额[{}]: 值='{}', 类型='{}'", i, jeList.get(i), jeList.get(i).getClass().getName());
        }
        
        for (OrderInvoiceItemEntity item : commonOrderInfo.getItemEntityList()) {
            log.info("原始明细项：金额:{}, 税额:{}, 数量:{}, 单价:{}", item.getJe(), item.getSe(), item.getXmsl(), item.getDj());
        }
        
        // 获取总价税合计和总税额 - 这是我们拆分的基准金额
        BigDecimal totalJshj = new BigDecimal(commonOrderInfo.getJshj());
        BigDecimal totalTax = new BigDecimal(commonOrderInfo.getKpse());
        
        // 检查传入的拆分金额是否符合预期
        BigDecimal totalSplitAmount = BigDecimal.ZERO;
        List<BigDecimal> parsedJeList = new ArrayList<>();
        for (String je : jeList) {
            // 使用字符串构造器确保精确保留原始值
            String trimmedJe = je.trim();
            log.info("处理拆分金额 - 原始值: '{}', 修整后: '{}'", je, trimmedJe);
            BigDecimal splitAmount = new BigDecimal(trimmedJe);
            log.info("创建BigDecimal后的值: {}, toString: {}, toPlainString: {}", 
                     splitAmount, splitAmount.toString(), splitAmount.toPlainString());
            
            if (splitAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new OrderSplitException("9999", "拆分金额必须大于0");
            }
            parsedJeList.add(splitAmount);
            totalSplitAmount = totalSplitAmount.add(splitAmount);
        }
        
        // 验证拆分总和是否超过原订单金额
        if (totalSplitAmount.compareTo(totalJshj) > 0) {
            throw new OrderSplitException("9999", "拆分金额总和大于订单总金额，无法拆分");
        }
        
        // 计算每个拆分订单应分摊的税额总和（用于后续验证）
        BigDecimal calculatedTotalTax = BigDecimal.ZERO;
        
        // 处理拆分订单
        for (int i = 0; i < parsedJeList.size(); i++) {
            // 重要：使用原始传入的金额值，避免精度丢失
            BigDecimal splitAmount = parsedJeList.get(i);
            log.info("准备创建拆分订单 - 索引: {}, 拆分金额值: {}, toPlainString: {}", 
                     i, splitAmount, splitAmount.toPlainString());
            
            // 创建新的订单
            OrderInvoiceInfoEntity newOrderInfo = new OrderInvoiceInfoEntity();
            BeanUtil.copyProperties(commonOrderInfo, newOrderInfo);
            
            // 按比例计算 - 明确保留每一个明细，并按比例计算金额
            BigDecimal ratio = splitAmount.divide(totalJshj, 10, RoundingMode.HALF_UP);
            log.info("拆分比例计算 - 拆分金额: {}, 原始金额: {}, 比例: {}", splitAmount, totalJshj, ratio);
            
            // 计算本订单的分摊税额（按比例）
            BigDecimal orderTax = totalTax.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
            calculatedTotalTax = calculatedTotalTax.add(orderTax);
            
            // 该订单下所有明细的税额总和
            BigDecimal itemsTaxTotal = BigDecimal.ZERO;
            
            // 复制所有明细项并调整金额
            List<OrderInvoiceItemEntity> newItems = new ArrayList<>();
            for (int j = 0; j < commonOrderInfo.getItemEntityList().size(); j++) {
                OrderInvoiceItemEntity originalItem = commonOrderInfo.getItemEntityList().get(j);
                OrderInvoiceItemEntity newItem = new OrderInvoiceItemEntity();
                BeanUtil.copyProperties(originalItem, newItem);
                
                // 处理金额 - 按比例计算
                BigDecimal newItemJe = new BigDecimal(originalItem.getJe()).multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                newItem.setJe(newItemJe.toPlainString());
                
                // 处理税额 - 按比例计算
                if (StringUtils.isNotBlank(originalItem.getSe())) {
                    BigDecimal newItemSe;
                    
                    // 如果是最后一个明细项，调整税额以确保所有明细税额之和等于订单税额
                    if (j == commonOrderInfo.getItemEntityList().size() - 1) {
                        // 计算前面明细项的税额总和
                        BigDecimal remainingTax = orderTax.subtract(itemsTaxTotal);
                        newItemSe = remainingTax;
                        log.info("最后一个明细项，调整税额以保持平衡: 计算税额={}, 调整后税额={}", 
                                 new BigDecimal(originalItem.getSe()).multiply(ratio).setScale(2, RoundingMode.HALF_UP),
                                 remainingTax);
                    } else {
                        newItemSe = new BigDecimal(originalItem.getSe()).multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                        itemsTaxTotal = itemsTaxTotal.add(newItemSe);
                    }
                    
                    newItem.setSe(newItemSe.toPlainString());
                }
                
                // 处理数量和单价
                if (StringUtils.isNotBlank(originalItem.getXmsl()) && StringUtils.isNotBlank(originalItem.getDj())) {
                    // 数量按比例计算
                    BigDecimal originalCount = new BigDecimal(originalItem.getXmsl());
                    BigDecimal newCount = originalCount.multiply(ratio).setScale(10, RoundingMode.HALF_UP);
                    
                    // 确保数量不为零
                    if (newCount.compareTo(BigDecimal.ZERO) > 0) {
                        newItem.setXmsl(newCount.toPlainString());
                        
                        // 调整单价，确保单价*数量=金额
                        BigDecimal adjustedPrice = newItemJe.divide(newCount, 10, RoundingMode.HALF_UP);
                        newItem.setDj(adjustedPrice.toPlainString());
                    } else {
                        // 数量为零时清空数量和单价
                        newItem.setXmsl("");
                        newItem.setDj("");
                    }
                }
                
                newItems.add(newItem);
            }
            
            // 确保税额 + 不含税金额 = 价税合计
            BigDecimal newHjbhsje = splitAmount.subtract(orderTax).setScale(2, RoundingMode.HALF_UP);
            
            // 设置最终值，这些值是精确计算的，不应被重新计算
            newOrderInfo.setKpse(orderTax.toPlainString());
            newOrderInfo.setHjbhsje(newHjbhsje.toPlainString());
            
            // 同时确保金额格式符合标准（带有2位小数）
            String exactAmount = parsedJeList.get(i).toPlainString();
            if (!exactAmount.contains(".")) {
                // 如果金额没有小数点，添加.00
                exactAmount = exactAmount + ".00";
            } else {
                // 如果有小数点但小数位不足2位，补足
                String[] parts = exactAmount.split("\\.");
                if (parts.length > 1 && parts[1].length() < 2) {
                    // 小数位不足2位
                    exactAmount = parts[0] + "." + String.format("%-2s", parts[1]).replace(' ', '0');
                }
            }
            log.info("处理后的精确金额: {}", exactAmount);
            newOrderInfo.setJshj(exactAmount);
            
            // 添加自定义属性，标记这是精确设置的金额，不要重新计算
            newOrderInfo.setByzd1("EXACT_AMOUNT");
            
            // 设置明细项
            newOrderInfo.setItemEntityList(newItems);
            
            log.info("设置最终价税合计 - 索引: {}, 原始输入值: '{}', 最终设置值: '{}'", 
                     i, jeList.get(i), newOrderInfo.getJshj());
            
            resultList.add(newOrderInfo);
            
            log.info("拆分后订单（对象内存地址: {}）：价税合计:{}, 不含税金额:{}, 税额={}", 
                     System.identityHashCode(newOrderInfo),
                     newOrderInfo.getJshj(), newOrderInfo.getHjbhsje(), newOrderInfo.getKpse());
        }
        
        // 如果拆分金额总和小于订单总金额，添加最后一个订单包含剩余金额
        if (totalSplitAmount.compareTo(totalJshj) < 0) {
            BigDecimal remainingAmount = totalJshj.subtract(totalSplitAmount);
            
            // 创建最后一个订单
            OrderInvoiceInfoEntity lastOrderInfo = new OrderInvoiceInfoEntity();
            BeanUtil.copyProperties(commonOrderInfo, lastOrderInfo);
            
            // 计算剩余比例
            BigDecimal remainingRatio = remainingAmount.divide(totalJshj, 10, RoundingMode.HALF_UP);
            
            // 计算剩余税额（确保总税额一致）
            BigDecimal remainingTax = totalTax.subtract(calculatedTotalTax).setScale(2, RoundingMode.HALF_UP);
            
            // 该订单下所有明细的税额总和
            BigDecimal itemsTaxTotal = BigDecimal.ZERO;
            
            // 复制所有明细项并调整金额
            List<OrderInvoiceItemEntity> lastItems = new ArrayList<>();
            for (int j = 0; j < commonOrderInfo.getItemEntityList().size(); j++) {
                OrderInvoiceItemEntity originalItem = commonOrderInfo.getItemEntityList().get(j);
                OrderInvoiceItemEntity lastItem = new OrderInvoiceItemEntity();
                BeanUtil.copyProperties(originalItem, lastItem);
                
                // 处理金额
                BigDecimal lastItemJe = new BigDecimal(originalItem.getJe()).multiply(remainingRatio).setScale(2, RoundingMode.HALF_UP);
                lastItem.setJe(lastItemJe.toPlainString());
                
                // 处理税额
                if (StringUtils.isNotBlank(originalItem.getSe())) {
                    BigDecimal lastItemSe;
                    
                    // 如果是最后一个明细项，调整税额以确保所有明细税额之和等于订单税额
                    if (j == commonOrderInfo.getItemEntityList().size() - 1) {
                        lastItemSe = remainingTax.subtract(itemsTaxTotal);
                    } else {
                        lastItemSe = new BigDecimal(originalItem.getSe()).multiply(remainingRatio).setScale(2, RoundingMode.HALF_UP);
                        itemsTaxTotal = itemsTaxTotal.add(lastItemSe);
                    }
                    
                    lastItem.setSe(lastItemSe.toPlainString());
                }
                
                // 处理数量和单价
                if (StringUtils.isNotBlank(originalItem.getXmsl()) && StringUtils.isNotBlank(originalItem.getDj())) {
                    BigDecimal originalCount = new BigDecimal(originalItem.getXmsl());
                    BigDecimal lastCount = originalCount.multiply(remainingRatio).setScale(10, RoundingMode.HALF_UP);
                    
                    if (lastCount.compareTo(BigDecimal.ZERO) > 0) {
                        lastItem.setXmsl(lastCount.toPlainString());
                        
                        // 调整单价，确保单价*数量=金额
                        BigDecimal adjustedPrice = lastItemJe.divide(lastCount, 10, RoundingMode.HALF_UP);
                        lastItem.setDj(adjustedPrice.toPlainString());
                    } else {
                        lastItem.setXmsl("");
                        lastItem.setDj("");
                    }
                }
                
                lastItems.add(lastItem);
            }
            
            // 确保税额 + 不含税金额 = 价税合计
            BigDecimal lastHjbhsje = remainingAmount.subtract(remainingTax).setScale(2, RoundingMode.HALF_UP);
            
            // 设置最终值
            lastOrderInfo.setKpse(remainingTax.toPlainString());
            lastOrderInfo.setHjbhsje(lastHjbhsje.toPlainString());
            lastOrderInfo.setJshj(remainingAmount.toPlainString());
            
            // 添加自定义属性，标记这是精确设置的金额，不要重新计算
            lastOrderInfo.setByzd1("EXACT_AMOUNT");
            
            // 设置明细项
            lastOrderInfo.setItemEntityList(lastItems);
            
            resultList.add(lastOrderInfo);
        }
        
        // 输出最终的拆分结果集情况
        log.info("最终拆分结果集大小: {}", resultList.size());
        for (int i = 0; i < resultList.size(); i++) {
            OrderInvoiceInfoEntity order = resultList.get(i);
            log.info("拆分结果[{}]: ID={}, 价税合计={}, 不含税金额={}, 税额={}, 明细数量={}", 
                     i, order.getId(), order.getJshj(), order.getHjbhsje(), order.getKpse(), 
                     order.getItemEntityList().size());
            
            // 验证明细税额之和是否等于订单税额
            BigDecimal itemTaxSum = BigDecimal.ZERO;
            for (OrderInvoiceItemEntity item : order.getItemEntityList()) {
                if (StringUtils.isNotBlank(item.getSe())) {
                    itemTaxSum = itemTaxSum.add(new BigDecimal(item.getSe()));
                }
            }
            
            log.info("订单[{}]税额验证: 订单税额={}, 明细税额之和={}, 差额={}", 
                     i, order.getKpse(), itemTaxSum.toPlainString(), 
                     new BigDecimal(order.getKpse()).subtract(itemTaxSum).toPlainString());
        }
        
        // 允许1分钱的误差
        BigDecimal afterSplitTotal = BigDecimal.ZERO;
        for (OrderInvoiceInfoEntity order : resultList) {
            afterSplitTotal = afterSplitTotal.add(new BigDecimal(order.getJshj()));
        }
        if (afterSplitTotal.subtract(totalJshj).abs().compareTo(new BigDecimal("0.01")) > 0) {
            log.error("拆分后总金额({})与原订单金额({})不一致，误差超过1分钱", afterSplitTotal, totalJshj);
            throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getKey(),
                    "拆分后的开票合计金额与拆分前的合计金额不一致! 原金额=" + totalJshj + ", 拆分后总和=" + afterSplitTotal);
        }
        
        return resultList;
    }

    private static List<OrderInvoiceInfoEntity> dealSplitJeBhs(String je, OrderInvoiceInfoEntity orderInfo, List<OrderInvoiceItemEntity> orderItemList, OrderSplitConfig config) throws OrderSplitException, OrderSeparationException {
        List<OrderInvoiceInfoEntity> resultList = new ArrayList();
        BigDecimal hjje = new BigDecimal("0.00");
        BigDecimal lastHjje = new BigDecimal("0.00");
        List<OrderInvoiceItemEntity> orderItemInfoList = new ArrayList();

        // 记录原始itemList，防止多次拆分时丢失明细
        List<OrderInvoiceItemEntity> originalItemList = new ArrayList<>(orderItemList);

        // 含税拆分模式特殊处理
        boolean isTaxIncluded = OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(config.getHsbz());
        
        for(int i = 0; i < orderItemList.size(); ++i) {
            OrderInvoiceItemEntity orderItemInfo = orderItemList.get(i);
            BigDecimal bigDecimal = new BigDecimal(je).subtract(lastHjje).setScale(2, RoundingMode.HALF_UP);
            if (!OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(orderItemInfo.getFphxz())) {
                if (OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(orderItemList.get(i).getFphxz())) {
                    continue;
                }
                if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(config.getHsbz())) {
                    if (OrderSplitEnum.ORDER_HSBZ_0.getKey().equals(orderItemInfo.getHsbz())) {
                        hjje = hjje.add(new BigDecimal(orderItemInfo.getJe())).add(new BigDecimal(orderItemInfo.getSe()));
                    } else {
                        hjje = hjje.add(new BigDecimal(orderItemInfo.getJe()));
                    }
                } else {
                    hjje = hjje.add(new BigDecimal(orderItemInfo.getJe()));
                }

                if (hjje.compareTo(new BigDecimal(je)) > 0) {
                    OrderSplitConfig splitConfig = new OrderSplitConfig();
                    BeanUtil.copyProperties(config, splitConfig);
                    splitConfig.setLimitJe(bigDecimal.toPlainString());
                    splitConfig.setPage("2");
                    List overLimitSplitByJeArray;
                    if (OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_SL.getKey().equals(splitConfig.getSplitRule())) {
                        overLimitSplitByJeArray = overLimitSplitByDj(orderInfo, orderItemList.get(i), null, splitConfig);
                    } else {
                        overLimitSplitByJeArray = overLimitSplitByJe(orderInfo, orderItemList.get(i), null, splitConfig);
                    }

                    // 添加当前明细前的所有明细
                    if (isTaxIncluded && i > 0) {
                        for (int j = 0; j < i; j++) {
                            OrderInvoiceItemEntity prevItem = originalItemList.get(j);
                            if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(prevItem.getFphxz()) && 
                                !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(prevItem.getFphxz())) {
                                OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                BeanUtil.copyProperties(prevItem, copyItem);
                                orderItemInfoList.add(copyItem);
                            }
                        }
                    }

                    // 添加拆分项
                    orderItemInfoList.addAll(((OrderInvoiceInfoEntity)overLimitSplitByJeArray.get(0)).getItemEntityList());
                    convertAndAdd(orderInfo, orderItemInfoList, resultList);
                    
                    // 更新剩余明细列表
                    orderItemList.subList(0, i + 1).clear();
                    orderItemList.addAll(0, ((OrderInvoiceInfoEntity)overLimitSplitByJeArray.get(1)).getItemEntityList());
                    
                    // 如果是含税模式，添加剩余的其他明细项到当前订单
                    if (isTaxIncluded && i < originalItemList.size() - 1) {
                        for (int j = i + 1; j < originalItemList.size(); j++) {
                            OrderInvoiceItemEntity nextItem = originalItemList.get(j);
                            if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(nextItem.getFphxz()) && 
                                !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(nextItem.getFphxz())) {
                                OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                BeanUtil.copyProperties(nextItem, copyItem);
                                orderItemList.add(copyItem);
                            }
                        }
                    }
                    
                    return resultList;
                }

                if (hjje.compareTo(new BigDecimal(je)) == 0) {
                    orderItemInfoList.add(orderItemList.get(i));
                    
                    // 含税拆分时添加所有其他非折扣明细
                    if (isTaxIncluded) {
                        for (int j = 0; j < originalItemList.size(); j++) {
                            if (j == i) continue; // 跳过当前已添加项
                            OrderInvoiceItemEntity otherItem = originalItemList.get(j);
                            if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(otherItem.getFphxz()) && 
                                !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(otherItem.getFphxz())) {
                                OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                BeanUtil.copyProperties(otherItem, copyItem);
                                orderItemInfoList.add(copyItem);
                            }
                        }
                    }
                    
                    convertAndAdd(orderInfo, orderItemInfoList, resultList);
                    orderItemList.subList(0, i + 1).clear();
                    return resultList;
                }

                orderItemInfoList.add(orderItemList.get(i));
                if (i == orderItemList.size() - 1) {
                    // 含税拆分时添加所有明细
                    if (isTaxIncluded) {
                        for (int j = 0; j < originalItemList.size(); j++) {
                            if (j <= i) continue; // 跳过已处理项
                            OrderInvoiceItemEntity otherItem = originalItemList.get(j);
                            if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(otherItem.getFphxz()) && 
                                !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(otherItem.getFphxz())) {
                                OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                BeanUtil.copyProperties(otherItem, copyItem);
                                orderItemInfoList.add(copyItem);
                            }
                        }
                    }
                    
                    orderItemList.subList(0, i + 1).clear();
                    convertAndAdd(orderInfo, orderItemInfoList, resultList);
                    return resultList;
                }
            } else {
                OrderInvoiceItemEntity discountOrderItem = (OrderInvoiceItemEntity)orderItemList.get(i + 1);
                if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(config.getHsbz())) {
                    if (OrderSplitEnum.ORDER_HSBZ_0.getKey().equals(orderItemInfo.getHsbz())) {
                        hjje = hjje.add(new BigDecimal(orderItemInfo.getJe())).add(new BigDecimal(orderItemInfo.getSe())).add(new BigDecimal(discountOrderItem.getJe())).add(new BigDecimal(discountOrderItem.getSe()));
                    } else {
                        hjje = hjje.add(new BigDecimal(orderItemInfo.getJe())).add(new BigDecimal(discountOrderItem.getJe()));
                    }
                } else {
                    hjje = hjje.add(new BigDecimal(orderItemInfo.getJe())).add(new BigDecimal(discountOrderItem.getJe()));
                }

                if (hjje.compareTo(new BigDecimal(je)) > 0) {
                    if (!OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_SL.getKey().equals(config.getSplitRule()) || !StringUtils.isNotBlank(orderItemList.get(i).getDj()) || (new BigDecimal(orderItemList.get(i).getDj())).compareTo(bigDecimal) <= 0) {
                        OrderSplitConfig splitConfig = new OrderSplitConfig();
                        BeanUtil.copyProperties(config, splitConfig);
                        splitConfig.setLimitJe(bigDecimal.toPlainString());
                        splitConfig.setPage("2");
                        List overLimitSplitByJeArray;
                        if (OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_SL.getKey().equals(splitConfig.getSplitRule())) {
                            overLimitSplitByJeArray = overLimitSplitByDj(orderInfo, orderItemList.get(i), orderItemList.get(i + 1), splitConfig);
                        } else {
                            overLimitSplitByJeArray = overLimitSplitByJe(orderInfo, orderItemList.get(i), orderItemList.get(i + 1), splitConfig);
                        }

                        // 含税拆分时添加前面的其他非折扣明细
                        if (isTaxIncluded && i > 0) {
                            for (int j = 0; j < i; j++) {
                                OrderInvoiceItemEntity prevItem = originalItemList.get(j);
                                if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(prevItem.getFphxz()) && 
                                    !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(prevItem.getFphxz())) {
                                    OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                    BeanUtil.copyProperties(prevItem, copyItem);
                                    orderItemInfoList.add(copyItem);
                                }
                            }
                        }

                        orderItemInfoList.addAll(((OrderInvoiceInfoEntity)overLimitSplitByJeArray.get(0)).getItemEntityList());
                        convertAndAdd(orderInfo, orderItemInfoList, resultList);
                        orderItemList.subList(0, i + 2).clear();
                        orderItemList.addAll(0, ((OrderInvoiceInfoEntity)overLimitSplitByJeArray.get(1)).getItemEntityList());
                        
                        // 含税拆分时添加后面的其他非折扣明细到剩余项中
                        if (isTaxIncluded && i + 2 < originalItemList.size()) {
                            for (int j = i + 2; j < originalItemList.size(); j++) {
                                OrderInvoiceItemEntity nextItem = originalItemList.get(j);
                                if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(nextItem.getFphxz()) && 
                                    !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(nextItem.getFphxz())) {
                                    OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                    BeanUtil.copyProperties(nextItem, copyItem);
                                    orderItemList.add(copyItem);
                                }
                            }
                        }
                        
                        return resultList;
                    }

                    if (i != 0) {
                        // 含税拆分时添加前面的其他非折扣明细
                        if (isTaxIncluded) {
                            for (int j = 0; j < i; j++) {
                                OrderInvoiceItemEntity prevItem = originalItemList.get(j);
                                if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(prevItem.getFphxz()) && 
                                    !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(prevItem.getFphxz())) {
                                    OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                    BeanUtil.copyProperties(prevItem, copyItem);
                                    orderItemInfoList.add(copyItem);
                                }
                            }
                        }
                        
                        convertAndAdd(orderInfo, orderItemInfoList, resultList);
                        orderItemList.subList(0, i).clear();
                        return resultList;
                    }
                } else {
                    if (hjje.compareTo(new BigDecimal(je)) == 0) {
                        orderItemInfoList.add(orderItemList.get(i));
                        orderItemInfoList.add(orderItemList.get(i + 1));
                        
                        // 含税拆分时添加其他非折扣明细
                        if (isTaxIncluded) {
                            for (int j = 0; j < originalItemList.size(); j++) {
                                if (j == i || j == i + 1) continue; // 跳过当前项和折扣项
                                OrderInvoiceItemEntity otherItem = originalItemList.get(j);
                                if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(otherItem.getFphxz()) && 
                                    !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(otherItem.getFphxz())) {
                                    OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                    BeanUtil.copyProperties(otherItem, copyItem);
                                    orderItemInfoList.add(copyItem);
                                }
                            }
                        }
                        
                        convertAndAdd(orderInfo, orderItemInfoList, resultList);
                        orderItemList.subList(0, i + 2).clear();
                        return resultList;
                    }

                    orderItemInfoList.add(orderItemList.get(i));
                    orderItemInfoList.add(orderItemList.get(i + 1));
                    if (i == orderItemList.size() - 2) {
                        // 含税拆分时添加剩余未处理明细
                        if (isTaxIncluded) {
                            for (int j = 0; j < originalItemList.size(); j++) {
                                if (j <= i + 1) continue; // 跳过已处理项
                                OrderInvoiceItemEntity otherItem = originalItemList.get(j);
                                if (!OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(otherItem.getFphxz()) && 
                                    !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(otherItem.getFphxz())) {
                                    OrderInvoiceItemEntity copyItem = new OrderInvoiceItemEntity();
                                    BeanUtil.copyProperties(otherItem, copyItem);
                                    orderItemInfoList.add(copyItem);
                                }
                            }
                        }
                        
                        orderItemList.subList(0, i + 2).clear();
                        convertAndAdd(orderInfo, orderItemInfoList, resultList);
                        return resultList;
                    }
                }
            }
            lastHjje = hjje;
        }
        return resultList;
    }
    private static void convertAndAdd(OrderInvoiceInfoEntity selectByPrimaryKey, List<OrderInvoiceItemEntity> orderItemInfoList, List<OrderInvoiceInfoEntity> resultList) {
        OrderInvoiceInfoEntity orderInfo = new OrderInvoiceInfoEntity();
        BeanUtil.copyProperties(selectByPrimaryKey, orderInfo);
        orderInfo.setItemEntityList(orderItemInfoList);
        resultList.add(orderInfo);
    }
    public static List<OrderInvoiceInfoEntity> overLimitSplitByJe(OrderInvoiceInfoEntity orderInfo,
                                                            OrderInvoiceItemEntity orderItemInfo,
                                                            OrderInvoiceItemEntity disCountOrderItemInfo,
                                                            OrderSplitConfig orderSplitConfig)
        throws OrderSplitException {

        log.debug("订单拆分入参,OrderInfo:{},orderItemInfo:{},orderSplitConfig:{}",
                JsonUtils.getInstance().toJsonString(orderInfo),
                JsonUtils.getInstance().toJsonString(orderItemInfo),
                JsonUtils.getInstance().toJsonString(disCountOrderItemInfo));

        if (orderItemInfo == null || orderSplitConfig == null) {
            throw new OrderSplitException("9999","参数不能为空");
        }

        BigDecimal limit = new BigDecimal(orderSplitConfig.getLimitJe());
        String fpzlDm = orderSplitConfig.getFpzlDm();
        String qdfwlx = orderSplitConfig.getQdfwlx();

        // 含税标志处理
        handleHsbz(orderItemInfo, disCountOrderItemInfo, orderSplitConfig);

        List<OrderInvoiceInfoEntity> orderSplit = new ArrayList<>();
        boolean isContainsSlOrDj = containsSlOrDj(orderItemInfo);

        BigDecimal beforeDivide = calculateBeforeDivide(orderItemInfo, disCountOrderItemInfo);

        if (disCountOrderItemInfo != null) {
            processWithDiscount(orderSplit, orderInfo, orderItemInfo, disCountOrderItemInfo, limit, fpzlDm, qdfwlx, beforeDivide, isContainsSlOrDj, orderSplitConfig);
        } else {
            processWithoutDiscount(orderSplit, orderInfo, orderItemInfo, limit, fpzlDm, qdfwlx, beforeDivide, isContainsSlOrDj, orderSplitConfig);
        }

        BigDecimal afterDivide = calculateAfterDivide(orderSplit);
        if (beforeDivide.compareTo(afterDivide) != 0) {
            log.error("明细拆分前后金额不一致!");
            throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getKey(),
                    OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getValue());
        }

        return orderSplit;
}

private static void handleHsbz(OrderInvoiceItemEntity orderItemInfo,
                               OrderInvoiceItemEntity disCountOrderItemInfo,
                               OrderSplitConfig orderSplitConfig) {
    if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz())) {
        convertToTaxIncluded(orderItemInfo);
        if (disCountOrderItemInfo != null) {
            convertToTaxIncluded(disCountOrderItemInfo);
        }
    }
}

private static void convertToTaxIncluded(OrderInvoiceItemEntity item) {
    if (OrderInfoEnum.HSBZ_0.getKey().equals(item.getHsbz())) {
        String hsje = DecimalCalculateUtil.bigDecimalAddNew(item.getJe(), item.getSe(), 2);
        item.setJe(hsje);
        if (StringUtils.isNotBlank(item.getXmsl())) {
            String newXmdj = DecimalCalculateUtil.divNew(hsje, item.getXmsl(), 20);
            item.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, "", ""));
        }
        item.setHsbz(OrderInfoEnum.HSBZ_1.getKey());
    }
}

private static boolean containsSlOrDj(OrderInvoiceItemEntity orderItemInfo) {
    return StringUtils.isNotBlank(orderItemInfo.getXmsl()) || StringUtils.isNotBlank(orderItemInfo.getDj());
}

private static BigDecimal calculateBeforeDivide(OrderInvoiceItemEntity orderItemInfo,
                                               OrderInvoiceItemEntity disCountOrderItemInfo) {
    BigDecimal beforeDivide = new BigDecimal(orderItemInfo.getJe());
    if (OrderInfoEnum.HSBZ_0.getKey().equals(orderItemInfo.getHsbz()) && StringUtils.isNotBlank(orderItemInfo.getSe())) {
        beforeDivide = beforeDivide.add(new BigDecimal(orderItemInfo.getSe()));
    }
    if (disCountOrderItemInfo != null) {
        if (OrderInfoEnum.HSBZ_0.getKey().equals(disCountOrderItemInfo.getHsbz())) {
            beforeDivide = beforeDivide.add(new BigDecimal(disCountOrderItemInfo.getJe()))
                    .add(new BigDecimal(disCountOrderItemInfo.getSe()));
        } else {
            beforeDivide = beforeDivide.add(new BigDecimal(disCountOrderItemInfo.getJe()));
        }
    }
    return beforeDivide;
}

private static void processWithDiscount(List<OrderInvoiceInfoEntity> orderSplit,
                                        OrderInvoiceInfoEntity orderInfo,
                                        OrderInvoiceItemEntity orderItemInfo,
                                        OrderInvoiceItemEntity disCountOrderItemInfo,
                                        BigDecimal limit,
                                        String fpzlDm,
                                        String qdfwlx,
                                        BigDecimal beforeDivide,
                                        boolean isContainsSlOrDj,
                                        OrderSplitConfig orderSplitConfig) throws OrderSplitException {
    BigDecimal afterDivide = new BigDecimal(orderItemInfo.getJe()).add(new BigDecimal(disCountOrderItemInfo.getJe()));
    BigDecimal zklimit = new BigDecimal(disCountOrderItemInfo.getJe())
            .multiply(limit.divide(afterDivide, 20, RoundingMode.HALF_UP))
            .setScale(2, RoundingMode.DOWN);
    if (zklimit.compareTo(BigDecimal.ZERO) == 0) {
        zklimit = new BigDecimal("-0.01");
    }

    BigDecimal commonLimit = limit.subtract(zklimit);
    BigDecimal avgSl = null;
    if (isContainsSlOrDj) {
        String newXmsl = DecimalCalculateUtil.divNew(commonLimit.toPlainString(), orderItemInfo.getDj(), 20);
        avgSl = new BigDecimal(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
    }

    int page = getPage(orderSplitConfig, new BigDecimal(orderItemInfo.getJe()), commonLimit);
    checkPageLimit(page, orderSplitConfig);

    List<List<OrderInvoiceItemEntity>> orderItemInfos = getOrderItems(orderItemInfo, disCountOrderItemInfo, page, avgSl, commonLimit, zklimit, orderSplitConfig);
    for (List<OrderInvoiceItemEntity> list : orderItemInfos) {
        orderSplit.add(rebuildCommonOrderInfo(orderInfo, list));
    }
}

private static void processWithoutDiscount(List<OrderInvoiceInfoEntity> orderSplit,
                                          OrderInvoiceInfoEntity orderInfo,
                                          OrderInvoiceItemEntity orderItemInfo,
                                          BigDecimal limit,
                                          String fpzlDm,
                                          String qdfwlx,
                                          BigDecimal beforeDivide,
                                          boolean isContainsSlOrDj,
                                          OrderSplitConfig orderSplitConfig) throws OrderSplitException {
    BigDecimal bhsje = new BigDecimal(orderItemInfo.getJe());
    int page = getPage(orderSplitConfig, bhsje, limit);
    checkPageLimit(page, orderSplitConfig);

    BigDecimal zklimit = null;
    if (isContainsSlOrDj) {
        String newXmsl = DecimalCalculateUtil.divNew(limit.toPlainString(), orderItemInfo.getDj(), 20);
        zklimit = new BigDecimal(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
    }

    List<List<OrderInvoiceItemEntity>> orderItemInfos = getOrderItems(orderItemInfo, null, page, zklimit, limit, null, orderSplitConfig);
    for (List<OrderInvoiceItemEntity> list : orderItemInfos) {
        orderSplit.add(rebuildCommonOrderInfo(orderInfo, list));
    }
}

private static int getPage(OrderSplitConfig orderSplitConfig, BigDecimal numerator, BigDecimal denominator) {
    if (StringUtils.isNotBlank(orderSplitConfig.getPage())) {
        return Integer.parseInt(orderSplitConfig.getPage());
    } else {
        int page = numerator.divide(denominator, 0, RoundingMode.HALF_UP).intValue();
        if (denominator.multiply(new BigDecimal(page)).compareTo(numerator) < 0) {
            page++;
        }
        return page;
    }
}

private static void checkPageLimit(int page, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
    if (StringUtils.isNotBlank(orderSplitConfig.getCountUpperLimit()) && page > Integer.parseInt(orderSplitConfig.getCountUpperLimit())) {
        throw new OrderSplitException("9999", "拆分时最大支持拆分为" + orderSplitConfig.getCountUpperLimit() + "个子订单");
    }
}

private static List<List<OrderInvoiceItemEntity>> getOrderItems(OrderInvoiceItemEntity orderItemInfo,
                                                               OrderInvoiceItemEntity disCountOrderItemInfo,
                                                               int page,
                                                               BigDecimal avgSl,
                                                               BigDecimal commonLimit,
                                                               BigDecimal zklimit,
                                                               OrderSplitConfig orderSplitConfig) throws OrderSplitException {
    if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz())) {
        return splitItemTax(orderItemInfo, page, avgSl, commonLimit, disCountOrderItemInfo, zklimit, orderSplitConfig);
    } else {
        return splitItemNoTax(orderItemInfo, page, avgSl, commonLimit, disCountOrderItemInfo, zklimit, orderSplitConfig);
    }
}

private static BigDecimal calculateAfterDivide(List<OrderInvoiceInfoEntity> orderSplit) {
    BigDecimal afterDivide = BigDecimal.ZERO;
    for (OrderInvoiceInfoEntity entity : orderSplit) {
        for (OrderInvoiceItemEntity item : entity.getItemEntityList()) {
            if (OrderInfoEnum.HSBZ_0.getKey().equals(item.getHsbz())) {
                afterDivide = afterDivide.add(new BigDecimal(item.getJe()));
                if (StringUtils.isNotBlank(item.getSe())) {
                    afterDivide = afterDivide.add(new BigDecimal(item.getSe()));
                }
            } else {
                afterDivide = afterDivide.add(new BigDecimal(item.getJe()));
            }
        }
    }
    return afterDivide;
}

    public static List<OrderInvoiceInfoEntity> overLimitSplitByDj(OrderInvoiceInfoEntity orderInfo, OrderInvoiceItemEntity orderItemInfo, OrderInvoiceItemEntity disCountItemInfo, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        List<OrderInvoiceInfoEntity> orderSplit = new ArrayList();
        BigDecimal limit = new BigDecimal(orderSplitConfig.getLimitJe());
        String fpzlDm = orderSplitConfig.getFpzlDm();
        String qdfwlx = orderSplitConfig.getQdfwlx();
        if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz())) {
            String hsje;
            if (OrderInfoEnum.HSBZ_0.getKey().equals(orderItemInfo.getHsbz())) {
                hsje = DecimalCalculateUtil.bigDecimalAddNew(orderItemInfo.getJe(), orderItemInfo.getSe(), 2);
                orderItemInfo.setJe(hsje);
                if (StringUtils.isNotBlank(orderItemInfo.getXmsl())) {
                    String newXmdj = DecimalCalculateUtil.divNew(hsje, orderItemInfo.getXmsl(), 20);
                    orderItemInfo.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
                }
                orderItemInfo.setHsbz(OrderInfoEnum.HSBZ_1.getKey());
            }

            if (disCountItemInfo != null && OrderInfoEnum.HSBZ_0.getKey().equals(disCountItemInfo.getHsbz())) {
                hsje = DecimalCalculateUtil.bigDecimalAddNew(disCountItemInfo.getJe(), disCountItemInfo.getSe(), 2);
                disCountItemInfo.setJe(hsje);
                disCountItemInfo.setHsbz(OrderInfoEnum.HSBZ_1.getKey());
            }
        }

        BigDecimal beforeDivide = new BigDecimal(orderItemInfo.getJe());
        if (OrderInfoEnum.HSBZ_0.getKey().equals(orderItemInfo.getHsbz()) && StringUtils.isNotBlank(orderItemInfo.getSe())) {
            beforeDivide = beforeDivide.add(new BigDecimal(orderItemInfo.getSe()));
        }

        if (disCountItemInfo != null) {
            if (OrderInfoEnum.HSBZ_0.getKey().equals(disCountItemInfo.getHsbz())) {
                beforeDivide = beforeDivide.add(new BigDecimal(disCountItemInfo.getJe())).add(new BigDecimal(disCountItemInfo.getSe()));
            } else {
                beforeDivide = beforeDivide.add(new BigDecimal(disCountItemInfo.getJe()));
            }
        }

        BigDecimal zklimit;
        BigDecimal commonLimit;
        List orderItemInfos;
        BigDecimal afterDivide;
        if (disCountItemInfo != null) {
            if (StringUtils.isNotBlank(disCountItemInfo.getDj())) {
                disCountItemInfo.setDj("");
            }

            if (StringUtils.isNotBlank(disCountItemInfo.getXmsl())) {
                disCountItemInfo.setXmsl("");
            }

            BigDecimal divide = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(orderItemInfo.getDj())) {
                if (OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_SL.getKey().equals(orderSplitConfig.getSplitRule())) {
                    divide = limit.divide(new BigDecimal(orderItemInfo.getDj()), 0, RoundingMode.DOWN);
                    if (divide.compareTo(BigDecimal.ZERO) <= 0) {
                        log.error(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE2.getValue());
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE2);
                    }
                } else {
                    String newXmsl = limit.divide(new BigDecimal(orderItemInfo.getDj()), 20, RoundingMode.DOWN).toPlainString();
                    divide = new BigDecimal(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
                    if (divide.setScale(2, RoundingMode.HALF_EVEN).compareTo(new BigDecimal("0.01")) <= 0) {
                        log.error("拆分后数量小于0.01!");
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE);
                    }
                }

                if (OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fpzlDm) || OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fpzlDm)) {
                    BigDecimal divide1 = limit.divide(new BigDecimal(orderItemInfo.getDj()), 0, RoundingMode.DOWN);
                    if (divide1.compareTo(BigDecimal.ZERO) <= 0) {
                        log.error("单价大于限额无法保证数量是整数!");
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE1);
                    }
                }
                commonLimit = (new BigDecimal(orderItemInfo.getDj())).multiply(divide).setScale(2, RoundingMode.HALF_DOWN);
            } else {
                commonLimit = limit;
            }
            zklimit = (new BigDecimal(disCountItemInfo.getJe())).multiply(commonLimit.divide(new BigDecimal(orderItemInfo.getJe()), 20, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
            if (zklimit.compareTo(BigDecimal.ZERO) == 0) {
                zklimit = (new BigDecimal("0.01")).negate();
            }

            int page;
            if (StringUtils.isNotBlank(orderSplitConfig.getPage())) {
                page = Integer.parseInt(orderSplitConfig.getPage());
            } else {
                page = (int)(new BigDecimal(orderItemInfo.getJe())).divide(commonLimit, 0, RoundingMode.DOWN).longValue();
                if (commonLimit.multiply(new BigDecimal(page)).setScale(2, RoundingMode.HALF_UP).subtract(new BigDecimal(orderItemInfo.getJe())).abs().compareTo(new BigDecimal("0.01")) > 0) {
                    ++page;
                }
            }

            if (StringUtils.isNotBlank(orderSplitConfig.getCountUpperLimit()) && page > Integer.parseInt(orderSplitConfig.getCountUpperLimit())) {
                throw new OrderSplitException("9999", "拆分时最大支持拆分为" + orderSplitConfig.getCountUpperLimit() + "个子订单");
            }
            if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz())) {
                orderItemInfos = splitItemTax(orderItemInfo, page, divide, commonLimit, disCountItemInfo, zklimit, orderSplitConfig);
            } else {
                orderItemInfos = splitItemNoTax(orderItemInfo, page, divide, commonLimit, disCountItemInfo, zklimit, orderSplitConfig);
            }

            OrderInvoiceInfoEntity commonOrderInfo;
            for(Iterator var15 = orderItemInfos.iterator(); var15.hasNext(); orderSplit.add(commonOrderInfo)) {
                List<OrderInvoiceItemEntity> list = (List)var15.next();
                commonOrderInfo = rebuildCommonOrderInfo(orderInfo, list);
            }
        } else {
            zklimit = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(orderItemInfo.getDj())) {
                if (OrderInfoEnum.ORDER_SPLIT_OVERLIMIT_SL.getKey().equals(orderSplitConfig.getSplitRule())) {
                    zklimit = limit.divide(new BigDecimal(orderItemInfo.getDj()), 0, RoundingMode.DOWN);
                    if (zklimit.compareTo(BigDecimal.ZERO) <= 0) {
                        log.error(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE2.getValue());
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE2);
                    }
                } else {
                    String newXmsl = limit.divide(new BigDecimal(orderItemInfo.getDj()), 20, RoundingMode.DOWN).toPlainString();
                    zklimit = new BigDecimal(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
                    if (zklimit.setScale(2, RoundingMode.HALF_EVEN).compareTo(new BigDecimal("0.01")) <= 0) {
                        log.error("拆分后数量小于0.01!");
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE);
                    }
                }

                if (OrderInfoEnum.ORDER_INVOICE_TYPE_42.getKey().equals(fpzlDm) || OrderInfoEnum.ORDER_INVOICE_TYPE_12.getKey().equals(fpzlDm)) {
                    commonLimit = limit.divide(new BigDecimal(orderItemInfo.getDj()), 0, RoundingMode.DOWN);
                    if (commonLimit.compareTo(BigDecimal.ZERO) <= 0) {
                        log.error("单价大于限额无法保证数量是整数!");
                        throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_DJ_OVER_JE1);
                    }
                }

                afterDivide = (new BigDecimal(orderItemInfo.getDj())).multiply(zklimit).setScale(2, RoundingMode.HALF_UP);
            } else {
                afterDivide = limit;
            }

            int page;
            if (StringUtils.isNotBlank(orderSplitConfig.getPage())) {
                page = Integer.parseInt(orderSplitConfig.getPage());
            } else {
                page = (int)(new BigDecimal(orderItemInfo.getJe())).divide(afterDivide, 0, RoundingMode.DOWN).longValue();
                if (afterDivide.multiply(new BigDecimal(page)).setScale(2, RoundingMode.HALF_UP).subtract(new BigDecimal(orderItemInfo.getJe())).abs().compareTo(new BigDecimal("0.01")) > 0) {
                    ++page;
                }
            }

            if (StringUtils.isNotBlank(orderSplitConfig.getCountUpperLimit()) && page > Integer.parseInt(orderSplitConfig.getCountUpperLimit())) {
                throw new OrderSplitException("9999", "拆分时最大支持拆分为" + orderSplitConfig.getCountUpperLimit() + "个子订单");
            }

            if (OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz())) {
                orderItemInfos = splitItemTax(orderItemInfo, page, zklimit, afterDivide, null, null, orderSplitConfig);
            } else {
                orderItemInfos = splitItemNoTax(orderItemInfo, page, zklimit, afterDivide, null, null, orderSplitConfig);
            }

            OrderInvoiceInfoEntity commonOrderInfo;
            for(Iterator var30 = orderItemInfos.iterator(); var30.hasNext(); orderSplit.add(commonOrderInfo)) {
                orderItemInfos = (List)var30.next();
                commonOrderInfo = rebuildCommonOrderInfo(orderInfo, orderItemInfos);
            }
        }

        afterDivide = BigDecimal.ZERO;
        Iterator var24 = orderSplit.iterator();

        while(var24.hasNext()) {
            OrderInvoiceInfoEntity commonOrderInfo = (OrderInvoiceInfoEntity) var24.next();
            Iterator var28 = commonOrderInfo.getItemEntityList().iterator();

            while(var28.hasNext()) {
                OrderInvoiceItemEntity orderItem = (OrderInvoiceItemEntity) var28.next();
                afterDivide = afterDivide.add(new BigDecimal(orderItem.getJe()));
                if (OrderInfoEnum.HSBZ_0.getKey().equals(orderItem.getHsbz()) && StringUtils.isNotBlank(orderItem.getSe())) {
                    afterDivide = afterDivide.add(new BigDecimal(orderItem.getSe()));
                }
            }
        }
        if (beforeDivide.compareTo(afterDivide) != 0) {
            log.error("明细拆分前后金额不一致!");
            throw new OrderSplitException(OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getKey(), OrderSplitErrorMessageEnum.ORDER_SPLIT_ORDERINFO_KPJE_DIFF_ERROR.getValue());
        } else {
            return orderSplit;
        }
    }
    public static List<List<OrderInvoiceItemEntity>> splitItemNoTax(OrderInvoiceItemEntity orderItem, int page, BigDecimal avgSl, BigDecimal pageJe, OrderInvoiceItemEntity discountItem, BigDecimal zklimit, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        List<List<OrderInvoiceItemEntity>> orderSplit = new ArrayList();
        BigDecimal limit = new BigDecimal(orderSplitConfig.getLimitJe());
        BigDecimal avgse;
        OrderInvoiceItemEntity itemInfo;
        if (OrderSplitEnum.ORDER_RESPERATION_0.getKey().equals(orderSplitConfig.getIsReSeparation())) {
            avgse = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(orderItem.getSe())) {
                avgse = (new BigDecimal(orderItem.getSe())).multiply(limit.divide(new BigDecimal(orderItem.getJe()), 20, RoundingMode.HALF_UP));
                avgse = new BigDecimal(DecimalCalculateUtil.decimalSeFormat(avgse));
            }
            if (discountItem != null) {
                DecimalCalculateUtil.decimalSeFormat(avgse);
            }

            Map<String, BigDecimal> map = new HashMap(5);
            map.put("jeAdd", BigDecimal.ZERO);
            map.put("seAdd", BigDecimal.ZERO);
            map.put("slAdd", BigDecimal.ZERO);
            map.put("discountJeAdd", BigDecimal.ZERO);
            map.put("discountSeAdd", BigDecimal.ZERO);

            for(int i = 0; i < page; ++i) {
                if (i == page - 1) {
                    itemInfo = getItem(orderItem, pageJe.toPlainString(), false, map, "1", orderSplitConfig);
                    List<OrderInvoiceItemEntity> list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        itemInfo = getItem(discountItem, zklimit.toPlainString(), true, map, "1", orderSplitConfig);
                        list.add(itemInfo);
                    }

                    orderSplit.add(list);
                } else {
                    List<OrderInvoiceItemEntity> list = new ArrayList();
                    itemInfo = getItem(orderItem, pageJe.toPlainString(), false, map, "0", orderSplitConfig);
                    list.add(itemInfo);
                    if (discountItem != null) {
                        itemInfo = getItem(discountItem, zklimit.toPlainString(), true, map, "0", orderSplitConfig);
                        list.add(itemInfo);
                    }
                    orderSplit.add(list);
                }
            }
        } else {
            avgse = BigDecimal.ZERO;
            if (StringUtils.isNotBlank(orderItem.getSe())) {
                avgse = (new BigDecimal(orderItem.getSe())).multiply(pageJe.divide(new BigDecimal(orderItem.getJe()), 20, RoundingMode.HALF_UP));
                avgse = new BigDecimal(DecimalCalculateUtil.decimalSeFormat(avgse));
            }

            BigDecimal discountAvgSe = BigDecimal.ZERO;
            boolean isFinishDiscount = false;
            BigDecimal leaveDiscountJe = BigDecimal.ZERO;
            if (discountItem != null) {
                leaveDiscountJe = new BigDecimal(discountItem.getJe());
                discountAvgSe = (new BigDecimal(discountItem.getSe())).multiply(zklimit.divide(new BigDecimal(discountItem.getJe()), 20, RoundingMode.HALF_UP));
                discountAvgSe = new BigDecimal(DecimalCalculateUtil.decimalSeFormat(discountAvgSe));
            }

            for(int i = 0; i < page; ++i) {
                ArrayList list;
                OrderInvoiceItemEntity zkItem;
                if (i == page - 1) {
                    itemInfo = getItem(orderItem, pageJe, avgse, page, avgSl, true, orderSplitConfig);
                    list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        if (!isFinishDiscount) {
                            zkItem = getItem(discountItem, zklimit, discountAvgSe, page, (BigDecimal)null, true, orderSplitConfig);
                            list.add(zkItem);
                        } else {
                            itemInfo.setFphxz(OrderInfoEnum.FPHXZ_CODE_0.getKey());
                        }
                    }

                    orderSplit.add(list);
                } else {
                    itemInfo = getItem(orderItem, pageJe, avgse, page, avgSl, false, orderSplitConfig);
                    list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        if (!isFinishDiscount) {
                            leaveDiscountJe = leaveDiscountJe.subtract(zklimit);
                            if (leaveDiscountJe.compareTo(BigDecimal.ZERO) >= 0) {
                                isFinishDiscount = true;
                                zkItem = getItem(discountItem, zklimit, discountAvgSe, i + 1, (BigDecimal)null, true, orderSplitConfig);
                                list.add(zkItem);
                            } else {
                                zkItem = getItem(discountItem, zklimit, discountAvgSe, page, (BigDecimal)null, false, orderSplitConfig);
                                list.add(zkItem);
                            }
                        } else {
                            itemInfo.setFphxz(OrderInfoEnum.FPHXZ_CODE_0.getKey());
                        }
                    }
                    orderSplit.add(list);
                }
            }
        }
        return orderSplit;
    }
    public static List<List<OrderInvoiceItemEntity>> splitItemTax(OrderInvoiceItemEntity orderItem, int page, BigDecimal avgSl, BigDecimal pageJe, OrderInvoiceItemEntity discountItem, BigDecimal zklimit, OrderSplitConfig orderSplitConfig) throws OrderSplitException  {
        List<List<OrderInvoiceItemEntity>> orderSplit = new ArrayList();
        if (OrderSplitEnum.ORDER_RESPERATION_0.getKey().equals(orderSplitConfig.getIsReSeparation())) {
            Map<String, BigDecimal> map = new HashMap(5);
            map.put("jeAdd", BigDecimal.ZERO);
            map.put("seAdd", BigDecimal.ZERO);
            map.put("slAdd", BigDecimal.ZERO);
            map.put("discountJeAdd", BigDecimal.ZERO);
            map.put("discountSeAdd", BigDecimal.ZERO);

            for(int i = 0; i < page; ++i) {
                OrderInvoiceItemEntity itemInfo;
                ArrayList list;
                OrderInvoiceItemEntity zkItem;
                if (i == page - 1) {
                    itemInfo = getItem(orderItem, pageJe.toPlainString(), false, map, "1", orderSplitConfig);
                    list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        zkItem = getItem(discountItem, zklimit.toPlainString(), true, map, "1", orderSplitConfig);
                        list.add(zkItem);
                    }

                    orderSplit.add(list);
                } else {
                    itemInfo = getItem(orderItem, pageJe.toPlainString(), false, map, "0", orderSplitConfig);
                    list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        zkItem = getItem(discountItem, zklimit.toPlainString(), true, map, "0", orderSplitConfig);
                        list.add(zkItem);
                    }

                    orderSplit.add(list);
                }
            }
        } else {
            boolean isFinishDiscount = false;
            BigDecimal leaveDiscountJe = BigDecimal.ZERO;
            if (discountItem != null) {
                leaveDiscountJe = new BigDecimal(discountItem.getJe());
            }

            for(int i = 0; i < page; ++i) {
                OrderInvoiceItemEntity zkItem;
                OrderInvoiceItemEntity itemInfo;
                ArrayList list;
                if (i == page - 1) {
                    itemInfo = getItem(orderItem, pageJe, null, page, avgSl, true, orderSplitConfig);
                    list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        if (!isFinishDiscount) {
                            zkItem = getItem(discountItem, zklimit, (BigDecimal)null, page, (BigDecimal)null, true, orderSplitConfig);
                            list.add(zkItem);
                        } else {
                            itemInfo.setFphxz(OrderInfoEnum.FPHXZ_CODE_0.getKey());
                        }
                    }
                    orderSplit.add(list);
                } else {
                    itemInfo = getItem(orderItem, pageJe, (BigDecimal)null, page, avgSl, false, orderSplitConfig);
                    list = new ArrayList();
                    list.add(itemInfo);
                    if (discountItem != null) {
                        if (!isFinishDiscount) {
                            leaveDiscountJe = leaveDiscountJe.subtract(zklimit);
                            if (leaveDiscountJe.compareTo(BigDecimal.ZERO) >= 0) {
                                isFinishDiscount = true;
                                zkItem = getItem(discountItem, zklimit, (BigDecimal)null, i + 1, (BigDecimal)null, true, orderSplitConfig);
                                list.add(zkItem);
                            } else {
                                zkItem = getItem(discountItem, zklimit, (BigDecimal)null, page, (BigDecimal)null, false, orderSplitConfig);
                                list.add(zkItem);
                            }
                        } else {
                            itemInfo.setFphxz(OrderInfoEnum.FPHXZ_CODE_0.getKey());
                        }
                    }
                    orderSplit.add(list);
                }
            }
        }
        return orderSplit;
    }
    private static OrderInvoiceItemEntity getItem(OrderInvoiceItemEntity orderItem, BigDecimal jeLimit, BigDecimal avgse, int page, BigDecimal avgSl, boolean isLast, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        OrderInvoiceItemEntity orderItemCopy = new OrderInvoiceItemEntity();
        BeanUtil.copyProperties(orderItem, orderItemCopy);
        String fpzlDm = orderSplitConfig.getFpzlDm();
        String qdfwlx = orderSplitConfig.getQdfwlx();
        boolean isTaxIncluded = OrderSplitEnum.ORDER_HSBZ_1.getKey().equals(orderSplitConfig.getHsbz());
        
        if (isLast) {
            BigDecimal xmje = (new BigDecimal(orderItem.getJe())).subtract(jeLimit.multiply(new BigDecimal(page - 1)));
            orderItemCopy.setJe(DecimalCalculateUtil.decimalSeFormat(xmje));
            if (StringUtils.isNotBlank(orderItem.getSe()) && avgse != null) {
                // 含税模式下，需要根据金额比例计算税额
                if (isTaxIncluded) {
                    BigDecimal originalJe = new BigDecimal(orderItem.getJe());
                    BigDecimal originalSe = new BigDecimal(orderItem.getSe());
                    BigDecimal ratio = xmje.divide(originalJe, 10, RoundingMode.HALF_UP);
                    BigDecimal leaveSe = originalSe.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                    orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(leaveSe));
                } else {
                    BigDecimal leaveSe = (new BigDecimal(orderItem.getSe())).subtract(avgse.multiply(new BigDecimal(page - 1)));
                    orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(leaveSe));
                }
            }

            if (avgSl != null && StringUtils.isNotBlank(orderItem.getXmsl())) {
                String xmsl = DecimalCalculateUtil.bigDecimalSub(orderItem.getXmsl(), avgSl.multiply(new BigDecimal(page - 1)).toPlainString(), 20);
                if (DecimalCalculateUtil.stringCompare(xmsl, "0.00") < 0) {
                    throw new OrderSplitException("9999", "订单拆分,拆分最后一行数量小于0");
                }

                if (DecimalCalculateUtil.stringIsZero(xmsl)) {
                    orderItemCopy.setXmsl("");
                    orderItemCopy.setDj("");
                } else {
                    orderItemCopy.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(xmsl, fpzlDm, qdfwlx));
                    String newXmdj = DecimalCalculateUtil.divNew(orderItemCopy.getJe(), orderItemCopy.getXmsl(), 20);
                    orderItemCopy.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
                }
            }
        } else {
            orderItemCopy.setJe(DecimalCalculateUtil.decimalSeFormat(jeLimit));
            if (avgSl != null) {
                if (avgSl.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new OrderSplitException("9999", "订单拆分,拆分后的数量不能小于等于0");
                }

                orderItemCopy.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(avgSl.toPlainString(), fpzlDm, qdfwlx));
                String jsXmje = DecimalCalculateUtil.decimalFormatToStringNew(DecimalCalculateUtil.mul(orderItemCopy.getXmsl(), orderItem.getDj()), 2);
                if (!DecimalCalculateUtil.stringIsEquals(jsXmje, orderItemCopy.getJe())) {
                    recalculateSlOrDj(orderItemCopy, orderSplitConfig);
                }
            }

            if (StringUtils.isNotBlank(orderItem.getSe()) && avgse != null) {
                // 含税模式下，需要根据金额比例计算税额
                if (isTaxIncluded) {
                    BigDecimal originalJe = new BigDecimal(orderItem.getJe());
                    BigDecimal originalSe = new BigDecimal(orderItem.getSe());
                    BigDecimal ratio = jeLimit.divide(originalJe, 10, RoundingMode.HALF_UP);
                    BigDecimal itemSe = originalSe.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                    orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(itemSe));
                } else {
                    orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(avgse));
                }
            }
        }
        return orderItemCopy;
    }
    private static void recalculateSlOrDj(OrderInvoiceItemEntity orderItemCopy, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        String fpzlDm = orderSplitConfig.getFpzlDm();
        String qdfwlx = orderSplitConfig.getQdfwlx();
        String newXmdj = DecimalCalculateUtil.divNew(orderItemCopy.getJe(), orderItemCopy.getXmsl(), 20);
        orderItemCopy.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
        String jsXmje1 = DecimalCalculateUtil.mulNew(orderItemCopy.getXmsl(), orderItemCopy.getDj(), 2);
        if (!DecimalCalculateUtil.stringIsEquals(jsXmje1, orderItemCopy.getJe())) {
            String newXmsl = DecimalCalculateUtil.divNew(orderItemCopy.getJe(), orderItemCopy.getDj(), 20);
            orderItemCopy.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
            String jsXmje2 = DecimalCalculateUtil.mulNew(orderItemCopy.getXmsl(), orderItemCopy.getDj(), 2);
            if (!DecimalCalculateUtil.stringIsEquals(jsXmje2, orderItemCopy.getJe())) {
                throw new OrderSplitException("9999", "单价大于限额订单拆分时,拆分后单价*数量不等于金额");
            }
        }
    }
    private static OrderInvoiceItemEntity getItem(OrderInvoiceItemEntity orderItem, String limit, boolean isDiscountItem, Map<String, BigDecimal> map, String isLast, OrderSplitConfig orderSplitConfig) throws OrderSplitException {
        OrderInvoiceItemEntity orderItemCopy = new OrderInvoiceItemEntity();
        BeanUtil.copyProperties(orderItem, orderItemCopy);
        String fpzlDm = orderSplitConfig.getFpzlDm();
        String qdfwlx = orderSplitConfig.getQdfwlx();
        BigDecimal jeAdd = map.get("jeAdd");
        BigDecimal seAdd = map.get("seAdd");
        BigDecimal slAdd = map.get("slAdd");
        BigDecimal discountJeAdd = map.get("discountJeAdd");
        BigDecimal discountSeAdd = map.get("discountSeAdd");
        BigDecimal xmje;
        BigDecimal se;
        BigDecimal sl;
        if ("0".equals(isLast)) {
            xmje = (new BigDecimal(orderItem.getSe())).multiply(discountJeAdd.divide(new BigDecimal(orderItem.getJe()), 30, RoundingMode.HALF_UP));
            if (isDiscountItem) {
                if (xmje.compareTo(seAdd) > 0) {
                    se = (new BigDecimal(orderItem.getSe())).multiply((new BigDecimal(limit)).divide(new BigDecimal(orderItem.getJe()), 2, RoundingMode.DOWN));
                } else {
                    se = (new BigDecimal(orderItem.getSe())).multiply((new BigDecimal(limit)).divide(new BigDecimal(orderItem.getJe()), 2, RoundingMode.UP));
                }
            } else if (xmje.compareTo(discountSeAdd) > 0) {
                se = (new BigDecimal(orderItem.getSe())).multiply((new BigDecimal(limit)).divide(new BigDecimal(orderItem.getJe()), 2, RoundingMode.DOWN));
            } else {
                se = (new BigDecimal(orderItem.getSe())).multiply((new BigDecimal(limit)).divide(new BigDecimal(orderItem.getJe()), 2, RoundingMode.UP));
            }

            orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(se));
            if (StringUtils.isNotBlank(orderItem.getXmsl())) {
                BigDecimal shouldSl = (new BigDecimal(orderItem.getXmsl())).multiply(jeAdd.divide(new BigDecimal(orderItem.getJe()), 30, RoundingMode.HALF_UP));
                String xmsl;
                if (shouldSl.compareTo(slAdd) > 0) {
                    xmsl = (new BigDecimal(orderItem.getXmsl())).multiply((new BigDecimal(limit)).divide(new BigDecimal(orderItem.getJe()), 20, RoundingMode.DOWN)).toPlainString();
                    sl = new BigDecimal(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(xmsl, fpzlDm, qdfwlx));
                } else {
                    xmsl = (new BigDecimal(orderItem.getXmsl())).multiply((new BigDecimal(limit)).divide(new BigDecimal(orderItem.getJe()), 20, RoundingMode.UP)).toPlainString();
                    sl = new BigDecimal(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(xmsl, fpzlDm, qdfwlx));
                }

                orderItemCopy.setXmsl(DecimalCalculateUtil.decimalSeFormat(sl));
                slAdd = slAdd.add(sl);
            }

            orderItemCopy.setJe(limit);
            if (!isDiscountItem) {
                jeAdd = jeAdd.add(new BigDecimal(limit));
                seAdd = seAdd.add(se);
                map.put("jeAdd", jeAdd);
                map.put("seAdd", seAdd);
            }
            map.put("slAdd", slAdd);
        } else {
            if (isDiscountItem) {
                xmje = (new BigDecimal(orderItem.getJe())).subtract(discountJeAdd);
                if (StringUtils.isNotBlank(orderItem.getSe())) {
                    se = (new BigDecimal(orderItem.getSe())).subtract(discountSeAdd);
                    orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(se));
                }
            } else {
                xmje = (new BigDecimal(orderItem.getJe())).subtract(jeAdd);
                if (StringUtils.isNotBlank(orderItem.getSe())) {
                    se = (new BigDecimal(orderItem.getSe())).subtract(seAdd);
                    orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(se));
                }
            }

            orderItemCopy.setJe(DecimalCalculateUtil.decimalSeFormat(xmje));
            if (StringUtils.isNotBlank(orderItem.getXmsl())) {
                String newXmsl = DecimalCalculateUtil.bigDecimalSub(orderItem.getXmsl(), slAdd.toPlainString(), 20);
                orderItemCopy.setXmsl(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmsl, fpzlDm, qdfwlx));
                sl = (new BigDecimal(orderItemCopy.getXmsl())).multiply(new BigDecimal(orderItemCopy.getDj())).subtract(new BigDecimal(orderItemCopy.getJe()));
                if (DecimalCalculateUtil.stringCompareAbs(sl.toPlainString(), "0.01") > 0) {
                    if (Double.parseDouble(orderItemCopy.getXmsl()) == 0.0) {
                        throw new OrderSplitException("9999", "订单金额误差大于0.01");
                    }

                    String newXmdj = DecimalCalculateUtil.divNew(orderItemCopy.getJe(), orderItemCopy.getXmsl(), 20);
                    orderItemCopy.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
                }
            }
        }
        return orderItemCopy;
    }

    public static OrderInvoiceInfoEntity rebuildCommonOrderInfo(OrderInvoiceInfoEntity orderInfo, List<OrderInvoiceItemEntity> subList) {
        if (orderInfo == null) {
            throw new IllegalArgumentException("订单信息不能为空");
        }
        if (subList == null || subList.isEmpty()) {
            throw new IllegalArgumentException("订单明细列表不能为空");
        }

        OrderInvoiceInfoEntity comm = new OrderInvoiceInfoEntity();
        BeanUtil.copyProperties(orderInfo, comm);
        
        // 检查是否是已经精确设置了金额的订单，如果是则不重新计算
        if ("EXACT_AMOUNT".equals(orderInfo.getByzd1())) {
            comm.setJshj(orderInfo.getJshj());
            comm.setHjbhsje(orderInfo.getHjbhsje());
            comm.setKpse(orderInfo.getKpse());
            comm.setItemEntityList(new ArrayList<>(subList));
            return comm;
        }

        String totalAmountWithoutTax = "0.00"; // 合计不含税金额
        String totalTax = "0.00";             // 合计税额
        String totalWithTax = "0.00";         // 含税金额合计

        for (OrderInvoiceItemEntity orderItem : subList) {
            if (orderItem == null) {
                continue; // 跳过 null 元素，防止 NPE
            }

            if (OrderInfoEnum.HSBZ_0.getKey().equals(orderItem.getHsbz())) {
                // 不含税模式
                totalAmountWithoutTax = DecimalCalculateUtil.bigDecimalAdd(totalAmountWithoutTax, orderItem.getJe());
                if (StringUtils.isNotBlank(orderItem.getSe())) {
                    totalTax = DecimalCalculateUtil.bigDecimalAdd(totalTax, orderItem.getSe());
                }
            } else {
                // 含税模式
                totalWithTax = DecimalCalculateUtil.bigDecimalAdd(totalWithTax, orderItem.getJe());
                // 如果明细中包含税额信息，累加到总税额中
                if (StringUtils.isNotBlank(orderItem.getSe())) {
                    totalTax = DecimalCalculateUtil.bigDecimalAdd(totalTax, orderItem.getSe());
                    // 计算不含税金额
                    String itemAmountWithoutTax = DecimalCalculateUtil.bigDecimalSub(orderItem.getJe(), orderItem.getSe());
                    totalAmountWithoutTax = DecimalCalculateUtil.bigDecimalAdd(totalAmountWithoutTax, itemAmountWithoutTax);
                }
            }

            String xmsl = orderItem.getXmsl();
            if (StringUtils.isBlank(xmsl)) {
                xmsl = "0";
            }
            if (DecimalCalculateUtil.stringIsZero(xmsl)) {
                // 不建议修改原对象，除非明确需要
                OrderInvoiceItemEntity newItem = new OrderInvoiceItemEntity();
                BeanUtil.copyProperties(orderItem, newItem);
                newItem.setJe(orderItem.getJe());
                newItem.setSe(orderItem.getSe());
                newItem.setHsbz(orderItem.getHsbz());
            }
        }

        boolean isTaxIncluded = OrderInfoEnum.HSBZ_1.getKey().equals(subList.get(0).getHsbz());

        if (isTaxIncluded) {
            // 含税模式
            comm.setJshj(totalWithTax);  // 价税合计等于含税金额
            comm.setHjbhsje(totalAmountWithoutTax); // 合计不含税金额
            comm.setKpse(totalTax);      // 开票税额
        } else {
            // 不含税模式
            String totalIncludingTax = DecimalCalculateUtil.bigDecimalAdd(totalAmountWithoutTax, totalTax);
            comm.setHjbhsje(totalAmountWithoutTax);
            comm.setJshj(totalIncludingTax);
            comm.setKpse(totalTax);
        }

        comm.setItemEntityList(new ArrayList<>(subList)); // 避免引用外部列表
        return comm;
    }


    private static OrderInvoiceItemEntity getItemByFixedSl(OrderInvoiceItemEntity orderItem, String limit, BigDecimal avgse, int page, String isLast, OrderSplitConfig orderSplitConfig) {
        OrderInvoiceItemEntity orderItemCopy = new OrderInvoiceItemEntity();
        BeanUtil.copyProperties(orderItem, orderItemCopy);
        String fpzlDm = orderSplitConfig.getFpzlDm();
        String qdfwlx = orderSplitConfig.getQdfwlx();
        if ("1".equals(isLast)) {
            BigDecimal xmje = (new BigDecimal(orderItem.getJe())).subtract((new BigDecimal(limit)).multiply(new BigDecimal(page - 1)));
            orderItemCopy.setJe(DecimalCalculateUtil.decimalSeFormat(xmje));
            if (StringUtils.isNotBlank(orderItem.getSe()) && avgse != null) {
                BigDecimal leaveSe = (new BigDecimal(orderItem.getSe())).subtract(avgse.multiply(new BigDecimal(page - 1))).setScale(2, RoundingMode.HALF_UP);
                orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(leaveSe));
            }

            String newXmdj = DecimalCalculateUtil.divNew(xmje.toPlainString(), orderItem.getXmsl(), 20);
            orderItemCopy.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
        } else {
            orderItemCopy.setJe(DecimalCalculateUtil.decimalSeFormat(new BigDecimal(limit)));
            if (StringUtils.isNotBlank(orderItem.getSe()) && avgse != null) {
                orderItemCopy.setSe(DecimalCalculateUtil.decimalSeFormat(avgse));
            }
            String newXmdj = DecimalCalculateUtil.divNew(limit, orderItem.getXmsl(), 20);
            orderItemCopy.setDj(DecimalCalculateUtil.dynamicDecimalFormatToStringWithoutZero(newXmdj, fpzlDm, qdfwlx));
        }
        return orderItemCopy;
    }
    /**
     * 保存拆分订单信息
     * @param invoiceInfoEntityList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderSplitInfo(List<OrderInvoiceInfoEntity> invoiceInfoEntityList) {
        if(CollectionUtils.isNotEmpty(invoiceInfoEntityList)){
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = invoiceInfoEntityList.get(0);
            //将原订单信息置为已删除
            OrderInvoiceInfoEntity oldOrderInfo = orderInvoiceInfoDao.selectById(orderInvoiceInfoEntity.getYddid());
            oldOrderInfo.setIsDelete("1");
            oldOrderInfo.setUpdateTime(new Date());
            oldOrderInfo.setUpdateBy(ssoUtil.getUserName());
            orderInvoiceInfoDao.updateById(oldOrderInfo);
            
            // 打印拆分订单列表详情，检查是否有精度变化
            log.info("开始保存拆分订单，拆分订单数量: {}", invoiceInfoEntityList.size());
            for (int i = 0; i < invoiceInfoEntityList.size(); i++) {
                OrderInvoiceInfoEntity entity = invoiceInfoEntityList.get(i);
                log.info("保存前的拆分订单[{}]: ID={}, 价税合计={}, 不含税金额={}, 税额={}, 明细数量={}", 
                         i, entity.getId(), entity.getJshj(), entity.getHjbhsje(), entity.getKpse(), 
                         entity.getItemEntityList().size());
                
                // 检查明细税额之和是否等于订单税额
                BigDecimal itemTaxSum = BigDecimal.ZERO;
                for (OrderInvoiceItemEntity item : entity.getItemEntityList()) {
                    if (StringUtils.isNotBlank(item.getSe())) {
                        log.info("订单[{}]明细税额: {}", i, item.getSe());
                        itemTaxSum = itemTaxSum.add(new BigDecimal(item.getSe()));
                    }
                }
                log.info("订单[{}]税额验证: 订单税额={}, 明细税额之和={}, 差额={}", 
                         i, entity.getKpse(), itemTaxSum.toPlainString(), 
                         new BigDecimal(entity.getKpse()).subtract(itemTaxSum).toPlainString());
                
                // 如果明细税额之和与订单税额不一致，调整最后一个明细项的税额
                if (itemTaxSum.compareTo(new BigDecimal(entity.getKpse())) != 0) {
                    log.info("订单[{}]明细税额之和与订单税额不一致，进行调整", i);
                    for (int j = entity.getItemEntityList().size() - 1; j >= 0; j--) {
                        OrderInvoiceItemEntity item = entity.getItemEntityList().get(j);
                        if (StringUtils.isNotBlank(item.getSe())) {
                            BigDecimal adjustment = new BigDecimal(entity.getKpse()).subtract(itemTaxSum);
                            BigDecimal newItemSe = new BigDecimal(item.getSe()).add(adjustment);
                            log.info("调整明细项[{}]的税额: {} -> {}, 调整量: {}", 
                                   j, item.getSe(), newItemSe.toPlainString(), adjustment.toPlainString());
                            item.setSe(newItemSe.toPlainString());
                            break;
                        }
                    }
                }
            }
            
            for (OrderInvoiceInfoEntity commonOrderInfo : invoiceInfoEntityList) {
                // 记录保存前的原始值
                String originalJshj = commonOrderInfo.getJshj();
                String originalHjbhsje = commonOrderInfo.getHjbhsje();
                String originalKpse = commonOrderInfo.getKpse();
                String isExactAmount = commonOrderInfo.getByzd1();
                
                // 确保价税合计是带两位小数的格式
                if (originalJshj.contains(".")) {
                    String[] parts = originalJshj.split("\\.");
                    if (parts.length > 1 && parts[1].length() < 2) {
                        // 小数位不足2位，补足
                        originalJshj = parts[0] + "." + String.format("%-2s", parts[1]).replace(' ', '0');
                        log.info("调整价税合计格式为2位小数: {}", originalJshj);
                        commonOrderInfo.setJshj(originalJshj);
                    }
                } else {
                    // 没有小数点，添加.00
                    originalJshj = originalJshj + ".00";
                    log.info("添加价税合计小数位: {}", originalJshj);
                    commonOrderInfo.setJshj(originalJshj);
                }
                
                //保存新的订单信息
                commonOrderInfo.setCreateTime(new Date());
                commonOrderInfo.setCreateBy(ssoUtil.getUserName());
                commonOrderInfo.setUpdateTime(new Date());
                commonOrderInfo.setUpdateBy(ssoUtil.getUserName());
                commonOrderInfo.setIsDelete("0");
                commonOrderInfo.setDdscrq(new Date());
                
                // 确保金额没有被格式化或修改
                if ("EXACT_AMOUNT".equals(isExactAmount)) {
                    if (!originalJshj.equals(commonOrderInfo.getJshj())) {
                        log.warn("价税合计在保存前被修改，原值: {}, 当前值: {}", originalJshj, commonOrderInfo.getJshj());
                        commonOrderInfo.setJshj(originalJshj);
                    }
                    if (!originalHjbhsje.equals(commonOrderInfo.getHjbhsje())) {
                        log.warn("不含税金额在保存前被修改，原值: {}, 当前值: {}", originalHjbhsje, commonOrderInfo.getHjbhsje());
                        commonOrderInfo.setHjbhsje(originalHjbhsje);
                    }
                    if (!originalKpse.equals(commonOrderInfo.getKpse())) {
                        log.warn("税额在保存前被修改，原值: {}, 当前值: {}", originalKpse, commonOrderInfo.getKpse());
                        commonOrderInfo.setKpse(originalKpse);
                    }
                }
                
                // 记录保存操作前的值
                log.info("准备保存订单, ID={}, 价税合计={}", commonOrderInfo.getId(), commonOrderInfo.getJshj());
                
                int insertResult = orderInvoiceInfoDao.insert(commonOrderInfo);
                if (insertResult <= 0) {
                    log.error("保存订单失败, ID={}", commonOrderInfo.getId());
                    throw new RuntimeException("保存订单失败");
                }
                
                // 获取保存后的订单，验证金额是否正确
                OrderInvoiceInfoEntity savedEntity = orderInvoiceInfoDao.selectById(commonOrderInfo.getId());
                if (savedEntity != null) {
                    log.info("保存后的订单: ID={}, 价税合计={}, 数据库中的值={}", 
                             savedEntity.getId(), originalJshj, savedEntity.getJshj());
                    
                    // 检查是否有精度变化
                    if (!originalJshj.equals(savedEntity.getJshj())) {
                        log.warn("价税合计在保存过程中发生变化, 原值: {}, 数据库中的值: {}", 
                                 originalJshj, savedEntity.getJshj());
                    }
                }
                
                // 保存明细项前，先验证每个明细项的税额
                BigDecimal itemTaxSum = BigDecimal.ZERO;
                for (OrderInvoiceItemEntity item : commonOrderInfo.getItemEntityList()) {
                    if (StringUtils.isNotBlank(item.getSe())) {
                        itemTaxSum = itemTaxSum.add(new BigDecimal(item.getSe()));
                    }
                }
                
                if (itemTaxSum.compareTo(new BigDecimal(commonOrderInfo.getKpse())) != 0) {
                    log.warn("保存明细前税额验证: 订单税额={}, 明细税额之和={}, 差额={}", 
                            commonOrderInfo.getKpse(), itemTaxSum, 
                            new BigDecimal(commonOrderInfo.getKpse()).subtract(itemTaxSum));
                }
                
                for (OrderInvoiceItemEntity orderItem : commonOrderInfo.getItemEntityList()){
                    //保存新的订单明细
                    orderItem.setId(DistributedKeyMaker.generateShotKey());
                    orderItem.setOrderInvoiceId(commonOrderInfo.getId());
                    orderItem.setCreateTime(new Date());
                    orderItem.setCreateBy(ssoUtil.getUserName());
                    orderItem.setUpdateTime(new Date());
                    orderItem.setUpdateBy(ssoUtil.getUserName());
                    orderItem.setIsDelete("0");
                    
                    log.info("保存明细项: 金额={}, 税额={}, 数量={}, 单价={}", 
                             orderItem.getJe(), orderItem.getSe(), orderItem.getXmsl(), orderItem.getDj());
                    
                    orderInvoiceItemDao.insert(orderItem);
                }
            }
            
            // 验证所有订单是否正确保存
            log.info("所有拆分订单保存完毕，验证保存结果");
            for (OrderInvoiceInfoEntity entity : invoiceInfoEntityList) {
                OrderInvoiceInfoEntity savedEntity = orderInvoiceInfoDao.selectById(entity.getId());
                if (savedEntity != null) {
                    log.info("验证已保存订单: ID={}, 价税合计={}, 不含税金额={}, 税额={}", 
                             savedEntity.getId(), savedEntity.getJshj(), 
                             savedEntity.getHjbhsje(), savedEntity.getKpse());
                    
                    // 验证保存后的明细税额之和
                    List<OrderInvoiceItemEntity> savedItems = orderInvoiceItemDao.selectItemListById(savedEntity.getId());
                    if (savedItems != null && !savedItems.isEmpty()) {
                        BigDecimal savedItemTaxSum = BigDecimal.ZERO;
                        for (OrderInvoiceItemEntity item : savedItems) {
                            if (StringUtils.isNotBlank(item.getSe())) {
                                savedItemTaxSum = savedItemTaxSum.add(new BigDecimal(item.getSe()));
                            }
                        }
                        
                        log.info("保存后税额验证: 订单税额={}, 明细税额之和={}, 差额={}", 
                                savedEntity.getKpse(), savedItemTaxSum, 
                                new BigDecimal(savedEntity.getKpse()).subtract(savedItemTaxSum));
                    }
                } else {
                    log.error("订单保存失败, 无法找到ID={}", entity.getId());
                }
            }
        }
    }
}
