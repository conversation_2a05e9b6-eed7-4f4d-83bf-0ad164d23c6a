package com.dxhy.order.modules.entity;

import lombok.Data;

/**
 * @Auther: admin
 * @Date: 2023/8/23 16:40
 * @Description:
 */
@Data
public class ApiHzkpqrxxmxReq {
    //蓝字发票明细序号
    private String lzmxxh;
    //序号
    private String xh;
    //商品和服务税收分类合并编码
    private String sphfwssflhbbm;
    //项目名称
    private String xmmc;
    //规格型号
    private String ggxh;
    //单位
    private String dw;
    //单价
    private String fpspdj;
    //数量
    private String fpspsl;
    //金额
    private String je;
    //税率
    private String slv;
    //税额
    private String se;






}
