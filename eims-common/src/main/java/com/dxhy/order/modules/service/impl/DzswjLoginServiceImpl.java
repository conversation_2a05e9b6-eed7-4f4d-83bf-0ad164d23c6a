package com.dxhy.order.modules.service.impl;

import com.dxhy.order.config.OrderConfig;
import com.dxhy.order.constant.RedisConstant;
import com.dxhy.order.modules.entity.ApiLoginReqBO;
import com.dxhy.order.modules.entity.InvoiceIssueRes;
import com.dxhy.order.modules.service.DzswjLoginService;
import com.dxhy.order.pojo.HbhxGetSessionCommonReq;
import com.dxhy.order.pojo.HbhxGetSessionCommonResp;
import com.dxhy.order.pojo.HbhxGetSessionReq;
import com.dxhy.order.pojo.HbhxHhxx;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 附加信息表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/28 16:26
 * @Version 1.0
 **/
@Service("dzswjLoginService")
@Slf4j
public class DzswjLoginServiceImpl  implements DzswjLoginService {

    @Resource
    private OrderConfig orderConfig;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    private static final String LOGGER = "(电子税局登录)";
    @Override
    public InvoiceIssueRes dzswjLogin(ApiLoginReqBO apiLoginReqBO) {


        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);
        log.info("{}，登录接口数据入参: {}", LOGGER, invoiceInfoReString);
        String resString = HttpUtils.doPost(orderConfig.getTaxBureauLoginUrl(), invoiceInfoReString);
        log.info("{}，登录接口数据出参: {}", LOGGER,resString);
        R r = JsonUtils.getInstance().parseObject(resString, R.class);


        return InvoiceIssueRes.res(r.get("code").toString(),r.get("msg").toString(),r.get("data"));
    }

    @Override
    public InvoiceIssueRes setSms(ApiLoginReqBO apiLoginReqBO) {
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);
        log.info("{}，设置登录验证码入参: {}", LOGGER, invoiceInfoReString);
        String resString = HttpUtils.doPost(orderConfig.getSetSmsUrl(), invoiceInfoReString);
        log.info("{}，设置登录验证码出参: {}", LOGGER,resString);
        R r = JsonUtils.getInstance().parseObject(resString, R.class);


        return InvoiceIssueRes.res(r.get("code").toString(),r.get("msg").toString());
    }

    @Override
    public InvoiceIssueRes getConfirmQrcode(ApiLoginReqBO apiLoginReqBO) {
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);
        log.info("{}，获取实名二维码入参: {}", LOGGER, invoiceInfoReString);
        String resString = HttpUtils.doPost(orderConfig.getConfirmQrcodeUrl(), invoiceInfoReString);
        log.info("{}，获取实名二维码出参: {}", LOGGER,resString);
        R r = JsonUtils.getInstance().parseObject(resString, R.class);


        return InvoiceIssueRes.res(r.get("code").toString(),r.get("msg").toString(),r.get("data"));
    }

    @Override
    public InvoiceIssueRes getConfirmStatus(ApiLoginReqBO apiLoginReqBO) {
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);
        log.info("{}，获取实名状态入参: {}", LOGGER, invoiceInfoReString);
        String resString = HttpUtils.doPost(orderConfig.getConfirmStatusUrl(), invoiceInfoReString);
        log.info("{}，获取实名状态出参: {}", LOGGER,resString);
        R r = JsonUtils.getInstance().parseObject(resString, R.class);


        return InvoiceIssueRes.res(r.get("code").toString(),r.get("msg").toString(),r.get("data"));
    }

    @Override
    public InvoiceIssueRes getSessionInfo(ApiLoginReqBO apiLoginReqBO) {


        //redis key "HBHX:TAX_NAME:" + 税号    获取可信账号
        String key = String.format(RedisConstant.REDIS_DZSWJ_ZH, apiLoginReqBO.getNsrsbh());
        String value = (String) redisTemplate.opsForValue().get(key);
        Thread.currentThread().getId();
        log.info("{}获取电局登录信息通过Redis key:{},获取到的信息为:{}",Thread.currentThread().getId(),key,value);
        if(StringUtils.isBlank(value)){
            return InvoiceIssueRes.error("9999","纳税人识别号"+apiLoginReqBO.getNsrsbh()+"获取到的可信账号为空");
        }
        //调河北航信接口获取登录信息
        HbhxGetSessionReq hbhxGetSessionReq = new HbhxGetSessionReq();
        hbhxGetSessionReq.setDlsf("130000");
        hbhxGetSessionReq.setQqly(orderConfig.getXtbs());
        hbhxGetSessionReq.setQysh(apiLoginReqBO.getNsrsbh());
        hbhxGetSessionReq.setKxzh(value);
        hbhxGetSessionReq.setHqlx("01");
        HbhxGetSessionCommonReq hbhxGetSessionCommonReq = new HbhxGetSessionCommonReq();
        hbhxGetSessionCommonReq.setXtbs(orderConfig.getXtbs());
        hbhxGetSessionCommonReq.setSjc(String.valueOf(System.currentTimeMillis()));
        // 公钥 bC2BRC_47=934f21   私钥   0cA_232f6C12=AEb   签名秘钥  Dxhtxxsignatur7345361879
        String datagram = SM2Utils.encryptBase64(orderConfig.getGy(), JsonUtils.getInstance().toJsonString(hbhxGetSessionReq));
        hbhxGetSessionCommonReq.setDatagram(datagram);
        String data = hbhxGetSessionCommonReq.getXtbs() + hbhxGetSessionCommonReq.getSjc() + datagram+ orderConfig.getQmmy();
        String qm = Sha256Utils.sha256(data);
        hbhxGetSessionCommonReq.setQm(qm);
        log.info("{}调用河北航信获取电局登录信息接口,请求报文:{}",Thread.currentThread().getId(),JsonUtils.getInstance().toJsonString(hbhxGetSessionCommonReq));
        String s = HttpUtils.doPost(orderConfig.getSessionInfoUrl(), JsonUtils.getInstance().toJsonString(hbhxGetSessionCommonReq));

        //1000-接口调用成功 不一定登录成功
        HbhxGetSessionCommonResp hbhxGetSessionCommonResp = JsonUtils.getInstance().fromJson(s,HbhxGetSessionCommonResp.class);
        log.info("{}调用河北航信获取电局登录信息接口,返回报文:{}",Thread.currentThread().getId(),JsonUtils.getInstance().toJsonString(hbhxGetSessionCommonResp));
        if("1000".equals(hbhxGetSessionCommonResp.getCode()) ){
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(hbhxGetSessionCommonResp.getDatagram());
            if("HHZX_SYS_0000".equals(jsonObject.getString("code"))){
                com.alibaba.fastjson.JSONObject data1 = jsonObject.getJSONObject("data").getJSONObject("hhxx");
                HbhxHhxx hbhxHhxx = JsonUtils.getInstance().fromJson(data1.toJSONString(), HbhxHhxx.class);
                return InvoiceIssueRes.ok(hbhxHhxx.getSd());
            }
            return InvoiceIssueRes.error(jsonObject.getString("code"),jsonObject.getString("msg"));
        }else{
            return InvoiceIssueRes.error("9999","纳税人识别号"+apiLoginReqBO.getNsrsbh()+"未获取到登录信息");
        }

    }
    public static void main(String[] args) {
        String s = "{\"code\":\"1000\",\"datagram\":\"{\\\"code\\\":\\\"HHZX_SYS_0000\\\",\\\"msg\\\":\\\"交易处理成功\\\",\\\"sign\\\":null,\\\"data\\\":{\\\"hhsxx\\\":{\\\"hhskey\\\":\\\"jszt:hhzx:1000552f2431d72743c49674dfeadf4d0011_lock:,5338ccf5f2374c1e8f7f4e6627076f05\\\",\\\"gqsj\\\":120,\\\"sdyw\\\":\\\"DXQST_B7E77162CCCB11EFAFF10CC47A934F28\\\"},\\\"hhxx\\\":{\\\"gs\\\":null,\\\"kx\\\":null,\\\"sd\\\":{\\\"dzfp-ssotoken\\\":\\\"0befba90020f4c118dee228756fe3835\\\",\\\"SSO_SECURITY_CHECK_TOKEN\\\":\\\"f724013431314481ab77aba3ce4b41b8\\\",\\\"bitNum\\\":\\\"3\\\",\\\"tokenKey\\\":\\\"dfa31f2ef3214bc88f6127382457f45c\\\"},\\\"jdj\\\":null,\\\"zf\\\":null,\\\"dwsb\\\":null,\\\"xdj\\\":null,\\\"xdjsb\\\":null,\\\"xdjszc\\\":null,\\\"yzmid\\\":null,\\\"sjh\\\":null,\\\"dlsf\\\":\\\"130000\\\"}}}\",\"msg\":\"\"}";
        HbhxGetSessionCommonResp hbhxGetSessionCommonResp = JsonUtils.getInstance().fromJson(s,HbhxGetSessionCommonResp.class);
        log.info("{}调用河北航信获取电局登录信息接口,返回报文:{}",Thread.currentThread().getId(),JsonUtils.getInstance().toJsonString(hbhxGetSessionCommonResp));
        if("1000".equals(hbhxGetSessionCommonResp.getCode()) ){
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(hbhxGetSessionCommonResp.getDatagram());
            if("HHZX_SYS_0000".equals(jsonObject.getString("code"))){
                com.alibaba.fastjson.JSONObject data1 = jsonObject.getJSONObject("data").getJSONObject("hhxx");
                HbhxHhxx hbhxHhxx = JsonUtils.getInstance().fromJson(data1.toJSONString(), HbhxHhxx.class);
                System.out.println(hbhxHhxx.getSd());
            }
            System.out.println(hbhxGetSessionCommonResp.getDatagram());
        }else{
            System.out.println("9999");
        }
    }
}
