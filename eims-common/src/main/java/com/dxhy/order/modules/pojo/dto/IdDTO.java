package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * ID DTO
 *
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ID DTO")
public class IdDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(name = "ID", required = true)
    private String id;

    private String baseNsrsbh;
}
