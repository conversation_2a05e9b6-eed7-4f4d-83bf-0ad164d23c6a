package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单状态操作记录表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-23 11:36:51
 */
@TableName("order_status_opt_record")
public class OrderStatusOptRecordEntity extends BasePage implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 订单号
	 */
	private String ddh;
	/**
	 * 订单开票表主键
	 */
	private String orderInvoiceId;
	/**
	 * 订单开票操作主键
	 */
	private String orderInvoiceFreshId;
	/**
	 * 订单操作记录状态(0: 合并 ,1: 退回)
	 */
	private String orderOptStatus;
	/**
	 * 逻辑删除
	 */
	private String isDelete;
	/**
	 * 0 合并同类明细
	 * 1 不合并同类明细
	 */
	private String byzd1;
	/**
	 * 备用字段2
	 */
	private String byzd2;
	/**
	 * 备用字段3
	 */
	private String byzd3;
	/**
	 * 备用字段4
	 */
	private String byzd4;
	/**
	 * 备用字段5
	 */
	private String byzd5;
	/**
	 * 备用字段6
	 */
	private String byzd6;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 设置：主键
	 */
	public void setId(String id) {
		this.id = id;
	}
	/**
	 * 获取：主键
	 */
	public String getId() {
		return id;
	}
	/**
	 * 设置：订单号
	 */
	public void setDdh(String ddh) {
		this.ddh = ddh;
	}
	/**
	 * 获取：订单号
	 */
	public String getDdh() {
		return ddh;
	}
	/**
	 * 设置：订单开票表主键
	 */
	public void setOrderInvoiceId(String orderInvoiceId) {
		this.orderInvoiceId = orderInvoiceId;
	}
	/**
	 * 获取：订单开票表主键
	 */
	public String getOrderInvoiceId() {
		return orderInvoiceId;
	}
	/**
	 * 设置：订单开票操作主键
	 */
	public void setOrderInvoiceFreshId(String orderInvoiceFreshId) {
		this.orderInvoiceFreshId = orderInvoiceFreshId;
	}
	/**
	 * 获取：订单开票操作主键
	 */
	public String getOrderInvoiceFreshId() {
		return orderInvoiceFreshId;
	}
	/**
	 * 设置：订单操作记录状态(0: 合并 ,1: 退回)
	 */
	public void setOrderOptStatus(String orderOptStatus) {
		this.orderOptStatus = orderOptStatus;
	}
	/**
	 * 获取：订单操作记录状态(0: 合并 ,1: 退回)
	 */
	public String getOrderOptStatus() {
		return orderOptStatus;
	}
	/**
	 * 设置：逻辑删除
	 */
	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}
	/**
	 * 获取：逻辑删除
	 */
	public String getIsDelete() {
		return isDelete;
	}
	/**
	 * 设置： 0 同类明细合并,保总数量相同
	 *          1 同类明细合并,保总单价相同
	 *          2 不合并同类明细
	 */
	public void setByzd1(String byzd1) {
		this.byzd1 = byzd1;
	}
	/**
	 * 获取：备用字段1
	 */
	public String getByzd1() {
		return byzd1;
	}
	/**
	 * 设置：备用字段2
	 */
	public void setByzd2(String byzd2) {
		this.byzd2 = byzd2;
	}
	/**
	 * 获取：备用字段2
	 */
	public String getByzd2() {
		return byzd2;
	}
	/**
	 * 设置：备用字段3
	 */
	public void setByzd3(String byzd3) {
		this.byzd3 = byzd3;
	}
	/**
	 * 获取：备用字段3
	 */
	public String getByzd3() {
		return byzd3;
	}
	/**
	 * 设置：备用字段4
	 */
	public void setByzd4(String byzd4) {
		this.byzd4 = byzd4;
	}
	/**
	 * 获取：备用字段4
	 */
	public String getByzd4() {
		return byzd4;
	}
	/**
	 * 设置：备用字段5
	 */
	public void setByzd5(String byzd5) {
		this.byzd5 = byzd5;
	}
	/**
	 * 获取：备用字段5
	 */
	public String getByzd5() {
		return byzd5;
	}
	/**
	 * 设置：备用字段6
	 */
	public void setByzd6(String byzd6) {
		this.byzd6 = byzd6;
	}
	/**
	 * 获取：备用字段6
	 */
	public String getByzd6() {
		return byzd6;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
	/**
	 * 设置：创建人
	 */
	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}
	/**
	 * 获取：创建人
	 */
	public String getCreateBy() {
		return createBy;
	}
	/**
	 * 设置：更新时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	/**
	 * 获取：更新时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}
	/**
	 * 设置：更新人
	 */
	public void setUpdateBy(String updateBy) {
		this.updateBy = updateBy;
	}
	/**
	 * 获取：更新人
	 */
	public String getUpdateBy() {
		return updateBy;
	}
}
