package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 红字开票信息录入 返回对象
 */
@Data
public class ApiHzkpqrxxlrReqBO implements Serializable {
    private static final long serialVersionUID = -7991482183313394887L;

    /**
     * 购销方选择 0 销售方，1 购买方
     */
    private String gxfxz;
    /**
     * 蓝字全电发票号码
     */
    private String lzqdfphm;
    /**
     * 冲红原因 以下内容中选择【开票有误、销货退回、服务中止、销售折让】。其中, 商品服务编码为以 1(货物)、2(劳务)开头的冲红原因不允许“服务中止”；
     */
    private String chyymc;
    /**
     * 开票日期 格式：yyyy-mm-dd
     */
    private String kprq;
    /**
     * 纳税人识别号
     */
    private String nsrsbh;
    //原始发票代码
    private String ysfpdm;
    //原始发票号码
    private String ysfphm;
    //红字确认信息明细
    private List<ApiHzkpqrxxmxReq> hzkpqrxxmx;
    //系统来源
    private String xtly;

}
