package com.dxhy.order.modules.pojo.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 上传客户信息 客户信息BO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel("上传客户信息 客户信息BO")
public class CustomerUpdateExcelBO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "客户分类名称", index = 0)
    @ApiModelProperty("客户分类名称")
    private String khflmc;

    @ExcelProperty(value = "客户名称", index = 1)
    @ApiModelProperty("客户名称")
    private String khmc;

    @ExcelProperty(value = "统一社会信用代码/纳税人识别号", index = 2)
    @ApiModelProperty("统一社会信用代码/纳税人识别号")
    private String nsrsbh;

    @ExcelProperty(value = "简码", index = 3)
    @ApiModelProperty("简码")
    private String jm;

    @ExcelProperty(value = "地址", index = 4)
    @ApiModelProperty("地址")
    private String dz;

    @ExcelProperty(value = "电话", index = 5)
    @ApiModelProperty("电话")
    private String dh;

    @ExcelProperty(value = "开户行名称", index = 6)
    @ApiModelProperty("开户行名称")
    private String khhmc;

    @ExcelProperty(value = "银行账号", index = 7)
    @ApiModelProperty("银行账号")
    private String yhzh;

    @ExcelProperty(value = "联系邮箱", index = 8)
    @ApiModelProperty("联系邮箱")
    private String lxyx;

    @ExcelProperty(value = "是否默认地址", index = 9)
    @ApiModelProperty("是否默认地址 Y-是 N-否")
    private String sfmrdz;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    public void clearNull(){
        khflmc = khflmc == null ? "" : khflmc;
        khmc = khmc == null ? "" : khmc;
        nsrsbh = nsrsbh == null ? "" : nsrsbh;
        jm = jm == null ? "" : jm;
        dz = dz == null ? "" : dz;
        dh = dh == null ? "" : dh;
        khhmc = khhmc == null ? "" : khhmc;
        yhzh = yhzh == null ? "" : yhzh;
        lxyx = lxyx == null ? "" : lxyx;
        sfmrdz = sfmrdz == null ? "" : sfmrdz;
        errorMsg = errorMsg == null ? "" : errorMsg;
    }

    @Override
    public boolean equals(Object o){
        CustomerUpdateExcelBO data = (CustomerUpdateExcelBO)o;
        if (StringUtils.isNotEmpty(data.getKhflmc())
                && data.getKhflmc().equals(this.khflmc)
                && StringUtils.isNotEmpty(data.getKhmc())
                && data.getKhmc().equals(this.khmc)
                && StringUtils.isNotEmpty(data.getNsrsbh())
                && data.getNsrsbh().equals(this.nsrsbh)) {
            return true;
        }
        return false;
    }

    @Override
    public int hashCode(){
        return (khflmc + khmc + nsrsbh).hashCode();
    }

}
