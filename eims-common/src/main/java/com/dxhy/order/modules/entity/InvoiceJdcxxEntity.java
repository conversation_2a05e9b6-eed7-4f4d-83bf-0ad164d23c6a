package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-机动车信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_jdcxx")
@Data
public class InvoiceJdcxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 机动车信息主键
	 */
	@ApiModelProperty(value = "机动车信息主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("车辆类型")
	private String cllx;

	@ApiModelProperty("厂牌型号")
	private String cpxh;

	@ApiModelProperty("产地")
	private String clcd;

	@ApiModelProperty("合格证号")
	private String hgzh;

	@ApiModelProperty("进口证明书号")
	private String jkzmsh;

	@ApiModelProperty("商检单号")
	private String sjdh;

	@ApiModelProperty("发动机号码")
	private String fdjhm;

	@ApiModelProperty("车辆识别单号")
	private String clsbdh;

	@ApiModelProperty("完税凭证号码")
	private String wspzhm;

	@ApiModelProperty("吨位")
	private String dw;

	@ApiModelProperty("限乘人数")
	private String xcrs;

	@ApiModelProperty("车辆识别代号uuid")
	private String clsbdhuuid;

	@ApiModelProperty("生产企业名称")
	private String scqymc;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
