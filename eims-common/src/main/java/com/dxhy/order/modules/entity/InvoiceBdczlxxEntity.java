package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-不动产租赁信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_bdczlxx")
@Data
public class InvoiceBdczlxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 不动产租赁主键
	 */
	@ApiModelProperty(value = "不动产租赁主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("序号")
	private String xh;

	@ApiModelProperty("不动产地区")
	private String bdcdq;

	@ApiModelProperty("不动产地址")
	private String bdcdz;

	@ApiModelProperty("跨地(市)标志")
	private String kdsbz;

	@ApiModelProperty("房屋产权证书号/不动产权证号")
	private String cqzsh;

	@ApiModelProperty("面积单位")
	private String mjdw;

	/**
	 * 1.数电平台:
	 * 税收分类编码为 3040502020200000000(停车 费)时，租赁期起止格式为
	 * yyyy-MM-dd HH:mm yyyy-MM-dd HH:mm， 中间用英文空格隔开,
	 * 例如:2022-12-01 12:00 2022-12-01 16:00 其他税编时租赁期起止格式为
	 * yyyy-MM-dd yyyy-MM-dd，中间用英文空格隔 开,例如 2022-12-01 2022-12-02
	 * 2.乐企:
	 * 租赁期起止格式为
	 * yyyy-MM-dd yyyy-MM-dd，中间用英文空格隔 开,例如 2022-12-01 2022-12-02
	 */
	@ApiModelProperty("租赁期起止")
	private String zlqqz;

	@ApiModelProperty("车牌号")
	private String cph;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
