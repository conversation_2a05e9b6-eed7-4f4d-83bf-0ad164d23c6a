package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户信息分类表
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@TableName("customer_group")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("客户信息分类表")
public class CustomerGroupEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户信息分类主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("客户信息分类主键")
    private String id;

    /**
     * 上级客户信息分类
     */
    @ApiModelProperty("上级客户信息分类")
    private String parentId;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String baseNsrsbh;

    /**
     * 客户分类名称
     */
    @ApiModelProperty("客户分类名称")
    private String khflmc;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
}
