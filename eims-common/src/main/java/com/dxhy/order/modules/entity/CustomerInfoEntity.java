package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户信息表
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@TableName("customer_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("客户信息表")
public class CustomerInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户信息主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("客户信息主键")
    private String id;

    /**
     * 客户分类主键
     */
    @ApiModelProperty("客户分类主键")
    private String parentId;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty("所属纳税人识别号")
    private String baseNsrsbh;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String gsmc;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String nsrsbh;

    /**
     * 公司地址
     */
    @ApiModelProperty("公司地址")
    private String gsdz;

    /**
     * 公司电话
     */
    @ApiModelProperty("公司电话")
    private String gsdh;

    /**
     * 开户银行
     */
    @ApiModelProperty("开户银行")
    private String khyh;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String yhzh;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String lxr;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String sjhm;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String scode;

    /**
     * 插入类型 1 手动录入
     */
    @ApiModelProperty("插入类型 1 手动录入")
    private String intoType;

    /**
     * 逻辑删除
     */
    @ApiModelProperty("逻辑删除 0 正常 1 已删除")
    private String isDelete;

    /**
     * 备用字段1
     */
    @ApiModelProperty("备用字段1")
    private String byzd1;

    /**
     * 备用字段2
     */
    @ApiModelProperty("备用字段2")
    private String byzd2;

    /**
     * 备用字段3
     */
    @ApiModelProperty("备用字段3")
    private String byzd3;

    /**
     * 备用字段4
     */
    @ApiModelProperty("备用字段4")
    private String byzd4;

    /**
     * 备用字段5
     */
    @ApiModelProperty("备用字段5")
    private String byzd5;

    /**
     * 备用字段6
     */
    @ApiModelProperty("备用字段6")
    private String byzd6;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
}
