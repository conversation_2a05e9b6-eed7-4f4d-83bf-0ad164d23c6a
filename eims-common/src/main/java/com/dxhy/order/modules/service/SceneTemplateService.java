package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.SceneTemplateEntity;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.dto.SceneTemplateListDTO;
import com.dxhy.order.modules.pojo.dto.SceneTemplateSaveDTO;
import com.dxhy.order.utils.R;

/**
 * 场景模板表 service
 * <AUTHOR>
 * @Date 2022/6/27 12:10
 * @Version 1.0
 **/
public interface SceneTemplateService extends IService<SceneTemplateEntity> {

    /**
     * 查询列表
     * @param sceneTemplateListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R queryPage(SceneTemplateListDTO sceneTemplateListDTO);

    /**
     * 查询列表
     * @param sceneTemplateListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listWithoutPage(SceneTemplateListDTO sceneTemplateListDTO);

    /**
     * 根据ID查询数据
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R queryDataById(String id);

    /**
     * 新增或修改附加信息 新增时返回数据ID
     * @param sceneTemplateSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveData(SceneTemplateSaveDTO sceneTemplateSaveDTO);

    /**
     * 删除数据
     * @param idsDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R deleteData(IdsDTO idsDTO);

    /**
     * 查询场景模板并更新到数据库
     * @return
     */
    R getSceneTemplateTask(String baseNsrsbh);

}

