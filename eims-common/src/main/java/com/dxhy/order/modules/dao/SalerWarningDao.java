package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.SalerWarningInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【saler_warning(发票预警表)】的数据库操作Mapper
* @createDate 2022-07-21 13:39:13
* @Entity generator.domain.SalerWarning
*/
@Mapper
public interface SalerWarningDao extends BaseMapper<SalerWarningInfo> {


    /**
     * 根据纳税人识别号 查询数据
     */
    SalerWarningInfo selectWarnInfoByNsrsbh(String nsrsbh);

    /**
     * 查询所有状态为启用的预警信息
     */
    List<SalerWarningInfo> selectWarnInfoQy();

    /**
     * 根据id更新三个上限额度：月度 季度 年度
     */
    int updateWarnInfoById(@Param("salerWarningInfo") SalerWarningInfo salerWarningInfo);

    /**
     * 新增一条数据
     */
    int insert(SalerWarningInfo salerWarningInfo);

}
