package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceLkysfwxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 旅客运输服务信息的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceLkysfwxxService extends IService<InvoiceLkysfwxxEntity> {
    void saveBatch(List<InvoiceLkysfwxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
