package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-旅客运输服务信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceIssueLkysfwxxParam implements Serializable{
	private static final long serialVersionUID = 1L;
	/**
	 * 出行序号
	 */
	private String cxrxh;

	/**
	 * 出行人
	 */
	private String cxr;

	/**
	 * 出行日期
	 */
	private String cxrq;

	/**
	 * 出行人证件类型
	 */
	private String cxrzjlx;

	/**
	 * 出行人证件号码
	 */
	private String cxrzjhm;

	/**
	 * 旅客运输出发地
	 */
	private String lkyscfd;

	/**
	 * 旅客运输到达地
	 */
	private String lkysddd;

	/**
	 * 交通工具类型
	 */
	private String jtgjlx;

	/**
	 * 座位等级
	 */
	private String zwdj;
}
