package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.ItemGroupEntity;
import com.dxhy.order.modules.pojo.dto.ItemGroupSaveDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目信息分类表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface ItemGroupDao extends BaseMapper<ItemGroupEntity> {

    /**
     * 根据纳税人识别号查询项目分类
     * @param baseNsrsbh
     * @return java.util.List<com.dxhy.order.modules.entity.ItemGroupEntity>
     * <AUTHOR>
     **/
    List<ItemGroupEntity> listByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 新增和修改时候查重项目分类
     * @param itemGroupSaveDTO
     * @return java.util.List<com.dxhy.order.modules.entity.ItemGroupEntity>
     * <AUTHOR>
     **/
    List<ItemGroupEntity> selectListByItemGroupSaveDTO(ItemGroupSaveDTO itemGroupSaveDTO);

    /**
     * 根据父级ID查询下级ID列表（删除用）
     * @param parentId
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     **/
    List<String> listIdByParentId(@Param("parentId") String parentId);

    /**
     * 根据纳税人识别号查询二级项目信息分类（excel上传数据时用）
     * @param
     * @return java.util.List<com.dxhy.order.modules.entity.ItemGroupEntity>
     * <AUTHOR>
     **/
    List<ItemGroupEntity> listLevelTwoDataByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 批量保存数据
     * @param itemGroupEntityList
     * @return void
     * <AUTHOR>
     **/
    void insertList(@Param("customerGroupEntityList") List<ItemGroupEntity> itemGroupEntityList);

}
