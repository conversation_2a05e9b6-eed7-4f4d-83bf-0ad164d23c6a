package com.dxhy.order.modules.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.config.OrderConfig;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.model.dxOpenPlatform.EWMXX;
import com.dxhy.order.modules.dao.ETaxAccountDao;
import com.dxhy.order.modules.entity.ApiLoginReqBO;
import com.dxhy.order.modules.entity.ETaxAccountEntity;
import com.dxhy.order.modules.entity.GlobalInfo;
import com.dxhy.order.modules.service.DzswjLoginService;
import com.dxhy.order.modules.service.ETaxAccountService;
import com.dxhy.order.utils.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


@Service("eTaxAccountService")
@lombok.extern.slf4j.Slf4j
public class ETaxAccountServiceImpl extends ServiceImpl<ETaxAccountDao, ETaxAccountEntity> implements ETaxAccountService {


    @Resource
    private OrderConfig orderConfig;

    @Resource
    private DzswjLoginService dzswjLoginService;

    @Override
    public R activeEtaxAccount(ETaxAccountEntity eTaxAccountEntity) {

        //调用大象接口 激活账号
        // 获取token
        String access_token = DxUtils.getDxToken(orderConfig.getAppkey(), orderConfig.getAppSecret(), orderConfig.getTokenUrl());
        if (StringUtils.isEmpty(access_token)) {
            return R.error();
        }
        // 组装
        String reqUrl = orderConfig.getActiveUrl() + ConfigureConstant.STRING_OPEN_API_ACCESS_TOKEN + access_token;
        Map mapReq = new HashMap();
        mapReq.put("NSRSBH",eTaxAccountEntity.getNsrsbh());
        mapReq.put("YHM",eTaxAccountEntity.getYhm());
        mapReq.put("MM",eTaxAccountEntity.getMm());
        mapReq.put("DLFS","1");

        String content = JsonUtils.getInstance().toJsonString(mapReq);
        String reqJson = DxUtils.buildGlobalInfoCheck(content, orderConfig.getEntcode());
        String resp = HttpUtils.doPostDx(reqUrl, reqJson);

        GlobalInfo globalInfo = JsonUtils.getInstance().parseObject(resp, GlobalInfo.class);
        if (!ObjectUtils.isEmpty(globalInfo) && "0000".equals(globalInfo.getReturnStateInfo().getReturnCode())) {
            Map map = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(globalInfo.getContent()), HashMap.class);
            String result = map.get("ZTDM").toString();
            if("000000".equals(result)){
                eTaxAccountEntity.setStatus("1");//1-激活状态
                baseMapper.updateById(eTaxAccountEntity);
                return R.ok();
            }else{
                return R.error(map.get("ZTXX").toString());
            }
        }

        return R.error("激活失败,请联系管理员");
    }

    @Override
    public R login(ETaxAccountEntity eTaxAccountEntity) {
        // 获取token
//        String access_token = DxUtils.getDxToken(orderConfig.getAppkey(), orderConfig.getAppSecret(), orderConfig.getTokenUrl());
//        if (StringUtils.isEmpty(access_token)) {
//            return R.error();
//        }
//        // 组装
//        String reqUrl = orderConfig.getLoginUrl() + ConfigureConstant.STRING_OPEN_API_ACCESS_TOKEN + access_token;
//        Map mapReq = new HashMap();
//        mapReq.put("NSRSBH",eTaxAccountEntity.getNsrsbh());
//        mapReq.put("FFM",eTaxAccountEntity.getFfm());
//        mapReq.put("YHM",eTaxAccountEntity.getYhm());
//        mapReq.put("MM",eTaxAccountEntity.getMm());
//        mapReq.put("ZZRLX",eTaxAccountEntity.getMm());
//        mapReq.put("DXYZM",eTaxAccountEntity.getDxyzm());
//
//        String content = JsonUtils.getInstance().toJsonString(mapReq);
//        String reqJson = DxUtils.buildGlobalInfoCheck(content, orderConfig.getEntcode());
//        String resp = HttpUtils.doPostDx(reqUrl, reqJson);
//
//        GlobalInfo globalInfo = JsonUtils.getInstance().parseObject(resp, GlobalInfo.class);
//        if (!ObjectUtils.isEmpty(globalInfo) && "0000".equals(globalInfo.getReturnStateInfo().getReturnCode())) {
//
//            DxQdResponse dxQdResponse = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(globalInfo.getContent()), DxQdResponse.class);
//            String result = dxQdResponse.getZtdm();
//            if("000000".equals(result)){
//                DLXX dxll = dxQdResponse.getDLXX();
//                return R.ok().put("data",dxll);
//            }else {
//                return R.error(dxQdResponse.getZtxx());
//            }
//        }
//
//
//
//        return R.error("登陆失败,请联系管理员");

        ApiLoginReqBO apiLoginReqBO = new ApiLoginReqBO();
        apiLoginReqBO.setNsrsbh(eTaxAccountEntity.getNsrsbh());
        apiLoginReqBO.setDzswjyh(eTaxAccountEntity.getYhm());
        apiLoginReqBO.setDzswjmm(eTaxAccountEntity.getMm());
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);

        String resString = HttpUtils.doPost(orderConfig.getTaxBureauLoginUrl(), invoiceInfoReString);
        log.info("{}，登录接口数据出参: {}", "电子税局登录",resString);
        return JsonUtils.getInstance().parseObject(resString, R.class);


    }

    @Override
    public R getAuthQrcode(ETaxAccountEntity eTaxAccountEntity) {
        // 获取token
//        String access_token = DxUtils.getDxToken(orderConfig.getAppkey(), orderConfig.getAppSecret(), orderConfig.getTokenUrl());
//        if (StringUtils.isEmpty(access_token)) {
//            return R.error();
//        }
//        // 组装
//        String reqUrl = orderConfig.getAuthQrcodeUrl() + ConfigureConstant.STRING_OPEN_API_ACCESS_TOKEN + access_token;
//        Map mapReq = new HashMap();
//        mapReq.put("NSRSBH",eTaxAccountEntity.getNsrsbh());
//
//        String content = JsonUtils.getInstance().toJsonString(mapReq);
//        String reqJson = DxUtils.buildGlobalInfoCheck(content, orderConfig.getEntcode());
//        String resp = HttpUtils.doPostDx(reqUrl, reqJson);
//
//        GlobalInfo globalInfo = JsonUtils.getInstance().parseObject(resp, GlobalInfo.class);
//        if (!ObjectUtils.isEmpty(globalInfo) && "0000".equals(globalInfo.getReturnStateInfo().getReturnCode())) {
//            DxQdResponse dxQdResponse = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(globalInfo.getContent()), DxQdResponse.class);
//            String result = dxQdResponse.getZtdm();
//            if("000000".equals(result)){
//                EWMXX ewm = dxQdResponse.getEWMXX();
//                ewm.setEwm(QrCodeUtil.drawLogoQrCode(null,ewm.getEwm(),null,null));
//                return R.ok().put("data",ewm);
//            }else {
//                return R.error(dxQdResponse.getZtxx());
//            }
//        }
//
//        return R.error("获取认证二维码失败,请联系管理员");


        ApiLoginReqBO apiLoginReqBO = new ApiLoginReqBO();
        apiLoginReqBO.setNsrsbh(eTaxAccountEntity.getNsrsbh());
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);

        String resString = HttpUtils.doPost(orderConfig.getConfirmQrcodeUrl(), invoiceInfoReString);
        log.info("{}，获取实名二维码出参: {}", "电子税局登录",resString);
        R r = JsonUtils.getInstance().parseObject(resString, R.class);
        JSONObject data = (JSONObject) r.get("data");
        String qrCode = data.get("qrCode").toString();
        EWMXX ewm = new EWMXX();


        ewm.setEwm(QrCodeUtil.drawLogoQrCode(null,qrCode,null,null));
        return R.ok().put("data",ewm);


    }

    @Override
    public R getAuthStatus(ETaxAccountEntity eTaxAccountEntity) {
        // 获取token
//        String access_token = DxUtils.getDxToken(orderConfig.getAppkey(), orderConfig.getAppSecret(), orderConfig.getTokenUrl());
//        if (StringUtils.isEmpty(access_token)) {
//            return R.error();
//        }
//        // 组装
//        String reqUrl = orderConfig.getAuthStatusUrl() + ConfigureConstant.STRING_OPEN_API_ACCESS_TOKEN + access_token;
//        Map mapReq = new HashMap();
//        mapReq.put("NSRSBH",eTaxAccountEntity.getNsrsbh());
//        mapReq.put("RZID",eTaxAccountEntity.getRzid());
//        String content = JsonUtils.getInstance().toJsonString(mapReq);
//        String reqJson = DxUtils.buildGlobalInfoCheck(content, orderConfig.getEntcode());
//        String resp = HttpUtils.doPostDx(reqUrl, reqJson);
//
//        GlobalInfo globalInfo = JsonUtils.getInstance().parseObject(resp, GlobalInfo.class);
//        if (!ObjectUtils.isEmpty(globalInfo) && "0000".equals(globalInfo.getReturnStateInfo().getReturnCode())) {
//            Map map = JsonUtils.getInstance().parseObject(Base64Encoding.decodeToString(globalInfo.getContent()), HashMap.class);
//            String dlxx = map.get("RZZTXX").toString();
//            return R.ok().put("data",JsonUtils.getInstance().toJsonString(dlxx));
//        }
//
//        return R.error("获取认证二维码失败,请联系管理员");

        ApiLoginReqBO apiLoginReqBO = new ApiLoginReqBO();
        apiLoginReqBO.setNsrsbh(eTaxAccountEntity.getNsrsbh());
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);

        String resString = HttpUtils.doPost(orderConfig.getConfirmStatusUrl(), invoiceInfoReString);
        log.info("{}，获取实名状态出参: {}", "电子税局登录",resString);
        return JsonUtils.getInstance().parseObject(resString, R.class);
    }

    @Override
    public R selectByNsrsbh(ETaxAccountEntity eTaxAccountEntity) {
        Page page = new Page(eTaxAccountEntity.getCurrent(),eTaxAccountEntity.getSize());

        baseMapper.selectPage(page,new LambdaQueryWrapper<ETaxAccountEntity>().eq(ObjectUtil.isNotEmpty(eTaxAccountEntity.getNsrsbh()),ETaxAccountEntity::getNsrsbh,eTaxAccountEntity.getNsrsbh()));
        return R.ok().put("page",new PageUtils(page));
    }

    @Override
    public R setSms(ETaxAccountEntity eTaxAccountEntity) {
        ApiLoginReqBO apiLoginReqBO = new ApiLoginReqBO();
        apiLoginReqBO.setNsrsbh(eTaxAccountEntity.getNsrsbh());
        apiLoginReqBO.setYzm(eTaxAccountEntity.getDxyzm());
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(apiLoginReqBO);

        String resString = HttpUtils.doPost(orderConfig.getSetSmsUrl(), invoiceInfoReString);
        log.info("{}，设置登录验证码出参: {}", "电子税局登录",resString);
        return JsonUtils.getInstance().parseObject(resString, R.class);
    }
}