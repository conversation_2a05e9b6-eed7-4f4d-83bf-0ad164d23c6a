package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.TaxpayerInfo;
import com.dxhy.order.pojo.DeptInfo;
import com.dxhy.order.utils.R;


/**
* <AUTHOR>
* @description 针对表【taxpayer_info(销方企业信息表)】的数据库操作Service
* @createDate 2022-06-29 17:13:17
*/
public interface TaxpayerInfoService extends IService<TaxpayerInfo> {

    TaxpayerInfo selectZsxedByNsrsbh(String nsrsbh);

    R insertDept(DeptInfo deptInfo) throws  Exception;
}
