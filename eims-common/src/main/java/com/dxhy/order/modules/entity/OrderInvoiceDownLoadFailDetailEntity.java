package com.dxhy.order.modules.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrderInvoiceDownLoadFailDetailEntity {
    // 应收单号
    @ExcelProperty(value = "应收单号*",index = 0)
    private String ysdh;
    // 发票流水号
    @ExcelProperty(value = "订单号*",index = 1)
    private String fpqqlsh;
    // 发票类型
    @ExcelProperty(value = "发票类型*",index = 2)
    private String fpzlDm;
    // 是否含税
    @ExcelProperty(value = "是否含税*",index = 3)
    private String hsbz;
    // 受票方自然人标识
    @ExcelProperty(value = "受票方自然人标识",index = 4)
    private String spfzrrbs;
    // 购买方名称
    @ExcelProperty(value = "购买方名称*",index = 5)
    private String ghfMc;
    // 购买方纳税人识别号
    @ExcelProperty(value = "购买方纳税人识别号",index = 6)
    private String ghfNsrsbh;
    // 购买方地址
    @ExcelProperty(value = "购买方地址",index = 7)
    private String ghfDz;
    // 购买方电话
    @ExcelProperty(value = "购买方电话",index = 8)
    private String ghfDh;
    // 购买方开户银行
    @ExcelProperty(value = "购买方开户银行",index = 9)
    private String ghfYh;
    // 购买方银行账号
    @ExcelProperty(value = "购买方银行账号",index = 10)
    private String ghfZh;
    // 备注
    @ExcelProperty(value = "备注",index = 11)
    private String bz;
    // 购买方邮箱
    @ExcelProperty(value = "购买方邮箱",index = 12)
    private String ghfYx;
    // 购买方经办人证件类型
    @ExcelProperty(value = "购买方经办人姓名",index = 13)
    private String gmfjbrxm;
    // 购买方经办人证件类型
    @ExcelProperty(value = "购买方经办人证件类型",index = 14)
    private String jbrzjlx;
    // 购买方经办人证件号码
    @ExcelProperty(value = "购买方经办人证件号码",index = 15)
    private String jbrzjhm;
    // 经办人国籍(地区)
    @ExcelProperty(value = "经办人国籍(地区)",index = 16)
    private String jbrgj;
    // 经办人自然人纳税人识别号
    @ExcelProperty(value = "经办人自然人纳税人识别号",index = 17)
    private String jbrnsrsbh;
    // 小规模非免税开具
    /*@ExcelProperty(value = "小规模非免税开具原因",index = 18)
    private String xgmfmskjyy; */
    //放弃享受减按1%征收率原因
    @ExcelProperty(value = "放弃享受减按1%征收率原因", index = 18)
    private String giveUpReason;
    @ExcelProperty(value = "交付手机",index = 19)
    private String ghfSj;
    @ExcelProperty(value = "交付邮箱",index = 20)
    private String jfyx;
}
