package com.dxhy.order.modules.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import org.springframework.web.multipart.MultipartFile;

/**
 * 订单开票明细表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 18:31:05
 */
public interface OrderInvoiceItemService extends IService<OrderInvoiceItemEntity> {

    PageUtils queryPage(OrderInvoiceItemEntity orderInvoiceItemEntity);

    R uploadInvoiceItemInfo(MultipartFile file, String hsbs, String baseNsrsbh);
}

