package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceJdcxxDao;
import com.dxhy.order.modules.entity.InvoiceJdcxxEntity;
import com.dxhy.order.modules.service.InvoiceJdcxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 机动车的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceJdcxxService")
@Slf4j
@RefreshScope
public class InvoiceJdcxxServiceImpl extends ServiceImpl<InvoiceJdcxxDao, InvoiceJdcxxEntity>
        implements InvoiceJdcxxService {

    @Override
    public void saveBatch(List<InvoiceJdcxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceJdcxxEntity> jdcxxWrapper = Wrappers.lambdaUpdate();
        jdcxxWrapper.set(InvoiceJdcxxEntity::getIsDelete, "1");
        jdcxxWrapper.eq(InvoiceJdcxxEntity::getOrderInvoiceInfoId, invoiceId);
        jdcxxWrapper.eq(InvoiceJdcxxEntity::getInvoiceTdywId, tdywId);
        this.update(jdcxxWrapper);
    }
}




