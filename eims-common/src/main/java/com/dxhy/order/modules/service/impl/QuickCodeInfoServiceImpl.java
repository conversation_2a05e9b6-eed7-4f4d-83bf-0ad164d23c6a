package com.dxhy.order.modules.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.RedisConstant;
import com.dxhy.order.model.*;
import com.dxhy.order.modules.dao.*;
import com.dxhy.order.modules.service.QuickCodeInfoService;
import com.dxhy.order.modules.service.RedisService;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 静态码业务层
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
@Service
@Slf4j
public class QuickCodeInfoServiceImpl implements QuickCodeInfoService {
	
	@Resource
	InvoiceTypeCodeExtMapper invoiceTypeCodeExtMapper;
	
	@Resource
	QuickResponseCodeInfoMapper quickResponseCodeInfoMapper;
	
	@Resource
	QuickResponseCodeItemInfoMapper quickResponseCodeItemInfoMapper;
	
	@Resource
	EwmConfigInfoMapper ewmConfigInfoMapper;
	
	@Resource
	EwmConfigItemInfoMapper ewmConfigItemInfoMapper;
	
//	@Resource
//	private OrderInfoMapper orderInfoMapper;

	
	@Resource
	private RedisService redisService;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveQrcodeInfo(QuickResponseCodeInfo qrcodeInfo, List<QuickResponseCodeItemInfo> itemList,
								  List<InvoiceTypeCodeExt> extList) {
		
		if (qrcodeInfo != null) {
            int insertSelective = quickResponseCodeInfoMapper.insertSelective(qrcodeInfo);
			if (insertSelective <= 0) {
				return false;
			}
			/**
			 * todo 满足mycat临时使用的缓存,后期优化
			 * 目前开票接收成功后,添加发票请求流水号与销方税号对应关系的缓存
			 * 添加发票请求批次号与销方税号对应关系
			 *
			 */
			String cacheTqm = String.format(RedisConstant.REDIS_TQM, qrcodeInfo.getTqm());
			if (StringUtils.isBlank(redisService.get(cacheTqm))) {
				redisService.set(cacheTqm, qrcodeInfo.getTqm(), RedisConstant.REDIS_EXPIRE_TIME_DEFAULT);
			}
			
		}
		
		if (ObjectUtil.isNotEmpty(itemList)) {
			for (QuickResponseCodeItemInfo item : itemList) {
				int insertSelective = quickResponseCodeItemInfoMapper.insertSelective(item);
				if (insertSelective <= 0) {
					return false;
				}
			}
		}
		
		if(ObjectUtil.isNotEmpty(extList)){
			for(InvoiceTypeCodeExt ext : extList){
				int insertSelective = invoiceTypeCodeExtMapper.insertInvoiceTypeCodeExt(ext);
				if (insertSelective <= 0) {
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * 查询二维码列表
	 */
	@Override
	public PageUtils queryQrCodeList(Map paramMap, List<String> shList) {
		int currPage = Integer.parseInt(String.valueOf(paramMap.get(ConfigureConstant.STRING_CURR_PAGE)));
		int pageSize = Integer.parseInt(String.valueOf(paramMap.get(ConfigureConstant.STRING_PAGE_SIZE)));
		Page page = new Page(currPage, pageSize);
		List<Map> list = quickResponseCodeInfoMapper.selectQrCodeList(page,paramMap, shList);
		PageInfo<Map> pageInfo = new PageInfo<>(list);
		return new PageUtils(pageInfo.getList(), (long) pageInfo.getTotal(), (long)pageInfo.getPageSize(), (long)pageInfo.getPageNum());
	}
	
	/**
	 * 查询二维码详情
	 */
	@Override
	public QuickResponseCodeInfo queryQrCodeDetail(String qrcodeId, List<String> xhfNsrsbh) {
		return quickResponseCodeInfoMapper.selectQuickResponseCodeById(qrcodeId, xhfNsrsbh);
	}
	
	@Override
	public List<QuickResponseCodeItemInfo> queryQrCodeItemListByQrcodeId(String qrcodeId, List<String> xhfNsrsbh) {
		return quickResponseCodeItemInfoMapper.selectByQrcodeId(qrcodeId, xhfNsrsbh);
	}
	
	@Override
	public List<InvoiceTypeCodeExt> queryInvoiceTypeByQrcodeId(String qrcodeId, List<String> xhfNsrsbh) {
		return invoiceTypeCodeExtMapper.selectByQrcodeId(qrcodeId, xhfNsrsbh);
	}
	
	@Override
	public QuickResponseCodeInfo queryQrCodeDetailByTqm(String tqm, List<String> shList, String qrCodeType) {
		return quickResponseCodeInfoMapper.queryQrCodeDetailByTqm(tqm, shList, qrCodeType);
	}

	@Override
	public EwmConfigInfo queryEwmConfigInfo(Map<String, Object> paramMap) {
		return ewmConfigInfoMapper.queryEwmConfigInfo(paramMap);
	}
	
	
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean addEwmConfigInfo(EwmConfigInfo ewmConfig,List<EwmConfigItemInfo> ewmConfigItemList) {
		int insert = ewmConfigInfoMapper.insertSelective(ewmConfig);
		if(insert <= 0){
			return false;
		}
		
		for(EwmConfigItemInfo item : ewmConfigItemList){
			int insertSelective = ewmConfigItemInfoMapper.insertEwmConfigItem(item);
			if (insertSelective <= 0) {
				return false;
			}
		}
		return true;
	}
	
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean updateEwmConfigInfo(EwmConfigInfo ewmConfig, List<EwmConfigItemInfo> ewmConfigItemList) {
		int updateByXhfNsrsbh = ewmConfigInfoMapper.updateByPrimaryKeySelective(ewmConfig);
		if (updateByXhfNsrsbh <= 0) {
			return false;
		}
		
		ewmConfigItemInfoMapper.deleteByEwmConfigId(ewmConfig.getId());
		for (EwmConfigItemInfo item : ewmConfigItemList) {
			int insertSelective = ewmConfigItemInfoMapper.insertEwmConfigItem(item);
			if (insertSelective <= 0) {
				return false;
			}
		}
		
		return true;
	}
	
	@Override
	public List<EwmConfigItemInfo> queryEwmConfigItemInfoById(String id) {
		return ewmConfigItemInfoMapper.queryEwmItemInfoByEwmConfigId(id);
	}
	
	/*@Override
	public Map<String, Object> queryEwmDetailByFpqqlsh(String fpqqlsh) {
		
		return quickResponseCodeInfoMapper.queryEwmDetailByFpqqlsh(fpqqlsh);
	}*/
	
	//@Override
	/*public QuickResponseCodeInfo queryQrCodeDetailByDdqqlshAndNsrsbh(String fpqqlsh, String nsrsbh) {
		
		return quickResponseCodeInfoMapper.queryQrCodeDetailByDdqqlshAndNsrsbh(fpqqlsh, nsrsbh);
	}*/
	
	/*@Override
	public List<QuickResponseCodeInfo> selectQuickResponseCodeListByFpqqlshDdhNsrsbh(String xsfNsrsbh, String ddh, String tqm, String fpqqlsh) {
		return quickResponseCodeInfoMapper.selectQuickResponseCodeListByFpqqlshDdhNsrsbh(xsfNsrsbh, ddh, tqm, fpqqlsh);
	}*/
	
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateEwmDetailInfo(QuickResponseCodeInfo quickInfo, List<String> shList) {
		int updateByPrimaryKeySelective = quickResponseCodeInfoMapper.updateQrCodeInfo(quickInfo, shList);
		return updateByPrimaryKeySelective > 0;
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public R updateStaticEwmInfo(QuickResponseCodeInfo qrCodeInfo, List<QuickResponseCodeItemInfo> itemList, List<InvoiceTypeCodeExt> extList) {
		List<String> shList = new ArrayList<>();
		shList.add(qrCodeInfo.getXhfNsrsbh());
		int i = quickResponseCodeInfoMapper.updateQrCodeInfo(qrCodeInfo, shList);
		if (i <= 0) {
			log.error("更新静态码信息失败,id:{}", qrCodeInfo.getId());
			return R.error();
		}
		
		//删除老的明细
		quickResponseCodeItemInfoMapper.deleteByQrId(qrCodeInfo.getId(), shList);
		//删除老的发票种类
		invoiceTypeCodeExtMapper.deleteByQrId(qrCodeInfo.getId(), shList);
		
		if (ObjectUtil.isNotEmpty(itemList)) {
			for (QuickResponseCodeItemInfo item : itemList) {
				int insertSelective = quickResponseCodeItemInfoMapper.insertSelective(item);
				if (insertSelective <= 0) {
					return R.error();
				}
				
			}
		}

		if(ObjectUtil.isNotEmpty(extList)){
			for(InvoiceTypeCodeExt ext : extList) {
				int insertSelective = invoiceTypeCodeExtMapper.insertInvoiceTypeCodeExt(ext);
				if (insertSelective <= 0) {
					return R.error();
				}
			}
		}


		return R.ok();
	}


}
