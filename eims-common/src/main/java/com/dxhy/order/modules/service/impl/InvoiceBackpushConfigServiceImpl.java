package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceBackpushConfigDao;
import com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity;
import com.dxhy.order.modules.service.InvoiceBackpushConfigService;
import com.dxhy.order.utils.DistributedKeyMaker;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service("invoiceBackpushConfigService")
public class InvoiceBackpushConfigServiceImpl extends ServiceImpl<InvoiceBackpushConfigDao, InvoiceBackpushConfigEntity> implements InvoiceBackpushConfigService {

    @Autowired
    private InvoiceBackpushConfigDao invoiceBackpushConfigDao;

    @Override
    public R saveInvoiceBackpushConfig(InvoiceBackpushConfigEntity invoiceBackpushConfig) {
        try {
            // 校验同一个order_config_id同一个xtly最多只能有一条回推地址的数据
            int count = invoiceBackpushConfigDao.countByOrderConfigIdAndXtly(invoiceBackpushConfig.getOrderConfigId(), invoiceBackpushConfig.getXtly());
            if (count > 0) {
                return R.error("9999", "同一个开票配置下相同系统来源只能配置一个回推地址");
            }

            // 设置主键和创建时间
            invoiceBackpushConfig.setId(DistributedKeyMaker.generateShotKey());
            invoiceBackpushConfig.setCreateTime(new Date());
            invoiceBackpushConfig.setUpdateTime(new Date());

            // 保存数据
            invoiceBackpushConfigDao.insert(invoiceBackpushConfig);
            return R.ok();
        } catch (Exception e) {
            log.error("saveInvoiceBackpushConfig 异常: {}", e);
            return R.error("9999", "保存失败！");
        }
    }

    @Override
    public R updateInvoiceBackpushConfig(InvoiceBackpushConfigEntity invoiceBackpushConfig) {
        try {
            if (StringUtils.isBlank(invoiceBackpushConfig.getId())) {
                return R.error("9999", "ID不能为空");
            }

            // 查询原数据
            InvoiceBackpushConfigEntity oldConfig = invoiceBackpushConfigDao.selectById(invoiceBackpushConfig.getId());
            if (Objects.isNull(oldConfig)) {
                return R.error("9999", "未找到对应的回推配置");
            }

            // 如果修改了系统来源，需要校验是否已存在
            if (!oldConfig.getXtly().equals(invoiceBackpushConfig.getXtly())) {
                int count = invoiceBackpushConfigDao.countByOrderConfigIdAndXtly(invoiceBackpushConfig.getOrderConfigId(), invoiceBackpushConfig.getXtly());
                if (count > 0) {
                    return R.error("9999", "同一个开票配置下相同系统来源只能配置一个回推地址");
                }
            }

            // 设置更新时间
            invoiceBackpushConfig.setUpdateTime(new Date());

            // 更新数据
            invoiceBackpushConfigDao.updateById(invoiceBackpushConfig);
            return R.ok();
        } catch (Exception e) {
            log.error("updateInvoiceBackpushConfig 异常: {}", e);
            return R.error("9999", "更新失败！");
        }
    }

    @Override
    public R deleteInvoiceBackpushConfigByOrderConfigId(String orderConfigId) {
        try {
            if (StringUtils.isBlank(orderConfigId)) {
                return R.error("9999", "开票配置ID不能为空");
            }
            // 删除数据
            invoiceBackpushConfigDao.delete(Wrappers.lambdaQuery(InvoiceBackpushConfigEntity.class)
                    .eq(InvoiceBackpushConfigEntity::getOrderConfigId, orderConfigId));

            return R.ok();
        } catch (Exception e) {
            log.error("deleteInvoiceBackpushConfigByOrderConfigId 异常: {}", e);
            return null;
        }
    }

    @Override
    public List<InvoiceBackpushConfigEntity> getInvoiceBackpushConfigByOrderConfigId(String orderConfigId) {
        return invoiceBackpushConfigDao.selectByOrderConfigId(orderConfigId);
    }

    @Override
    public InvoiceBackpushConfigEntity getInvoiceBackpushConfigByOrderConfigIdAndXtly(String orderConfigId, String xtly) {
        return invoiceBackpushConfigDao.selectByOrderConfigIdAndXtly(orderConfigId, xtly);
    }
}