package com.dxhy.order.modules.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dxhy.order.utils.BasePage;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 无需开票 批量开票发票订单DTO
 * @date 2022/12/6 17:00
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("无需开票 批量开票发票订单DTO")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class NoOrderInvoiceInfoListDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @ApiModelProperty(name = "ID")
    private String id;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "订单号")
    private String ddh;

    /**
     * 生成订单时间
     */
    @ApiModelProperty(name = "生成订单时间")
    private String ddscrq;

    /**
     * 购方名称
     */
    @ApiModelProperty(name = "购方名称")
    private String ghfMc;

    /**
     * 购方税号
     */
    @ApiModelProperty(name = "购方税号")
    private String ghfNsrsbh;

    /**
     * 不含税金额
     */
    @ApiModelProperty(name = "不含税金额")
    @JsonProperty("jehj")
    private String hjbhsje;

    /**
     * 不含税金额起
     */
    @ApiModelProperty(name = "不含税金额起")
    private String startJehj;

    /**
     * 不含税金额止
     */
    @ApiModelProperty(name = "不含税金额止")
    private String endJehj;

    /**
     * 价税合计
     */
    @ApiModelProperty(name = "价税合计")
    private String jshj;

    /**
     * 价税合计起
     */
    @ApiModelProperty(name = "价税合计起")
    private String startJshj;

    /**
     * 价税合计止
     */
    @ApiModelProperty(name = "价税合计止")
    private String endJshj;

    /**
     * 税额
     */
    @ApiModelProperty(name = "税额")
    private String kpse;

    /**
     * 税额起
     */
    @ApiModelProperty(name = "税额")
    private String startKpse;

    /**
     * 税额止
     */
    @ApiModelProperty(name = "税额")
    private String endKpse;

    /**
     * 发票类型 发票种类代码 0:全电电普,1:全电电专
     */
    @ApiModelProperty(name = "发票类型")
    private String fpzlDm;

    /**
     * 订单来源
     */
    @ApiModelProperty(name = "订单来源")
    private String ddly;

    /**
     * 订单状态 (0: 正常 ,1: 合并, 2: 退回,3:无效)
     */
    @ApiModelProperty(name = "订单状态")
    private String ddzt;

    /**
     * 开票状态 (0:待开;1:开票中;2:开票成功;3:开票失败;4:无需开票;5:手工标记为开票)
     */
    @ApiModelProperty(name = "开票状态")
    private String kpzt;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 交付手机号
     */
    @ApiModelProperty(name = "交付手机号")
    private String ghfSj;

    /**
     * 交付邮箱地址
     */
    @ApiModelProperty(name = "交付邮箱地址")
    private String ghfYx;

    /**
     * 应收单号
     */
    @ApiModelProperty(name = "应收单号")
    private String ysdh;

    /**
     * 原应收单号
     */
    @ApiModelProperty(name = "原应收单号")
    private String yysdh;

    /**
     * 原订单号
     */
    @ApiModelProperty(name = "原订单号")
    private String yddh;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注")
    private String bz;

    /**
     * 订单生成日期起
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "订单生成日期起")
    private String ddscrqq;

    /**
     * 订单生成日期止
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "订单生成日期止")
    private String ddscrqz;

}
