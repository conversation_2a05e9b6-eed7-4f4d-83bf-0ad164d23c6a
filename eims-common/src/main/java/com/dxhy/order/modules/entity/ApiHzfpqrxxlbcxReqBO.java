package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ApiHzfpqrxxlbcxReqBO implements Serializable {

    private static final long serialVersionUID = -3038843894165590891L;

    /**
     * 购销方选择
     */
    private String gxfxz;
    /**
     * 确认单状态
     */
    private String hzqrxxztdm;
    /**
     * 对方纳税人名称
     */
    private String dfnsrmc;
    /**
     * 开票日期起
     */
    private String kprqq;
    /**
     * 开票日期止
     */
    private String kprqz;
    /**
     * 开票状态
     */
    private String kpzt;
    /**
     * 销货方纳税人识别号
     */
    private String nsrsbh;
    /**
     * 录入方身份
     */
    private String lrfsf;
    /**
     * 第几页
     */
    private String current;
    /**
     * 每页几条记录，默认 10，可选值[10，20，50]
     */
    private String size;

}
