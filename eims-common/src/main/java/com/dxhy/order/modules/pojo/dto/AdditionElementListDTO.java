package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 查询列表 附加信息DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("查询列表 附加信息DTO")
public class AdditionElementListDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 附加信息名称
     */
    @ApiModelProperty(name = "附加信息名称")
    private String fjxxmc;

}
