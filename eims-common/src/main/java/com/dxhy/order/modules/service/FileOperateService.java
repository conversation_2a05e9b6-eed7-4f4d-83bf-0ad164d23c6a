package com.dxhy.order.modules.service;


import com.dxhy.order.modules.entity.DataStatisticQueryList;
import com.dxhy.order.modules.entity.InvoiceRecordQueryList;
import com.dxhy.order.modules.entity.InvoiceRecordZip;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 文件操作
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-28 17:35
 */
public interface FileOperateService {

    /**
     * 开票记录 - 批量导出zip
     *
     * @return
     */
    void exportZip(HttpServletRequest request, HttpServletResponse response, InvoiceRecordZip invoiceRecordZip);


    /**
     * 开票记录 - OFD/pdf 文件下载
     *
     * @param qdfphm,request,response
     * @return
     */
    void downLoadOFD(String qdfphm, HttpServletRequest request, HttpServletResponse response);

    /**
     * 开票记录 - 导出Excel
     *
     * @param list
     * @return
     */
    void exportExcel(InvoiceRecordQueryList list, HttpServletRequest request, HttpServletResponse response);

    /**
     * 数据统计 - 导出Excel
     *
     * @param dataStatisticQueryList
     * @return
     */
    void exportStaisticList(DataStatisticQueryList dataStatisticQueryList, HttpServletRequest request, HttpServletResponse response);
}
