package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票回推配置表
 *
 * <AUTHOR> @email
 * @date 2024-05-09
 */
@Mapper
public interface InvoiceBackpushConfigDao extends BaseMapper<InvoiceBackpushConfigEntity> {

    /**
     * 根据开票配置ID查询回推配置列表
     * @param orderConfigId 开票配置ID
     * @return 回推配置列表
     */
    List<InvoiceBackpushConfigEntity> selectByOrderConfigId(@Param("orderConfigId") String orderConfigId);
    
    /**
     * 根据开票配置ID和系统来源查询回推配置
     * @param orderConfigId 开票配置ID
     * @param xtly 系统来源
     * @return 回推配置
     */
    InvoiceBackpushConfigEntity selectByOrderConfigIdAndXtly(@Param("orderConfigId") String orderConfigId, @Param("xtly") String xtly);
    
    /**
     * 检查同一个order_config_id和xtly是否已存在
     * @param orderConfigId 开票配置ID
     * @param xtly 系统来源
     * @return 数量
     */
    int countByOrderConfigIdAndXtly(@Param("orderConfigId") String orderConfigId, @Param("xtly") String xtly);
} 