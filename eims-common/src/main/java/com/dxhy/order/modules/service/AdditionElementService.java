package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.AdditionElementEntity;
import com.dxhy.order.modules.entity.AdditionElementSaveDTO;
import com.dxhy.order.modules.pojo.dto.AdditionElementListDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.vo.AdditionElementVO;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;

import java.util.List;

/**
 * 附加信息表 service
 * <AUTHOR>
 * @Date 2022/6/28 16:25
 * @Version 1.0
 **/
public interface AdditionElementService extends IService<AdditionElementEntity> {

    /**
     * 查询列表
     * @param additionElementListDTO
     * @return com.dxhy.order.utils.PageUtils
     * <AUTHOR>
     **/
    PageUtils queryPage(AdditionElementListDTO additionElementListDTO);

    /**
     * 查询全量数据
     * @param nsrsbh
     * @return java.util.List<com.dxhy.order.modules.pojo.vo.AdditionElementVO>
     * <AUTHOR>
     **/
    List<AdditionElementVO> listAll(String nsrsbh);

    /**
     * 根据场景模板ID查询附加信息
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listAdditionElementBySceneTemplateId(String id);

    /**
     * 根据ID查询数据
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R queryDataById(String id);

    /**
     * 新增或修改附加信息 新增时返回数据ID
     * @param additionElementSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveData(AdditionElementSaveDTO additionElementSaveDTO);

    /**
     * 删除数据
     * @param idsDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R deleteData(IdsDTO idsDTO);

    /**
     * 刷新引用状态
     * @return void
     * <AUTHOR>
     **/
    void refreshYyzt();

    /**
     * 查询附加信息定时任务
     * @return
     */
    R getAdditionElementTask(String nsrsbh);

}

