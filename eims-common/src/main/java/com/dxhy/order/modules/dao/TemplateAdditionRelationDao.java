package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.TemplateAdditionRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景模板 附加信息关联关系表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface TemplateAdditionRelationDao extends BaseMapper<TemplateAdditionRelationEntity> {

    /**
     * 通过场景模板ID删除场景模板 附加信息关联关系表
     * @param idList
     * @return void
     * <AUTHOR>
     **/
    void deleteBySceneTemplateIdList(@Param("idList") List<String> idList);

    List<TemplateAdditionRelationEntity> queryByAllParam(@Param("entity") TemplateAdditionRelationEntity entity);

}
