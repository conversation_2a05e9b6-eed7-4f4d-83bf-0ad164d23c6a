package com.dxhy.order.modules.pojo.vo;

import com.dxhy.order.modules.pojo.bo.ItemInfoUpdateExcelBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 上传项目信息 商品信息VO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel("上传项目信息 商品信息VO")
public class ItemInfoUpdateExcelVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("校验成功的数据")
    List<ItemInfoUpdateExcelBO> rightList;

    @ApiModelProperty("校验成功的数量")
    private String rightNum;

    @ApiModelProperty("校验失败的数据")
    List<ItemInfoUpdateExcelBO> errorList;

    @ApiModelProperty("校验失败的数量")
    private String errorNum;

}
