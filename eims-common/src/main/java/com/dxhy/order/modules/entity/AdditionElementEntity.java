package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 附加信息表
 * <AUTHOR>
 * @Date 2022/6/28 16:34
 * @Version 1.0
 **/
@TableName("addition_element")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("附加信息表")
public class AdditionElementEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 附加信息主键
	 */
	@TableId(type = IdType.INPUT)
	@ApiModelProperty("附加信息主键")
	private String id;

	/**
	 * 纳税人识别号
	 */
	@ApiModelProperty("纳税人识别号")
	private String baseNsrsbh;

	/**
	 * 附加信息名称
	 */
	@ApiModelProperty("附加信息名称")
	private String fjxxmc;

	/**
	 * 数据类型 1 文本型 2 数值型 3 日期型
	 */
	@ApiModelProperty("数据类型 1 文本型 2 数值型 3 日期型")
	private String sjlx;

	/**
	 * 输入方式 1 手工录入 2 Excel导入
	 */
	@ApiModelProperty("输入方式 1 手工录入 2 Excel导入")
	private String srfs;

	/**
	 * 引用状态 0 未引用 1 已引用
	 */
	@ApiModelProperty("引用状态 0 未引用 1 已引用")
	private String yyzt;

	/**
	 * 逻辑删除 0 正常 1 已删除
	 */
	@ApiModelProperty("逻辑删除 0 正常 1 已删除")
	private String isDelete;

	/**
	 * 备用字段1
	 */
	@ApiModelProperty("备用字段1")
	private String byzd1;

	/**
	 * 备用字段2
	 */
	@ApiModelProperty("备用字段2")
	private String byzd2;

	/**
	 * 备用字段3
	 */
	@ApiModelProperty("备用字段3")
	private String byzd3;

	/**
	 * 备用字段4
	 */
	@ApiModelProperty("备用字段4")
	private String byzd4;

	/**
	 * 备用字段5
	 */
	@ApiModelProperty("备用字段5")
	private String byzd5;

	/**
	 * 备用字段6
	 */
	@ApiModelProperty("备用字段6")
	private String byzd6;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date createTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * uuid 第三方接口慧企 返回的UUID
	 */
	@ApiModelProperty("慧企uuid")
	private String uuid;

	/**
	 * 纳税人名称
	 */
	@ApiModelProperty("纳税人名称")
	private String nsrmc;

	/**
	 * 有效标志
	 */
	@ApiModelProperty("有效标志")
	private String yxbz;

}
