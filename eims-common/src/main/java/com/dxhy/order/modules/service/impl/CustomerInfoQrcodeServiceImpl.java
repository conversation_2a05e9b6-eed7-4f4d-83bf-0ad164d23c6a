package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.CustomerInfoQrcodeDao;
import com.dxhy.order.modules.entity.CustomerInfoQrcodeEntity;
import com.dxhy.order.modules.entity.NsrsbhTenantRelationEntity;
import com.dxhy.order.modules.pojo.dto.BaseNsrsbhWithPageDTO;
import com.dxhy.order.modules.pojo.dto.CustomerInfoQrcodeSaveDTO;
import com.dxhy.order.modules.service.CustomerInfoQrcodeService;
import com.dxhy.order.modules.service.NsrsbhTenantRelationService;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.utils.DistributedKeyMaker;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 客户扫码信息表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/27 12:13
 * @Version 1.0
 **/
@Service("customerInfoQrcodeService")
public class CustomerInfoQrcodeServiceImpl extends ServiceImpl<CustomerInfoQrcodeDao, CustomerInfoQrcodeEntity> implements CustomerInfoQrcodeService {

    @Autowired
    private CustomerInfoQrcodeDao customerInfoQrcodeDao;
    @Autowired
    private NsrsbhTenantRelationService nsrsbhTenantRelationService;
    @Autowired
    private TenantRdsService tenantRdsService;

    @Override
    public R saveQrcodeCustomer(CustomerInfoQrcodeSaveDTO customerInfoQrcodeSaveDTO) {
        String checkCustomerInfoQrcodeSaveDTOResult = this.checkCustomerInfoQrcodeSaveDTO(customerInfoQrcodeSaveDTO);
        if(StringUtils.isNotEmpty(checkCustomerInfoQrcodeSaveDTOResult)){
            return R.error(checkCustomerInfoQrcodeSaveDTOResult);
        }
        // 先切换回主库
        DynamicDataSource.setDataSourceDefault();
        NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity = nsrsbhTenantRelationService.getById(customerInfoQrcodeSaveDTO.getBaseNsrsbh());
        if(nsrsbhTenantRelationEntity == null || StringUtils.isEmpty(nsrsbhTenantRelationEntity.getTenantCode())){
            return R.error("租户数据有误");
        }
        String changeTenantResult = tenantRdsService.switchRds(nsrsbhTenantRelationEntity.getTenantCode());
        if(StringUtils.isNotEmpty(changeTenantResult)){
            return R.error(changeTenantResult);
        }
        CustomerInfoQrcodeEntity saveData = new CustomerInfoQrcodeEntity();
        saveData.setId(DistributedKeyMaker.generateShotKey());
        saveData.setBaseNsrsbh(customerInfoQrcodeSaveDTO.getBaseNsrsbh());
        saveData.setGsmc(customerInfoQrcodeSaveDTO.getGsmc());
        saveData.setNsrsbh(customerInfoQrcodeSaveDTO.getNsrsbh());
        saveData.setGsdz(customerInfoQrcodeSaveDTO.getGsdz());
        saveData.setGsdh(customerInfoQrcodeSaveDTO.getGsdh());
        saveData.setKhyh(customerInfoQrcodeSaveDTO.getKhyh());
        saveData.setYhzh(customerInfoQrcodeSaveDTO.getYhzh());
        saveData.setLxyx(customerInfoQrcodeSaveDTO.getLxyx());
        saveData.setLxdh(customerInfoQrcodeSaveDTO.getLxdh());
        saveData.setCreateTime(new Date());
        customerInfoQrcodeDao.insert(saveData);
        return R.ok();
    }

    private String checkCustomerInfoQrcodeSaveDTO(CustomerInfoQrcodeSaveDTO customerInfoQrcodeSaveDTO){
        if(customerInfoQrcodeSaveDTO == null || StringUtils.isEmpty(customerInfoQrcodeSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isEmpty(customerInfoQrcodeSaveDTO.getGsmc())){
            return "公司名称不能为空";
        }
        return "";
    }

    @Override
    public R listQrcodeCustomer(BaseNsrsbhWithPageDTO baseNsrsbhWithPageDTO) {
        if(baseNsrsbhWithPageDTO == null || StringUtils.isEmpty(baseNsrsbhWithPageDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        Page page = new Page(baseNsrsbhWithPageDTO.getCurrPage(), baseNsrsbhWithPageDTO.getPageSize());
        List<CustomerInfoQrcodeEntity> list = customerInfoQrcodeDao.listData(page, baseNsrsbhWithPageDTO.getBaseNsrsbh());
        page.setRecords(list);
        PageUtils pageUtil = new PageUtils(page);
        return R.ok().put("data", pageUtil);
    }
}
