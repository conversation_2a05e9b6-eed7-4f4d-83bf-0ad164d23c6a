package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceJzfwxxDao;
import com.dxhy.order.modules.entity.InvoiceJzfwxxEntity;
import com.dxhy.order.modules.service.InvoiceJzfwxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 建筑服务信息的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceJzfwxxService")
@Slf4j
@RefreshScope
public class InvoiceJzfwxxServiceImpl extends ServiceImpl<InvoiceJzfwxxDao, InvoiceJzfwxxEntity>
        implements InvoiceJzfwxxService {

    @Override
    public void saveBatch(List<InvoiceJzfwxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceJzfwxxEntity> jzfwxxWrapper = Wrappers.lambdaUpdate();
        jzfwxxWrapper.set(InvoiceJzfwxxEntity::getIsDelete, "1");
        jzfwxxWrapper.eq(InvoiceJzfwxxEntity::getOrderInvoiceInfoId, invoiceId);
        jzfwxxWrapper.eq(InvoiceJzfwxxEntity::getInvoiceTdywId, tdywId);
        this.update(jzfwxxWrapper);
    }
}




