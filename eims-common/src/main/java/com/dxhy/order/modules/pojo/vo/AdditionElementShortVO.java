package com.dxhy.order.modules.pojo.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 附加信息表VO（少量表字段）
 * <AUTHOR>
 * @Date 2022/6/28 16:34
 * @Version 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel("附加信息表VO（少量表字段）")
public class AdditionElementShortVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 附加信息主键
	 */
	@TableId(type = IdType.INPUT)
	@ApiModelProperty("附加信息主键")
	private String id;

	/**
	 * 附加信息名称
	 */
	@ApiModelProperty("附加信息名称")
	private String fjxxmc;

	/**
	 * 数据类型 1 文本型 2 数值型 3 日期型
	 */
	@ApiModelProperty("数据类型 1 文本型 2 数值型 3 日期型")
	private String sjlx;

}
