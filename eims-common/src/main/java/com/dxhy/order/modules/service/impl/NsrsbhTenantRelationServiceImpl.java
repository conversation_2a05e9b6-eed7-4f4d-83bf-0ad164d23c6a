package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.NsrsbhTenantRelationDao;
import com.dxhy.order.modules.entity.NsrsbhTenantRelationEntity;
import com.dxhy.order.modules.service.NsrsbhTenantRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 销方税号租户关联表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/27 12:13
 * @Version 1.0
 **/
@Service("nsrsbhTenantRelationService")
public class NsrsbhTenantRelationServiceImpl extends ServiceImpl<NsrsbhTenantRelationDao, NsrsbhTenantRelationEntity> implements NsrsbhTenantRelationService {

    @Autowired
    private NsrsbhTenantRelationDao nsrsbhTenantRelationDao;


    @Override
    public List<NsrsbhTenantRelationEntity> listAll() {
        return nsrsbhTenantRelationDao.listAll();
    }
}
