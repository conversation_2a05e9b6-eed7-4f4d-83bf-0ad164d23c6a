package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity;
import com.dxhy.order.utils.R;

import java.util.List;

/**
 * 发票回推配置表
 *
 * <AUTHOR> @email
 * @date 2024-05-09
 */
public interface InvoiceBackpushConfigService extends IService<InvoiceBackpushConfigEntity> {

    /**
     * 保存发票回推配置
     * @param invoiceBackpushConfig 回推配置实体
     * @return 结果
     */
    R saveInvoiceBackpushConfig(InvoiceBackpushConfigEntity invoiceBackpushConfig);

    /**
     * 更新发票回推配置
     * @param invoiceBackpushConfig 回推配置实体
     * @return 结果
     */
    R updateInvoiceBackpushConfig(InvoiceBackpushConfigEntity invoiceBackpushConfig);

    /**
     * 根据开票配置ID删除发票回推配置
     * @param orderConfigId 主键ID
     * @return 结果
     */
    R deleteInvoiceBackpushConfigByOrderConfigId(String orderConfigId);

    /**
     * 根据开票配置ID查询回推配置列表
     * @param orderConfigId 开票配置ID
     * @return 回推配置列表
     */
    List<InvoiceBackpushConfigEntity> getInvoiceBackpushConfigByOrderConfigId(String orderConfigId);

    /**
     * 根据开票配置ID和系统来源查询回推配置
     * @param orderConfigId 开票配置ID
     * @param xtly 系统来源
     * @return 回推配置
     */
    InvoiceBackpushConfigEntity getInvoiceBackpushConfigByOrderConfigIdAndXtly(String orderConfigId, String xtly);
} 