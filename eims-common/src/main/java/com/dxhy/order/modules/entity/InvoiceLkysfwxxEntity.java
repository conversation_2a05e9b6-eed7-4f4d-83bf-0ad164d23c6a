package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-旅客运输服务信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_lkysfwxx")
@Data
public class InvoiceLkysfwxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 旅客运输服务信息主键
	 */
	@ApiModelProperty(value = "主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("出行序号")
	private String cxrxh;

	@ApiModelProperty("出行人")
	private String cxr;

	@ApiModelProperty("出行日期")
	private String cxrq;
	//HDSTODO
	@ApiModelProperty("出行人证件类型")
	private String cxrzjlx;

	@ApiModelProperty("出行人证件号码")
	private String cxrzjhm;

	@ApiModelProperty("旅客运输出发地")
	private String lkyscfd;

	@ApiModelProperty("旅客运输到达地")
	private String lkysddd;

	//HDSTODO
	@ApiModelProperty("交通工具类型")
	private String jtgjlx;

	@ApiModelProperty("座位等级")
	private String zwdj;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
