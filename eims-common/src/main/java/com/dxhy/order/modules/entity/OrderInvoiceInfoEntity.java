package com.dxhy.order.modules.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单和发票关系表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
@TableName("order_invoice_info")
@ApiModel("发票信息")
@Data
@Setter
@Getter
public class OrderInvoiceInfoEntity extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单发票主键
     */
    @TableId(type = IdType.INPUT)
    @JsonProperty("ID")
    private String id;
    /**
     * 请求批次号
     */
    @ApiModelProperty("批次号")
    @JsonProperty("PCH")
    private String pch;
    /**
     * 发票请求流水号
     */
    @ApiModelProperty("发票请求流水号")
    @JSONField(name = "DDQQLSH")
    @JsonProperty("DDQQLSH")
    private String fpqqlsh;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @JSONField(name = "DDH")
    @JsonProperty("DDH")
    private String ddh;
    /**
     * 购货方名称
     */
    @ApiModelProperty("购货方名称")
    @JSONField(name = "GMFMC")
    @JsonProperty("GMFMC")
    private String ghfMc;
    /**
     * 购货方识别号
     */
    @ApiModelProperty("购货方识别号")
    @JSONField(name = "GMFSBH")
    @JsonProperty("GMFSBH")
    private String ghfNsrsbh;
    /**
     * 购货方企业类型（01企业，02机关事业单位，03个人，04其他）
     */
    @ApiModelProperty("企业类型 01企业，02机关事业单位，03个人，04其他")
    @JSONField(name = "QYLX")
    @JsonProperty("QYLX")
    private String ghfQylx;
    /**
     * 购货方省份
     */
    @ApiModelProperty("购货方省份")
    @JSONField(name = "GMFSF")
    @JsonProperty("GMFSF")
    private String ghfSf;
    /**
     * 购货方编码id
     */
    @ApiModelProperty("购货方编码")
    @JSONField(name = "GMFBM")
    @JsonProperty("GMFBM")
    private String ghfId;
    /**
     * 购货方地址
     */
    @ApiModelProperty("购货方地址")
    @JSONField(name = "GMFDZ")
    @JsonProperty("GMFDZ")
    private String ghfDz;
    /**
     * 购货方电话
     */
    @ApiModelProperty("购货方电话")
    @JSONField(name = "GMFDH")
    @JsonProperty("GMFDH")
    private String ghfDh;
    /**
     * 购货方银行
     */
    @ApiModelProperty("购货方银行")
    @JSONField(name = "GMFYH")
    @JsonProperty("GMFYH")
    private String ghfYh;
    /**
     * 购货方账号
     */
    @ApiModelProperty("购货方账号")
    @JSONField(name = "GMFZH")
    @JsonProperty("GMFZH")
    private String ghfZh;
    /**
     * 购货方手机
     */
    @ApiModelProperty("购货方手机号")
    @JSONField(name = "GMFSJH")
    @JsonProperty("GMFSJH")
    private String ghfSj;
    /**
     * 购货方邮箱
     */
    @ApiModelProperty("购货方邮箱")
    @JSONField(name = "GMFDZYX")
    @JsonProperty("GMFDZYX")
    private String ghfYx;
    /**
     * 销货方名称
     */
    @ApiModelProperty("销货方名称")
    @JSONField(name = "XHFMC")
    @JsonProperty("XHFMC")
    private String xhfMc;
    /**
     * 销货方识别号
     */
    @ApiModelProperty("销货方识别号")
    @JSONField(name = "XHFSBH")
    @JsonProperty("XHFSBH")
    private String xhfNsrsbh;
    /**
     * 销货方地址
     */
    @ApiModelProperty("销货方地址")
    @JSONField(name = "XHFDZ")
    @JsonProperty("XHFDZ")
    private String xhfDz;
    /**
     * 销货方电话
     */
    @ApiModelProperty("销货方电话")
    @JSONField(name = "XHFDH")
    @JsonProperty("XHFDH")
    private String xhfDh;
    /**
     * 销货方银行
     */
    @ApiModelProperty("销货方银行")
    @JSONField(name = "XHFYH")
    @JsonProperty("XHFYH")
    private String xhfYh;
    /**
     * 销货方账号
     */
    @ApiModelProperty("销货方账号")
    @JSONField(name = "XHFZH")
    @JsonProperty("XHFZH")
    private String xhfZh;
    /**
     * 特定业务类型代码
     */
    @ApiModelProperty("特定业务类型代码")
    @JsonProperty("TDYW")
    private String tdyw;
    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    @JSONField(name = "YWLX")
    @JsonProperty("YWLX")
    private String businessType;
    /**
     * 开票类型（0：蓝票；1：红票）
     */
    @ApiModelProperty("开票类型 0：蓝票；1：红票")
    @JSONField(name = "KPLX")
    @JsonProperty("KPLX")
    private String kplx;
    /**
     * 价税合计
     */
    @ApiModelProperty("价税合计")
    @JSONField(name = "JSHJ")
    @JsonProperty("JSHJ")
    private String jshj;
    /**
     * 合计不含税金额
     */
    @ApiModelProperty("合计不含税金额")
    @JSONField(name = "HJJE")
    @JsonProperty("HJJE")
    private String hjbhsje;
    /**
     * 开票税额
     */
    @ApiModelProperty("开票税额")
    @JSONField(name = "HJSE")
    @JsonProperty("HJSE")
    private String kpse;
    /**
     * 开票状态,(0:待开;1:开票中;2:开票成功;3:开票失败;4:无需开票)
     */
    @ApiModelProperty("开票状态,(0:待开;1:开票中;2:开票成功;3:开票失败;4:无需开票;5:已手工开票;6:未勾选)")
    @JsonProperty("KPZT")
    private String kpzt;
    /**
     * 开票日期
     */
    @ApiModelProperty("开票日期")
    @JsonProperty("KPRQ")
    @JSONField(name = "KPRQ")
    private Date kprq;
    /**
     * 订单生成日期
     */
    @ApiModelProperty("订单生成日期")
    @JSONField(name = "DDSJ")
    @JsonProperty("DDSJ")
    private Date ddscrq;
    /**
     * 订单来源(0：扫码开票；1：批量导入，2接口，3手动填开)
     */
    @ApiModelProperty("订单来源(0：扫码开票；1：批量导入，2接口，3手动填开 4 慧企)")
    @JsonProperty("DDLY")
    private String ddly;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @JSONField(name = "BZ")
    @JsonProperty("BZ")
    private String bz;
    /**
     * 订单状态 (0: 正常 ,1: 合并, 2: 退回,3:无效 4:拆分 5 异常)
     */
    @ApiModelProperty("订单状态 (0: 正常 ,1: 合并, 2: 退回,3:无效 4:拆分 5 异常)")
    @JsonProperty("DDZT")
    private String ddzt;
    /**
     * 全电发票号码
     */
    @ApiModelProperty("全电发票号码")
    @JsonProperty("QDFPHM")
    private String qdfphm;
    /**
     * 发票代码
     */
    @ApiModelProperty("发票代码")
    @JsonProperty("FPDM")
    private String fpdm;
    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    @JsonProperty("FPHM")
    private String fphm;
    /**
     * 发票种类代码 0:电子发票（增值税普通发票）,1:电子发票（增值税专用发票）
     */
    @ApiModelProperty("发票种类代码 0:电子发票（增值税普通发票）,1:电子发票（增值税专用发票）")
    @JSONField(name = "FPLXDM")
    @JsonProperty("FPLXDM")
    private String fpzlDm;
    /**
     * 校验码
     */
    @ApiModelProperty("校验码")
    @JsonProperty("JYM")
    private String jym;
    /**
     * 开票人
     */
    @ApiModelProperty("开票人")
    @JSONField(name = "KPR")
    @JsonProperty("KPR")
    private String kpr;
    /**
     * 复核人
     */
    @ApiModelProperty("复核人")
    @JSONField(name = "FHR")
    @JsonProperty("FHR")
    private String fhr;
    /**
     * 收款人
     */
    @ApiModelProperty("收款人")
    @JSONField(name = "SKR")
    @JsonProperty("SKR")
    private String skr;
    /**
     * 购买方经办人姓名
     */
    @ApiModelProperty("经办人姓名")
    @JSONField(name = "GMFJBRXM")
    @JsonProperty("GMFJBRXM")
    private String gmfjbrxm;
    /**
     * 经办人证件类型
     */
    @ApiModelProperty("经办人证件类型")
    @JSONField(name = "JBRZJLX")
    @JsonProperty("JBRZJLX")
    private String jbrzjlx;
    /**
     * 经办人证件号
     */
    @ApiModelProperty("经办人证件号")
    @JSONField(name = "JBRZJHM")
    @JsonProperty("JBRZJHM")
    private String jbrzjhm;
    /**
     * 经办人联系电话
     */
    @ApiModelProperty("经办人联系电话")
    @JsonProperty("JBRLXDH")
    private String jbrlxdh;
    /**
     * 防伪码
     */
    @ApiModelProperty("防伪码")
    @JsonProperty("FWM")
    private String fwm;
    /**
     * 二维码
     */
    @ApiModelProperty("二维码")
    @JsonProperty("EWM")
    private String ewm;
    /**
     * 机器编号
     */
    @ApiModelProperty("机器编号")
    @JsonProperty("JQBH")
    private String jqbh;
    /**
     * ofdurl地址
     */
    @ApiModelProperty("ofdurl地址")
    @JSONField(name = "OFDZJL")
    @JsonProperty("OFDZJL")
    private String ofdUrl;
    /**
     * pdfurl地址
     */
    @ApiModelProperty("pdfurl地址")
    @JSONField(name = "PDFZJL")
    @JsonProperty("PDFZJL")
    private String pdfUrl;
    /**
     * 是否拆分
     */
    @ApiModelProperty("是否拆分")
    @JSONField(name = "SFCF")
    @JsonProperty("SFCF")
    private String sfcf;
    /**
     * 冲红标志(0:正常;1:全部冲红成功;2:全部冲红中;3:全部冲红失败;4:部分冲红成功;5:部分冲红中;6:部分冲红失败;(特殊说明:部分冲红只记录当前最后一次操作的记录))
     */
    @ApiModelProperty("冲红标志(0:正常;1:全部冲红成功;2:全部冲红中;3:全部冲红失败;4:部分冲红成功;5:部分冲红中;6:部分冲红失败;(特殊说明:部分冲红只记录当前最后一次操作的记录))")
    @JSONField(name = "TSCHBZ")
    @JsonProperty("TSCHBZ")
    private String chBz;
    /**
     * 剩余可冲红金额
     */
    @ApiModelProperty("剩余可红冲金额")
    @JsonProperty("SYKCHJE")
    private String sykchje;
    /**
     * 剩余可冲红不含税金额
     */
    @ApiModelProperty("剩余可冲红不含税金额")
    @JsonProperty("SYKCHBHSJE")
    private String sykchbhsje;
    /**
     * 剩余可充红税额
     */
    @ApiModelProperty("剩余可红冲税额")
    @JsonProperty("SYKCHSE")
    private String sykchse;
    /**
     * 冲红时间
     */
    @ApiModelProperty("冲红时间")
    @JsonProperty("CHSJ")
    private Date chsj;
    /**
     * 冲红原因 0 开票有误 1 销售退回 2 销售折让
     */
    @ApiModelProperty("冲红原因 0 开票有误 1 销售退回 2 销售折让")
    @JSONField(name = "CHYY")
    @JsonProperty("CHYY")
    private String chyy;
    /**
     * 作废标志(0:正常;1:已作废;2:作废中;3:作废失败)
     */
    @ApiModelProperty("作废标志(0:正常;1:已作废;2:作废中;3:作废失败)")
    @JsonProperty("ZFBZ")
    private String zfBz;
    /**
     * 作废时间
     */
    @ApiModelProperty("作废时间")
    @JsonProperty("ZFSJ")
    private Date zfsj;
    /**
     * 作废原因
     */
    @ApiModelProperty("作废原因")
    @JsonProperty("ZFYY")
    private String zfyy;
    /**
     * 开票失败原因
     */
    @ApiModelProperty("开票失败原因")
    @JsonProperty("SBYY")
    private String sbyy;
    /**
     *
     */
    @ApiModelProperty("受理点名称")
    @JsonProperty("SLDMC")
    private String sldMc;
    /**
     * 开票终端
     */
    @ApiModelProperty("开票终端")
    @JsonProperty("SLD")
    private String sld;
    /**
     * 房间号
     */
    @ApiModelProperty("房间号")
    @JsonProperty("FJH")
    private String fjh;
    /**
     * 0 不带清单 1 带清单
     */
    @ApiModelProperty("清单标志 0 不带清单 1 带清单")
    @JSONField(name = "QDBZ")
    @JsonProperty("QDBZ")
    private String qdBz;
    /**
     * 打印状态 0 未打印 1 已打印
     */
    @ApiModelProperty("打印状态 0 未打印 1 已打印")
    @JsonProperty("DYZT")
    private String dyzt;
    /**
     * 推送业务系统状态,(0:未推送;1:推送成功;2:推送失败;)
     */
    @ApiModelProperty("推送业务系统状态,(0:未推送;1:推送成功;2:推送失败;)")
    @JsonProperty("PUSHSTATUS")
    private String pushStatus;
    /**
     * 0 初始化 1 推送中 2 推送成功 3 推送失败
     */
    @ApiModelProperty("邮件推送状态 0 初始化 1 推送中 2 推送成功 3 推送失败")
    @JsonProperty("EMAILPUSHSTATUS")
    private String emailPushStatus;
    /**
     * 短信推送状态 (0 初始未推送 1 推送中 2.推送成功 3 推送失败)
     */
    @ApiModelProperty("短信推送状态 (0 初始未推送 1 推送中 2.推送成功 3 推送失败)")
    @JsonProperty("SHORTMSGPUSHSTATUS")
    private String shortMsgPushStatus;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonProperty("CREATETIME")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @JsonProperty("CREATEBY")
    private String createBy;
    /**
     * 修改人
     */
    @JsonProperty("UPDATEBY")
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonProperty("UPDATETIME")
    private Date updateTime;
    /**
     * mongodb服务中pdf文件存储对应的id
     */
    @JsonProperty("MONGODBID")
    private String mongodbId;
    /**
     * 上传状态
     */
    @JsonProperty("UPLOADSTATE")
    private String uploadState;
    /**
     * 含税标志 0 不含税 1含税
     */
    @JsonProperty("HSBZ")
    @JSONField(name = "HSBZ")
    @ApiModelProperty("含税标志")
    private String hsbz;

    /**
     * 成品油
     */
    @ApiModelProperty("成品油")
    @JSONField(name = "CPYBS")
    @JsonProperty("CPYBS")
    private String cpy;
    /**
     * 购买方类型
     */
    @ApiModelProperty("购买方类型")
    @JSONField(name = "GMFLX")
    @JsonProperty("GMFLX")
    private String gmflx;
    /**
     * 原发票代码
     */
    @ApiModelProperty("原发票代码")
    @JSONField(name = "YFPDM")
    @JsonProperty("YFPDM")
    private String yfpdm;
    /**
     * 原发票号码
     */
    @ApiModelProperty("原发票号码")
    @JSONField(name = "YFPHM")
    @JsonProperty("YFPHM")
    private String yfphm;
    /**
     * 原全电发票号码
     */
    @ApiModelProperty("原全电发票号码")
    @JSONField(name = "YQDFPHM")
    @JsonProperty("YQDFPHM")
    private String yqdfphm;

    /**
     * 增值税用途状态 0 未勾选 1已勾选
     */
    @ApiModelProperty("增值税用途状态")
    @JsonProperty("ZZSYTZT")
    private String zzsytzt;

    /**
     * 消费税用途状态 0 未勾选 1已勾选
     */
    @ApiModelProperty("消费税用途状态")
    @JsonProperty("XFSYTZT")
    private String xfsytzt;

    /**
     * 入账状态 0 未入账 1已入账
     */
    @ApiModelProperty("入账状态")
    @JsonProperty("RZZT")
    private String rzzt;
    /**
     * 开票方式 0 自动开票  1 手动开票
     */
    @ApiModelProperty("开票方式 0 自动开票  1 手动开票")
    @JSONField(name = "KPFS")
    @JsonProperty("KPFS")
    private String kpfs;

    /**
     * 逻辑删除
     */
    @JsonProperty("ISDELETE")
    private String isDelete;
    /**
     * code 返回码
     */
    @JsonProperty("BYZD1")
    private String byzd1;
    /**
     * msg 返回信息
     */
    @JsonProperty("BYZD2")
    private String byzd2;
    /**
     * 备用字段3
     */
    @JsonProperty("BYZD3")
    private String byzd3;
    /**
     * 备用字段4
     */
    @JsonProperty("BYZD4")
    private String byzd4;
    /**
     * 备用字段5
     */
    @JsonProperty("BYZD5")
    private String byzd5;

    /**
     * 单据层级设置 0 订单  1应收单+订单
     */
    @JsonProperty("DJCJSZ")
    private String djcjsz;

    /**
     * 清单项目名称
     */
    @ApiModelProperty("清单项目名称")
    @JSONField(name = "QDXMMC")
    @JsonProperty("QDXMMC")
    private String qdxmmc;
    /**
     * 红字信息表编号
     */
    @ApiModelProperty("红字信息表编号")
    @JSONField(name = "HZXXBBH")
    @JsonProperty("HZXXBBH")
    private String hzxxbbh;
    /**
     * 红字信息表状态
     */
    @ApiModelProperty("红字信息表状态")
    private String hzqrxxztDm;

    /**
     * 查询类型 1销项发票 2 进项发票
     */
    @ApiModelProperty("红字信息表状态")
    private String gjbq;

    /**
     * 发票来源
     */
    @ApiModelProperty("发票来源")
    @JSONField(name = "FPLY")
    @JsonProperty("FPLY")
    private String fply;

    /**
     * 发票状态
     */
    @ApiModelProperty("发票状态")
    @JSONField(name = "FPZT")
    @JsonProperty("FPZT")
    private String fpzt;
    /**
     * 是否蓝字发票
     */
    @ApiModelProperty("是否蓝字发票")
    @JSONField(name = "SFLZFP")
    @JsonProperty("SFLZFP")
    private String sflzfp;

    /**
     * 开票方纳税人识别号
     */
    @ApiModelProperty("开票方纳税人识别号")
    @JSONField(name = "KPFNSRSBH")
    @JsonProperty("KPFNSRSBH")
    private String kpfnsrsbh;

    /**
     * 发票票种代码
     */
    @ApiModelProperty("发票票种代码")
    @JSONField(name = "FPPZDM")
    @JsonProperty("FPPZDM")
    private String fppzdm;

    /**
     * 差额征税类型代码
     */
    @ApiModelProperty("差额征税类型代码")
    @JSONField(name = "CEZSLXDM")
    @JsonProperty("CEZSLXDM")
    private String cezslxdm;

    private String kprqStr;

    /**
     * 扣除额
     */
    @ApiModelProperty("扣除额")
    @JSONField(name = "KCE")
    @JsonProperty("KCE")
    private String kce;

    /**
     * 受票方是否为自然人 (0否  1是)
     */
    @ApiModelProperty("受票方是否为自然人")
    @JSONField(name = "SPFZRRBS")
    @JsonProperty("SPFZRRBS")
    private String spfzrrbs;
    /**
     * 销售方自然人标志 (0否  1是)
     */
    @ApiModelProperty("销售方自然人标识")
    @JsonProperty("XSFZRRBS")
    private String xsfzrrbs;
    /**
     * 应收单号
     */
    @ApiModelProperty("应收单号")
    @JSONField(name = "YSDH")
    @JsonProperty("YSDH")
    private String ysdh;
    /**
     * 原应收单号
     */
    @ApiModelProperty("原应收单号")
    @JSONField(name = "YYSDH")
    @JsonProperty("YYSDH")
    private String yysdh;

    /**
     * 原订单号
     */
    @ApiModelProperty("原订单号")
    @JSONField(name = "YDDH")
    @JsonProperty("YDDH")
    private String yddh;

    /**
     * 原订单id
     */
    @ApiModelProperty("原订单id")
    @JSONField(name = "YDDID")
    @JsonProperty("YDDID")
    private String yddid;

    /**
     * 经办人国籍
     */
    @ApiModelProperty("经办人国籍")
    @JSONField(name = "JBRGJ")
    @JsonProperty("JBRGJ")
    private String jbrgj;

    /**
     * 经办人自然人纳税人识别号
     */
    @ApiModelProperty("经办人自然人纳税人识别号")
    @JSONField(name = "JBRZRRNSRSBH")
    @JsonProperty("JBRZRRNSRSBH")
    private String jbrzrrnsrsbh;

    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;

    @TableField(exist = false)
    private String startJshj;

    @TableField(exist = false)
    private String endJshj;

    @TableField(exist = false)
    private String redInvoiceId;

    @TableField(exist = false)
    private String baseNsrsbh;

    @TableField(exist = false)
    private String gxfxz;

    // ddly 0 1 2 3 为标普系统 ---1  4 电服平台----0
    @TableField(exist = false)
    private String kpqd;

    @TableField(exist = false)
    private String dfnsrsbh;

    @TableField(exist = false)
    private String dfnsrmc;

    @TableField(exist = false)
    @JsonProperty("BLUEHJJE")
    private String blueHjje;

    /**
     * 单据层级设置  0-显示订单    1-显示应收单+订单
     */
    @JsonProperty("DDORYSD")
    @TableField(exist = false)
    private String ddhOrYsd;

    /**
     * 编辑前总金额  0-开具    1-暂存
     */
    @TableField(exist = false)
    private String kjzcbs;

    /**
     * 商品明细行是否显示  0-显示    1-不显示
     */
    @TableField(exist = false)
    @JsonProperty("SPMXHXS")
    private String spmxhxs;

    /**
     * 进入标志 0-立即开票 1-编辑 2-对外接口
     */
    @TableField(exist = false)
    private String bjbz;

    @TableField(exist = false)
    @JsonProperty("NSRSBH")
    @JSONField(name = "NSRSBH")
    private String nsrsbh;

    @TableField(exist = false)
    private RedInvoiceConfirmEntity redConfirmInfo;

    @TableField(exist = false)
    private List<DdhAndInvoiceItemInfo> ddhAndInvoiceItemInfos;

    @TableField(exist = false)
    private List<String> yysdhList;
    @TableField(exist = false)
    private List<String> yddhList;

    /**
     * 发票明细
     */
    @ApiModelProperty("发票明细行集合")
    @JSONField(name = "DDMXXX")
    @JsonProperty("DDMXXX")
    @TableField(exist = false)
    List<OrderInvoiceItemEntity> itemEntityList;

    /**
     * 附加信息
     */
    @ApiModelProperty("添加附加信息集合")
    @JSONField(name = "DDFJXX")
    @JsonProperty("DDFJXX")
    @TableField(exist = false)
    List<InvoiceAdditionInfoEntity> infoEntityList;

    /**
     * 交付状态(包含三种：1交付手机号 2交付邮箱 3未交付)
     */
    @TableField(exist = false)
    private List<String> jfzts;

    /**
     * 是否手工标记为开票完成（0全部 1是 2否）
     */
    @TableField(exist = false)
    private String sgbj;

    /**
     * 开票日期起
     */
    @TableField(exist = false)
    private String kprqq;

    /**
     * 开票日期止
     */
    @TableField(exist = false)
    private String kprqz;

    /**
     * 不含税金额起
     */
    @TableField(exist = false)
    private String startBhsje;

    /**
     * 不含税金额止
     */
    @TableField(exist = false)
    private String endBhsje;

    /**
     * 税额起
     */
    @TableField(exist = false)
    private String startSe;

    /**
     * 税额止
     */
    @TableField(exist = false)
    private String endSe;

    /**
     * 冲红状态(0代表其他 1代表冲红)
     */
    @TableField(exist = false)
    private String chzt;

    /**
     * id列表，用于批量开票（好像没啥用）
     */
    @TableField(exist = false)
    private List<String> ids;

    /**
     * XML下载地址
     */
    @TableField(exist = false)
    @JsonProperty("XMLZJL")
    private String xmlUrl;

    /**
     * 是否手机交付
     */
    @ApiModelProperty("手机交付状态")
    @JSONField(name = "SJJFZT")
    @JsonProperty("SJJFZT")
    private String sjjfzt;

    /**
     * 是否邮件交付
     */
    @ApiModelProperty("邮件交付状态")
    @JSONField(name = "YXJFZT")
    @JsonProperty("YXJFZT")
    private String yxjfzt;

    /**
     * 被冲红蓝票的对应红票
     */
    @TableField(exist = false)
    @JsonProperty("DYHP")
    private String DYHP;

    /**
     * 编辑删除 明细ID
     */
    @TableField(exist = false)
    private List<String> deleteItemId;
    /**
     * 放弃税收优惠原因
     */
    @ApiModelProperty("放弃税收优惠原因")
    @JSONField(name = "GIVEUPREASON")
    @JsonProperty("GIVEUPREASON")
    private String giveUpReason;

    /**
     * 特定业务
     */
    @ApiModelProperty("特定业务")
    @JsonProperty("tdyw")
    @TableField(exist = false)
    private InvoiceTdywEntity invoiceTdywEntity;

    @ApiModelProperty("是否展示购方地址电话")
    @TableField(exist = false)
    private String sfzsgfdzdh;

    @ApiModelProperty("是否展示购方银行账号")
    private String sfzsgfyhzh;

    @ApiModelProperty("是否展示销方地址电话")
    @TableField(exist = false)
    private String sfzsxfdzdh;

    @ApiModelProperty("是否展示销方银行账号")
    private String sfzsxfyhzh;

    @ApiModelProperty("差额征税集合")
    @TableField(exist = false)
    private List<InvoiceCezsEntity> cezslist;
    /**
     * 购买方证件类型
     */
    private String gmfzjlx;
    /**
     * 购买方证件号码
     */
    private String gmfzjhm;
    /**
     * 购买方国籍
     */
    private String gmfgj;
    /**
     * 电商平台原始订单编号
     */
    private String dsptysddbh;
    /**
     * 电商平台类型
     */
    private String dsptlx;
    /**
     * 提取码，动态码开票使用
     */
    @TableField(exist = false)
    private String tqm;
    //结算方式代码
    private String jsfsdm;
    //开票人证件号码
    private String kprzjhm;
    //开票人证件类型
    private String kprzjlx;
    //征收方式 空：非差额发票 01：全额开票 02：差额开票
    private String zsfs;
    /**
     * 凭证类型（差额征税勾选凭证时使用）
     */
    @TableField(exist = false)
    private String pzlx;
    /**
     * 系统来源（用于自动回推时匹配业务系统使用）
     */
    private String xtly;
}
