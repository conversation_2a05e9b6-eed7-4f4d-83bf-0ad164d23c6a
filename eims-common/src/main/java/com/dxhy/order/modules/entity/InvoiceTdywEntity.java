package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 发票特定业务
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_tdyw")
@Data
public class InvoiceTdywEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * OrderInfoEnum.ORDER_QD_TDYS_00 相关枚举
	 */
	@ApiModelProperty("特定要素")
	private String tdys;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;

	/**
	 * 不动产租赁数据集合
	 */
	@ApiModelProperty("不动产租赁信息集合")
	@TableField(exist = false)
	private List<InvoiceBdczlxxEntity> bdczlxx;

	/**
	 * 不动产销售数据集合
	 */
	@ApiModelProperty("不动产销售信息集合")
	@TableField(exist = false)
	private List<InvoiceBdcxsxxEntity> bdcxsxx;

	/**
	 * 建筑服务信息
	 */
	@ApiModelProperty("建筑服务信息")
	@TableField(exist = false)
	private InvoiceJzfwxxEntity jzfwxx;

	/**
	 * 旅客运输服务信息数据集合
	 */
	@ApiModelProperty("旅客运输服务信息数据集合")
	@TableField(exist = false)
	private List<InvoiceLkysfwxxEntity> lkysfwxx;

	/**
	 * 货物运输服务信息数据集合
	 */
	@ApiModelProperty("货物运输信息")
	@TableField(exist = false)
	private List<InvoiceHwysxxEntity> hwysxx;

	/**
	 * 机动车信息
	 */
	@ApiModelProperty("机动车信息")
	@TableField(exist = false)
	private InvoiceJdcxxEntity jdcxx;

	/**
	 * 二手车信息
	 */
	@ApiModelProperty("二手车信息")
	@JsonProperty("escxx")
	@TableField(exist = false)
	private InvoiceEscxxEntity escxx;

}
