package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceConfigDao;
import com.dxhy.order.modules.entity.InvoiceConfigInfo;
import com.dxhy.order.modules.service.InvoiceConfigService;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @description 针对表【invoice_config】的数据库操作Service实现
 * @createDate 2022-08-26 12:14:23
 */
@Service("InvoiceConfigService")
@Slf4j
@RefreshScope
public class InvoiceConfigServiceImpl extends ServiceImpl<InvoiceConfigDao, InvoiceConfigInfo>
        implements InvoiceConfigService {

    private static final String LOGGER_MSG = "(开票配置)";
    @Value("${sso.deptByNameAndCodeUrl}")
    private String deptByNameAndCodeUrl;
    @Value("${sso.authNameByTaxno}")
    private String authNameByTaxno;

    @Resource
    private InvoiceConfigDao invoiceConfigDao;

    /**
     * 查詢
     */
    @Override
    public R queryList(InvoiceConfigInfo invoiceConfigInfo) {
        log.info("{}，执行查询操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo));

        // 如果没有默认数据，先拉取默认数据入库
        List<InvoiceConfigInfo> list = invoiceConfigDao.selectList(null, invoiceConfigInfo);
        try {
            if (list.size() < 1) {

                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = attributes.getRequest();
                // 获取token
                String auth = request.getHeader("Authorization");
                log.info("{}，get Authorization: {}", LOGGER_MSG, auth);
                Map token = new HashMap();
                token.put("Authorization", auth);
                log.info("{}，token：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(token));

                Map req = new HashMap();
                req.put("name", "");
                req.put("taxpayerCode", invoiceConfigInfo.getNsrsbh());
                // String res = HttpUtils.doPost(deptByNameAndCodeUrl, JsonUtils.getInstance().toJsonString(req));
                String res = HttpUtils.doPostWithHeader(deptByNameAndCodeUrl, JsonUtils.getInstance().toJsonString(req), token);
                Map resp = JsonUtils.getInstance().parseObject(res, HashMap.class);
                log.info("{}，执行新增数据拉取返回:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(resp));
                if ("0000".equals(resp.get("code").toString())) {
                    log.info("{}，执行填充新增拉取到数据：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(resp.get("data")));
                    Map data = JsonUtils.getInstance().parseObject(resp.get("data").toString(), HashMap.class);

                    if (ObjectUtils.isEmpty(data.get("taxpayerAddress"))
                            && ObjectUtils.isEmpty(data.get("taxpayerPhone"))
                            && ObjectUtils.isEmpty(data.get("taxpayerBank"))
                            && ObjectUtils.isEmpty(data.get("taxpayerAccount"))) {
                        log.info("{}，调用门户查询企业信息成功，但数据参数为空", LOGGER_MSG);
                    } else {

                        InvoiceConfigInfo invoiceConfigInfo1 = new InvoiceConfigInfo();
                        invoiceConfigInfo1.setId(DistributedKeyMaker.generateShotKey());
                        invoiceConfigInfo1.setQymc(ObjectUtils.isEmpty(data.get("name")) ? "" : data.get("name").toString());
                        invoiceConfigInfo1.setNsrsbh(invoiceConfigInfo.getNsrsbh());
                        invoiceConfigInfo1.setDz(ObjectUtils.isEmpty(data.get("taxpayerAddress")) ? "" : data.get("taxpayerAddress").toString());
                        invoiceConfigInfo1.setDh(ObjectUtils.isEmpty(data.get("taxpayerPhone")) ? "" : data.get("taxpayerPhone").toString());
                        invoiceConfigInfo1.setKhyh(ObjectUtils.isEmpty(data.get("taxpayerBank")) ? "" : data.get("taxpayerBank").toString());
                        invoiceConfigInfo1.setYhzh(ObjectUtils.isEmpty(data.get("taxpayerAccount")) ? "" : data.get("taxpayerAccount").toString());
//                invoiceConfigInfo1.setKpr(StringUtils.isEmpty(data.get("contactName").toString()) ? "" : data.get("contactName").toString());
                        invoiceConfigInfo1.setZhlx("0");
                        invoiceConfigInfo1.setCreateTime(new Date());
                        invoiceConfigInfo1.setUpdateTime(new Date());
                        log.info("{}，执行新增拉取到数据：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo1));
                        int i = invoiceConfigDao.insert(invoiceConfigInfo1);
                        if (i < 1) {
                            log.error("{}，执行新增数据入库失败", LOGGER_MSG);
                        }
                    }
                } else {
                    log.error("{}，调用门户查询企业信息失败", LOGGER_MSG);
                }
            }
        } catch (Exception e) {
            log.error("{}，调用门户查询企业信息异常：{}", LOGGER_MSG, e);
            return R.ok().put("data", null);
        }

        Page page = new Page(invoiceConfigInfo.getCurrPage(), invoiceConfigInfo.getPageSize());
        List<InvoiceConfigInfo> list1 = invoiceConfigDao.selectList(page, invoiceConfigInfo);
        page.setRecords(list1);
        PageUtils pageUtil = new PageUtils(page);
        log.info("{}，执行查询操作成功：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(list));
        return R.ok().put("data", pageUtil);
    }

    /**
     * 查詢开票人
     */
    @Override
    public R queryPersons(InvoiceConfigInfo invoiceConfigInfo) {
        log.info("{}，执行开票人入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo));
        Map req = new HashMap();
        req.put("taxNo", invoiceConfigInfo.getNsrsbh());
        req.put("flag", "false");
        String res = "";
        try {
            res = HttpClientUtil.doGet(authNameByTaxno, req);
            log.info("{}，执行开票人拉取返回：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(res));
        } catch (Exception e) {
            log.info("{}，执行开票人拉取返回异常：{}", LOGGER_MSG, e);
        }
        Map resp = JsonUtils.getInstance().parseObject(res, HashMap.class);
        if ("0000".equals(resp.get("code")) && !StringUtils.isEmpty(resp.get("data").toString())) {
            List<String> list = JsonUtils.getInstance().parseObject(resp.get("data").toString(), ArrayList.class);
            List<String> listNotNull = new ArrayList<>();
            for (String s : list) {
                if (!StringUtils.isEmpty(s)) {
                    listNotNull.add(s);
                }
            }
            log.info("{}，执行开票人返回：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(listNotNull));
            return R.ok().put("data", listNotNull);
        }
        return R.ok();
    }


    /**
     * 新增
     */
    @Override
    public R insertInvoiceConfig(InvoiceConfigInfo invoiceConfigInfo) {
        log.info("{}，执行新增操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo));

        // 重复数据校验
        int cnt = invoiceConfigDao.countForPhone(invoiceConfigInfo.getDh());
        if (cnt > 0 || StringUtils.isEmpty(invoiceConfigInfo.getDh())) {
            return R.error("0001", "手机号重复，请重新填写！");
        }

        invoiceConfigInfo.setId(DistributedKeyMaker.generateShotKey());
        invoiceConfigInfo.setCreateTime(new Date());
        invoiceConfigInfo.setUpdateTime(new Date());

        // 如果无默认数据，则新增的第一条数据就设置为默认数据
        List<InvoiceConfigInfo> selectList = invoiceConfigDao.selectList(null, invoiceConfigInfo);
        if (selectList.size() < 1) {
            invoiceConfigInfo.setZhlx("0");
            log.info("{}，新增数据设置为默认勾选", LOGGER_MSG);
        } else {
            // 当前数据被勾选默认（1 默认账户），则需要修改另一个被勾选的数据为未被勾选 （0普通账户）
            if ("0".equals(invoiceConfigInfo.getZhlx())) {
                // 判断是否有其他数据被勾选默认
                InvoiceConfigInfo invoiceConfigInfo1 = invoiceConfigDao.checkHadConfigByNsrsbh(invoiceConfigInfo);
                if (!ObjectUtils.isEmpty(invoiceConfigInfo1)) {
                    int j = invoiceConfigDao.updateZhlxByNsrsbh(invoiceConfigInfo);
                    if (j < 1) {
                        log.info("{}，新增更新勾选默认失败", LOGGER_MSG);
                    }
                }
            }
        }

        int i = invoiceConfigDao.insert(invoiceConfigInfo);
        if (i < 1) {
            log.info("{}，执行新增操作失败ID:{}", LOGGER_MSG, invoiceConfigInfo.getId());
            return R.error();
        }
        log.info("{}，执行新增操作成功ID：{}", LOGGER_MSG, invoiceConfigInfo.getId());
        return R.ok();
    }

    /**
     * 编辑
     */
    @Override
    public R updateInvoiceConfig(InvoiceConfigInfo invoiceConfigInfo) {
        log.info("{}，执行编辑操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo));
        invoiceConfigInfo.setUpdateTime(new Date());
        if ("0".equals(invoiceConfigInfo.getZhlx())) {
            InvoiceConfigInfo invoiceConfigInfo1 = invoiceConfigDao.checkHadConfigByNsrsbh(invoiceConfigInfo);
            if (!ObjectUtils.isEmpty(invoiceConfigInfo1)) {
                int j = invoiceConfigDao.updateZhlxByNsrsbh(invoiceConfigInfo);
                if (j < 1) {
                    log.info("{}，编辑更新勾选默认失败", LOGGER_MSG);
                }
            }
        }
        int i = invoiceConfigDao.updateById(invoiceConfigInfo);
        if (i < 1) {
            log.info("{}，执行编辑操作失败ID", LOGGER_MSG, invoiceConfigInfo.getId());
            return R.error();
        }
        log.info("{}，执行编辑操作成功ID：{}", LOGGER_MSG, invoiceConfigInfo.getId());
        return R.ok();
    }

    /**
     * 删除
     */
    @Override
    public R delInvoiceConfig(InvoiceConfigInfo invoiceConfigInfo) {
        log.info("{}，执行删除操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo));
        int i = invoiceConfigDao.deleteById(invoiceConfigInfo.getId());
        if (i < 1) {
            log.info("{}，执行删除操作失败", LOGGER_MSG);
            return R.error();
        }
        log.info("{}，执行删除操作成功ID：{}", LOGGER_MSG, invoiceConfigInfo.getId());
        return R.ok();
    }

    /**
     * 批量删除
     */
    @Override
    public R delMoreInvoiceConfig(Map map) {
        log.info("{}，执行删除操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(map));
        String s = map.get("ids").toString();
        String[] strings = s.split(",");
        for (String ele : strings) {
            InvoiceConfigInfo invoiceConfigInfo = new InvoiceConfigInfo();
            invoiceConfigInfo.setId(ele);
            int i = invoiceConfigDao.deleteById(invoiceConfigInfo.getId());
            if (i < 1) {
                log.info("{}，执行删除操作失败", LOGGER_MSG);
                return R.error();
            }
        }
        log.info("{}，执行删除操作成功", LOGGER_MSG);
        return R.ok();
    }


    /**
     * 默认账户勾选校验
     */
    @Override
    public R checkHadConfig(InvoiceConfigInfo invoiceConfigInfo) {
        log.info("{}，默认账户勾选校验入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(invoiceConfigInfo));
        InvoiceConfigInfo invoiceConfigInfo1 = invoiceConfigDao.checkHadConfigByNsrsbh(invoiceConfigInfo);
        if (ObjectUtils.isEmpty(invoiceConfigInfo1)) {
            log.info("{}，无默认账户：{}", LOGGER_MSG, invoiceConfigInfo.getNsrsbh());
            return R.ok().put("data", "");
        }
        log.info("{}，已有默认账户：{}", LOGGER_MSG, invoiceConfigInfo.getNsrsbh());
        return R.ok().put("data", invoiceConfigInfo1);
    }
}




