package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 销方税号租户关联表
 * <AUTHOR>
 * @Date 2022/6/27 12:04
 * @Version 1.0
 **/
@TableName("nsrsbh_tenant_relation")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("销方税号租户关联表")
public class NsrsbhTenantRelationEntity implements Serializable {
   private static final long serialVersionUID = 1L;
   /**
    * 纳税人识别号
    */
   @ApiModelProperty("税收分类编码id")
   @TableId(type = IdType.INPUT)
   private String nsrsbh;


   private String nsrmc;


   @TableField(exist = false)
   private String dbUrl;

   private int id;
   /**
    * 租户编码
    */
   @ApiModelProperty("租户编码")
   private String tenantCode;

}
