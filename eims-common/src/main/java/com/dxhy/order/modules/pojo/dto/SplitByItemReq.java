package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import lombok.Data;

import java.util.List;

@Data
public class SplitByItemReq {
    private List<OrderInvoiceItemEntity> itemInfos;
    private String ddh;
    private String yddh;
    private String ysdh;
    private String yysdh;
    private String baseNsrsbh;
    private String hsbz;//0  不含税   1 含税
}
