package com.dxhy.order.modules.entity;


import com.dxhy.order.utils.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 开票记录 - 查询请求实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-23 11:37:24
 */
@Data
public class InvoiceRecordQueryList extends BasePage implements Serializable {

    /**
     * 开票日期 yyyy-MM-dd
     */
    private String ksrq;

    /**
     * 结束日期 yyyy-MM-dd
     */
    private String jsrq;

    /**
     * 发票票种
     */
    private String fpzldm;

    /**
     * 开票类型
     */
    private String kplx;

    /**
     * 开具结果
     */
    private String kjjg;

    /**
     * 购方名称
     */
    private String ghfmc;

    /**
     * 购方税号
     */
    private String ghfNsrsbh;

    /**
     * 全电发票号码
     */
    private String qdfphm;

    /**
     * 项目名称
     */
    private String xmmc;

    /**
     * 发票状态
     */
    private String kpzt;

    /**
     * 开票人
     */
    private String kpr;

    /**
     * 基础纳税人识别号
     */
    private String baseNsrsbh;

    /**
     * 订单来源 ： ddly  订单来源(0：扫码开票；1：批量导入，2接口，3手动填开 4 慧企)
     * 前端显示 ： 0 1 2 3 是 标普系统，4  是电服平台，
     */
    private String ddly;


}
