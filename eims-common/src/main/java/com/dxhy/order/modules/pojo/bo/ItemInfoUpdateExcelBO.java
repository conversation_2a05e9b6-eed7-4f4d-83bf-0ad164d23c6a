package com.dxhy.order.modules.pojo.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 上传项目信息 商品信息BO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel("上传项目信息 商品信息BO")
public class ItemInfoUpdateExcelBO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "项目名称", index = 0)
    @ApiModelProperty("项目名称")
    private String xmmc;

    @ExcelProperty(value = "商品和服务分类简称", index = 1)
    @ApiModelProperty("商品和服务分类简称")
    private String sphfffljc;

    @ExcelProperty(value = "商品和税收分类编码", index = 2)
    @ApiModelProperty("商品和税收分类编码")
    private String sphssflbm;

    @ExcelProperty(value = "单价", index = 3)
    @ApiModelProperty("单价")
    private String dj;

    @ExcelProperty(value = "含税标志", index = 4)
    @ApiModelProperty("含税标志 含税&不含税")
    private String hsbs;

    @ExcelProperty(value = "规格型号", index = 5)
    @ApiModelProperty("规格型号")
    private String ggxh;

    @ExcelProperty(value = "税率/征收率", index = 6)
    @ApiModelProperty("税率/征收率 6位小数")
    private String sl;

    @ExcelProperty(value = "单位", index = 7)
    @ApiModelProperty("单位")
    private String dw;

    @ExcelProperty(value = "简码", index = 8)
    @ApiModelProperty("简码")
    private String jm;

    @ExcelProperty(value = "是否使用优惠政策", index = 9)
    @ApiModelProperty("是否使用优惠政策 是&否")
    private String sfsyyhzc;

    @ExcelProperty(value = "优惠政策类型", index = 10)
    @ApiModelProperty("优惠政策类型")
    private String yhzclx;

    @ExcelProperty(value = "项目分类名称", index = 11)
    @ApiModelProperty("项目分类名称")
    private String xmflmc;

    @ExcelProperty(value = "项目分类编码", index = 12)
    @ApiModelProperty("项目分类编码")
    private String xmflbm;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    public void clearNull(){
        xmmc = xmmc == null ? "" : xmmc;
        sphfffljc = sphfffljc == null ? "" : sphfffljc;
        sphssflbm = sphssflbm == null ? "" : sphssflbm;
        dj = dj == null ? "" : dj;
        hsbs = hsbs == null ? "" : hsbs;
        ggxh = ggxh == null ? "" : ggxh;
        sl = sl == null ? "" : sl;
        dw = dw == null ? "" : dw;
        jm = jm == null ? "" : jm;
        sfsyyhzc = sfsyyhzc == null ? "" : sfsyyhzc;
        yhzclx = yhzclx == null ? "" : yhzclx;
        xmflmc = xmflmc == null ? "" : xmflmc;
        xmflbm = xmflbm == null ? "" : xmflbm;
        errorMsg = errorMsg == null ? "" : errorMsg;
    }

}
