package com.dxhy.order.modules.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrderInvoiceItemExcelFailEntity {
    // 发票流水号
    @ExcelProperty(value = "订单号*",index = 0)
    private String fpqqlsh;
    // 项目名称
    @ExcelProperty(value = "项目名称*",index = 1)
    private String xmmc;
    // 商品和服务税收编码
    @ExcelProperty(value = "商品和服务税收编码*",index = 2)
    private String spbm;
    // 规格型号
    @ExcelProperty(value = "规格型号",index = 3)
    private String ggxh;
    // 单位
    @ExcelProperty(value = "单位*",index = 4)
    private String dw;
    // 数量
    @ExcelProperty(value = "数量",index = 5)
    private String xmsl;
    // 单价
    @ExcelProperty(value = "单价",index = 6)
    private String dj;
    // 金额
    @ExcelProperty(value = "金额",index = 7)
    private String je;
    // 税率
    @ExcelProperty(value = "税率",index = 8)
    private String sl;
    // 折扣金额
    @ExcelProperty(value = "折扣金额",index = 9)
    private String zke;
    // 是否使用优惠政策
    @ExcelProperty(value = "是否使用优惠政策",index = 10)
    private String yhzcbs;
    // 优惠政策类型
    @ExcelProperty(value = "优惠政策类型",index = 11)
    private String byzd1;
    // 即征即退类型
    @ExcelProperty(value = "即征即退类型",index = 12)
    private String jzjtlx;
}
