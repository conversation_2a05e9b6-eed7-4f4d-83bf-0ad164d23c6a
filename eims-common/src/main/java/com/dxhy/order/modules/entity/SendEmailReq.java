package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendEmailReq implements Serializable {

    /** 请求上下 (参考上方通用报文格式) **/
    private SendMsgReqContext sessionContext;
    /** 平台编号 **/
    private String platformNo;
    /** 平台密钥 **/
    private String platformKey;
    /** 调用端ip地址 **/
    private String ipAddress;
    /** 请求上下 (参考上方通用报文格式) **/
    private String title;
    /** 邮件内容，支持html**/
    private String content;
    /** 收件人邮件地址列表 **/
    private List<String> receiverList;
    /** 抄送人邮件地址列表 **/
    private List<String> ccList;
    /** 密送人邮件地址列表 **/
    private List<String> bccList;
    /** 附件列表 **/
    private List<SendEmailReqFile> attachmentList;
    /** 发送渠道 **/
    private String channel;
}
