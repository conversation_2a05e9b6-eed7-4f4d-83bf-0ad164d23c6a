package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 场景模板 附加信息关联关系表
 * <AUTHOR>
 * @Date 2022/6/27 12:04
 * @Version 1.0
 **/
@TableName("template_addition_relation")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
public class TemplateAdditionRelationEntity implements Serializable {
   private static final long serialVersionUID = 1L;
   /**
    * id
    */
   @ApiModelProperty("场景模板主键")
   @TableId(type = IdType.INPUT)
   private String id;
   /**
    * 场景模板id
    */
   @ApiModelProperty("场景模板id")
   private String sceneTemplateId;
   /**
    * 附加信息id
    */
   @ApiModelProperty("附加信息id")
   private String additionEleId;
}
