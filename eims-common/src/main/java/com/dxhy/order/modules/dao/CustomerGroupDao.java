package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.CustomerGroupEntity;
import com.dxhy.order.modules.pojo.dto.CustomerGroupSaveDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户信息分类表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface CustomerGroupDao extends BaseMapper<CustomerGroupEntity> {

    /**
     * 根据纳税人识别号查询客户信息分类
     * @param baseNsrsbh
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerGroupEntity>
     * <AUTHOR>
     **/
    List<CustomerGroupEntity> listByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 根据纳税人识别号查询二级客户信息分类（excel上传数据时用）
     * @param baseNsrsbh
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerGroupEntity>
     * <AUTHOR>
     **/
    List<CustomerGroupEntity> listLevelTwoDataByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 新增和修改时候查重客户信息分类
     * @param customerGroupSaveDTO
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerGroupEntity>
     * <AUTHOR>
     **/
    List<CustomerGroupEntity> selectListByCustomerGroupSaveDTO(CustomerGroupSaveDTO customerGroupSaveDTO);

    /**
     * 根据父级ID查询下级ID列表（删除用）
     * @param parentId
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     **/
    List<String> listIdByParentId(@Param("parentId") String parentId);

    /**
     * 批量保存数据
     * @param customerGroupEntityList
     * @return void
     * <AUTHOR>
     **/
    void insertList(@Param("customerGroupEntityList") List<CustomerGroupEntity> customerGroupEntityList);

}
