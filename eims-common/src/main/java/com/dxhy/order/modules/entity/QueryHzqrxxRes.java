package com.dxhy.order.modules.entity;

import lombok.Data;

import java.util.List;

@Data
public class QueryHzqrxxRes {
    private String Result;
    private String Message;
    private String uuid;
    private String hzfpxxqrdbh;
    private String lrfsf;
    private String xsfnsrsbh;
    private String xsfmc;
    private String gmfnsrsbh;
    private String gmfmc;
    private String lzfphm;
    private String lzfpdm;
    private String lzkprq;
    private String lzhjje;
    private String lzhjse;
    private String lzfppzDm;
    private String zzsytDm;
    private String xfsytDm;
    private String fprzztDm;
    private String hzcxje;
    private String hzcxse;
    private String chyyDm;
    private String hzqrxxztDm;
    private String ykjhzfpbz;
    private List<HzqrxxmxListRes> hzqrxxmxList;
}
