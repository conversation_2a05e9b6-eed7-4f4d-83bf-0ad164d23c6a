package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.SalerWarningInfo;
import com.dxhy.order.utils.R;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【saler_warning(发票预警表)】的数据库操作Service
* @createDate 2022-07-21 13:39:13
*/
public interface SalerWarningService extends IService<SalerWarningInfo> {

    /**
     * 发票预警 - 查询
     * @param map
     * @return
     */
    Map queryWarnInfo(Map map);

    /**
     * 发票预警 - 更新
     * @param map
     * @return
     */
    R updateWarnInfo(Map map);

    /**
     * 发票预警 - 预警定时任务
     * @return
     */
    void warningTask();


}
