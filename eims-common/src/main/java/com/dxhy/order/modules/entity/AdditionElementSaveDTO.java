package com.dxhy.order.modules.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 保存 附加信息DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("保存 附加信息DTO")
public class AdditionElementSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 附加信息主键（此参数为空时表示新增 不为空时表示修改）
     */
    @ApiModelProperty("附加信息主键（此参数为空时表示新增 不为空时表示修改）")
    private String id;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 附加信息名称
     */
    @ApiModelProperty(name = "附加信息名称", required = true)
    private String fjxxmc;

    /**
     * 数据类型 1 文本型 2 数值型 3 日期型
     */
    @ApiModelProperty(name = "数据类型 1 文本型 2 数值型 3 日期型", required = true)
    private String sjlx;

    /**
     * 第三方接口慧企 返回的UUID
     */
    private String uuid;

    private String current;

    private String size;

    /**
     * 第三方接口慧企 删除需要的uuid集合
     */
    private List<String> uuidList;

}
