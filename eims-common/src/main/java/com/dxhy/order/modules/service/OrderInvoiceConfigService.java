package com.dxhy.order.modules.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.OrderInvoiceConfigEntity;
import com.dxhy.order.utils.R;

/**
 * 开票配置表
 *
 * <AUTHOR>
 * @email
 * @date 2022-12-06 20:00:18
 */
public interface OrderInvoiceConfigService extends IService<OrderInvoiceConfigEntity> {

    R saveOrderInvoiceConfigEntity(OrderInvoiceConfigEntity orderInvoiceConfig);

    R saveChgz(OrderInvoiceConfigEntity orderInvoiceConfig);

    R selectOrderInvoiceConfigEntity(OrderInvoiceConfigEntity orderInvoiceConfig);

    R taxpayerNsrsbh(OrderInvoiceConfigEntity orderInvoiceConfig);

    int insertDefaultInfo(String nsrsbh);

}

