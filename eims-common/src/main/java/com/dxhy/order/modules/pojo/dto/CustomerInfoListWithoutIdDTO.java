package com.dxhy.order.modules.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 列表 - 不选择客户分类 客户信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("列表 - 不选择客户分类 客户信息DTO")
public class CustomerInfoListWithoutIdDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty(name = "所属纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String gsmc;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String nsrsbh;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String scode;

    /**
     * 插入类型 1 手动录入
     */
    @ApiModelProperty(name = "插入类型 1 手动录入", required = true)
    private String intoType;

    /**
     * 租户内客户列表开关
     */
    @ApiModelProperty("开关判断")
    private String check = "ON";

    /**
     * 根据税号获取的集团下所有企业税号列表
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "根据税号获取的集团下所有企业税号列表", required = true)
    private List<String> baseNsrsbhList;


}
