package com.dxhy.order.modules.service;

import com.dxhy.order.modules.entity.InvoiceIssueTdywParam;
import com.dxhy.order.modules.entity.InvoiceTdywEntity;

import java.util.Collection;
import java.util.List;

/**
 * 特定业务接口服务
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
public interface InvoiceTdywService {

    /**
     * 关联保存特定业务入库
     *
     * @param invoiceTdywEntity
     * @return
     */
    void saveTdywRelation(InvoiceTdywEntity invoiceTdywEntity,String invoiceId);

    /**
     * 关联逻辑删除特定业务
     *
     * @param id
     * @return
     */
    void deleteByInvoiceId(String invoiceId);

    /**
     * 特定业务深拷贝（从param拷贝到entity）
     * @param tdywParam
     * @param tdywEntity
     */
    void copyTdywRelation(InvoiceIssueTdywParam tdywParam,InvoiceTdywEntity tdywEntity);

    /**
     * 特定业务深拷贝（从entity拷贝到param）
     * @param tdywParam
     * @param tdywEntity
     */
    void copyTdywEntityToParam(InvoiceTdywEntity tdywEntity,InvoiceIssueTdywParam tdywParam);

    /**
     * 根据发票主表id查询特定业务关联信息
     * @param invoiceIds
     * @return
     */
    List<InvoiceTdywEntity> queryTdywRelation(Collection<String> invoiceIds);
}

