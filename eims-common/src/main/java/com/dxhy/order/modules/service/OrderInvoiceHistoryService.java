package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.OrderInvoiceHistoryEntity;
import com.dxhy.order.utils.R;

/**
 * 发票下载历史表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
public interface OrderInvoiceHistoryService extends IService<OrderInvoiceHistoryEntity> {
    /**
     * 分页查询列表
     *
     * @param orderInvoiceHistoryEntity
     * @return
     */
    R queryPage(OrderInvoiceHistoryEntity orderInvoiceHistoryEntity);

    /**
     * 触发下载
     * @param orderInvoiceHistoryEntity
     */
    void downLoad(OrderInvoiceHistoryEntity orderInvoiceHistoryEntity);
}

