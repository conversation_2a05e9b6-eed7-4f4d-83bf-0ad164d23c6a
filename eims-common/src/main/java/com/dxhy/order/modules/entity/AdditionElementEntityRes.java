package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class AdditionElementEntityRes implements Serializable {

    /** uuid **/
    private String uuid;
    /** 纳税人识别号 **/
    private String nsrsbh;
    /** 纳税人名称 **/
    private String nsrmc;
    /** 附加要素项目名称 **/
    private String fjysxmmc;
    /** 数据类型 **/
    private String sjlx1;
    /** 有效标志 **/
    private String yxbz;
    /** 录入时间 **/
    private String lrrq;
    /** 修改日期 **/
    private String xgrq;
    /** 录入人名称 **/
    private String lrrmc;
    /** 修改人名称 **/
    private String xgrmc;
    /** 引用状态 1 引用0 未引用 **/
    private String yyzt;

}
