package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.CustomerInfoQrcodeEntity;
import com.dxhy.order.modules.pojo.dto.BaseNsrsbhWithPageDTO;
import com.dxhy.order.modules.pojo.dto.CustomerInfoQrcodeSaveDTO;
import com.dxhy.order.utils.R;

/**
 * 客户扫码信息表 service
 * <AUTHOR>
 * @Date 2022/6/27 12:10
 * @Version 1.0
 **/
public interface CustomerInfoQrcodeService extends IService<CustomerInfoQrcodeEntity> {

    /**
     * 新增扫码客户
     * @param customerInfoQrcodeSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveQrcodeCustomer(CustomerInfoQrcodeSaveDTO customerInfoQrcodeSaveDTO);

    /**
     * 扫码客户列表
     * @param baseNsrsbhWithPageDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listQrcodeCustomer(BaseNsrsbhWithPageDTO baseNsrsbhWithPageDTO);

}

