package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户扫码信息表
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@TableName("customer_info_qrcode")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("客户扫码信息表")
public class CustomerInfoQrcodeEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户扫码信息主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("客户扫码信息主键")
    private String id;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty("所属纳税人识别号")
    private String baseNsrsbh;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String gsmc;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String nsrsbh;

    /**
     * 公司地址
     */
    @ApiModelProperty("公司地址")
    private String gsdz;

    /**
     * 公司电话
     */
    @ApiModelProperty("公司电话")
    private String gsdh;

    /**
     * 开户银行
     */
    @ApiModelProperty("开户银行")
    private String khyh;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String yhzh;

    /**
     * 联系邮箱
     */
    @ApiModelProperty("联系邮箱")
    private String lxyx;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String lxdh;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
}
