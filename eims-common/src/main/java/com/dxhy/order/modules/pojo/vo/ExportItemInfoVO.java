package com.dxhy.order.modules.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 项目信息导出VO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@TableName("item_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("项目信息导出VO")
public class ExportItemInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String xmmc;

    /**
     * 商品和服务分类简称
     */
    @ApiModelProperty("商品和服务分类简称")
    private String sphssfljc;

    /**
     * 商品和服务税收分类编码
     */
    @ApiModelProperty("商品和服务税收分类编码")
    private String sphssflbm;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String dj;

    /**
     * 含税标识 0 不含税 1 含税
     */
    @ApiModelProperty("含税标识 0 不含税 1 含税")
    private String hsbs;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String ggxh;

    /**
     * 税率/征收率
     */
    @ApiModelProperty("税率/征收率")
    private String sl;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String dw;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String jm;

    /**
     * 是否使用优惠政策标识 0 否 1 是
     */
    @ApiModelProperty("是否使用优惠政策标识 0 否 1 是")
    private String yhzcbs;

    /**
     * 优惠政策类型
     */
    @ApiModelProperty("优惠政策类型")
    private String yhzclx;

    /**
     * 项目分类名称
     */
    @ApiModelProperty("项目分类名称")
    private String itemGroupName;

    /**
     * 项目分类编码
     */
    @ApiModelProperty("项目分类编码")
    private String itemGroupCode;

}
