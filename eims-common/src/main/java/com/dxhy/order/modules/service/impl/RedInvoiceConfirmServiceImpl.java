package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.constant.InvoiceStatusEnum;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.constant.OrderSourceEnum;
import com.dxhy.order.modules.dao.*;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.pojo.*;
import com.dxhy.order.pojo.single.FPZTXX;
import com.dxhy.order.pojo.single.SingleInvoiceInfoResult;
import com.dxhy.order.pojo.single.SingleInvoiceItemRes;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
@RefreshScope
public class RedInvoiceConfirmServiceImpl extends ServiceImpl<RedInvoiceConfirmDao, RedInvoiceConfirmEntity> implements RedInvoiceConfirmService {
    @Value("${order.encrypt.invoiceIssueUrl}")
    private String invoiceIssueUrl;

    @Value("${order.encrypt.invoiceInfosAndItemsUrl}")
    private String invoiceInfosAndItemsUrl;

    @Value("${order.encrypt.singelInvoiceInfosAndItemsUrl}")
    private String singelInvoiceInfosAndItemsUrl;

    @Value("${order.encrypt.redInvoiceConfirmSldhUrl}")
    private String redInvoiceConfirmSldhUrl;

    @Value("${order.encrypt.redInvoiceIssueUrl}")
    private String redInvoiceIssueUrl;

    @Value("${order.encrypt.redInvoiceResultInfoUrl}")
    private String redInvoiceResultInfoUrl;

    @Value("${order.encrypt.queryHzqrxxUrl}")
    private String queryHzqrxxUrl;

    @Value("${order.encrypt.hzqrxxUpdateUrl}")
    private String hzqrxxUpdateUrl;

    @Value("${order.encrypt.queryHzqrxxStatusUrl}")
    private String queryHzqrxxStatusUrl;

    @Value("${order.encrypt.invoiceGenerateRedUrl}")
    private String invoiceGenerateRedUrl;
    @Autowired
    private RedInvoiceConfirmDao redInvoiceConfirmDao;
    @Autowired
    OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    OrderInvoiceItemDao orderInvoiceItemDao;
    @Autowired
    InvoiceAdditionInfoDao invoiceAdditionInfoDao;
    @Autowired
    private InvoiceTdywService invoiceTdywService;
    @Autowired
    InvoiceCezsDao invoiceCezsDao;
    @Autowired
    ItemInfoDao itemInfoDao;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private SsoUtil ssoUtil;
    @Autowired
    private SalerWarningDao salerWarningDao;
    @Autowired
    private NsrsbhTenantRelationService nsrsbhTenantRelationService;
    @Autowired
    private TenantRdsService tenantRdsService;
    @Resource
    private OrderInvoiceConfigDao orderInvoiceConfigDao;
    @Autowired
    private OpenApiService openApiService;
    @Autowired
    private InvoiceBackpushConfigService invoiceBackpushConfigService;

    @Override
    public PageUtils queryPage(RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        Page page = new Page(redInvoiceConfirmEntity.getCurrPage(), redInvoiceConfirmEntity.getPageSize());
        List<RedInvoiceConfirmEntity> list = redInvoiceConfirmDao.selectList(page, redInvoiceConfirmEntity);
        if (!CollectionUtils.isEmpty(list)) {
            for (RedInvoiceConfirmEntity invoiceConfirmEntity : list) {
                // 格式化开票日期
                if (!StringUtils.isEmpty(invoiceConfirmEntity.getKprq())) {
                    String s = DateUtil.formatDate(invoiceConfirmEntity.getKprq());
                    invoiceConfirmEntity.setKprqStr(s);
                }
            }
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public R redInvoiceConfirmChoose(IdDTO idDTO) {
        String id = idDTO.getId();
        String baseNsrsbh = idDTO.getBaseNsrsbh();
        R detail = null;
        try {
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectById(id);
            if ("0".equals(orderInvoiceInfoEntity.getDdly())) {
                // 0：扫码开票
                orderInvoiceInfoEntity.setKpqd("1");
            } else if ("1".equals(orderInvoiceInfoEntity.getDdly())) {
                // 1：批量导入
                orderInvoiceInfoEntity.setKpqd("1");
            } else if ("2".equals(orderInvoiceInfoEntity.getDdly())) {
                // 2接口
                orderInvoiceInfoEntity.setKpqd("1");
            } else if ("3".equals(orderInvoiceInfoEntity.getDdly())) {
                // 3手动填开
                orderInvoiceInfoEntity.setKpqd("1");
            } else if ("4".equals(orderInvoiceInfoEntity.getDdly())) {
                // 惠企
                orderInvoiceInfoEntity.setKpqd("0");
            }
            // 格式化开票日期
            if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getKprq())) {
                String s = DateUtil.formatDate(orderInvoiceInfoEntity.getKprq());
                orderInvoiceInfoEntity.setKprqStr(s);
            }
            // 标普系统
            if ("1".equals(orderInvoiceInfoEntity.getKpqd())) {
                if (Objects.isNull(orderInvoiceInfoEntity)) {
                    return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "未查询到此记录");
                }
                BigDecimal Hjbhsje = new BigDecimal("0");
                // 查询该发票下 明细信息
                List<BigDecimal> jeList = new ArrayList<>();
                List<BigDecimal> seList = new ArrayList<>();
                List<BigDecimal> sykchjeList = new ArrayList<>();
                List<BigDecimal> sykchseList = new ArrayList<>();

                List<OrderInvoiceInfoEntity> infos = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(orderInvoiceInfoEntity.getQdfphm());
                List<OrderInvoiceItemEntity> orderInvoiceItemEntities = new ArrayList<>();

                for (OrderInvoiceInfoEntity info : infos) {
                    id = info.getId();
                    //数据库中查询该发票下明细信息
                    List<OrderInvoiceItemEntity> itemEntities = orderInvoiceItemDao.selectItemListById(info.getId());
                    // 累加该发票下明细金额、税额
                    Hjbhsje = Hjbhsje.add(new BigDecimal(info.getHjbhsje()));
                    if (CollectionUtils.isEmpty(itemEntities)) {
                        log.info("redInvoiceConfirmChoose，id:{}，查询该发票下明细信息为空", id);
                    } else {
                        // 负数-明细金额，税额相加
                        for (int i = 0; i < itemEntities.size(); i++) {
                            OrderInvoiceItemEntity orderInvoiceItemEntity = itemEntities.get(i);
                            //不管折扣行还是被折扣行，金额、税额都可以直接相加
                            jeList.add(StringUtils.isEmpty(orderInvoiceItemEntity.getJe()) ? BigDecimal.ZERO : new BigDecimal(orderInvoiceItemEntity.getJe()));
                            seList.add(StringUtils.isEmpty(orderInvoiceItemEntity.getSe()) ? BigDecimal.ZERO : new BigDecimal(orderInvoiceItemEntity.getSe()));
                            if ("0".equals(orderInvoiceItemEntity.getFphxz())) {
                                //正常行剩余数量、金额、税额加 - 号赋值
                                String sykchsl = StringUtils.isEmpty(orderInvoiceItemEntity.getSykchsl()) ? orderInvoiceItemEntity.getXmsl() : orderInvoiceItemEntity.getSykchsl();
                                String sykchje = StringUtils.isEmpty(orderInvoiceItemEntity.getSykchje()) ? orderInvoiceItemEntity.getJe() : orderInvoiceItemEntity.getSykchje();
                                String sykchse = StringUtils.isEmpty(orderInvoiceItemEntity.getSykchse()) ? orderInvoiceItemEntity.getSe() : orderInvoiceItemEntity.getSykchse();
                                if(MathUtil.compare(sykchje, "0") <= 0){
                                    continue;
                                }
                                if (!sykchsl.contains("-")) {
                                    orderInvoiceItemEntity.setSykchsl("-" + sykchsl);
                                }
                                if (!sykchje.contains("-")) {
                                    sykchjeList.add(StringUtils.isEmpty(sykchje) ? BigDecimal.ZERO : new BigDecimal(sykchje));
                                    orderInvoiceItemEntity.setSykchje("-" + sykchje);
                                }
                                if (!sykchse.contains("-")) {
                                    sykchseList.add(StringUtils.isEmpty(sykchse) ? BigDecimal.ZERO : new BigDecimal(sykchse));
                                    orderInvoiceItemEntity.setSykchse("-" + sykchse);
                                }
                                if (!orderInvoiceItemEntity.getXmsl().contains("-")) {
                                    orderInvoiceItemEntity.setXmsl("-" + orderInvoiceItemEntity.getXmsl());
                                }
                                if (!orderInvoiceItemEntity.getJe().contains("-")) {
                                    orderInvoiceItemEntity.setJe("-" + orderInvoiceItemEntity.getJe());
                                }
                                if (!orderInvoiceItemEntity.getSe().contains("-")) {
                                    orderInvoiceItemEntity.setSe("-" + orderInvoiceItemEntity.getSe());
                                }
                                orderInvoiceItemEntities.add(orderInvoiceItemEntity);
                            }else if("2".equals(orderInvoiceItemEntity.getFphxz())){
                                //被折扣行 数量直接加- 号
                                if (!orderInvoiceItemEntity.getXmsl().contains("-")) {
                                    orderInvoiceItemEntity.setXmsl("-" + orderInvoiceItemEntity.getXmsl());
                                }
                                //获取折扣行明细
                                OrderInvoiceItemEntity zkhItem = itemEntities.get(i+1);
                                // 金额是折扣行的金额（负数）加被折扣行的金额 再加-号
                                String bzkhJe = MathUtil.addStr(orderInvoiceItemEntity.getJe(),zkhItem.getJe());
                                orderInvoiceItemEntity.setJe("-"+bzkhJe);
                                //税额是折扣行的税额（负数）加被折扣行的税额 再加-号
                                String bzkhSe = MathUtil.addStr(orderInvoiceItemEntity.getSe(),zkhItem.getSe());
                                orderInvoiceItemEntity.setSe("-"+bzkhSe);
                                //剩余的可冲红数量、金额、税额
                                String sykchsl = StringUtils.isEmpty(orderInvoiceItemEntity.getSykchsl()) ? orderInvoiceItemEntity.getXmsl() : orderInvoiceItemEntity.getSykchsl();
                                String sykchje = StringUtils.isEmpty(orderInvoiceItemEntity.getSykchje()) ? bzkhJe : orderInvoiceItemEntity.getSykchje();
                                String sykchse = StringUtils.isEmpty(orderInvoiceItemEntity.getSykchse()) ? bzkhSe : orderInvoiceItemEntity.getSykchse();
                                if(MathUtil.compare(sykchje, "0") <= 0){
                                    continue;
                                }
                                if (!sykchsl.contains("-")) {
                                    orderInvoiceItemEntity.setSykchsl("-" + sykchsl);
                                }
                                if (!sykchje.contains("-")) {
                                    sykchjeList.add(StringUtils.isEmpty(sykchje) ? BigDecimal.ZERO : new BigDecimal(sykchje));
                                    orderInvoiceItemEntity.setSykchje("-" + sykchje);
                                }
                                if (!sykchse.contains("-")) {
                                    sykchseList.add(StringUtils.isEmpty(sykchse) ? BigDecimal.ZERO : new BigDecimal(sykchse));
                                    orderInvoiceItemEntity.setSykchse("-" + sykchse);
                                }
                                orderInvoiceItemEntities.add(orderInvoiceItemEntity);
                            } else {
                                continue;
                            }
                            String sl = orderInvoiceItemEntity.getSl();
                            if (!sl.contains("%")) {
                                if ("免税".equals(sl)) {
                                    orderInvoiceItemEntity.setSe("***");
                                } else if ("不征税".equals(sl)) {
                                    orderInvoiceItemEntity.setSe("");
                                } else {
                                    sl = this.getPercent(Float.valueOf(sl), 0);
                                    orderInvoiceItemEntity.setSl(sl);
                                }
                            }
                        }
                    }
                }

                log.info("redInvoiceConfirmChoose，jeList:{}", JsonUtils.getInstance().toJsonString(jeList));
                log.info("redInvoiceConfirmChoose，seList:{}", JsonUtils.getInstance().toJsonString(seList));
                orderInvoiceInfoEntity.setBlueHjje(Hjbhsje.toString());

                // 计算金额相加
                if (!CollectionUtils.isEmpty(jeList)) {
                    BigDecimal je = jeList.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setHjbhsje("-"+je);
                } else {
                    orderInvoiceInfoEntity.setHjbhsje(BigDecimal.ZERO.toString());
                }
                // 计算税额相加
                if (!CollectionUtils.isEmpty(seList)) {
                    BigDecimal se = seList.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setKpse("-"+se);
                } else {
                    orderInvoiceInfoEntity.setKpse(BigDecimal.ZERO.toString());
                }
                // 计算剩余可冲红金额相加
                if (!CollectionUtils.isEmpty(sykchjeList)) {
                    BigDecimal sykchje = sykchjeList.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setSykchbhsje("-"+ sykchje);
                } else {
                    orderInvoiceInfoEntity.setSykchbhsje(BigDecimal.ZERO.toString());
                }
                // 计算剩余可冲红税额相加
                if (!CollectionUtils.isEmpty(sykchseList)) {
                    BigDecimal sykchse = sykchseList.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setSykchse("-"+sykchse);
                } else {
                    orderInvoiceInfoEntity.setSykchse(BigDecimal.ZERO.toString());
                }
                log.info("redInvoiceConfirmChoose，明细行:{}", JsonUtils.getInstance().toJsonString(orderInvoiceItemEntities));
                orderInvoiceInfoEntity.setItemEntityList(orderInvoiceItemEntities);
                List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = new ArrayList<>();
                for (OrderInvoiceInfoEntity info : infos) {
                    List<InvoiceAdditionInfoEntity> entities = invoiceAdditionInfoDao.selectAdditionListById(info.getId());
                    for (InvoiceAdditionInfoEntity additionInfoEntity : entities) {
                        invoiceAdditionInfoEntities.add(additionInfoEntity);
                    }
                }

                // 查询该发票下 附加信息
                if (CollectionUtils.isEmpty(invoiceAdditionInfoEntities)) {
                    log.info("redInvoiceConfirmChoose，id:{}，查询该发票下附加信息为空", id);
                }
                orderInvoiceInfoEntity.setInfoEntityList(invoiceAdditionInfoEntities);

                // 不含税 计算价税合计和剩余可冲红金额含税
                if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                    String hjje = orderInvoiceInfoEntity.getHjbhsje();
                    String kpse = orderInvoiceInfoEntity.getKpse();
                    if (hjje.contains("-")) {
                        hjje = hjje.replace("-", "");
                    } else {
                        hjje = "0";
                    }
                    if (kpse.contains("-")) {
                        kpse = kpse.replace("-", "");
                    } else {
                        kpse = "0";
                    }
                    orderInvoiceInfoEntity.setJshj("-"+MathUtil.addStr(hjje, kpse));
                    String sykchbhsje = orderInvoiceInfoEntity.getSykchbhsje();
                    String sykchse = orderInvoiceInfoEntity.getSykchse();
                    if (sykchbhsje.contains("-")) {
                        sykchbhsje = sykchbhsje.replace("-", "");
                    } else {
                        sykchbhsje = "0";
                    }
                    if (sykchse.contains("-")) {
                        sykchse = sykchse.replace("-", "");
                    } else {
                        sykchse = "0";
                    }
                    orderInvoiceInfoEntity.setSykchje("-"+MathUtil.addStr(sykchbhsje, sykchse));
                } else {
                    // 含税
                    orderInvoiceInfoEntity.setJshj("-"+orderInvoiceInfoEntity.getHjbhsje());
                    orderInvoiceInfoEntity.setSykchje("-"+orderInvoiceInfoEntity.getSykchbhsje());
                }
                detail = R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, orderInvoiceInfoEntity);
            } else if ("0".equals(orderInvoiceInfoEntity.getKpqd())) {
                // 电服平台
                // 数据库存在直接返回
                List<OrderInvoiceInfoEntity> infos = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(orderInvoiceInfoEntity.getQdfphm());
                List<OrderInvoiceItemEntity> list = new ArrayList<>();
                for (OrderInvoiceInfoEntity info : infos) {
                    id = info.getId();
                    List<OrderInvoiceItemEntity> list1 = orderInvoiceItemDao.selectItemListById(id);
                    for (OrderInvoiceItemEntity item : list1) {
                        list.add(item);
                    }
                }
                // List<OrderInvoiceItemEntity> list = orderInvoiceItemDao.selectItemListById(id);
                if (list.size() > 0) {
                    log.info("redInvoiceConfirmChoose，id:{}，查询本地库item已存在，直接返回数据", id);
                    list.forEach(x->{
                        if(org.apache.commons.lang3.StringUtils.isNotBlank(x.getXmsl())){
                            x.setXmsl("-"+x.getXmsl());
                        }

                        x.setJe("-"+x.getJe());
                        x.setSe("-"+x.getSe());
                    });

                    log.info("redInvoiceConfirmChoose，明细行list:{}", JsonUtils.getInstance().toJsonString(list));

                    orderInvoiceInfoEntity.setItemEntityList(list);
                } else {
                    log.info("redInvoiceConfirmChoose，id:{}，查询本地库item不存在，开始调用接口", id);
                    // 惠企接口 调用单张发票查询 -------------根据 全电发票号码 和发票开票日期 格式为：yyyy-MM-dd 查询单张发票信息
                    InvoiceInfoReq invoiceInfoRe = new InvoiceInfoReq();
                    invoiceInfoRe.setQdfphm(orderInvoiceInfoEntity.getQdfphm());
                    if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getKprq())) {
                        String kpro = DateUtil.formatDate(orderInvoiceInfoEntity.getKprq());
                        invoiceInfoRe.setKprq(kpro);
                    }
                    invoiceInfoRe.setNsrsbh(baseNsrsbh);
                    long lon = System.currentTimeMillis();
                    String invoiceInfoReString = JsonUtils.getInstance().toJsonString(invoiceInfoRe);
                    log.info("redInvoiceConfirmChoose，id:{}，invoiceInfoReString 抽取单张发票信息入参: {}", id, invoiceInfoReString);
                    String resString = HttpUtils.doPost(singelInvoiceInfosAndItemsUrl, invoiceInfoReString);
                    log.info("redInvoiceConfirmChoose，id:{}，invoiceInfoReString 结束抽取单张发票信息接口 耗时: {}", id, System.currentTimeMillis() - lon);
                    log.info("redInvoiceConfirmChoose，id:{}，invoiceInfoReString 调用抽取单张发票信息出参: {}", id, resString);
                    R rest = JsonUtils.getInstance().fromJson(resString, R.class);
                    if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                        String data = (String) rest.get("data");
                        SingleInvoiceInfoResult singleInvoiceInfoResult = JsonUtils.getInstance().fromJson(data, SingleInvoiceInfoResult.class);
                        if (Objects.nonNull(singleInvoiceInfoResult)) {
                            FPZTXX fpztxx = singleInvoiceInfoResult.getFpztxx();
                            if (Objects.nonNull(fpztxx)) {
                                orderInvoiceInfoEntity.setZzsytzt(fpztxx.getZzsytDm());
                                orderInvoiceInfoEntity.setXfsytzt(fpztxx.getXfsytDm());
                                orderInvoiceInfoEntity.setRzzt(fpztxx.getFprzztDm());
                            }
                            List<SingleInvoiceItemRes> fpmxxx = singleInvoiceInfoResult.getFpmxxx();
                            List<OrderInvoiceItemEntity> orderInvoiceItemEntities = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(fpmxxx)) {
                                for (SingleInvoiceItemRes singleInvoiceItemRes : fpmxxx) {
                                    OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                                    orderInvoiceItemEntity.setXh(singleInvoiceItemRes.getXh());
                                    orderInvoiceItemEntity.setXmmc(singleInvoiceItemRes.getXmmc());
                                    orderInvoiceItemEntity.setGgxh(singleInvoiceItemRes.getGgxh());
                                    orderInvoiceItemEntity.setDw(singleInvoiceItemRes.getDw());
                                    orderInvoiceItemEntity.setXmsl(singleInvoiceItemRes.getSpsl());
                                    orderInvoiceItemEntity.setDj(singleInvoiceItemRes.getDj());
                                    orderInvoiceItemEntity.setJe(singleInvoiceItemRes.getJe());
                                    orderInvoiceItemEntity.setSl(singleInvoiceItemRes.getSlv());
                                    orderInvoiceItemEntity.setSe(singleInvoiceItemRes.getSe());
                                    orderInvoiceItemEntity.setSpbm(singleInvoiceItemRes.getSphfwssflhbbm());
                                    orderInvoiceItemEntity.setFphxz(singleInvoiceItemRes.getFphxzDm());
                                    orderInvoiceItemEntity.setByzd4(singleInvoiceItemRes.getHwhyslwfwmc());
                                    orderInvoiceItemEntity.setYhzcbs(singleInvoiceItemRes.getXsyhzcbz());
                                    orderInvoiceItemEntity.setByzd1(singleInvoiceItemRes.getSsyhzclxDm());
                                    orderInvoiceItemEntity.setByzd5(singleInvoiceItemRes.getSpfwjc());
                                    orderInvoiceItemEntity.setHsbz("0");
                                    orderInvoiceItemEntity.setCreateTime(new Date());
                                    orderInvoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                                    orderInvoiceItemEntity.setOrderInvoiceId(orderInvoiceInfoEntity.getId());
                                    orderInvoiceItemDao.insert(orderInvoiceItemEntity);
                                    //返回给前端的值为负数
                                    orderInvoiceItemEntity.setJe("-"+singleInvoiceItemRes.getJe());
                                    if(org.apache.commons.lang3.StringUtils.isNotBlank(orderInvoiceItemEntity.getXmsl())){
                                        orderInvoiceItemEntity.setXmsl("-"+singleInvoiceItemRes.getSpsl());
                                    }
                                    orderInvoiceItemEntity.setSe("-"+singleInvoiceItemRes.getSe());
                                    orderInvoiceItemEntities.add(orderInvoiceItemEntity);
                                }
                            }
                            orderInvoiceInfoEntity.setItemEntityList(orderInvoiceItemEntities);
                        }
                    } else {
                        String msg = (String) rest.get("msg");
                        detail = R.error(msg);
                        return detail;
                    }
                }
                orderInvoiceInfoEntity.setJshj("-"+orderInvoiceInfoEntity.getJshj());
                orderInvoiceInfoEntity.setHjbhsje("-"+orderInvoiceInfoEntity.getHjbhsje());
                orderInvoiceInfoEntity.setKpse("-"+orderInvoiceInfoEntity.getKpse());

                detail = R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, orderInvoiceInfoEntity);
            }

        } catch (Exception e) {
            detail = R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "选择发票详情异常");
            log.error("redInvoiceConfirmChoose，id:{}，选择发票详情异常: {}", id, e);
        }
        log.info("redInvoiceConfirmChoose,detail:{}", JsonUtils.getInstance().toJsonString(detail));
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R redInvoiceConfirmSubmit(OrderInvoiceInfoEntity orderInvoiceInfoEntity) throws Exception {
        log.info("redInvoiceConfirmSubmit 红字发票确认信息录入-提交:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
        // 连续点击提交控制
        String qdfphm = orderInvoiceInfoEntity.getQdfphm();
        String key = "redInvoiceConfirmSubmit" + qdfphm;
        String baike = (String) redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(baike)) {
            redisTemplate.opsForValue().set(key, key, 300, TimeUnit.SECONDS);
        } else {
            return R.error("请勿连续点击提交");
        }
        try {
            // 红票确认信息封装
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = new RedInvoiceConfirmEntity();
            String id = DistributedKeyMaker.generateShotKey();
            redInvoiceConfirmEntity.setId(id);
            if (orderInvoiceInfoEntity.getBaseNsrsbh().equals(orderInvoiceInfoEntity.getXhfNsrsbh())) {
                // 销货方
                redInvoiceConfirmEntity.setGxsf("0");
                redInvoiceConfirmEntity.setDfnsrmc(orderInvoiceInfoEntity.getGhfMc());
                redInvoiceConfirmEntity.setDfnsrsbh(orderInvoiceInfoEntity.getGhfNsrsbh());
                redInvoiceConfirmEntity.setLrfsf("0");
            } else if (orderInvoiceInfoEntity.getBaseNsrsbh().equals(orderInvoiceInfoEntity.getGhfNsrsbh())) {
                // 购货方
                redInvoiceConfirmEntity.setGxsf("1");
                redInvoiceConfirmEntity.setDfnsrmc(orderInvoiceInfoEntity.getXhfMc());
                redInvoiceConfirmEntity.setDfnsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                redInvoiceConfirmEntity.setLrfsf("1");
            } else {
                redInvoiceConfirmEntity.setGxsf("");
                redInvoiceConfirmEntity.setDfnsrmc("");
            }
            redInvoiceConfirmEntity.setNsrsbh(orderInvoiceInfoEntity.getBaseNsrsbh());
            redInvoiceConfirmEntity.setDylpqdfphm(orderInvoiceInfoEntity.getQdfphm());
            redInvoiceConfirmEntity.setFpje(orderInvoiceInfoEntity.getHjbhsje());
            redInvoiceConfirmEntity.setFpse(orderInvoiceInfoEntity.getKpse());
            redInvoiceConfirmEntity.setJshj(orderInvoiceInfoEntity.getJshj());
            if ("开票有误".equals(orderInvoiceInfoEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("01");
            } else if ("销货退回".equals(orderInvoiceInfoEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("02");
            } else if ("服务中止".equals(orderInvoiceInfoEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("03");
            } else if ("销售折让".equals(orderInvoiceInfoEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("04");
            }
            // zt字段的中间状态
            redInvoiceConfirmEntity.setZtIng("0");
            redInvoiceConfirmEntity.setZt("10");
            redInvoiceConfirmEntity.setIsDelete("0");
            // 我发起的
            redInvoiceConfirmEntity.setFpfqr("0");
            // 未开具
            redInvoiceConfirmEntity.setKjzt("N");
            // 受理中
            redInvoiceConfirmEntity.setSlzt("1");
            redInvoiceConfirmEntity.setSqrq(new Date());
            redInvoiceConfirmEntity.setUpdateTime(new Date());
            redInvoiceConfirmEntity.setCreateTime(new Date());
            //原蓝字发票代码和号码
            redInvoiceConfirmEntity.setLzfpdm(orderInvoiceInfoEntity.getFpdm());
            redInvoiceConfirmEntity.setLzfphm(orderInvoiceInfoEntity.getFphm());
            //系统来源
            redInvoiceConfirmEntity.setXtly(orderInvoiceInfoEntity.getXtly());

            log.info("redInvoiceConfirmSubmit，qdfphm：{}，更改原蓝票冲红标志为红冲中---成功", qdfphm);
            // 根据蓝票全电号码查询蓝票
            OrderInvoiceInfoEntity oldOrderInvoiceInfo = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
            if (Objects.nonNull(oldOrderInvoiceInfo)) {
                RedConfirmReq redConfirmReq = new RedConfirmReq();
                redConfirmReq.setFplxdm(orderInvoiceInfoEntity.getFpzlDm());
                redConfirmReq.setTdyw(orderInvoiceInfoEntity.getTdyw());
                String kpro = DateUtil.formatDate(oldOrderInvoiceInfo.getKprq());
                redConfirmReq.setKprq(kpro);
                redConfirmReq.setLzqdfphm(oldOrderInvoiceInfo.getQdfphm());
                redConfirmReq.setFpdm(oldOrderInvoiceInfo.getFpdm());
                redConfirmReq.setFphm(oldOrderInvoiceInfo.getFphm());
                redConfirmReq.setGhfmc(oldOrderInvoiceInfo.getGhfMc());
                redConfirmReq.setGhfnsrsbh(oldOrderInvoiceInfo.getGhfNsrsbh());
                redConfirmReq.setXhfmc(oldOrderInvoiceInfo.getXhfMc());
                redConfirmReq.setXhfnsrsbh(oldOrderInvoiceInfo.getXhfNsrsbh());
                redConfirmReq.setHjje(redInvoiceConfirmEntity.getFpje());
                redConfirmReq.setHjse(redInvoiceConfirmEntity.getFpse());
                // 开票有误、销货退回、服务中止、销售折让  此参数order传给invoice的是文字
                redConfirmReq.setChyymc(orderInvoiceInfoEntity.getChyy());
                // 购销方选择 0 销售方，1 购买方
                redConfirmReq.setGxfxz(redInvoiceConfirmEntity.getGxsf());
                redConfirmReq.setNsrsbh(oldOrderInvoiceInfo.getXhfNsrsbh());
                List<Hzmx> hzmxList = new ArrayList<>();
                //  获取前台传过来的红字确认单明细
                List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
                itemEntityList.forEach(item->{
                    Hzmx hzmx = new Hzmx();
                    hzmx.setSpmc(item.getXmmc());
                    hzmx.setGgxh(item.getGgxh());
                    hzmx.setDw(item.getDw());
                    hzmx.setSpsl(item.getXmsl());
                    hzmx.setSpdj(item.getDj());
                    hzmx.setJe(item.getJe());
                    hzmx.setSl(item.getSl());
                    hzmx.setSe(item.getSe());
                    hzmx.setSpbm(item.getSpbm());
                    hzmxList.add(hzmx);
                });
                redConfirmReq.setHzmx(hzmxList);
                // 发起生成红字确认单请求
                long lo = System.currentTimeMillis();
                String jsonString = JsonUtils.getInstance().toJsonString(redConfirmReq);
                log.info("redInvoiceConfirmSubmit，qdfphm：{}，发起生成红字确认单请求接口入参: {}", qdfphm, jsonString);
                String res = HttpUtils.doPost(invoiceGenerateRedUrl, jsonString);
                log.info("redInvoiceConfirmSubmit，qdfphm：{}，结束调用发起生成红字确认单请求接口 耗时: {}", qdfphm, System.currentTimeMillis() - lo);
                log.info("redInvoiceConfirmSubmit，qdfphm：{}，发起生成红字确认单请求接口出参: {}", qdfphm, res);
                R rest = JsonUtils.getInstance().fromJson(res, R.class);
                if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                    String data = (String) rest.get("data");
                    RedInvoiceConfirmSldh redInvoiceConfirmSldh = JsonUtils.getInstance().fromJson(data, RedInvoiceConfirmSldh.class);
                    if (Objects.nonNull(redInvoiceConfirmSldh)) {
                        redInvoiceConfirmEntity.setSldh(redInvoiceConfirmSldh.getSldh());
                        log.info("redInvoiceConfirmSubmit，qdfphm：{}，红字信息表入库数据:{}", qdfphm, redInvoiceConfirmEntity);
                        redInvoiceConfirmDao.insert(redInvoiceConfirmEntity);
                        log.info("redInvoiceConfirmSubmit，qdfphm：{}，发起生成红字确认单请求接口返回受理单号: {}", qdfphm, data);
                        itemEntityList.forEach(item->{
                            OrderInvoiceItemEntity oldItem = orderInvoiceItemDao.selectById(item.getId());
                            //剩余可冲红金额 默认正常行
                            String oldjeStr = StringUtils.isEmpty(oldItem.getSykchje()) ? oldItem.getJe() : oldItem.getSykchje();
                            //剩余可冲红税额 默认正常行
                            String oldseStr = StringUtils.isEmpty(oldItem.getSykchse()) ? oldItem.getSe() : oldItem.getSykchse();
                            //剩余可冲红数量
                            String oldslStr = StringUtils.isEmpty(oldItem.getSykchsl()) ? oldItem.getXmsl() : oldItem.getSykchsl();
                            if("2".equals(item.getFphxz())){
                                //被折扣行，数据库中找到该行对应的折扣行，把金额加回来
                                String xh = oldItem.getXh();
                                LambdaQueryWrapper<OrderInvoiceItemEntity> queryWrapper = Wrappers.lambdaQuery();
                                queryWrapper.select(OrderInvoiceItemEntity::getJe, OrderInvoiceItemEntity::getSe);
                                queryWrapper.eq(OrderInvoiceItemEntity::getXh, Integer.valueOf(xh) + 1);
                                queryWrapper.eq(OrderInvoiceItemEntity::getOrderInvoiceId, oldItem.getOrderInvoiceId());
                                OrderInvoiceItemEntity itemZkh = orderInvoiceItemDao.selectOne(queryWrapper);
                                oldjeStr = MathUtil.addStr(oldjeStr, itemZkh.getJe());
                                oldseStr = MathUtil.addStr(oldseStr, itemZkh.getSe());
                            }
                            String newje = MathUtil.addStr(oldjeStr, item.getJe());
                            String newse = MathUtil.addStr(oldseStr, item.getSe());
                            String newsl = "";
                            if(StringUtils.isEmpty(item.getXmsl())){
                                newsl = oldslStr;
                            }else {
                                newsl = MathUtil.addStr(oldslStr, item.getXmsl());
                            }
                            oldItem.setSykchje(newje);
                            item.setSykchje(newje);
                            oldItem.setSykchse(newse);
                            item.setSykchse(newse);
                            oldItem.setSykchsl(newsl);
                            item.setSykchsl(newsl);
                            //更新原蓝票明细
                            orderInvoiceItemDao.updateById(oldItem);
                            //保存新的红字确认单明细
                            item.setId(DistributedKeyMaker.generateShotKey());
                            item.setOrderInvoiceId(id);
                            item.setSl(oldItem.getSl());
                            orderInvoiceItemDao.insert(item);
                        });
                        log.info("重新计算并更新原发票明细的可冲红金额等信息成功");
                        // 更改原蓝票 冲红标志为 红冲中
                        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(orderInvoiceInfoEntity.getQdfphm());
                        if (Objects.nonNull(invoiceInfoEntity)) {
                            if(StringUtils.isEmpty(invoiceInfoEntity.getChyy())){
                                invoiceInfoEntity.setChyy(invoiceInfoEntity.getChyy());
                            }else {
                                StringBuffer sb = new StringBuffer(invoiceInfoEntity.getChyy());
                                sb.append(","+orderInvoiceInfoEntity.getChyy());
                                invoiceInfoEntity.setChyy(sb.toString());
                            }
                            invoiceInfoEntity.setChsj(new Date());
                            invoiceInfoEntity.setUpdateBy(ssoUtil.getUserName());
                            invoiceInfoEntity.setUpdateTime(new Date());
                            //剩余可冲红不含税金额
                            String oldBhsjeStr = StringUtils.isEmpty(oldOrderInvoiceInfo.getSykchbhsje()) ? oldOrderInvoiceInfo.getHjbhsje() : oldOrderInvoiceInfo.getSykchbhsje();
                            invoiceInfoEntity.setSykchbhsje(MathUtil.addStr(oldBhsjeStr, orderInvoiceInfoEntity.getHjbhsje()));
                            //剩余可冲红价税合计金额
                            String oldJshjStr = StringUtils.isEmpty(oldOrderInvoiceInfo.getSykchje()) ? oldOrderInvoiceInfo.getJshj() : oldOrderInvoiceInfo.getSykchje();
                            invoiceInfoEntity.setSykchje(MathUtil.addStr(oldJshjStr, orderInvoiceInfoEntity.getJshj()));
                            //剩余可冲红税额
                            String oldKpseStr = StringUtils.isEmpty(oldOrderInvoiceInfo.getSykchse()) ? oldOrderInvoiceInfo.getKpse() : oldOrderInvoiceInfo.getSykchse();
                            invoiceInfoEntity.setSykchse(MathUtil.addStr(oldKpseStr, orderInvoiceInfoEntity.getKpse()));
                            if(MathUtil.compare(invoiceInfoEntity.getSykchbhsje(),"0") > 0){
                                // 部分红冲中
                                invoiceInfoEntity.setChBz("5");
                            }else {
                                // 全部红冲中
                                invoiceInfoEntity.setChBz("2");
                            }
                            orderInvoiceInfoDao.updateById(invoiceInfoEntity);
                        }
                    }

                } else {
                    log.error("redInvoiceConfirmSubmit 返回失败");
                    redisTemplate.delete(key);
                    String errorData = StringUtils.isEmpty((String)rest.get(OrderManagementConstant.MESSAGE)) ? "远程调用底层接口发生异常" : (String)rest.get(OrderManagementConstant.MESSAGE);
                    return R.error(errorData);
                }
            }
            redisTemplate.delete(key);
            return R.ok();
        } catch (Exception e) {
            log.error("redInvoiceConfirmSubmit，qdfphm：{}，出现异常:{}", qdfphm, e);
            redisTemplate.delete(key);
            //重新抛出异常，防止事务失效
            throw new Exception("远程调用底层接口发生异常");
        }
    }

    @Override
    public R redInvoiceConfirmIssueQuery(String id) {
        try {
            log.info("redInvoiceConfirmIssueQuery 开具红票查询Id:{}", id);
            RedInvoiceConfirmEntity redInvoiceConfirm = redInvoiceConfirmDao.selectById(id);
            if (StringUtils.isEmpty(redInvoiceConfirm.getHztzdbh())) {
                return R.error("redInvoiceConfirmIssueQuery，id：{}，红字通知单编码为空,不可开票", id);
            }
            // 查询原蓝票信息
            OrderInvoiceInfoEntity oldOrderInvoiceInfo = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirm.getDylpqdfphm());
            if (Objects.isNull(oldOrderInvoiceInfo)) {
                return R.error("redInvoiceConfirmIssueQuery，id：{}，蓝字发票信息不存在", id);
            }
            String kpro = DateUtil.formatDate(oldOrderInvoiceInfo.getKprq());
            oldOrderInvoiceInfo.setKprqStr(kpro);
            // 查询红字确认单明细信息，不查原蓝票明细
            List<OrderInvoiceItemEntity> orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(id);
            for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceItemEntities) {
                String sl = orderInvoiceItemEntity.getSl();
                if (!sl.contains("%") && sl.contains("0")) {
                    sl = this.getPercent(Float.valueOf(sl), 0);
                    orderInvoiceItemEntity.setSl(sl);
                } else {
                    orderInvoiceItemEntity.setSl(sl);
                }
            }
            oldOrderInvoiceInfo.setItemEntityList(orderInvoiceItemEntities);
            // 附加信息
            List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(oldOrderInvoiceInfo.getId());
            oldOrderInvoiceInfo.setInfoEntityList(invoiceAdditionInfoEntities);
            //特定业务
            List<InvoiceTdywEntity> tdywEntityList = invoiceTdywService.queryTdywRelation(Collections.singleton(oldOrderInvoiceInfo.getId()));
            if(!CollectionUtils.isEmpty(tdywEntityList)){
                oldOrderInvoiceInfo.setInvoiceTdywEntity(tdywEntityList.get(0));
            }
            StringBuilder sb = new StringBuilder();
            //发票预警配置
            SalerWarningInfo salerWarningInfo = salerWarningDao.selectWarnInfoByNsrsbh(oldOrderInvoiceInfo.getXhfNsrsbh());
            if (Objects.nonNull(salerWarningInfo) && "0".equals(salerWarningInfo.getAutoBz())) {
                // 对应蓝票信息
                if (!StringUtils.isEmpty(oldOrderInvoiceInfo.getBz())) {
                    sb.append("原蓝票备注: ");
                    sb.append(oldOrderInvoiceInfo.getBz());
                    sb.append(" ");
                }
            }
            // 拼接红票备注
            sb.append("被红冲蓝字全电发票号码: ");
            sb.append(oldOrderInvoiceInfo.getQdfphm());
            sb.append(" ");
            sb.append("红字发票信息确认单编号: ");
            sb.append(redInvoiceConfirm.getHztzdbh());
            oldOrderInvoiceInfo.setBz(sb.toString());
            oldOrderInvoiceInfo.setHjbhsje(redInvoiceConfirm.getFpje());
            oldOrderInvoiceInfo.setKpse(redInvoiceConfirm.getFpse());
            oldOrderInvoiceInfo.setJshj(redInvoiceConfirm.getJshj());
            log.info("redInvoiceConfirmIssueQuery，id：{}，红票备注:{}", id, sb.toString());
            return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, oldOrderInvoiceInfo);
        } catch (Exception e) {
            log.error("redInvoiceConfirmIssueQuery，id：{}，出现异常:{}", id, e);
            return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "系统异常");
        }
    }

    @Override
    public R redInvoiceConfirmIssue(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("红字发票确认信息-开具红票:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
        // 连续点击提交控制
        String key = "redInvoiceConfirmIssue" + orderInvoiceInfoEntity.getId();
        String baike = (String) redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(baike)) {
            redisTemplate.opsForValue().set(key, key, 300, TimeUnit.SECONDS);
        } else {
            return R.error("请勿连续点击开具按钮");
        }
        try {
            //  查询红票信息
            OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.selectById(orderInvoiceInfoEntity.getId());
            if (Objects.nonNull(orderInvoiceInfo)) {
                orderInvoiceInfo.setBz(orderInvoiceInfoEntity.getBz());
                // 红票明细信息
                List<OrderInvoiceItemEntity> orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(orderInvoiceInfo.getId());
                orderInvoiceInfo.setItemEntityList(orderInvoiceItemEntities);
                //拼接项目简称
                if (!CollectionUtils.isEmpty(orderInvoiceItemEntities)) {
                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceItemEntities) {
                        ItemInfoEntity itemInfoEntity1 = new ItemInfoEntity();
                        itemInfoEntity1.setSphssflbm(orderInvoiceItemEntity.getSpbm());
                        itemInfoEntity1.setBaseNsrsbh(orderInvoiceInfoEntity.getBaseNsrsbh());
                        itemInfoEntity1.setXmmc(orderInvoiceItemEntity.getXmmc());
                        ItemInfoEntity itemInfoEntity = itemInfoDao.selectItemInfoByName(itemInfoEntity1);
                        orderInvoiceItemEntity.setXmmc(Objects.isNull(itemInfoEntity) ? orderInvoiceItemEntity.getXmmc() : "*" + itemInfoEntity.getSphssfljc() + "*" + orderInvoiceItemEntity.getXmmc());
                    }
                }
                // 红票附加信息
                List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(orderInvoiceInfo.getId());
                orderInvoiceInfo.setInfoEntityList(invoiceAdditionInfoEntities);
                long lo = System.currentTimeMillis();
                String jsonString = JacksonUtils.toJsonPrettyString(orderInvoiceInfo);
                log.info("redInvoiceConfirmIssue，id:{}，调用开票接口入参: {}", orderInvoiceInfoEntity.getId(), jsonString);
                log.info("redInvoiceConfirmIssue，id:{}，调用开票接口订单号: {}", orderInvoiceInfoEntity.getId(), orderInvoiceInfo.getDdh());
                String res = HttpUtils.doPost(invoiceIssueUrl, jsonString);
                log.info("redInvoiceConfirmIssue，id:{}，结束调用开票接口 耗时: {}", orderInvoiceInfoEntity.getId(), System.currentTimeMillis() - lo);
                log.info("redInvoiceConfirmIssue，id:{}，调用开票接口出参: {}", orderInvoiceInfoEntity.getId(), jsonString);
                R r = JsonUtils.getInstance().fromJson(res, R.class);
                // 调用开票接口返回成功 --入库
                if (Objects.nonNull(r) && "0000".equals((String) r.get("code"))) {
                    orderInvoiceInfo.setByzd1((String) r.get("code"));
                    orderInvoiceInfo.setByzd2((String) r.get("msg"));
                    orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_1.getKey());
                    orderInvoiceInfo.setUpdateTime(new Date());
                    // 更细红票状态为 开票中
                    orderInvoiceInfo.setUpdateBy(ssoUtil.getUserName());
                    orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                    log.info("redInvoiceConfirmIssue，id：{}，红票更新为开票中,订单号:{}", orderInvoiceInfoEntity.getId(), orderInvoiceInfo.getDdh());
                    RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.selectById(orderInvoiceInfoEntity.getRedInvoiceId());
                    if (Objects.nonNull(invoiceConfirmEntity)) {
                        invoiceConfirmEntity.setByzd1("1");
                        invoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                    }
                } else {
                    if (Objects.nonNull(r)) {
                        String code = (String) r.get("code");
                        String msg1 = (String) r.get("msg");
                        log.info("redInvoiceConfirmIssue，id：{}，调用开票接口失败 返回code:{},msg:{}", orderInvoiceInfoEntity.getId(), code, msg1);
                        if ("9002".equals(code)) {
                            // 该任务正在处理中，不可重复提交
                            orderInvoiceInfo.setByzd1(code);
                            orderInvoiceInfo.setByzd2(msg1);
                            orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_1.getKey());
                            orderInvoiceInfo.setUpdateTime(new Date());
                            orderInvoiceInfo.setUpdateBy(ssoUtil.getUserName());
                            orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                        } else {
                            orderInvoiceInfo.setByzd1(code);
                            orderInvoiceInfo.setByzd2(msg1);
                            orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                            orderInvoiceInfo.setUpdateTime(new Date());
                            orderInvoiceInfo.setUpdateBy(ssoUtil.getUserName());
                            orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                        }
                    } else {
                        log.info("redInvoiceConfirmIssue，id：{}，调用开票接口失败 返回为空 只更新状态为失败", orderInvoiceInfoEntity.getId());
                        orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                        orderInvoiceInfo.setUpdateTime(new Date());
                        orderInvoiceInfo.setUpdateBy(ssoUtil.getUserName());
                        orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                    }
                    log.info("redInvoiceConfirmIssue，id：{}，订单开票接口调用返回异常,订单号:{}", orderInvoiceInfoEntity.getId(), orderInvoiceInfo.getDdh());
                }
            } else {
                log.info("redInvoiceConfirmIssue，id：{}，查询红票信息为空", orderInvoiceInfoEntity.getId());
            }
        } catch (Exception e) {
            log.error("redInvoiceConfirmIssue，id：{}，开具红票接口出现异常: {}", orderInvoiceInfoEntity.getId(), e);
            redisTemplate.delete(key);
            return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "开具红票失败");
        }
        redisTemplate.delete(key);
        return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, "开具红票成功");
    }

    @Override
    public PageUtils selectRedInvoiceConfirm(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("selectRedInvoiceConfirm 入口参数:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
        Page page = new Page(orderInvoiceInfoEntity.getCurrPage(), orderInvoiceInfoEntity.getPageSize());
        List<OrderInvoiceInfoEntity> list = orderInvoiceInfoDao.selectRedInvoiceConfirm(page, orderInvoiceInfoEntity);

        if (!CollectionUtils.isEmpty(list)) {
            for (OrderInvoiceInfoEntity invoiceInfoEntity : list) {
                Float aFloat = Float.valueOf(invoiceInfoEntity.getStartBhsje());
                String format = new DecimalFormat("0.00").format(aFloat);
                invoiceInfoEntity.setJshj(format);
                String kpro = DateUtil.formatDateTime(invoiceInfoEntity.getKprq());
                invoiceInfoEntity.setKprqStr(kpro);
                if ("0".equals(invoiceInfoEntity.getDdly())) {
                    // 0：扫码开票
                    invoiceInfoEntity.setKpqd("1");
                } else if ("1".equals(invoiceInfoEntity.getDdly())) {
                    // 1：批量导入
                    invoiceInfoEntity.setKpqd("1");
                } else if ("2".equals(invoiceInfoEntity.getDdly())) {
                    // 2接口
                    invoiceInfoEntity.setKpqd("1");
                } else if ("3".equals(invoiceInfoEntity.getDdly())) {
                    // 3手动填开
                    invoiceInfoEntity.setKpqd("1");
                } else if ("4".equals(invoiceInfoEntity.getDdly())) {
                    // 惠企
                    invoiceInfoEntity.setKpqd("0");
                }
            }
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public R redInvoiceConfirmDetail(String id) {
        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectById(id);
        return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, orderInvoiceInfoEntity);
    }

    @Override
    public R redInvoiceConfirmView(String id) {
        log.info("redInvoiceConfirmView 入口参数:{}", id);
        try {
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectById(id);
            if (Objects.isNull(redInvoiceConfirmEntity)) {
                return R.error("确认单信息不存在");
            }
            // 蓝票信息
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
            if (Objects.isNull(orderInvoiceInfoEntity)) {
                return R.error("蓝票信息不存在");
            }

            BigDecimal blueHjje = new BigDecimal("0.00");

            // 惠企
            String kpro = DateUtil.formatDate(orderInvoiceInfoEntity.getKprq());
            orderInvoiceInfoEntity.setKprqStr(kpro);
            // 明细集合
            List<OrderInvoiceItemEntity> orderInvoiceItemEntities = new ArrayList<>();
            // 附加要素
            List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = new ArrayList<>();
            //查询红字确认单或红字发票明细信息（当还未开红票时查红字确认单信息，已开红票时查红字发票信息），不查原蓝票明细
            if(StringUtils.isEmpty(redInvoiceConfirmEntity.getQdfphm())){
                //此时还未开具红票取红字确认单明细信息
                orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(id);
            }else {
                List<OrderInvoiceInfoEntity>  hzfpinfos = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getQdfphm());
                if(!CollectionUtils.isEmpty(hzfpinfos)){
                    orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(hzfpinfos.get(0).getId());
                }else {
                    return R.error("红字发票信息不存在");
                }
            }
            //查询附加要素信息
            invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(orderInvoiceInfoEntity.getId());
            //查询原蓝票合计金额
            blueHjje = blueHjje.add(new BigDecimal(orderInvoiceInfoEntity.getHjbhsje()));
            if (!CollectionUtils.isEmpty(orderInvoiceItemEntities)) {
                for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceItemEntities) {
                    String sl = orderInvoiceItemEntity.getSl();
                    if (!sl.contains("%")) {
                        if ("免税".equals(sl)) {
                            orderInvoiceItemEntity.setSe("***");
                        } else if ("不征税".equals(sl)) {
                            orderInvoiceItemEntity.setSe("");
                        } else {
                            sl = this.getPercent(Float.valueOf(sl), 0);
                            orderInvoiceItemEntity.setSl(sl);
                        }
                    }
                    //直接取红票信息时，不用再加- 号
                    /*if (!"1".equals(orderInvoiceItemEntity.getFphxz())) {
                        if (!StringUtils.isEmpty(orderInvoiceItemEntity.getXmsl())) {
                            orderInvoiceItemEntity.setXmsl("-" + orderInvoiceItemEntity.getXmsl());
                        }
                        if (!StringUtils.isEmpty(orderInvoiceItemEntity.getJe())) {
                            orderInvoiceItemEntity.setJe("-" + orderInvoiceItemEntity.getJe());
                        }
                        if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSe()) && !"免税".equals(orderInvoiceItemEntity.getSl())) {
                            orderInvoiceItemEntity.setSe("-" + orderInvoiceItemEntity.getSe());
                        }
                    }*/
                }
            }
            orderInvoiceInfoEntity.setItemEntityList(orderInvoiceItemEntities);
            // 附加信息集合
            orderInvoiceInfoEntity.setInfoEntityList(invoiceAdditionInfoEntities);
            // 蓝票信息
            orderInvoiceInfoEntity.setBlueHjje(blueHjje.toString());
            orderInvoiceInfoEntity.setHjbhsje(redInvoiceConfirmEntity.getFpje());
            orderInvoiceInfoEntity.setKpse(redInvoiceConfirmEntity.getFpse());
            orderInvoiceInfoEntity.setJshj(redInvoiceConfirmEntity.getJshj());
            return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, orderInvoiceInfoEntity);
        } catch (Exception e) {
            log.error("redInvoiceConfirmView 出现异常:{}", e);
            return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "系统异常");
        }
    }

    @Override
    public R redInvoiceConfirmBackOut(String id) {
        // todo 暂时 更新为已撤销
        // 逻辑删除红票记录
        RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectById(id);
        if (Objects.nonNull(redInvoiceConfirmEntity)) {
            redInvoiceConfirmEntity.setKjzt("3");
            redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
            // 逻辑删除红票
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByYqdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
            if (Objects.nonNull(orderInvoiceInfoEntity)) {
                orderInvoiceInfoEntity.setIsDelete("1");
                orderInvoiceInfoEntity.setUpdateBy(ssoUtil.getUserName());
                orderInvoiceInfoDao.updateById(orderInvoiceInfoEntity);
            }
        }
        return R.ok();
    }

    @Override
    public PageUtils selectRedInvoiceRecord(RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        Page page = new Page(redInvoiceConfirmEntity.getCurrPage(), redInvoiceConfirmEntity.getPageSize());
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmDao.selectRedInvoiceRecord(page, redInvoiceConfirmEntity);
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
            for (RedInvoiceConfirmEntity invoiceConfirmEntity : redInvoiceConfirmEntities) {
                // 格式化开票日期
                if (!StringUtils.isEmpty(invoiceConfirmEntity.getKprq())) {
                    String s = DateUtil.formatDate(invoiceConfirmEntity.getKprq());
                    invoiceConfirmEntity.setKprqStr(s);
                }
            }
        }
        page.setRecords(redInvoiceConfirmEntities);
        return new PageUtils(page);
    }

    @Override
    public R selectRedInvoiceRecordView(String id) {
        log.info("selectRedInvoiceRecordView 入口id:{}", id);
        try {
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectById(id);
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceRedInfoByQdfphm(redInvoiceConfirmEntity.getQdfphm());
            if (Objects.nonNull(orderInvoiceInfoEntity)) {
                // 格式化开票日期
                if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getKprq())) {
                    String s = DateUtil.formatDate(orderInvoiceInfoEntity.getKprq());
                    orderInvoiceInfoEntity.setKprqStr(s);
                }
                // 明细信息
                List<OrderInvoiceItemEntity> orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(orderInvoiceInfoEntity.getId());
                if (!CollectionUtils.isEmpty(orderInvoiceItemEntities)) {
                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceItemEntities) {
                        String sl = orderInvoiceItemEntity.getSl();
                        if (!sl.contains("%")) {
                            sl = this.getPercent(Float.valueOf(sl), 0);
                            orderInvoiceItemEntity.setSl(sl);
                        }
                    }
                }
                orderInvoiceInfoEntity.setItemEntityList(orderInvoiceItemEntities);
                // 附加信息
                List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(orderInvoiceInfoEntity.getId());
                orderInvoiceInfoEntity.setInfoEntityList(invoiceAdditionInfoEntities);
                return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, orderInvoiceInfoEntity);
            } else {
                log.info("selectRedInvoiceRecordView 无法查询到红票信息记录");
                return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "无法查询到红票信息记录");
            }
        } catch (Exception e) {
            log.error("selectRedInvoiceRecordView 出现异常:{}", e);
            return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "系统异常");
        }
    }

    @Override
    public R redInvoiceConfirmStatistics() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        // 本月起始
        Calendar thisMonthFirstDateCal = Calendar.getInstance();
        thisMonthFirstDateCal.set(Calendar.DAY_OF_MONTH, 1);
        // String thisMonthFirstTime = format.format(thisMonthFirstDateCal.getTime()) + " 00:00:00";
        String thisMonthFirstTime = format.format(thisMonthFirstDateCal.getTime());
        // 本月末尾
        Calendar thisMonthEndDateCal = Calendar.getInstance();
        thisMonthEndDateCal.set(Calendar.DAY_OF_MONTH, thisMonthEndDateCal.getActualMaximum(Calendar.DAY_OF_MONTH));
        // String thisMonthEndTime = format.format(thisMonthEndDateCal.getTime()) + " 23:59:59";
        String thisMonthEndTime = format.format(thisMonthEndDateCal.getTime());

        RedInvoiceConfirmStatisticsEntity redInvoiceConfirmStatisticsEntity = new RedInvoiceConfirmStatisticsEntity();
        // 我发出的确认单(列表总和)
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntityList = redInvoiceConfirmDao.selectRedInvoiceConfirmEntityList(thisMonthFirstTime, thisMonthEndTime);
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntityList)) {
            // 开具成功的红票
            List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmEntityList.stream().filter(a -> "1".equals(a.getKjzt())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
                List<String> fpjeList = redInvoiceConfirmEntities.stream().map(RedInvoiceConfirmEntity::getFpje).collect(Collectors.toList());
                List<BigDecimal> bigDecimalList = new ArrayList<>();
                for (String fpje : fpjeList) {
                    bigDecimalList.add(new BigDecimal(fpje));
                }
                // 红票金额总和
                BigDecimal totalPrice = bigDecimalList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal abs = totalPrice.abs();
                redInvoiceConfirmStatisticsEntity.setByhtsxed(abs.toString());
            } else {
                // 本月回退授信额度(红票开具成功的金额)
                redInvoiceConfirmStatisticsEntity.setByhtsxed("0");
            }
        } else {
            // 本月回退授信额度(红票开具成功的金额)
            redInvoiceConfirmStatisticsEntity.setByhtsxed("0");
        }
        // 我发出的确认单(列表总和)
        List<RedInvoiceConfirmEntity> invoiceConfirmEntities = redInvoiceConfirmDao.selectAll();
        if (!CollectionUtils.isEmpty(invoiceConfirmEntities)) {
            redInvoiceConfirmStatisticsEntity.setWfcdqrd(String.valueOf(invoiceConfirmEntities.size()));
        } else {
            redInvoiceConfirmStatisticsEntity.setWfcdqrd("0");
        }
        Random random = new Random();
        int r = random.nextInt(10000);
        redInvoiceConfirmStatisticsEntity.setBygsdhzfpje(String.valueOf(r));
        Random randomSe = new Random();
        int rse = randomSe.nextInt(10000);
        redInvoiceConfirmStatisticsEntity.setByyzcjxse(String.valueOf(rse));
        Random randomCl = new Random();
        int rCl = randomCl.nextInt(100);
        redInvoiceConfirmStatisticsEntity.setDqdcl(String.valueOf(rCl));
        Random randomRd = new Random();
        int rRd = randomRd.nextInt(20);
        redInvoiceConfirmStatisticsEntity.setWsdeqrd(String.valueOf(rRd));
        return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, redInvoiceConfirmStatisticsEntity);
    }

    public R getInvoiceInfosAndItems(String fp, String nsrsbh, String flag, String begin, String end) {
        log.info("sendReqInvoiceInfosAndItems 入参：fp：{}，nsrsbh:{}，flag：{}，begin：{}，end：{}", fp, nsrsbh, flag, begin, end);
        String startTime = "";
        String endTime = "";
        if ("1".equals(flag)) {
            startTime = begin;
            endTime = end;
        } else {
            // 前一天数据
            LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minus(1, ChronoUnit.DAYS);
            startTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            endTime = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
//            LocalDateTime endLocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).minus(1, ChronoUnit.DAYS);
//            endTime = endLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        R r = sendReqInvoiceInfosAndItems(fp, nsrsbh, "1", "50", flag, startTime, endTime);
        log.info("sendReqInvoiceInfosAndItems 执行完成:{}", r);
        if ("0000".equals(r.get("code").toString())) {
            InvoiceInfoRes invoiceInfoRes = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(r.get("data")), InvoiceInfoRes.class);
            if (!ObjectUtils.isEmpty(invoiceInfoRes)) {
                int pages = Integer.valueOf(invoiceInfoRes.getPages());
                if (pages > 1) {
                    for (int i = 2; i <= pages; i++) {
                        R resp = sendReqInvoiceInfosAndItems(fp, nsrsbh, String.valueOf(i), "50", flag, startTime, endTime);
                        if ("0000".equals(resp.get("code").toString())) {
                            log.info("sendReqInvoiceInfosAndItems 抽取发票信息第{}页成功", i);
                        } else {
                            log.info("sendReqInvoiceInfosAndItems 抽取发票信息第{}页失败", i);
                        }
                    }
                }
            }
        }
        log.info("sendReqInvoiceInfosAndItems 执行完成");
        return R.ok();
    }

    /**
     * 抽取出来循环调用的部分
     *
     * @return
     */
    public R sendReqInvoiceInfosAndItems(String fp, String nsrsbh, String current, String size, String flag, String startTime, String endTime) {

        try {
            InvoiceInfoReq invoiceInfoReq = new InvoiceInfoReq();
            // 1-销项发票； 2-进项发票
            invoiceInfoReq.setGjbq(fp);
            // 发票状态： 01-正常； 02-已作废； 03-已红冲-全额；04-已红冲-部分
            String[] fpztdms = new String[]{"01", "03", "04"};
            invoiceInfoReq.setFpztDm(fpztdms);
            // 0-全部; 1-增值税发票管理系统； 2-电子发票服务平台
            invoiceInfoReq.setFplyDm("2");
            //81-全电发票（增值税专用发票）； 82-全电发票（普通发票）；
            String[] fplxdms = new String[]{"81", "82"};
            invoiceInfoReq.setFplxDm(fplxdms);
            invoiceInfoReq.setKprqq(startTime);
            invoiceInfoReq.setKprqz(endTime);
            invoiceInfoReq.setCurrent(current);
            invoiceInfoReq.setSize(size);
            invoiceInfoReq.setNsrsbh(nsrsbh);
            long lo = System.currentTimeMillis();
            String jsonString = JsonUtils.getInstance().toJsonString(invoiceInfoReq);
            log.info("getInvoiceInfosAndItems 抽取发票信息入参: {}", jsonString);
            log.info("getInvoiceInfosAndItems 开始调用抽取发票信息");
            String res = HttpUtils.doPost(invoiceInfosAndItemsUrl, jsonString);
            log.info("getInvoiceInfosAndItems 结束调用开抽取发票信息口 耗时: {}", System.currentTimeMillis() - lo);
            log.info("getInvoiceInfosAndItems 调用抽取发票信息出参: {}", res);
            Map map = JsonUtils.getInstance().parseObject(res, HashMap.class);
            if (!"0000".equals(map.get("code").toString())) {
                log.info("getInvoiceInfosAndItems 调用抽取发票信息失败: {}", map.get("msg").toString());
                return R.error();
            }

            R r = JsonUtils.getInstance().fromJson(res, R.class);
            InvoiceInfoRes invoiceInfoRes = new InvoiceInfoRes();
            if (Objects.nonNull(r) && "0000".equals(r.get("code").toString())) {
                String data = r.get("data").toString();
                log.info("getInvoiceInfosAndItems 调用抽取发票信息data: {}", data);
                invoiceInfoRes = JsonUtils.getInstance().parseObject(data, InvoiceInfoRes.class);
                log.info("getInvoiceInfosAndItems 调用抽取发票信息invoiceInfoRes: {}", invoiceInfoRes);
                if (Objects.nonNull(invoiceInfoRes) && !CollectionUtils.isEmpty(invoiceInfoRes.getRecords())) {
                    List<InvoiceBaseInfoRes> records = invoiceInfoRes.getRecords();
                    for (InvoiceBaseInfoRes invoiceBaseInfoRes : records) {
                        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphmIsDele(invoiceBaseInfoRes.getQdfphm());
                        log.info("orderInvoiceInfoEntity 发票为:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
                        if (Objects.isNull(orderInvoiceInfoEntity)) {

                            insertInvoiceInfo(invoiceBaseInfoRes);

                        }
                    }
                }
            }
            return R.ok().put("data", invoiceInfoRes);
        } catch (Exception e) {
            log.error("getInvoiceInfosAndItems 异常:{}", e);
        }
        return R.error();
    }

    private void insertInvoiceInfo(InvoiceBaseInfoRes invoiceBaseInfoRes) {
        OrderInvoiceInfoEntity invoiceInfoEntity = new OrderInvoiceInfoEntity();
        String id = DistributedKeyMaker.generateShotKey();
        invoiceInfoEntity.setId(id);
        invoiceInfoEntity.setPch(DistributedKeyMaker.generateShotKey());
        invoiceInfoEntity.setFpqqlsh(DistributedKeyMaker.generateShotKey());
        invoiceInfoEntity.setDdh(DistributedKeyMaker.generateShotKey());
        invoiceInfoEntity.setGhfMc(invoiceBaseInfoRes.getGmfmc());
        invoiceInfoEntity.setGhfNsrsbh(invoiceBaseInfoRes.getGmfnsrsbh());
        invoiceInfoEntity.setGhfQylx("01");
        //  kplx  0--蓝票   1--红票
        if ("Y".equals(invoiceBaseInfoRes.getSflzfp())) {
            invoiceInfoEntity.setKplx("0");
        } else {
            invoiceInfoEntity.setKplx("1");
        }
        //  冲红标志(0:正常;1:全部冲红成功;2:全部冲红中;3:全部冲红失败;4:部分冲红成功;5:部分冲红中;6:部分冲红失败;(特殊说明:部分冲红只记录当前最后一次操作的记录))
        //发票状态代码： 01-正常； 02-已作废； 03-已红冲-全额 04-已红冲-部分
        if ("01".equals(invoiceBaseInfoRes.getFpztDm())) {
            invoiceInfoEntity.setChBz("0");
        } else if ("03".equals(invoiceBaseInfoRes.getFpztDm())) {
            invoiceInfoEntity.setChBz("1");
        } else if ("04".equals(invoiceBaseInfoRes.getFpztDm())) {
            invoiceInfoEntity.setChBz("4");
        } else {
            //未知状态
            invoiceInfoEntity.setChBz("10");
        }
        // 1-销项发票； 2-进项发票
        invoiceInfoEntity.setGjbq(invoiceBaseInfoRes.getGjbq());
        invoiceInfoEntity.setXhfMc(invoiceBaseInfoRes.getXsfmc());
        invoiceInfoEntity.setXhfNsrsbh(invoiceBaseInfoRes.getXsfnsrsbh());
        invoiceInfoEntity.setSflzfp(invoiceBaseInfoRes.getSflzfp());
        // 转换合计金额
        if (StringUtils.isEmpty(invoiceBaseInfoRes.getHjje())) {
            invoiceInfoEntity.setHjbhsje("0.00");
        } else {
            String s = formatDecimal(new BigDecimal(invoiceBaseInfoRes.getHjje()));
            invoiceInfoEntity.setHjbhsje(s);
        }
        // 转换合计税额
        if (StringUtils.isEmpty(invoiceBaseInfoRes.getHjse())) {
            invoiceInfoEntity.setKpse("0.00");
        } else {
            String s = formatDecimal(new BigDecimal(invoiceBaseInfoRes.getHjse()));
            invoiceInfoEntity.setKpse(s);
        }
        BigDecimal hjje = new BigDecimal(invoiceInfoEntity.getHjbhsje());
        BigDecimal hjse = new BigDecimal(invoiceInfoEntity.getKpse());
        // 不含税考虑
        BigDecimal add = hjje.add(hjse);
        String s = formatDecimal(add);
        invoiceInfoEntity.setJshj(s);
        invoiceInfoEntity.setKpzt("2");
        Date date = transDate(invoiceBaseInfoRes.getKprq());
        invoiceInfoEntity.setKprq(date);
        invoiceInfoEntity.setDdly("4");
        invoiceInfoEntity.setBz(invoiceBaseInfoRes.getBz());
        invoiceInfoEntity.setDdzt("0");
        invoiceInfoEntity.setHsbz("0");
        invoiceInfoEntity.setQdfphm(invoiceBaseInfoRes.getQdfphm());
        invoiceInfoEntity.setFpdm(invoiceBaseInfoRes.getFpdm());
        invoiceInfoEntity.setFphm(invoiceBaseInfoRes.getFphm());
        if ("81".equals(invoiceBaseInfoRes.getFplxDm())) {
            invoiceInfoEntity.setFpzlDm("1");
        } else if ("82".equals(invoiceBaseInfoRes.getFplxDm())) {
            invoiceInfoEntity.setFpzlDm("0");
        } else {
            invoiceInfoEntity.setFpzlDm(invoiceBaseInfoRes.getFplxDm());
        }
        invoiceInfoEntity.setEwm(invoiceBaseInfoRes.getEwm());
        invoiceInfoEntity.setKpr(invoiceBaseInfoRes.getKpr());
        invoiceInfoEntity.setFply(invoiceBaseInfoRes.getFplyDm());
        invoiceInfoEntity.setFpzt(invoiceBaseInfoRes.getFpztDm());

        invoiceInfoEntity.setTdyw(invoiceBaseInfoRes.getTdyslxDm());
        invoiceInfoEntity.setKpfnsrsbh(invoiceBaseInfoRes.getKpfnsrsbh());
        invoiceInfoEntity.setFppzdm(invoiceBaseInfoRes.getFppzDm());
        invoiceInfoEntity.setCezslxdm(invoiceBaseInfoRes.getCezslxDm());
        invoiceInfoEntity.setIsDelete("0");
        invoiceInfoEntity.setCreateTime(new Date());
        orderInvoiceInfoDao.insert(invoiceInfoEntity);
    }

    public R getOneYearInvoiceInfosAndItems(String fp, String nsrsbh, String startTime, String endTime) {
        R r = sendReqGetOneYearInvoiceInfosAndItems(fp, nsrsbh, "1", "50", startTime, endTime);
        log.info("getOneYearInvoiceInfosAndItems 执行完成:{}", r);
        if ("0000".equals(r.get("code").toString())) {
            InvoiceInfoRes invoiceInfoRes = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(r.get("data")), InvoiceInfoRes.class);
            if (!ObjectUtils.isEmpty(invoiceInfoRes)) {
                int pages = Integer.valueOf(invoiceInfoRes.getPages());
                if (pages > 1) {
                    for (int i = 2; i <= pages; i++) {
                        R resp = sendReqGetOneYearInvoiceInfosAndItems(fp, nsrsbh, String.valueOf(i), "50", startTime, endTime);
                        if ("0000".equals(resp.get("code").toString())) {
                            log.info("getOneYearInvoiceInfosAndItems 抽取发票信息第{}页成功", i);
                        } else {
                            log.info("getOneYearInvoiceInfosAndItems 抽取发票信息第{}页失败", i);
                        }
                    }
                }
            }
        }
        log.info("getOneYearInvoiceInfosAndItems 执行完成");
        return R.ok();
    }


    /**
     * 抽取出来循环调用的部分 getOneYearInvoiceInfosAndItems
     *
     * @return
     */
    public R sendReqGetOneYearInvoiceInfosAndItems(String fp, String nsrsbh, String current, String size, String startTime, String endTime) {

        try {
            InvoiceInfoReq invoiceInfoReq = new InvoiceInfoReq();
            // 1-销项发票； 2-进项发票
            invoiceInfoReq.setGjbq(fp);
            // 发票状态： 01-正常； 02-已作废； 03-已红冲-全额；04-已红冲-部分
            String[] fpztdms = new String[]{"01"};
            invoiceInfoReq.setFpztDm(fpztdms);
            // 0-全部; 1-增值税发票管理系统； 2-电子发票服务平台
            invoiceInfoReq.setFplyDm("0");
            //81-全电发票（增值税专用发票）； 82-全电发票（普通发票）；
            String[] fplxdms = new String[]{"81", "82"};
            invoiceInfoReq.setFplxDm(fplxdms);
            invoiceInfoReq.setKprqq(startTime);
            invoiceInfoReq.setKprqz(endTime);
            invoiceInfoReq.setCurrent(String.valueOf(current));
            invoiceInfoReq.setSize(String.valueOf(size));
            invoiceInfoReq.setNsrsbh(nsrsbh);
            long lo = System.currentTimeMillis();
            String jsonString = JsonUtils.getInstance().toJsonString(invoiceInfoReq);
            log.info("getOneYearInvoiceInfosAndItems 抽取发票信息入参: {}", jsonString);
            log.info("getOneYearInvoiceInfosAndItems 开始调用抽取发票信息");
            String res = HttpUtils.doPost(invoiceInfosAndItemsUrl, jsonString);
            log.info("getOneYearInvoiceInfosAndItems 结束调用开抽取发票信息口 耗时: {}", System.currentTimeMillis() - lo);
            log.info("getOneYearInvoiceInfosAndItems 调用抽取发票信息出参: {}", res);
            R r = JsonUtils.getInstance().fromJson(res, R.class);
            InvoiceInfoRes invoiceInfoRes = new InvoiceInfoRes();
            if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
                String data = (String) r.get("data");
                invoiceInfoRes = JsonUtils.getInstance().fromJson(data, InvoiceInfoRes.class);
                if (Objects.nonNull(invoiceInfoRes) && !CollectionUtils.isEmpty(invoiceInfoRes.getRecords())) {
                    List<InvoiceBaseInfoRes> records = invoiceInfoRes.getRecords();
                    for (InvoiceBaseInfoRes invoiceBaseInfoRes : records) {
                        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphmIsDele(invoiceBaseInfoRes.getQdfphm());
                        log.info("getOneYearInvoiceInfosAndItems orderInvoiceInfoEntity 发票为:{}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
                        if (Objects.isNull(orderInvoiceInfoEntity)) {
                            // 蓝票
                            if ("Y".equals(invoiceBaseInfoRes.getSflzfp())) {
                                OrderInvoiceInfoEntity info = orderInvoiceInfoDao.seleInvoiceInfoByQdfphmIsDele(invoiceBaseInfoRes.getQdfphm());
                                if (Objects.isNull(info)) {
                                    OrderInvoiceInfoEntity invoiceInfoEntity = new OrderInvoiceInfoEntity();
                                    String id = DistributedKeyMaker.generateShotKey();
                                    invoiceInfoEntity.setId(id);
                                    invoiceInfoEntity.setPch(DistributedKeyMaker.generateShotKey());
                                    invoiceInfoEntity.setFpqqlsh(DistributedKeyMaker.generateShotKey());
                                    invoiceInfoEntity.setDdh(DistributedKeyMaker.generateShotKey());
                                    invoiceInfoEntity.setGhfMc(invoiceBaseInfoRes.getGmfmc());
                                    invoiceInfoEntity.setGhfNsrsbh(invoiceBaseInfoRes.getGmfnsrsbh());
                                    invoiceInfoEntity.setGhfQylx("01");
                                    invoiceInfoEntity.setKplx("0");
                                    invoiceInfoEntity.setChBz("0");
                                    // 1-销项发票； 2-进项发票
                                    invoiceInfoEntity.setGjbq(fp);
                                    invoiceInfoEntity.setXhfMc(invoiceBaseInfoRes.getXsfmc());
                                    invoiceInfoEntity.setXhfNsrsbh(invoiceBaseInfoRes.getXsfnsrsbh());
                                    invoiceInfoEntity.setSflzfp(invoiceBaseInfoRes.getSflzfp());
                                    invoiceInfoEntity.setHjbhsje(StringUtils.isEmpty(invoiceBaseInfoRes.getHjje()) ? "0.00" : invoiceBaseInfoRes.getHjje());
                                    invoiceInfoEntity.setKpse(StringUtils.isEmpty(invoiceBaseInfoRes.getHjse()) ? "0.00" : invoiceBaseInfoRes.getHjse());
                                    BigDecimal hjje = new BigDecimal(invoiceInfoEntity.getHjbhsje());
                                    BigDecimal hjse = new BigDecimal(invoiceInfoEntity.getKpse());
                                    // 不含税考虑
                                    invoiceInfoEntity.setJshj(hjje.add(hjse).toString());
                                    invoiceInfoEntity.setKpzt("2");
                                    Date date = transDate(invoiceBaseInfoRes.getKprq());
                                    invoiceInfoEntity.setKprq(date);
                                    invoiceInfoEntity.setDdly("4");
                                    invoiceInfoEntity.setBz(invoiceBaseInfoRes.getBz());
                                    invoiceInfoEntity.setDdzt("0");
                                    invoiceInfoEntity.setHsbz("0");
                                    invoiceInfoEntity.setQdfphm(invoiceBaseInfoRes.getQdfphm());
                                    invoiceInfoEntity.setFpdm(invoiceBaseInfoRes.getFpdm());
                                    invoiceInfoEntity.setFphm(invoiceBaseInfoRes.getFphm());
                                    if ("81".equals(invoiceBaseInfoRes.getFplxDm())) {
                                        invoiceInfoEntity.setFpzlDm("1");
                                    } else if ("82".equals(invoiceBaseInfoRes.getFplxDm())) {
                                        invoiceInfoEntity.setFpzlDm("0");
                                    } else {
                                        invoiceInfoEntity.setFpzlDm(invoiceBaseInfoRes.getFplxDm());
                                    }
                                    invoiceInfoEntity.setEwm(invoiceBaseInfoRes.getEwm());
                                    invoiceInfoEntity.setKpr(invoiceBaseInfoRes.getKpr());
                                    invoiceInfoEntity.setFply(invoiceBaseInfoRes.getFplyDm());
                                    invoiceInfoEntity.setFpzt(invoiceBaseInfoRes.getFpztDm());
                                    if ("01".equals(invoiceBaseInfoRes.getFpztDm())) {
                                        invoiceInfoEntity.setChBz("0");
                                    } else if ("02".equals(invoiceBaseInfoRes.getFpztDm())) {
                                        invoiceInfoEntity.setZfBz("1");
                                    } else if ("03".equals(invoiceBaseInfoRes.getFpztDm())) {
                                        invoiceInfoEntity.setChBz("1");
                                    } else if ("04".equals(invoiceBaseInfoRes.getFpztDm())) {
                                        invoiceInfoEntity.setChBz("4");
                                    }
                                    invoiceInfoEntity.setTdyw(invoiceBaseInfoRes.getTdyslxDm());
                                    invoiceInfoEntity.setKpfnsrsbh(invoiceBaseInfoRes.getKpfnsrsbh());
                                    invoiceInfoEntity.setFppzdm(invoiceBaseInfoRes.getFppzDm());
                                    invoiceInfoEntity.setCezslxdm(invoiceBaseInfoRes.getCezslxDm());
                                    invoiceInfoEntity.setIsDelete("0");
                                    invoiceInfoEntity.setCreateTime(new Date());
                                    orderInvoiceInfoDao.insert(invoiceInfoEntity);
                                }
                            }
                        }
                    }
                }
                return R.ok().put("data", invoiceInfoRes);
            }
        } catch (Exception e) {
            log.error("getOneYearInvoiceInfosAndItems 异常:{}", e);
            return R.error();
        }
        return R.error();
    }

    @Override
    public R getHadInvoiceInfos(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("（红票记录），我开具的，入参: {}", orderInvoiceInfoEntity);
        Page page = new Page(orderInvoiceInfoEntity.getCurrPage(), orderInvoiceInfoEntity.getPageSize());
        List<OrderInvoiceInfoEntity> list = orderInvoiceInfoDao.selectIHadListByNsrsbh(page, orderInvoiceInfoEntity);
        for (OrderInvoiceInfoEntity invoiceInfoEntity : list) {
            if (StringUtils.isEmpty(invoiceInfoEntity.getChyy())) {
                invoiceInfoEntity.setChyy("");
            }
            if (StringUtils.isEmpty(invoiceInfoEntity.getQdfphm())) {
                continue;
            }
            List<RedInvoiceConfirmEntity> list1 = redInvoiceConfirmDao.selectByQdfphm(invoiceInfoEntity.getQdfphm());
            for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : list1) {
                if (StringUtils.isEmpty(redInvoiceConfirmEntity.getChyy())) {
                    continue;
                }
                if ("0".equals(redInvoiceConfirmEntity.getChyy())) {
                    invoiceInfoEntity.setChyy("开票有误");
                } else if ("1".equals(redInvoiceConfirmEntity.getChyy())) {
                    invoiceInfoEntity.setChyy("销售退回");
                } else if ("2".equals(redInvoiceConfirmEntity.getChyy())) {
                    invoiceInfoEntity.setChyy("销售折让");
                } else {
                    invoiceInfoEntity.setChyy(redInvoiceConfirmEntity.getChyy());
                }
                break;
            }
        }
        log.info("（红票记录），我开具的，出参: {}", list);
        page.setRecords(list);
        PageUtils pageUtils = new PageUtils(page);
        return R.ok().put(OrderManagementConstant.DATA, pageUtils);
    }

    @Override
    public R getHadInvoiceDetail(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("（红票记录），发票明细，入参: {}", orderInvoiceInfoEntity);
        OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.selectById(orderInvoiceInfoEntity.getId());
        List<OrderInvoiceItemEntity> list = orderInvoiceItemDao.selectItemListById(orderInvoiceInfoEntity.getId());
        List<RedInvoiceConfirmEntity> list1 = redInvoiceConfirmDao.selectByQdfphm(orderInvoiceInfo.getQdfphm());
        orderInvoiceInfo.setItemEntityList(list);
        if (ObjectUtils.isEmpty(list1)) {
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = new RedInvoiceConfirmEntity();
            redInvoiceConfirmEntity.setChyy("");
            redInvoiceConfirmEntity.setZt("");
            orderInvoiceInfo.setRedConfirmInfo(redInvoiceConfirmEntity);
        } else {
            orderInvoiceInfo.setRedConfirmInfo(list1.get(0));
            // 格式化开票日期
            if (!StringUtils.isEmpty(orderInvoiceInfo.getKprq())) {
                String s = DateUtil.formatChineseDate(orderInvoiceInfo.getKprq(), false, false);
                orderInvoiceInfo.setKprqStr(s);
            }
        }
        log.info("（红票记录），发票明细，出参: {}", orderInvoiceInfo);
        return R.ok().put("data", orderInvoiceInfo);
    }

    @Override
    public R getReceiveInvoiceInfos(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("（红票记录），我收到的，入参: {}", orderInvoiceInfoEntity);
        Page page = new Page(orderInvoiceInfoEntity.getCurrPage(), orderInvoiceInfoEntity.getPageSize());
        List<OrderInvoiceInfoEntity> list = orderInvoiceInfoDao.selectReceiveRedList(page, orderInvoiceInfoEntity);
        page.setRecords(list);
        log.info("（红票记录），我收到的，实体转换后出参: {}", JsonUtils.getInstance().toJsonString(page));
        return R.ok().put("data", page);
    }

    @Override
    public R getReceiveInvoiceDetail(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        log.info("（红票记录），我收到的-详情，入参: {}", orderInvoiceInfoEntity);
        if (StringUtils.isEmpty(orderInvoiceInfoEntity.getBaseNsrsbh())) {
            return R.error("当前税号为空");
        }
        if (StringUtils.isEmpty(orderInvoiceInfoEntity.getQdfphm())) {
            return R.error("全电发票号码不能为空");
        }
        if (StringUtils.isEmpty(orderInvoiceInfoEntity.getKprqStr())) {
            return R.error("开票日期不能为空");
        }
        if (orderInvoiceInfoEntity.getKprqStr().length() < 10) {
            return R.error("开票日期格式有误");
        }

        // 查询本地库
        OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.seleInvoiceRedInfoByQdfphm(orderInvoiceInfoEntity.getQdfphm());
        if (!ObjectUtils.isEmpty(orderInvoiceInfo)) {
            List<OrderInvoiceItemEntity> list = orderInvoiceItemDao.selectItemListById(orderInvoiceInfo.getId());
            orderInvoiceInfo.setItemEntityList(list);
        }

        log.info("（红票记录），我收到的-详情，出参: {}", JsonUtils.getInstance().toJsonString(orderInvoiceInfo));
        return R.ok().put("data", orderInvoiceInfo);
    }

    public R getAllInvoiceInfosAndItems(String nsrsbh, String flag, String begin, String end) {
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        for (String fp : list) {
            getInvoiceInfosAndItems(fp, nsrsbh, flag, begin, end);
        }
        return R.ok();
    }

    public R getOneYearAllInvoiceInfosAndItems(String nsrsbh, String startTime, String endTime) {
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        for (String fp : list) {
            getOneYearInvoiceInfosAndItems(fp, nsrsbh, startTime, endTime);
        }
        return R.ok();
    }

    public R pullRedInvoiceConfirmSldh() {
        // 受理中 集合
        log.info("红字确认单结果查询--开始查询受理中数据");
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntityList = redInvoiceConfirmDao.selectRedInsvoiceConfirmSlzt();
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntityList)) {
            for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : redInvoiceConfirmEntityList) {
                try {
                    RedConfirmReq redConfirmReq = new RedConfirmReq();
                    redConfirmReq.setSldh(redInvoiceConfirmEntity.getSldh());
                    redConfirmReq.setNsrsbh(redInvoiceConfirmEntity.getNsrsbh());
                    //查询原蓝票数据
                    OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
                    if (Objects.nonNull(orderInvoiceInfoEntity)) {
                        redConfirmReq.setFplxdm(orderInvoiceInfoEntity.getFpzlDm());
                        redConfirmReq.setTdyw(orderInvoiceInfoEntity.getTdyw());
                    }
                    long lon = System.currentTimeMillis();
                    String invoiceInfoReString = JsonUtils.getInstance().toJsonString(redConfirmReq);
                    log.info("pullRedInvoiceConfirmSldh 调用定时拉取红字确认单信息入参: {}", invoiceInfoReString);
                    String resString = HttpUtils.doPost(redInvoiceConfirmSldhUrl, invoiceInfoReString);
                    log.info("pullRedInvoiceConfirmSldh 结束调用定时拉取红字确认单信息 耗时: {}", System.currentTimeMillis() - lon);
                    log.info("pullRedInvoiceConfirmSldh 调用定时拉取红字确认单信息出参: {}", resString);
                    R rest = JsonUtils.getInstance().fromJson(resString, R.class);
                    String msg = (String) rest.get("msg");
                    String code = (String) rest.get("code");
                    if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                        String datt = (String) rest.get("data");
                        RedInvoiceConfirmSldhRes redInvoiceConfirmSldhRes = JsonUtils.getInstance().fromJson(datt, RedInvoiceConfirmSldhRes.class);
                        if (Objects.nonNull(redInvoiceConfirmSldhRes)) {
//                            RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByQdfphm(redInvoiceConfirmSldhRes.getQdfphm());
//                            if (Objects.nonNull(invoiceConfirmEntity)) {
                            redInvoiceConfirmEntity.setSlzt("2");
                            redInvoiceConfirmEntity.setHztzdbh(redInvoiceConfirmSldhRes.getHzfpxxqrdbh());
                            redInvoiceConfirmEntity.setKdsj(redInvoiceConfirmSldhRes.getKdsj());
                            redInvoiceConfirmEntity.setJshj(redInvoiceConfirmSldhRes.getJshj());
                            redInvoiceConfirmEntity.setXsfnsrsbh(redInvoiceConfirmSldhRes.getXsfnsrsbh());
                            redInvoiceConfirmEntity.setXsfmc(redInvoiceConfirmSldhRes.getXsfmc());
                            redInvoiceConfirmEntity.setZt(redInvoiceConfirmSldhRes.getHzqrxxztDm());
                            redInvoiceConfirmEntity.setGmfmc(redInvoiceConfirmSldhRes.getGmfmc());
                            redInvoiceConfirmEntity.setGmfnsrsbh(redInvoiceConfirmSldhRes.getGmfnsrsbh());
                            redInvoiceConfirmEntity.setUpdateTime(new Date());
                            redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
//                            }
                            // 根据配置 是否红字发票自动开具
                            // 默认选择：否，如果选择是，红字信息确认单审核通过后将自动开具红字发票
                            //OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectAllList();
                            OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(redInvoiceConfirmSldhRes.getXsfnsrsbh());
                            if (ObjectUtils.isEmpty(orderInvoiceConfigEntity)) {
                                return R.error("9999", "未配置开票设置");
                            }

                            log.info("红字确认单结果查询-pullRedInvoiceConfirmSldh-是否红字发票自动开具: {}", JsonUtils.getInstance().toJsonString(orderInvoiceConfigEntity));
                            if (Objects.nonNull(orderInvoiceConfigEntity)) {
                                if ("0".equals(orderInvoiceConfigEntity.getHzfpzdkj())) {
                                    // 红字发票自动开具
                                    RedInvoiceConfirmSldh invoiceConfirmSldh = new RedInvoiceConfirmSldh();
                                    invoiceConfirmSldh.setRedConfirmId(redInvoiceConfirmEntity.getId());
                                    R r = this.redInvoiceIssue(invoiceConfirmSldh);
                                    log.info("红字确认单结果查询-pullRedInvoiceConfirmSldh-是否红字发票自动开具 r返回 : {}", JsonUtils.getInstance().toJsonString(r));
                                }
                            }
                        }
                    } else if (msg.contains("系统正在同步中")) {
                        log.info("pullRedInvoiceConfirmSldh 申请失败受理单号:{}", redInvoiceConfirmEntity.getSldh());
                        redInvoiceConfirmEntity.setByzd2(code);
                        redInvoiceConfirmEntity.setByzd3(msg);
                        redInvoiceConfirmEntity.setZt("11");
                        redInvoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                    } else if ("300111".equals(code)) {
                        // 确认单 申请中状态
                        log.info("pullRedInvoiceConfirmSldh 300111 确认单 置为申请中状态");
                        redInvoiceConfirmEntity.setByzd2(code);
                        redInvoiceConfirmEntity.setByzd3(msg);
                        redInvoiceConfirmEntity.setZt("10");
                        redInvoiceConfirmEntity.setSlzt("1");
                        redInvoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                    } else {
                        log.info("pullRedInvoiceConfirmSldh 处理失败 不可重试");
                        // 其他状态 都为 处理失败 不可重试
                        redInvoiceConfirmEntity.setByzd2(code);
                        redInvoiceConfirmEntity.setByzd3(msg);
                        // 12 处理失败(不可重试)
                        redInvoiceConfirmEntity.setZt("12");
                        // 3 受理失败
                        redInvoiceConfirmEntity.setSlzt("3");
                        redInvoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);

                    }
                } catch (Exception e) {
                    log.error("pullRedInvoiceConfirmSldh 异常:{}", e);
                }
            }
        }
        return R.ok();
    }

    @Override
    public R redInvoiceIssue(RedInvoiceConfirmSldh redInvoiceConfirmSldh) {
        log.info("redInvoiceIssue 开具红票参数:{}", JsonUtils.getInstance().toJsonString(redInvoiceConfirmSldh));
        RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.selectById(redInvoiceConfirmSldh.getRedConfirmId());
        if (Objects.isNull(invoiceConfirmEntity)) {
            return R.error("sldh：{}，不存在红字确认单信息", redInvoiceConfirmSldh.getSldh());
        }
        RedInvoiceIssueReq redInvoiceIssueReq = new RedInvoiceIssueReq();
        redInvoiceIssueReq.setRequestId(DistributedKeyMaker.generateShotKey());
        redInvoiceIssueReq.setDfnsrmc(invoiceConfirmEntity.getDfnsrmc());
        redInvoiceIssueReq.setDfnsrsbh(invoiceConfirmEntity.getDfnsrsbh());
        redInvoiceIssueReq.setHzfpxxqrdbh(invoiceConfirmEntity.getHztzdbh());
        redInvoiceIssueReq.setHzqrxxztDm(invoiceConfirmEntity.getZt());
        // 根据蓝票 开票日期
        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(invoiceConfirmEntity.getDylpqdfphm());
        if (Objects.nonNull(invoiceInfoEntity)) {
            String kprq = DateUtil.formatDate(invoiceInfoEntity.getKprq());
            redInvoiceIssueReq.setKprq(kprq);
            redInvoiceIssueReq.setFpzl(invoiceInfoEntity.getFpzlDm());
            redInvoiceIssueReq.setTdyw(invoiceInfoEntity.getTdyw());
            //查询新的红字确认单明细
            List<OrderInvoiceItemEntity> orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(redInvoiceConfirmSldh.getRedConfirmId());
            redInvoiceIssueReq.setItemEntityList(orderInvoiceItemEntities);
        }
        redInvoiceIssueReq.setLzqdfphm(invoiceConfirmEntity.getDylpqdfphm());
        redInvoiceIssueReq.setNsrsbh(invoiceConfirmEntity.getNsrsbh());
        redInvoiceIssueReq.setChyy(invoiceConfirmEntity.getChyy());
        long lon = System.currentTimeMillis();
        String invoiceInfoReString = JsonUtils.getInstance().toJsonString(redInvoiceIssueReq);
        log.info("redInvoiceIssue，sldh:{}，调用开具红票入参: {}", redInvoiceConfirmSldh.getSldh(), invoiceInfoReString);
        String resString = HttpUtils.doPost(redInvoiceIssueUrl, invoiceInfoReString);
        log.info("redInvoiceIssue，sldh:{}，结束调用开具红票 耗时: {}", redInvoiceConfirmSldh.getSldh(), System.currentTimeMillis() - lon);
        log.info("redInvoiceIssue，sldh:{}，调用开具红票出参: {}", redInvoiceConfirmSldh.getSldh(), resString);
        R rest = JsonUtils.getInstance().fromJson(resString, R.class);
        String code = (String)rest.get(OrderManagementConstant.CODE);
        if (Objects.nonNull(rest) && "0000".equals(code)) {
            String datt = (String) rest.get("data");
            RedInvoiceConfirmSldh invoiceConfirmSldh = JsonUtils.getInstance().fromJson(datt, RedInvoiceConfirmSldh.class);
            if (Objects.nonNull(invoiceConfirmSldh)) {
                // confirm 开票状态置为开票中 和 记录开票结果受理单号
                String kpjgsldh = invoiceConfirmSldh.getSldh();
                invoiceConfirmEntity.setKpjgsldh(kpjgsldh);
                // 置为开具中
                invoiceConfirmEntity.setKjzt("1");
                invoiceConfirmEntity.setKprq(invoiceConfirmSldh.getKprq());
                invoiceConfirmEntity.setUpdateTime(new Date());
                redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                log.info("redInvoiceIssue，sldh:{}，红票开具接口返回信息为: {}", redInvoiceConfirmSldh.getSldh(), datt);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("kpjgsldh", kpjgsldh);
                return R.ok(resultMap);
            }else {
                return R.error("未返回开票结果受理单号");
            }
        }else {
            String errorData = StringUtils.isEmpty((String)rest.get(OrderManagementConstant.MESSAGE)) ? "红票开具接口调用异常" : (String)rest.get(OrderManagementConstant.MESSAGE);
            return R.error(errorData);
//                 R.setCodeAndMsg(OrderInfoContentEnum.ISSUE_INVOICE, errorData);
        }
    }

    public R pullRedInvoiceResultInfo(String tenantCode) {
        // 查询红票开票中数据
        log.info("查询红票开票中数据---------");
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntityList = redInvoiceConfirmDao.selectRedInsvoiceConfirmKjzt();
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntityList)) {
            for (RedInvoiceConfirmEntity invoiceConfirmEntity : redInvoiceConfirmEntityList) {
                RedConfirmReq redConfirmReq = new RedConfirmReq();
                List<OrderInvoiceInfoEntity> invoiceInfoByQdfphms = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(invoiceConfirmEntity.getDylpqdfphm());
                if(!CollectionUtils.isEmpty(invoiceInfoByQdfphms)){
                    OrderInvoiceInfoEntity orderInvoiceInfoEntity = invoiceInfoByQdfphms.get(0);
                    redConfirmReq.setFplxdm(orderInvoiceInfoEntity.getFpzlDm());
                    redConfirmReq.setTdyw(orderInvoiceInfoEntity.getTdyw());
                    redConfirmReq.setXhfmc(StringUtils.isEmpty(invoiceConfirmEntity.getXsfmc()) ? orderInvoiceInfoEntity.getXhfMc() : invoiceConfirmEntity.getXsfmc());
                }
                redConfirmReq.setSldh(invoiceConfirmEntity.getKpjgsldh());
                redConfirmReq.setNsrsbh(invoiceConfirmEntity.getNsrsbh());
                redConfirmReq.setHzxxbbh(invoiceConfirmEntity.getHztzdbh());
                redConfirmReq.setJshj(invoiceConfirmEntity.getJshj());
                redConfirmReq.setHzxxbzt(invoiceConfirmEntity.getZt());
                redConfirmReq.setKprq(DateUtil.format(invoiceConfirmEntity.getKprq(),"yyyy-MM-dd HH:mm:ss"));
                long lon = System.currentTimeMillis();
                String invoiceInfoReString = JsonUtils.getInstance().toJsonString(redConfirmReq);
                log.info("pullRedInvoiceResultInfo 调用查询红票开票中数据入参: {}", invoiceInfoReString);
                String resString = HttpUtils.doPost(redInvoiceResultInfoUrl, invoiceInfoReString);
                log.info("pullRedInvoiceResultInfo 结束调用查询红票开票中数据 耗时: {}", System.currentTimeMillis() - lon);
                log.info("pullRedInvoiceResultInfo 调用查询红票开票中数据出参: {}", resString);
                R rest = JsonUtils.getInstance().fromJson(resString, R.class);
                // 202230202 Lnn: 需要同时更新所有相同全电发票号码的数据
                List<OrderInvoiceInfoEntity>  infos = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(invoiceConfirmEntity.getDylpqdfphm());
                if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                    String code = (String) rest.get("code");
                    String msg = (String) rest.get("msg");
                    String datt = (String) rest.get("data");
                    RedInvoiceResultInfoRes redInvoiceResultInfoRes = JsonUtils.getInstance().fromJson(datt, RedInvoiceResultInfoRes.class);
                    if (Objects.nonNull(redInvoiceResultInfoRes)) {
                        // 更细 确认信息表 发票号码
                        invoiceConfirmEntity.setKjzt("Y");
                        invoiceConfirmEntity.setHztzdbh(redInvoiceResultInfoRes.getHzfpxxqrdbh());
                        invoiceConfirmEntity.setJshj(redInvoiceResultInfoRes.getJshj());
                        invoiceConfirmEntity.setQdfphm(redInvoiceResultInfoRes.getQdfphm());
                        invoiceConfirmEntity.setXsfmc(redInvoiceResultInfoRes.getXsfmc());
                        invoiceConfirmEntity.setXsfnsrsbh(redInvoiceResultInfoRes.getXsfnsrsbh());
                        invoiceConfirmEntity.setZt(redInvoiceResultInfoRes.getHzqrxxztDm());
                        invoiceConfirmEntity.setKjztCode(code);
                        invoiceConfirmEntity.setKjztMsg(msg);
                        invoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(invoiceConfirmEntity);

                        // 蓝票
                        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(invoiceConfirmEntity.getDylpqdfphm());

                        if (Objects.nonNull(invoiceInfoEntity)) {
                            OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
                            BeanUtils.copyProperties(invoiceInfoEntity, orderInvoiceInfoEntity);

                            // 新的红票
                            String invoiceId = DistributedKeyMaker.generateShotKey();
                            orderInvoiceInfoEntity.setId(invoiceId);
                            orderInvoiceInfoEntity.setPch(DistributedKeyMaker.generateShotKey());
                            orderInvoiceInfoEntity.setFpqqlsh(DistributedKeyMaker.generateShotKey());
                            orderInvoiceInfoEntity.setDdh(DistributedKeyMaker.generateShotKey());
                            orderInvoiceInfoEntity.setKplx("1");
                            orderInvoiceInfoEntity.setYqdfphm(invoiceInfoEntity.getQdfphm());
                            orderInvoiceInfoEntity.setQdfphm(invoiceConfirmEntity.getQdfphm());
                            // 订单状态 0: 正常
                            orderInvoiceInfoEntity.setDdzt("0");
                            // 开票方式 1 手动开票
                            orderInvoiceInfoEntity.setKpfs("1");
                            //开票成功 保存数据到本地 设置订单来源和 开票状态
                            orderInvoiceInfoEntity.setDdly(OrderSourceEnum.ORDER_SOURCE_ENUM_3.getKey());
                            orderInvoiceInfoEntity.setIsDelete("0");
                            orderInvoiceInfoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_2.getKey());
                            orderInvoiceInfoEntity.setDdscrq(new Date());
                            orderInvoiceInfoEntity.setCreateTime(new Date());
                            orderInvoiceInfoEntity.setUpdateTime(new Date());
                            orderInvoiceInfoEntity.setChBz("0");
                            orderInvoiceInfoEntity.setPdfUrl(redInvoiceResultInfoRes.getPdfxzUrl());
                            orderInvoiceInfoEntity.setOfdUrl(redInvoiceResultInfoRes.getOfdxzUrl());
                            //取红字确认单上的金额和税额
                            orderInvoiceInfoEntity.setHjbhsje(invoiceConfirmEntity.getFpje());
                            orderInvoiceInfoEntity.setKpse(invoiceConfirmEntity.getFpse());
                            orderInvoiceInfoEntity.setJshj(invoiceConfirmEntity.getJshj());
                            orderInvoiceInfoEntity.setKprq(transDate(redInvoiceResultInfoRes.getKprq()));
                            orderInvoiceInfoEntity.setHzxxbbh(invoiceConfirmEntity.getHztzdbh());
                            orderInvoiceInfoEntity.setHzqrxxztDm(invoiceConfirmEntity.getZt());
                            //取红字确认单上的系统来源
                            orderInvoiceInfoEntity.setXtly(invoiceConfirmEntity.getXtly());

                            StringBuilder sb = new StringBuilder();
                            SalerWarningInfo salerWarningInfo = salerWarningDao.selectWarnInfoByNsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                            if (Objects.nonNull(salerWarningInfo) && "0".equals(salerWarningInfo.getAutoBz())) {
                                // 对应蓝票信息
                                if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getBz())) {
                                    sb.append("原蓝票备注: ");
                                    sb.append(orderInvoiceInfoEntity.getBz());
                                    sb.append(" ");
                                }
                            }
                            // 拼接红票备注
                            sb.append("被红冲蓝字全电发票号码: ");
                            sb.append(invoiceInfoEntity.getQdfphm());
                            sb.append(" ");
                            sb.append("红字发票信息确认单编号: ");
                            sb.append(invoiceConfirmEntity.getHztzdbh());
                            orderInvoiceInfoEntity.setBz(sb.toString());
                            orderInvoiceInfoEntity.setEmailPushStatus("0");
                            orderInvoiceInfoEntity.setShortMsgPushStatus("0");
                            orderInvoiceInfoEntity.setPushStatus("0");
                            //查询配置进行自动推送
                            OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                            if (Objects.nonNull(orderInvoiceConfigEntity)) {
                                // 自动交付
                                if ("0".equals(orderInvoiceConfigEntity.getKpzdjf())) {
                                    log.info("getTaskInvoiceInfo 定时任务查询红票开票结果-自动推送邮件或者手机 Start");
                                    // 自动发送
                                    if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getGhfYx())) {
                                        // 自动发送邮箱
                                        log.info("getTaskInvoiceInfo 定时任务查询红票开票结果-自动发送邮件账号: {},全电号码: {}", orderInvoiceInfoEntity.getGhfYx(), orderInvoiceInfoEntity.getQdfphm());
                                        Map<String, Object> sendEmailMap = openApiService.sendEmail(orderInvoiceInfoEntity.getGhfYx(), orderInvoiceInfoEntity.getQdfphm());
                                        if ("0000".equals(String.valueOf(sendEmailMap.get(OrderManagementConstant.CODE)))) {
                                            orderInvoiceInfoEntity.setEmailPushStatus("2");
                                        }else {
                                            orderInvoiceInfoEntity.setEmailPushStatus("3");
                                        }
                                    }
                                    if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getGhfSj())) {
                                        // 自动发送手机
                                        log.info("getTaskInvoiceInfo 定时任务查询红票开票结果-自动发送手机号码: {},全电号码: {}", orderInvoiceInfoEntity.getGhfSj(), orderInvoiceInfoEntity.getQdfphm());
                                        Map<String, Object> sendShortMessageMap = openApiService.sendShortMessage(orderInvoiceInfoEntity.getGhfSj(), orderInvoiceInfoEntity.getQdfphm());
                                        if ("0000".equals(String.valueOf(sendShortMessageMap.get(OrderManagementConstant.CODE)))) {
                                            orderInvoiceInfoEntity.setShortMsgPushStatus("2");
                                        }else {
                                            orderInvoiceInfoEntity.setShortMsgPushStatus("3");
                                        }
                                    }
                                    log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动推送邮件或者手机 End");
                                }
                                // 自动回推
                                if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getXtly())) {
                                    InvoiceBackpushConfigEntity invoiceBackpushConfigEntity = invoiceBackpushConfigService.getInvoiceBackpushConfigByOrderConfigIdAndXtly(orderInvoiceConfigEntity.getId(), orderInvoiceInfoEntity.getXtly());
                                    if (Objects.nonNull(invoiceBackpushConfigEntity)) {
                                        log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动推送接口地址: {},全电号码: {}",invoiceBackpushConfigEntity.getBackpushUrl(),orderInvoiceInfoEntity.getQdfphm());
                                        Map<String, Object> backpushMap = openApiService.sendPushData(invoiceBackpushConfigEntity.getBackpushUrl(), orderInvoiceInfoEntity, tenantCode);
                                        if ("0000".equals(String.valueOf(backpushMap.get(OrderManagementConstant.CODE)))) {
                                            orderInvoiceInfoEntity.setPushStatus("1");
                                        }else {
                                            orderInvoiceInfoEntity.setPushStatus("2");
                                        }
                                    }
                                }
                            }
                            orderInvoiceInfoDao.insert(orderInvoiceInfoEntity);
                            log.info("redInvoiceConfirmSubmit 红票信息入库成功");
                            //查询红字确认单明细，不查原蓝票明细
                            List<OrderInvoiceItemEntity> orderConfirmItemEntities = orderInvoiceItemDao.selectItemListById(invoiceConfirmEntity.getId());
                            // 红票明细更新，直接将红字确认单的明细更新为红字发票明细即可，时间改一下
                            if (!CollectionUtils.isEmpty(orderConfirmItemEntities)) {
                                for (OrderInvoiceItemEntity orderConfirmItem : orderConfirmItemEntities) {
                                    orderConfirmItem.setIsDelete("0");
                                    orderConfirmItem.setCreateTime(new Date());
                                    orderConfirmItem.setUpdateTime(new Date());
                                    orderConfirmItem.setHsbz(orderInvoiceInfoEntity.getHsbz());
                                    orderConfirmItem.setSl(slTranst(orderConfirmItem.getSl()));
                                    orderConfirmItem.setOrderInvoiceId(invoiceId);
                                    orderInvoiceItemDao.updateById(orderConfirmItem);
                                }
                                orderInvoiceInfoEntity.setItemEntityList(orderConfirmItemEntities);
                                log.info("redInvoiceConfirmSubmit 红票明细信息入库成功");
                            }
                            // 附加信息
                            List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(invoiceInfoEntity.getId());
                            if (!CollectionUtils.isEmpty(invoiceAdditionInfoEntities)) {
                                for (InvoiceAdditionInfoEntity invoiceAdditionInfoEntity : invoiceAdditionInfoEntities) {
                                    invoiceAdditionInfoEntity.setId(DistributedKeyMaker.generateShotKey());
                                    invoiceAdditionInfoEntity.setOrderInvoiceInfoId(invoiceId);
                                    invoiceAdditionInfoDao.insert(invoiceAdditionInfoEntity);
                                }
                                orderInvoiceInfoEntity.setInfoEntityList(invoiceAdditionInfoEntities);
                                log.info("redInvoiceConfirmSubmit 红票附加信息入库成功");
                            }
                            //查询原蓝票特定业务
                            List<InvoiceTdywEntity> tdywEntityList = invoiceTdywService.queryTdywRelation(Collections.singleton(invoiceInfoEntity.getId()));
                            if(!CollectionUtils.isEmpty(tdywEntityList)){
                                // 保存红票特定业务信息
                                tdywEntityList.stream().forEach(invoiceTdywEntity -> {
                                    invoiceTdywService.saveTdywRelation(invoiceTdywEntity, invoiceId);
                                });
                                orderInvoiceInfoEntity.setInvoiceTdywEntity(tdywEntityList.get(0));
                                log.info("redInvoiceConfirmSubmit 红票特定业务信息入库成功");
                            }
                            //查询原蓝票差额征税信息
                            List<InvoiceCezsEntity> cezslist = invoiceCezsDao.selectList(Wrappers.lambdaQuery(InvoiceCezsEntity.class).eq(InvoiceCezsEntity::getOrderInvoiceInfoId, invoiceInfoEntity.getId()));
                            if(!CollectionUtils.isEmpty(cezslist)) {
                                // 保存红票差额征税信息
                                cezslist.stream().forEach(invoiceCezsEntity -> {
                                    invoiceCezsEntity.setId(DistributedKeyMaker.generateShotKey());
                                    invoiceCezsEntity.setOrderInvoiceInfoId(invoiceId);
                                    invoiceCezsDao.insert(invoiceCezsEntity);
                                });
                                orderInvoiceInfoEntity.setCezslist(cezslist);
                                log.info("redInvoiceConfirmSubmit 红票差额征税信息入库成功");
                            }
                            //更新冲红标志为 红冲成功
                            for (OrderInvoiceInfoEntity info : infos) {
                                if(StringUtils.isEmpty(info.getSykchbhsje())){
                                    info.setChBz("1");
                                }else {
                                    BigDecimal sykchbhsje = new BigDecimal(info.getSykchbhsje());
                                    if(MathUtil.compare(info.getSykchbhsje(),"0") > 0){
                                        //部分冲红成功
                                        info.setChBz("4");
                                    }else {
                                        //全部冲红成功
                                        info.setChBz("1");
                                    }
                                }
                                info.setChsj(new Date());
                                info.setUpdateTime(new Date());
                                orderInvoiceInfoDao.updateById(info);
                            }
                        }
                    }
                } else if (Objects.nonNull(rest) && !"0000".equals(rest.get("code"))) {
                    String code = (String) rest.get("code");
                    String msg = (String) rest.get("msg");
                    if ("300111".equals(code)) {
                        // 红字发票开具 开具中
                        // 1开具中
                        invoiceConfirmEntity.setKjzt("1");
                        invoiceConfirmEntity.setKjztCode(code);
                        invoiceConfirmEntity.setKjztMsg(msg);
                        invoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                    } else {
                        //更新冲红标志为 红冲成功
                        for (OrderInvoiceInfoEntity info : infos) {
                            if(StringUtils.isEmpty(info.getSykchbhsje())){
                                info.setChBz("3");
                            }else {
                                if(MathUtil.compare(info.getSykchbhsje(),"0") > 0){
                                    //部分冲红失败
                                    info.setChBz("6");
                                }else {
                                    //全部冲红失败
                                    info.setChBz("3");
                                }
                            }
                            info.setChsj(new Date());
                            info.setUpdateTime(new Date());
                            orderInvoiceInfoDao.updateById(info);
                        }
                        // 6开具失败
                        invoiceConfirmEntity.setKjzt("6");
                        invoiceConfirmEntity.setKjztCode(code);
                        invoiceConfirmEntity.setKjztMsg(msg);
                        invoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                    }
                }
            }
        }
        return R.ok();
    }

    @Override
    public PageUtils listRedInvoiceConfirm(RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        log.info("listRedInvoiceConfirm 我发起的确认单查询: {}", JsonUtils.getInstance().toJsonString(redInvoiceConfirmEntity));
        if (!StringUtils.isEmpty(redInvoiceConfirmEntity.getKprqStartTime())) {
            redInvoiceConfirmEntity.setKprqStartTime(redInvoiceConfirmEntity.getKprqStartTime().replace("-", "") + "000000");
        }
        if (!StringUtils.isEmpty(redInvoiceConfirmEntity.getKprqEndTime())) {
            redInvoiceConfirmEntity.setKprqEndTime(redInvoiceConfirmEntity.getKprqEndTime().replace("-", "") + "235959");
        }
        Page page = new Page(redInvoiceConfirmEntity.getCurrPage(), redInvoiceConfirmEntity.getPageSize());
        redInvoiceConfirmEntity.setNsrsbh(redInvoiceConfirmEntity.getBaseNsrsbh());
        List<RedInvoiceConfirmEntity> list = redInvoiceConfirmDao.listRedInvoiceConfirm(page, redInvoiceConfirmEntity);
        if (!CollectionUtils.isEmpty(list)) {
            for (RedInvoiceConfirmEntity invoiceConfirmEntity : list) {
                // 格式化开票日期
                if (!StringUtils.isEmpty(invoiceConfirmEntity.getKprq())) {
                    String s = DateUtil.formatDate(invoiceConfirmEntity.getKprq());
                    invoiceConfirmEntity.setKprqStr(s);
                }
            }
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    public R queryHzqrxx(String gxfxz, String nsrsbh, Map map) {
        List<String> hzqrxxztDms = new ArrayList<>();
//        hzqrxxztDms.add("01");
//        hzqrxxztDms.add("02");
//        hzqrxxztDms.add("03");
//        hzqrxxztDms.add("04");
//        hzqrxxztDms.add("05");
//        hzqrxxztDms.add("06");
//        hzqrxxztDms.add("07");
//        hzqrxxztDms.add("08");
//        hzqrxxztDms.add("09");
        hzqrxxztDms.add("10");//10 代表全部
        // 确认单状态：01无需确认 02销方录入待购 方确认 03购方录入待销方确认 04购销双方 已确认 05作废（销方录入购方否认） 06作废 （购方录入销方否认） 07作废（超72小时未确 认） 08作废（发起方撤销） 09作废（确认后撤销)
        for (String hzqrxxztDm : hzqrxxztDms) {
            getHzqrxxztDm(hzqrxxztDm, gxfxz, nsrsbh, map);
        }
        return R.ok();
    }

    private R getHzqrxxztDm(String hzqrxxztDm, String gxfxz, String nsrsbh, Map map) {
        String startTime = "";
        String endTime = "";
        String flag = map.get("flag").toString();
        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
            // 前一天数据
            startTime = map.get("beginTime").toString();
            endTime = map.get("endTime").toString();
        } else {
            // 前一天数据
            LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minus(1, ChronoUnit.DAYS);
            startTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            endTime = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
//            LocalDateTime endLocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).minus(1, ChronoUnit.DAYS);
//            endTime = endLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        RedConfirmListReq redConfirmListReq = new RedConfirmListReq();
        redConfirmListReq.setGxfxz(gxfxz);
        redConfirmListReq.setLrfsf(gxfxz.equals("0") ? "1" : "0");
        redConfirmListReq.setHzqrxxztDm(hzqrxxztDm);
        redConfirmListReq.setKprqq(startTime);
        redConfirmListReq.setKprqz(endTime);
        redConfirmListReq.setCurrent("1");
        redConfirmListReq.setSize("50");
        redConfirmListReq.setNsrsbh(nsrsbh);
        long lo = System.currentTimeMillis();
        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmListReq);
        log.info("getHzqrxxztDm 调用我收到的确认单入参: {}", jsonString);
        log.info("getHzqrxxztDm 开始调用我收到的确认单");
        String res = HttpUtils.doPost(queryHzqrxxUrl, jsonString);
        log.info("getHzqrxxztDm 结束调用我收到的确认单 耗时: {}", System.currentTimeMillis() - lo);
        log.info("getHzqrxxztDm 调用我收到的确认单出参: {}", res);
        R rl = JsonUtils.getInstance().fromJson(res, R.class);
        List<QueryHzqrxxRes> records = new ArrayList<>();
        int pages = 0;
        if (Objects.nonNull(rl) && "0000".equals(rl.get("code"))) {
            String data = (String) rl.get("data");
            CommonQueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().fromJson(data, CommonQueryHzqrxxRes.class);
            if (Objects.nonNull(queryHzqrxxRes) && !CollectionUtils.isEmpty(queryHzqrxxRes.getRecords())) {
                pages = Integer.valueOf(queryHzqrxxRes.getPages());
                records.addAll(queryHzqrxxRes.getRecords());
            } else {
                return R.error();
            }
        } else {
            return R.error();
        }
        if (pages > 1) {
            for (int a = 2; a <= pages; a++) {
                redConfirmListReq.setCurrent(String.valueOf(a));

                long loo = System.currentTimeMillis();
                String rest = JsonUtils.getInstance().toJsonString(redConfirmListReq);
                log.info("getHzqrxxztDm 循环调用入参: {}", rest);
                String reture = HttpUtils.doPost(queryHzqrxxUrl, rest);
                log.info("getHzqrxxztDm 结束调用循环调用 耗时: {}", System.currentTimeMillis() - loo);
                log.info("getHzqrxxztDm 循环调用出参: {}", reture);
                R r = JsonUtils.getInstance().fromJson(reture, R.class);

                if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
                    String data = (String) r.get("data");
                    //
                    CommonQueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().fromJson(data, CommonQueryHzqrxxRes.class);
                    if (Objects.nonNull(queryHzqrxxRes) && !CollectionUtils.isEmpty(queryHzqrxxRes.getRecords())) {
                        records.addAll(queryHzqrxxRes.getRecords());

                    }
                }

            }
        }
        for (QueryHzqrxxRes hzqrxxRes : records) {
            // 我收到的  fpfqr  1  我收到的
            RedInvoiceConfirmEntity confirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByQrdbh(hzqrxxRes.getHzfpxxqrdbh(), "1");
            if (Objects.isNull(confirmEntity)) {
                RedInvoiceConfirmEntity redInvoiceConfirmEntity = new RedInvoiceConfirmEntity();
                String id = DistributedKeyMaker.generateShotKey();
                redInvoiceConfirmEntity.setId(id);
                redInvoiceConfirmEntity.setDylpqdfphm(hzqrxxRes.getLzfphm());
                redInvoiceConfirmEntity.setNsrsbh(nsrsbh);
                redInvoiceConfirmEntity.setXsfnsrsbh(hzqrxxRes.getXsfnsrsbh());
                redInvoiceConfirmEntity.setXsfmc(hzqrxxRes.getXsfmc());
                redInvoiceConfirmEntity.setGmfnsrsbh(hzqrxxRes.getGmfnsrsbh());
                redInvoiceConfirmEntity.setGmfmc(hzqrxxRes.getGmfmc());
                if (redConfirmListReq.getNsrsbh().equals(hzqrxxRes.getXsfnsrsbh())) {
                    // 销货方
                    redInvoiceConfirmEntity.setGxsf("0");
                    redInvoiceConfirmEntity.setDfnsrmc(hzqrxxRes.getGmfmc());

                } else if (redConfirmListReq.getNsrsbh().equals(hzqrxxRes.getGmfnsrsbh())) {
                    // 购货方
                    redInvoiceConfirmEntity.setGxsf("1");
                    redInvoiceConfirmEntity.setDfnsrmc(hzqrxxRes.getXsfmc());

                } else {
                    redInvoiceConfirmEntity.setGxsf("");
                    redInvoiceConfirmEntity.setDfnsrmc("");
                }

                redInvoiceConfirmEntity.setSqrq(new Date());
                redInvoiceConfirmEntity.setLzkprq(hzqrxxRes.getLzkprq());
                redInvoiceConfirmEntity.setLzhjje(hzqrxxRes.getLzhjje());
                redInvoiceConfirmEntity.setLzhjse(hzqrxxRes.getLzhjse());
                redInvoiceConfirmEntity.setLzfppzDm(hzqrxxRes.getLzfppzDm());
                redInvoiceConfirmEntity.setZzsytDm(hzqrxxRes.getZzsytDm());
                redInvoiceConfirmEntity.setXfsytDm(hzqrxxRes.getXfsytDm());
                redInvoiceConfirmEntity.setFprzztDm(hzqrxxRes.getFprzztDm());
                redInvoiceConfirmEntity.setYkjhzfpbz(hzqrxxRes.getYkjhzfpbz());
                redInvoiceConfirmEntity.setKjzt(hzqrxxRes.getYkjhzfpbz());
                if (!StringUtils.isEmpty(hzqrxxRes.getHzcxse()) && !hzqrxxRes.getHzcxse().contains("-")) {
                    BigDecimal fpse = new BigDecimal(hzqrxxRes.getHzcxse());
                    redInvoiceConfirmEntity.setFpse("-" + formatDecimal(fpse));
                } else {
                    BigDecimal fpse1 = new BigDecimal(hzqrxxRes.getHzcxse());
                    redInvoiceConfirmEntity.setFpse(formatDecimal(fpse1));
                }

                if (!StringUtils.isEmpty(hzqrxxRes.getHzcxje())) {
                    String hzcxje = hzqrxxRes.getHzcxje().replace("-", "");
                    BigDecimal hzcxjeBig = new BigDecimal(hzcxje);
                    redInvoiceConfirmEntity.setFpje("-" + formatDecimal(hzcxjeBig));
                }
                redInvoiceConfirmEntity.setFpje(hzqrxxRes.getHzcxje());
                redInvoiceConfirmEntity.setChyy(hzqrxxRes.getChyyDm());
                redInvoiceConfirmEntity.setLrfsf(hzqrxxRes.getLrfsf());
                redInvoiceConfirmEntity.setUuid(hzqrxxRes.getUuid());
                redInvoiceConfirmEntity.setHztzdbh(hzqrxxRes.getHzfpxxqrdbh());
                redInvoiceConfirmEntity.setZt(hzqrxxRes.getHzqrxxztDm());
                redInvoiceConfirmEntity.setUpdateTime(new Date());
                redInvoiceConfirmEntity.setCreateTime(new Date());
                redInvoiceConfirmEntity.setIsDelete("0");
                // 我收到的
                redInvoiceConfirmEntity.setFpfqr("1");
                redInvoiceConfirmDao.insert(redInvoiceConfirmEntity);
                // 我收到的 没有开具按钮  所以基本信息不用入库
                if (!CollectionUtils.isEmpty(hzqrxxRes.getHzqrxxmxList())) {
                    // 明细信息
                    for (HzqrxxmxListRes hzqrxxmxListRes : hzqrxxRes.getHzqrxxmxList()) {
                        OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                        orderInvoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                        // 维护确认单表 和 明细表的关系
                        orderInvoiceItemEntity.setOrderInvoiceId(id);
                        orderInvoiceItemEntity.setXmmc(hzqrxxmxListRes.getXmmc());
                        orderInvoiceItemEntity.setGgxh(hzqrxxmxListRes.getGgxh());
                        orderInvoiceItemEntity.setDw(hzqrxxmxListRes.getDw());
                        orderInvoiceItemEntity.setXmsl(hzqrxxmxListRes.getFpspsl());
                        orderInvoiceItemEntity.setDj(hzqrxxmxListRes.getFpspdj());
                        orderInvoiceItemEntity.setJe(hzqrxxmxListRes.getJe());
                        orderInvoiceItemEntity.setSl(hzqrxxmxListRes.getSll());
                        orderInvoiceItemEntity.setSe(hzqrxxmxListRes.getSe());
                        orderInvoiceItemEntity.setSpbm(hzqrxxmxListRes.getSphfwssflhbbm());
                        orderInvoiceItemEntity.setIsDelete("0");
                        orderInvoiceItemEntity.setCreateTime(new Date());
                        orderInvoiceItemDao.insert(orderInvoiceItemEntity);
                    }
                }
            } else {
                confirmEntity.setZt(hzqrxxRes.getHzqrxxztDm());
                confirmEntity.setHztzdbh(hzqrxxRes.getHzfpxxqrdbh());
                confirmEntity.setChyy(hzqrxxRes.getChyyDm());
                confirmEntity.setXsfnsrsbh(hzqrxxRes.getXsfnsrsbh());
                confirmEntity.setXsfmc(hzqrxxRes.getXsfmc());
                confirmEntity.setGmfnsrsbh(hzqrxxRes.getGmfnsrsbh());
                confirmEntity.setGmfmc(hzqrxxRes.getGmfmc());
                confirmEntity.setUuid(hzqrxxRes.getUuid());
                confirmEntity.setUpdateTime(new Date());
                redInvoiceConfirmDao.updateById(confirmEntity);
            }
        }
        updateInvoiceInfoByFphm(records);

        return R.ok();
    }

    @Override
    public R hzqrxxUpdate(RedConfirmHandle redConfirmHandle) {
        // 对于已经申请的红冲信息进行操作(确认、否认、撤销)。
        // baseNsrsbh
        log.info("hzqrxxUpdate 红字发票确认信息处理:{}", JsonUtils.getInstance().toJsonString(redConfirmHandle));
        redConfirmHandle.setNsrsbh(redConfirmHandle.getBaseNsrsbh());
        redConfirmHandle.setXsfnsrsbh(redConfirmHandle.getBaseNsrsbh());
        //获取红字确认单信息
        RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectById(redConfirmHandle.getRedConfirmId());
        //获取原蓝票信息
        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
        if (Objects.nonNull(invoiceInfoEntity)) {
            redConfirmHandle.setFpzl(invoiceInfoEntity.getFpzlDm());
            redConfirmHandle.setTdyw(invoiceInfoEntity.getTdyw());
        }
        long lo = System.currentTimeMillis();
        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmHandle);
        log.info("hzqrxxUpdate，uuid：{}，调用红字发票确认信息处理入参: {}", redConfirmHandle.getUuid(), jsonString);
        String res = HttpUtils.doPost(hzqrxxUpdateUrl, jsonString);
        log.info("hzqrxxUpdate，uuid：{}，结束调用红字发票确认信息处理 耗时: {}", redConfirmHandle.getUuid(), System.currentTimeMillis() - lo);
        log.info("hzqrxxUpdate，uuid：{}，调用红字发票确认信息处理出参: {}", redConfirmHandle.getUuid(), res);
        R r = JsonUtils.getInstance().fromJson(res, R.class);
        if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
            String data = (String) r.get("data");
            //
            HzqrxxUpdateRes hzqrxxUpdateRes = JsonUtils.getInstance().fromJson(data, HzqrxxUpdateRes.class);
            if (Objects.nonNull(hzqrxxUpdateRes)) {
                if ("Y".equals(hzqrxxUpdateRes.getCode())) {

                    if (Objects.nonNull(redInvoiceConfirmEntity)) {
                        //处理类型【01 撤销、02 确认、03 拒绝】
                        if ("01".equals(redConfirmHandle.getCllx())) {
                            // 3已撤销
                            redInvoiceConfirmEntity.setKjzt("3");
                        } else if ("02".equals(redConfirmHandle.getCllx())) {
                            // 4已确认
                            redInvoiceConfirmEntity.setKjzt("4");
                        } else if ("03".equals(redConfirmHandle.getCllx())) {
                            // 5已拒绝
                            redInvoiceConfirmEntity.setKjzt("5");
                        } else {

                        }
                        redInvoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmEntity.setIsDelete("1");
                        redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);

                        // 撤销成功后  对应的蓝字发票继续回到【新增确认单】列表界面，这张票能继续发起红冲
                        if (Objects.nonNull(invoiceInfoEntity)) {
                            invoiceInfoEntity.setChBz("0");
                            invoiceInfoEntity.setUpdateTime(new Date());
                            orderInvoiceInfoDao.updateById(invoiceInfoEntity);
                        }
                    }
                    return R.ok();
                } else {
                    return R.error("撤销失败");
                }
            }
        } else {
            return R.error("撤销失败");
        }
        return R.ok();
    }

    public R queryHzqrxxInfo(String nsrsbh, Map map) {
        log.info("queryHzqrxxInfo 定时查询 我收到的确认单入口");
        List<String> gxfxzs = new ArrayList<>();
        // 购销方选择
        gxfxzs.add("0");
        gxfxzs.add("1");
        for (String gxfxz : gxfxzs) {
            queryHzqrxx(gxfxz, nsrsbh, map);

        }
        return R.ok();
    }

    @Override
    public PageUtils getListRedInvoiceConfirm(RedInvoiceConfirmEntity redInvoiceConfirmEntity) {
        log.info("getListRedInvoiceConfirm 我收到的确认单查询:{}", JsonUtils.getInstance().toJsonString(redInvoiceConfirmEntity));
        Page page = new Page(redInvoiceConfirmEntity.getCurrPage(), redInvoiceConfirmEntity.getPageSize());
        List<RedInvoiceConfirmEntity> list = redInvoiceConfirmDao.getListRedInvoiceConfirm(page, redInvoiceConfirmEntity);
        if (!CollectionUtils.isEmpty(list)) {
            for (RedInvoiceConfirmEntity invoiceConfirmEntity : list) {
                // 格式化开票日期
                if (!StringUtils.isEmpty(invoiceConfirmEntity.getKprq())) {
                    String s = DateUtil.formatDate(invoiceConfirmEntity.getKprq());
                    invoiceConfirmEntity.setKprqStr(s);
                }
            }
        }
        page.setRecords(list);
        return new PageUtils(page);
    }

    public R queryHzqrxxStatus() {
        log.info("queryHzqrxxStatus 定时查询 确认单状态入口");
        // 查询 02销方录入待购方确认 03购方录入待销方确认 状态
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmDao.queryHzqrxxStatus();
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
            for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : redInvoiceConfirmEntities) {
                RedConfirmHandle redConfirmHandle = new RedConfirmHandle();
                redConfirmHandle.setUuid(redInvoiceConfirmEntity.getUuid());
                redConfirmHandle.setXsfnsrsbh(redInvoiceConfirmEntity.getXsfnsrsbh());
                redConfirmHandle.setNsrsbh(redInvoiceConfirmEntity.getNsrsbh());
                // 蓝票信息
                OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
                redConfirmHandle.setFpzl(invoiceInfoEntity.getFpzlDm());
                redConfirmHandle.setTdyw(invoiceInfoEntity.getTdyw());

                long lo = System.currentTimeMillis();
                String jsonString = JsonUtils.getInstance().toJsonString(redConfirmHandle);
                log.info("queryHzqrxxStatus 调用定时查询 确认单状态入参: {}", jsonString);
                String res = HttpUtils.doPost(queryHzqrxxStatusUrl, jsonString);
                log.info("queryHzqrxxStatus 结束调用定时查询 确认单状态 耗时: {}", System.currentTimeMillis() - lo);
                log.info("queryHzqrxxStatus 调用定时查询 确认单状态出参: {}", res);
                R r = JsonUtils.getInstance().fromJson(res, R.class);
                if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
                    String data = (String) r.get("data");
                    //
                    QueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().fromJson(data, QueryHzqrxxRes.class);
                    if (Objects.nonNull(queryHzqrxxRes)) {
                        RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.queryHzqrxxStatusByUuid(queryHzqrxxRes.getUuid());
                        if (Objects.nonNull(invoiceConfirmEntity)) {
                            // 更新确认单状态 zt
                            invoiceConfirmEntity.setZt(queryHzqrxxRes.getHzqrxxztDm());
                            invoiceConfirmEntity.setXfsytDm(queryHzqrxxRes.getXfsytDm());
                            invoiceConfirmEntity.setLzfppzDm(queryHzqrxxRes.getLzfppzDm());
                            invoiceConfirmEntity.setZzsytDm(queryHzqrxxRes.getZzsytDm());
                            invoiceConfirmEntity.setUpdateTime(new Date());
                            redInvoiceConfirmDao.updateById(invoiceConfirmEntity);

                            if (Objects.nonNull(invoiceInfoEntity)) {
                                invoiceInfoEntity.setXfsytzt(queryHzqrxxRes.getXfsytDm());
                                invoiceInfoEntity.setRzzt(queryHzqrxxRes.getFprzztDm());
                                invoiceInfoEntity.setZzsytzt(queryHzqrxxRes.getZzsytDm());
                                invoiceInfoEntity.setUpdateTime(new Date());
                                orderInvoiceInfoDao.updateById(invoiceInfoEntity);
                            }

                        }
                    }
                }
            }
        }
        return R.ok();
    }

    @Override
    public R getInvoiceInfosAndItemsResult(String param) {
        log.info("getInvoiceInfosAndItemsResult 定时任务入参：{}", param);
        //{"flag":"1","nsrsbh":"91441900797773851T","beginTime":"yyyy-MM-dd","endTime":"yyyy-MM-dd"}
        Map map = JsonUtils.getInstance().parseObject(param, HashMap.class);

        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();

        String flag = map.get("flag").toString();
        // flag等于1，则表示执行单个税号操作
        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
            log.info("getInvoiceInfosAndItemsResult 进入定时任务单条逻辑");
            String nsrsbh = map.get("nsrsbh").toString();
            String begin = map.get("beginTime").toString();
            String end = map.get("endTime").toString();
            String tenantCode = "";
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    if (nsrsbhTenantRelationEntity.getNsrsbh().equals(nsrsbh)) {
                        tenantCode = nsrsbhTenantRelationEntity.getTenantCode();
                        break;
                    }
                }
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("getInvoiceInfosAndItemsResult 切换业务数据源失败tenantCode: {}", tenantCode);
                }
                getAllInvoiceInfosAndItems(nsrsbh, flag, begin, end);

            } else {
                log.info("getInvoiceInfosAndItemsResult 销方税号租户关联表数据为空");
            }

        } else {
            log.info("getInvoiceInfosAndItemsResult 进入定时任务批量逻辑");
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                Set<String> tenantCodeList = new HashSet<>();
                // 拿到所有的税号
                Set<String> nsrsbhList = new HashSet<>();
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
                    nsrsbhList.add(nsrsbhTenantRelationEntity.getNsrsbh());
                }
                for (String tenantCode : tenantCodeList) {
                    String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                    if (!StringUtils.isEmpty(changeTenantResult)) {
                        log.info("getInvoiceInfosAndItemsResult 切换业务数据源失败tenantCode: {}", tenantCode);
                        continue;
                    }
                    for (String nsrsbh : nsrsbhList) {
//                      if (checkNsrsbh(nsrsbh, flag, null, null)) {
                        getAllInvoiceInfosAndItems(nsrsbh, flag, null, null);
                    }
                }
            } else {
                log.info("getInvoiceInfosAndItemsResult 销方税号租户关联表数据为空");
            }
        }

        return R.ok();
    }

    @Override
    public R pullRedInvoiceConfirmSldhResult() {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
            }
            for (String tenantCode : tenantCodeList) {
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("pullRedInvoiceConfirmSldhResult 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                pullRedInvoiceConfirmSldh();
            }
        } else {
            log.info("pullRedInvoiceConfirmSldhResult 销方税号租户关联表数据为空");
        }
        return R.ok();
    }

    @Override
    public R queryHzqrxxResult(String param) {
        log.info("queryHzqrxxResult 定时任务入参：{}", param);
        Map map = JsonUtils.getInstance().parseObject(param, HashMap.class);

        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        String flag = map.get("flag").toString();
        // flag等于1，则表示执行单个税号操作
        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
            log.info("queryHzqrxxResult 进入定时任务单条逻辑");
            String nsrsbh = map.get("nsrsbh").toString();
            String tenantCode = "";
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    if (nsrsbhTenantRelationEntity.getNsrsbh().equals(nsrsbh)) {
                        tenantCode = nsrsbhTenantRelationEntity.getTenantCode();
                        break;
                    }
                }
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("queryHzqrxxResult 切换业务数据源失败tenantCode: {}", tenantCode);
                }
                //if (checkHzqrxxNsrsbh(nsrsbh, map)) {
                queryHzqrxxInfo(nsrsbh, map);

            } else {
                log.info("queryHzqrxxResult 销方税号租户关联表数据为空");
            }

        } else {
            log.info("queryHzqrxxResult 进入定时任务批量逻辑");
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                Set<String> tenantCodeList = new HashSet<>();
                Set<String> nsrsbhList = new HashSet<>();
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
                    nsrsbhList.add(nsrsbhTenantRelationEntity.getNsrsbh());
                }
                for (String tenantCode : tenantCodeList) {
                    String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                    if (!StringUtils.isEmpty(changeTenantResult)) {
                        log.info("queryHzqrxxResult 切换业务数据源失败tenantCode: {}", tenantCode);
                        continue;
                    }
                    for (String nsrsbh : nsrsbhList) {
                        //if (checkHzqrxxNsrsbh(nsrsbh, map)) {
                        queryHzqrxxInfo(nsrsbh, map);
                    }
                }
            } else {
                log.info("queryHzqrxxResult 销方税号租户关联表数据为空");
            }
        }
        return R.ok();
    }

//    private boolean checkHzqrxxNsrsbh(String nsrsbh, Map map) {
//        String startTime = "";
//        String endTime = "";
//        String flag = map.get("flag").toString();
//        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
//            // 前一天数据
//            startTime = map.get("beginTime").toString();
//            endTime = map.get("endTime").toString();
//        } else {
//            // 前一天数据
//            LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minus(1, ChronoUnit.DAYS);
//            startTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//            LocalDateTime endLocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).minus(1, ChronoUnit.DAYS);
//            endTime = endLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        }
//
//        RedConfirmListReq redConfirmListReq = new RedConfirmListReq();
//        redConfirmListReq.setGxfxz("0");
//        redConfirmListReq.setHzqrxxztDm("01");
//        redConfirmListReq.setKprqq(startTime);
//        redConfirmListReq.setKprqz(endTime);
//        redConfirmListReq.setCurrent("1");
//        redConfirmListReq.setSize("10");
//        redConfirmListReq.setNsrsbh(nsrsbh);
//        long lo = System.currentTimeMillis();
//        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmListReq);
//        log.info("checkHzqrxxNsrsbh 调用我收到的确认单入参: {}", jsonString);
//        log.info("checkHzqrxxNsrsbh 开始调用我收到的确认单");
//        String res = HttpUtils.doPost(queryHzqrxxUrl, jsonString);
//        log.info("checkHzqrxxNsrsbh 结束调用我收到的确认单 耗时: {}", System.currentTimeMillis() - lo);
//        log.info("checkHzqrxxNsrsbh 调用我收到的确认单出参: {}", res);
//        R rl = JsonUtils.getInstance().fromJson(res, R.class);
//        if (Objects.nonNull(rl) && "0000".equals(rl.get("code"))) {
//            return true;
//        }
//        return false;
//    }

//    private boolean checkHzqrxxNsrsbhLanuch(String nsrsbh, Map map) {
//        String startTime = "";
//        String endTime = "";
//        String flag = map.get("flag").toString();
//        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
//            // 前一天数据
//            startTime = map.get("beginTime").toString();
//            endTime = map.get("endTime").toString();
//        } else {
//            // 前一天数据
//            LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minus(1, ChronoUnit.DAYS);
//            startTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//            LocalDateTime endLocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).minus(1, ChronoUnit.DAYS);
//            endTime = endLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        }
//        RedConfirmListReq redConfirmListReq = new RedConfirmListReq();
//        redConfirmListReq.setGxfxz("0");
//        redConfirmListReq.setHzqrxxztDm("01");
//        redConfirmListReq.setKprqq(startTime);
//        redConfirmListReq.setKprqz(endTime);
//        redConfirmListReq.setCurrent("1");
//        redConfirmListReq.setSize("10");
//        redConfirmListReq.setNsrsbh(nsrsbh);
//        long lo = System.currentTimeMillis();
//        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmListReq);
//        log.info("checkHzqrxxNsrsbhLanuch 我发起的确认单入参: {}", jsonString);
//        log.info("checkHzqrxxNsrsbh 开始调用我收到的确认单");
//        String res = HttpUtils.doPost(queryHzqrxxUrl, jsonString);
//        log.info("checkHzqrxxNsrsbh 结束调用我发起的确认单 耗时: {}", System.currentTimeMillis() - lo);
//        log.info("checkHzqrxxNsrsbh 结束调用我发起的确认单出参: {}", res);
//        R rl = JsonUtils.getInstance().fromJson(res, R.class);
//        if (Objects.nonNull(rl) && "0000".equals(rl.get("code"))) {
//            return true;
//        }
//        return false;
//    }

    @Override
    public R pullRedInvoiceResultInfoResult() {

        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
            }
            for (String tenantCode : tenantCodeList) {
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("pullRedInvoiceConfirmSldhResult 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                pullRedInvoiceResultInfo(tenantCode);
            }
        } else {
            log.info("pullRedInvoiceConfirmSldhResult 销方税号租户关联表数据为空");
        }
        return R.ok();
    }

    @Override
    public R queryHzqrxxStatusResult() {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
            }
            for (String tenantCode : tenantCodeList) {
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("pullRedInvoiceConfirmSldhResult 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                queryHzqrxxStatus();
            }
        } else {
            log.info("pullRedInvoiceConfirmSldhResult 销方税号租户关联表数据为空");
        }
        return R.ok();
    }

    @Override
    public R getRedInvoiceConfirmInfo(String id) {
        log.info("getRedInvoiceConfirmInfo 入口参数:{}", id);
        try {
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectById(id);
            if (Objects.isNull(redInvoiceConfirmEntity)) {
                return R.error("确认单信息不存在");
            }
            // 明细集合
            List<OrderInvoiceItemEntity> orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(redInvoiceConfirmEntity.getId());
            if (!CollectionUtils.isEmpty(orderInvoiceItemEntities)) {
                for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceItemEntities) {
                    String sl = orderInvoiceItemEntity.getSl();
                    if (!sl.contains("%")) {
                        sl = this.getPercent(Float.valueOf(sl), 0);
                        orderInvoiceItemEntity.setSl(sl);
                    }
                }
            }
            BigDecimal bigDecimalJe = BigDecimal.ZERO;
            if (!StringUtils.isEmpty(redInvoiceConfirmEntity.getFpje())) {
                bigDecimalJe = new BigDecimal(redInvoiceConfirmEntity.getFpje());
            }
            BigDecimal bigDecimalSe = BigDecimal.ZERO;
            if (!StringUtils.isEmpty(redInvoiceConfirmEntity.getFpse())) {
                bigDecimalSe = new BigDecimal(redInvoiceConfirmEntity.getFpse());
            }
            BigDecimal add = bigDecimalJe.add(bigDecimalSe);
            redInvoiceConfirmEntity.setJshj(add.toString());
            redInvoiceConfirmEntity.setInvoiceItemEntities(orderInvoiceItemEntities);
            // 附加信息集合
            List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(redInvoiceConfirmEntity.getId());
            redInvoiceConfirmEntity.setInvoiceAdditionInfoEntities(invoiceAdditionInfoEntities);
            return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, redInvoiceConfirmEntity);
        } catch (Exception e) {
            log.error("redInvoiceConfirmView 出现异常:{}", e);
            return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, "系统异常");
        }
    }

    @Override
    public R queryHzqrxxOutResult(String param) {
        log.info("queryHzqrxxOutResult 定时任务入参：{}", param);
        Map map = JsonUtils.getInstance().parseObject(param, HashMap.class);

        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        String flag = map.get("flag").toString();
        // flag等于1，则表示执行单个税号操作
        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
            log.info("queryHzqrxxOutResult 进入定时任务单条逻辑");
            String nsrsbh = map.get("nsrsbh").toString();
            String tenantCode = "";
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    if (nsrsbhTenantRelationEntity.getNsrsbh().equals(nsrsbh)) {
                        tenantCode = nsrsbhTenantRelationEntity.getTenantCode();
                        break;
                    }
                }
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("queryHzqrxxOutResult 切换业务数据源失败tenantCode: {}", tenantCode);
                }
                //if (checkHzqrxxNsrsbhLanuch(nsrsbh, map)) {
                queryHzqrxxOutDownResult(nsrsbh, map);

            } else {
                log.info("queryHzqrxxOutResult 销方税号租户关联表数据为空");
            }

        } else {
            log.info("queryHzqrxxOutResult 进入定时任务批量逻辑");
            // 查询tenant_code
            if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
                Set<String> tenantCodeList = new HashSet<>();
                Set<String> nsrsbhList = new HashSet<>();
                for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                    // 获取数据源
                    // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
                    nsrsbhList.add(nsrsbhTenantRelationEntity.getNsrsbh());
                }
                for (String tenantCode : tenantCodeList) {
                    String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                    if (!StringUtils.isEmpty(changeTenantResult)) {
                        log.info("queryHzqrxxOutResult 切换业务数据源失败tenantCode: {}", tenantCode);
                        continue;
                    }
                    for (String nsrsbh : nsrsbhList) {
                        //if (checkHzqrxxNsrsbhLanuch(nsrsbh, map)) {
                        queryHzqrxxOutDownResult(nsrsbh, map);
                    }
                }
            } else {
                log.info("queryHzqrxxOutResult 销方税号租户关联表数据为空");
            }
        }
        return R.ok();
    }

    public R getComfirmJgcxUUid() {
        // 查询自己开出的发票-----UUID为空的 并且 fpfqr为0的
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmDao.getComfirmJgcxUUid();
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
            for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : redInvoiceConfirmEntities) {
                if (!StringUtils.isEmpty(redInvoiceConfirmEntity.getHztzdbh())) {
                    // 拿着红字确认单编号 查询 匹配fpfqr为1的 数据 返回对应的uuid
                    RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.getComfirmJgcxUUidByHzqrdbh(redInvoiceConfirmEntity.getHztzdbh());
                    if (Objects.nonNull(invoiceConfirmEntity)) {
                        // 开始更新uuid
                        redInvoiceConfirmEntity.setUuid(invoiceConfirmEntity.getUuid());
                        redInvoiceConfirmEntity.setUpdateTime(new Date());
                        redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                    }
                }
            }
        }
        return R.ok();
    }

    @Override
    public R getComfirmSldStatusResult() {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
            }
            for (String tenantCode : tenantCodeList) {
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("getComfirmSldStatusResult 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                getComfirmSldStatus();
            }
        } else {
            log.info("getComfirmSldStatusResult 销方税号租户关联表数据为空");
        }
        return R.ok();
    }

    @Override
    public R getComfirmJgcxUUidResult() {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
            }
            for (String tenantCode : tenantCodeList) {
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("getComfirmJgcxUUidResult 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                getComfirmJgcxUUid();
            }
        } else {
            log.info("getComfirmJgcxUUidResult 销方税号租户关联表数据为空");
        }
        return R.ok();
    }

    @Override
    public R getOneYearInvoiceInfosAndItems(String nsrsbh, String startTime, String endTime) {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntityList = nsrsbhTenantRelationService.list();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntityList)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntityList) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                tenantCodeList.add(nsrsbhTenantRelationEntity.getTenantCode());
            }
            for (String tenantCode : tenantCodeList) {
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("getOneYearInvoiceInfosAndItems 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                getOneYearAllInvoiceInfosAndItems(nsrsbh, startTime, endTime);
            }
        } else {
            log.info("getOneYearInvoiceInfosAndItems 销方税号租户关联表数据为空");
        }
        return R.ok();
    }

    @Override
    public R retryRedInvoiceConfirmSldh(String id) {
        try {
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectById(id);
            if ("01".equals(redInvoiceConfirmEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("开票有误");
            } else if ("02".equals(redInvoiceConfirmEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("销货退回");
            } else if ("03".equals(redInvoiceConfirmEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("服务中止");
            } else if ("04".equals(redInvoiceConfirmEntity.getChyy())) {
                redInvoiceConfirmEntity.setChyy("销售折让");
            }
            // 蓝票
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
            // 受理中 集合
            log.info("retryRedInvoiceConfirmSldh，id：{}，开始重试查询受理中数据", id);
            RedConfirmReq redConfirmReq = new RedConfirmReq();
            redConfirmReq.setFplxdm(invoiceInfoEntity.getFpzlDm());
            redConfirmReq.setTdyw(invoiceInfoEntity.getTdyw());
            // 开票有误、销货退回、服务中止、销售折让
            redConfirmReq.setChyymc(redInvoiceConfirmEntity.getChyy());
            // 购销方选择 0 销售方，1 购买方
            redConfirmReq.setGxfxz(redInvoiceConfirmEntity.getGxsf());
            redConfirmReq.setLzqdfphm(invoiceInfoEntity.getQdfphm());
            redConfirmReq.setNsrsbh(redInvoiceConfirmEntity.getNsrsbh());
            String kpro = DateUtil.formatDate(invoiceInfoEntity.getKprq());
            redConfirmReq.setKprq(kpro);
            // 发起生成红字确认单请求
            long lo = System.currentTimeMillis();
            String jsonString = JsonUtils.getInstance().toJsonString(redConfirmReq);
            log.info("retryRedInvoiceConfirmSldh，id：{}，发起生成红字确认单请求接口入参: {}", id, jsonString);
            String res = HttpUtils.doPost(invoiceGenerateRedUrl, jsonString);
            log.info("retryRedInvoiceConfirmSldh，id：{}，结束调用发起生成红字确认单请求接口 耗时: {}", id, System.currentTimeMillis() - lo);
            log.info("retryRedInvoiceConfirmSldh，id：{}，发起生成红字确认单请求接口出参: {}", id, res);
            R rest = JsonUtils.getInstance().fromJson(res, R.class);
            if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                String datt = (String) rest.get("data");
                String code = (String) rest.get("code");
                String msg = (String) rest.get("msg");
                RedInvoiceConfirmSldh redInvoiceConfirmSldh = JsonUtils.getInstance().fromJson(datt, RedInvoiceConfirmSldh.class);
                if (Objects.nonNull(redInvoiceConfirmSldh)) {
                    if ("开票有误".equals(redInvoiceConfirmEntity.getChyy())) {
                        redInvoiceConfirmEntity.setChyy("01");
                    } else if ("销货退回".equals(redInvoiceConfirmEntity.getChyy())) {
                        redInvoiceConfirmEntity.setChyy("02");
                    } else if ("服务中止".equals(redInvoiceConfirmEntity.getChyy())) {
                        redInvoiceConfirmEntity.setChyy("03");
                    } else if ("销售折让".equals(redInvoiceConfirmEntity.getChyy())) {
                        redInvoiceConfirmEntity.setChyy("04");
                    }
                    redInvoiceConfirmEntity.setSldh(redInvoiceConfirmSldh.getSldh());
                    redInvoiceConfirmEntity.setZt("10");
                    redInvoiceConfirmEntity.setByzd2(code);
                    redInvoiceConfirmEntity.setByzd3(msg);
                    redInvoiceConfirmEntity.setUpdateTime(new Date());
                    redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                    log.info("retryRedInvoiceConfirmSldh，id：{}，重试生成红字确认单请求接口返回受理单号: {}", id, datt);
                }
            } /*else {
                log.error("retryRedInvoiceConfirmSldh 底层接口,受理单号重试失败");
                String code = (String) rest.get("code");
                String msg = (String) rest.get("msg");
                redInvoiceConfirmEntity.setByzd2(code);
                redInvoiceConfirmEntity.setByzd3(msg);
                redInvoiceConfirmEntity.setZt("11");
                redInvoiceConfirmEntity.setUpdateTime(new Date());
                redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
            }*/
        } catch (Exception e) {
            log.error("retryRedInvoiceConfirmSldh 异常:{}", e);
        }
        return R.ok();
    }

    public R getComfirmSldStatus() {
        log.info("getComfirmSldStatus 定时任务入口");
        // 查询我发出的 受理单
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmDao.getComfirmSldStatus();
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
            for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : redInvoiceConfirmEntities) {
                if (!StringUtils.isEmpty(redInvoiceConfirmEntity.getSldh()) && !StringUtils.isEmpty(redInvoiceConfirmEntity.getHztzdbh())) {
                    RedConfirmReq redConfirmReq = new RedConfirmReq();
                    redConfirmReq.setSldh(redInvoiceConfirmEntity.getSldh());
                    redConfirmReq.setHzxxbbh(redInvoiceConfirmEntity.getHztzdbh());
                    redConfirmReq.setNsrsbh(redInvoiceConfirmEntity.getNsrsbh());
                    //查询原蓝票信息
                    OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
                    if (Objects.nonNull(orderInvoiceInfoEntity)) {
                        redConfirmReq.setFplxdm(orderInvoiceInfoEntity.getFpzlDm());
                        redConfirmReq.setTdyw(orderInvoiceInfoEntity.getTdyw());
                    }
                    long lon = System.currentTimeMillis();
                    String invoiceInfoReString = JsonUtils.getInstance().toJsonString(redConfirmReq);
                    log.info("getComfirmSldStatus 调用定时更新确认单状态入参: {}", invoiceInfoReString);
                    String resString = HttpUtils.doPost(redInvoiceConfirmSldhUrl, invoiceInfoReString);
                    log.info("getComfirmSldStatus 结束调用定时更新确认单状态 耗时: {}", System.currentTimeMillis() - lon);
                    log.info("getComfirmSldStatus 调用定时更新确认单状态出参: {}", resString);
                    R rest = JsonUtils.getInstance().fromJson(resString, R.class);
                    if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                        String datt = (String) rest.get("data");
                        RedInvoiceConfirmSldhRes redInvoiceConfirmSldhRes = JsonUtils.getInstance().fromJson(datt, RedInvoiceConfirmSldhRes.class);
                        if (Objects.nonNull(redInvoiceConfirmSldhRes)) {
                            RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByQdfphm(redInvoiceConfirmSldhRes.getQdfphm());
                            if (Objects.nonNull(invoiceConfirmEntity) && !StringUtils.isEmpty(invoiceConfirmEntity.getHztzdbh())) {
                                String hzqrxxztDm = redInvoiceConfirmSldhRes.getHzqrxxztDm();
                                if ("01".equals(hzqrxxztDm) || "04".equals(hzqrxxztDm)) {
                                    invoiceConfirmEntity.setZtIng("1");
                                }
                                invoiceConfirmEntity.setZt(redInvoiceConfirmSldhRes.getHzqrxxztDm());
                                invoiceConfirmEntity.setUpdateTime(new Date());
                                redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                            }
                        }
                    }
                }
            }
        }

        return R.ok();
    }

    public R queryHzqrxxOutDownResult(String nsrsbh, Map map) {
        List<String> gxfxzs = new ArrayList<>();
        // 购销方选择
        // 销售方
        gxfxzs.add("0");
        // 购买方
        gxfxzs.add("1");
        for (String gxfxz : gxfxzs) {
            if ("0".equals(gxfxz)) {
                // 购销方选择为 销方   则录入方也为销方
                this.queryHzqrxxOut(gxfxz, "0", nsrsbh, map);
            } else if ("1".equals(gxfxz)) {
                // 购销方选择为 购方   则录入方也为购方
                this.queryHzqrxxOut(gxfxz, "1", nsrsbh, map);
            }

        }
        return R.ok();
    }

    public R queryHzqrxxOut(String gxfxz, String lrfsf, String nsrsbh, Map map) {
        List<String> hzqrxxztDms = new ArrayList<>();
//        hzqrxxztDms.add("01");
//        hzqrxxztDms.add("02");
//        hzqrxxztDms.add("03");
//        hzqrxxztDms.add("04");
//        hzqrxxztDms.add("05");
//        hzqrxxztDms.add("06");
//        hzqrxxztDms.add("07");
//        hzqrxxztDms.add("08");
//        hzqrxxztDms.add("09");
        hzqrxxztDms.add("10");//10 代表全部
        // 确认单状态：01无需确认 02销方录入待购 方确认 03购方录入待销方确认 04购销双方 已确认 05作废（销方录入购方否认） 06作废 （购方录入销方否认） 07作废（超72小时未确 认） 08作废（发起方撤销） 09作废（确认后撤销)
        for (String hzqrxxztDm : hzqrxxztDms) {
            getHzqrxxztDmOut(hzqrxxztDm, gxfxz, lrfsf, nsrsbh, map);
        }
        return R.ok();
    }

    private R getHzqrxxztDmOut(String hzqrxxztDm, String gxfxz, String lrfsf, String nsrsbh, Map map) {
        String startTime = "";
        String endTime = "";
        String flag = map.get("flag").toString();
        if (!StringUtils.isEmpty(flag) && "1".equals(flag)) {
            // 前一天数据
            startTime = map.get("beginTime").toString();
            endTime = map.get("endTime").toString();
        } else {
            // 前一天数据
            LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minus(1, ChronoUnit.DAYS);
            startTime = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            endTime = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
//            LocalDateTime endLocalDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).minus(1, ChronoUnit.DAYS);
//            endTime = endLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }


        // 查询总total
        RedConfirmListReq redConfirmListReq = new RedConfirmListReq();
        redConfirmListReq.setGxfxz(gxfxz);
        redConfirmListReq.setLrfsf(lrfsf);
        redConfirmListReq.setHzqrxxztDm(hzqrxxztDm);
        redConfirmListReq.setKprqq(startTime);
        redConfirmListReq.setKprqz(endTime);
        redConfirmListReq.setCurrent("1");
        redConfirmListReq.setSize("50");
        redConfirmListReq.setNsrsbh(nsrsbh);

        long lon = System.currentTimeMillis();
        String json = JsonUtils.getInstance().toJsonString(redConfirmListReq);
        log.info("getHzqrxxztDmOut，hzqrxxztDm：{}，调用查询total入参: {}", hzqrxxztDm, json);
        String resJson = HttpUtils.doPost(queryHzqrxxUrl, json);
        log.info("getHzqrxxztDmOut，hzqrxxztDm：{}，结束调用查询total 耗时: {}", hzqrxxztDm, System.currentTimeMillis() - lon);
        log.info("getHzqrxxztDmOut，hzqrxxztDm：{}，调用查询total出参: {}", hzqrxxztDm, resJson);
        R rt = JsonUtils.getInstance().fromJson(resJson, R.class);
        List<QueryHzqrxxRes> records = new ArrayList<>();
        int zhs = 0;
        if (Objects.nonNull(rt) && "0000".equals(rt.get("code"))) {
            String data = (String) rt.get("data");
            CommonQueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().fromJson(data, CommonQueryHzqrxxRes.class);
            if (Objects.nonNull(queryHzqrxxRes) && !CollectionUtils.isEmpty(queryHzqrxxRes.getRecords())) {
//                zhs = Integer.valueOf(queryHzqrxxRes.getPages());
                records.addAll(queryHzqrxxRes.getRecords());
            } else {
                return R.error();
            }
        } else {
            return R.error();
        }
        if (zhs > 1) {
            for (int a = 2; a <= zhs; a++) {
                redConfirmListReq.setCurrent(String.valueOf(a));
                long lo = System.currentTimeMillis();
                String jsonString = JsonUtils.getInstance().toJsonString(redConfirmListReq);
                log.info("getHzqrxxztDmOut，hzqrxxztDm：{}，循环调用我发起的确认单接口入参: {}", hzqrxxztDm, jsonString);
                String res = HttpUtils.doPost(queryHzqrxxUrl, jsonString);
                log.info("getHzqrxxztDmOut，hzqrxxztDm：{}，结束调用我发起的确认单的接口 耗时: {}", hzqrxxztDm, System.currentTimeMillis() - lo);
                log.info("getHzqrxxztDmOut，hzqrxxztDm：{}，调用我我发起的确认单接口出参: {}", hzqrxxztDm, jsonString);
                R r = JsonUtils.getInstance().fromJson(res, R.class);
                if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
                    String data = (String) r.get("data");
                    CommonQueryHzqrxxRes queryHzqrxxRes = JsonUtils.getInstance().fromJson(data, CommonQueryHzqrxxRes.class);
                    if (Objects.nonNull(queryHzqrxxRes) && !CollectionUtils.isEmpty(queryHzqrxxRes.getRecords())) {
                        records.addAll(queryHzqrxxRes.getRecords());
                    }
                }
            }
        }

        for (QueryHzqrxxRes hzqrxxRes : records) {
            // 我发出的确认单 用红字确认信息编号 和 fpfqr  0-我发起的  1-我收到的
            RedInvoiceConfirmEntity confirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByQrdbh(hzqrxxRes.getHzfpxxqrdbh(), "0");
            if (Objects.nonNull(confirmEntity)) {
                confirmEntity.setUuid(hzqrxxRes.getUuid());
                confirmEntity.setUpdateTime(new Date());
                confirmEntity.setZt(hzqrxxRes.getHzqrxxztDm());
                redInvoiceConfirmDao.updateById(confirmEntity);

            }
        }
        updateInvoiceInfoByFphm(records);

        return R.ok();
    }

    private void updateInvoiceInfoByFphm(List<QueryHzqrxxRes> records) {
        //对返回报文进行分组
        Map<String, List<QueryHzqrxxRes>> listMap = records.stream().collect(Collectors.groupingBy(QueryHzqrxxRes::getLzfphm));
        for (String s : listMap.keySet()) {
            List<String> qrdDmList = listMap.get(s).stream().map(QueryHzqrxxRes::getHzqrxxztDm).distinct().collect(Collectors.toList());
            List<String> sfkpList = listMap.get(s).stream().map(QueryHzqrxxRes::getYkjhzfpbz).distinct().collect(Collectors.toList());
            //有01-04 并且是未开票   ch_bz更新为 5  冲红中
            log.info("发票号码:{},对应的确认单状态:{} 确认单对应的开票状态{}", s, qrdDmList.toString(), sfkpList.toString());
            if (qrdDmList.toString().contains("01") || qrdDmList.toString().contains("02") || qrdDmList.toString().contains("03") || qrdDmList.toString().contains("04")) {
                if (sfkpList.toString().contains("N")) {
                    List<OrderInvoiceInfoEntity> invoiceInfoByQdfphm = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(s);
                    if (invoiceInfoByQdfphm.size() > 0) {
                        //更新蓝字发票 ch_bz 为5   冲红中
                        invoiceInfoByQdfphm.forEach(x -> {
                            log.info("蓝票发票号码{}需要更新冲红标志为5 冲红中", s);
                            x.setChBz("5");
                            orderInvoiceInfoDao.updateById(x);
                        });
                    }
                }
            } else {
                //ch_bz更新为 0
                List<OrderInvoiceInfoEntity> invoiceInfoByQdfphm = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(s);
                if (invoiceInfoByQdfphm.size() > 0) {
                    //更新蓝字发票 ch_bz 为0
                    invoiceInfoByQdfphm.forEach(x -> {
                        log.info("蓝票发票号码{}需要更新冲红标志为0 正常状态", s);
                        x.setChBz("0");
                        orderInvoiceInfoDao.updateById(x);
                    });
                }
            }
        }
    }

    private String slTranst(String sl) {
        Float f = 0.00f;
        if (!StringUtils.isEmpty(sl)) {
            if (sl.contains("%")) {
                sl = sl.replace("%", "");
                f = Float.valueOf(sl) / 100;
            } else if ("免税".equals(sl) || "不征税".equals(sl)) {
                return sl;
            } else {
                return Float.valueOf(sl).toString();
            }
        }
        return f.toString();
    }

    // 小数转百分数
    private String getPercent(double data, int digit) {
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMinimumFractionDigits(digit);
        return numberFormat.format(data);
    }

    private Date transDate(String kprqStr) {
        // String time = "2020-02-13 16:01:30";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(kprqStr);
        } catch (ParseException e) {
            log.error("日期转换失败");
        }
        return date;
    }

    // 不足两位小数补0
    private String formatDecimal(BigDecimal bc) {
        // 不足两位小数补0
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(bc);
    }
}
