package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.dto.InvoiceRecordSumNumberDTO;
import com.dxhy.order.modules.pojo.dto.NoOrderInvoiceInfoListDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 订单和发票关系表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
@Mapper
public interface OrderInvoiceInfoDao extends BaseMapper<OrderInvoiceInfoEntity> {

    List<OrderInvoiceInfoEntity> selectList1(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    List<OrderInvoiceInfoEntity> selectAllList(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);


    int deleteByInvoiceId(@Param("id") String id);

    int updateInvoiceStatusById(@Param("id") String id);

    /**
     * 开票记录 - 查询列表
     *
     * @param page
     * @param invoiceRecordQueryList
     * @return
     */
    List<InvoiceRecordInfoEntity> selectInvoiceRecordList(Page page, @Param("invoiceRecordQueryList") InvoiceRecordQueryList invoiceRecordQueryList);

    /**
     * 本月已开张数
     */
    List<FirstPagePortEntity> selectMonthDataYkzs(@Param("nsrsbh") String nsrsbh, @Param("month") String month);

    /**
     * 本月已开金额
     */
    List<FirstPagePortEntity> selectMonthDataYkje(@Param("nsrsbh") String nsrsbh, @Param("month") String month);

    /**
     * 本月红冲金额
     */
    List<FirstPagePortEntity> selectMonthDataHcje(@Param("nsrsbh") String nsrsbh, @Param("month") String month);

    /**
     * 本月红冲张数
     */
    List<FirstPagePortEntity> selectMonthDataHczs(@Param("nsrsbh") String nsrsbh, @Param("month") String month);

    /**
     * 累计开票金额 - 年
     */
    List<FirstPagePortEntity> selectTotalDataLjkjje(@Param("nsrsbh") String nsrsbh, @Param("year") String year);

    /**
     * 累计开具税额 - 年
     */
    List<FirstPagePortEntity> selectTotalDataLjkjse(@Param("nsrsbh") String nsrsbh, @Param("year") String year);

    /**
     * 累计开具数量 - 年
     */
    List<FirstPagePortEntity> selectTotalDatalLjksl(@Param("nsrsbh") String nsrsbh, @Param("year") String year);

    /**
     * 季度开具额度 - 季度
     */
    List<FirstPagePortEntity> selectTotalDataJdkjed(@Param("nsrsbh") String nsrsbh, @Param("begin") Date begin, @Param("end") Date end);


    /**
     * 全电概览 - 已开发票 - 查询
     *
     * @param page
     * @param orderInvoiceInfoEntity
     * @return
     */
    List<OrderInvoiceInfoEntity> selectInvoicedList(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity, @Param("month") String month);

    /**
     * 数据统计 - 按项目查询
     *
     * @param baseNsrsbh
     * @param beginTime
     * @param endTime
     * @return
     */
    List<DataStaticsQueryResultList> selectXMStaisticList(@Param("baseNsrsbh") String baseNsrsbh, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("fpzldm") String fpzldm);

    /**
     * 数据统计 - 按种类查询
     *
     * @param baseNsrsbh
     * @param beginTime
     * @param endTime
     * @return
     */
    List<DataStaticsQueryResultList> selectZLStaisticList(@Param("baseNsrsbh") String baseNsrsbh, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime, @Param("fpzldm") String fpzldm);

    /**
     * 外部接口查询开票结果
     *
     * @param ddqqlsh
     * @return
     */
    OrderInvoiceInfoEntity queryStatusByDdqqlsh(@Param("ddqqlsh") String ddqqlsh);

    List<OrderInvoiceInfoEntity> selectTaskInvoiceInfo();

    /**
     * PDF对应mongodb_id查询，暂临时版本：用fphm查询
     */
    OrderInvoiceInfoEntity selectMongodbIdByFphm(@Param("fphm") String nsrsbh);

    List<OrderInvoiceInfoEntity> selectRedInvoiceConfirm(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    OrderInvoiceInfoEntity seleInvoiceInfoByQdfphm(@Param("qdfphm") String qdfphm);

    OrderInvoiceInfoEntity seleInvoiceRedInfoByQdfphm(@Param("qdfphm") String qdfphm);

    OrderInvoiceInfoEntity seleInvoiceInfoByYqdfphm(@Param("yqdfpdm") String yqdfpdm);

    List<OrderInvoiceInfoEntity> selectListByInfo(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    List<OrderInvoiceInfoEntity> selectIHadListByNsrsbh(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    OrderInvoiceInfoEntity seleInvoiceInfoByQdfphmIsDele(@Param("qdfphm") String qdfphm);

    List<OrderInvoiceInfoEntity> getInvoiceInfoByQdfphm(@Param("qdfphm") String qdfphm);

    OrderInvoiceInfoEntity queryStatusByDdh(@Param("ddqqlsh") String fpqqlsh, @Param("baseNsrsbh") String baseNsrsbh);

    int queryStatusByDdhCount(@Param("ddqqlsh") String fpqqlsh, @Param("baseNsrsbh") String baseNsrsbh);

    OrderInvoiceInfoEntity queryStatusByDdqqlshAndNsrsbh(@Param("ddqqlsh") String ddqqlsh, @Param("nsrsbh") String nsrsbh);

    /**
     * 红票记录-我收到的
     *
     * @param page
     * @param orderInvoiceInfoEntity
     * @return
     */
    List<OrderInvoiceInfoEntity> selectReceiveRedList(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    void updateKpztById(@Param("id") String id, @Param("baseNsrsbh") String baseNsrsbh);

    List<OrderInvoiceInfoEntity> selectKpztIsDkList(@Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    List<String> manualInvoiceInfo();

    List<OrderInvoiceInfoEntity> selectDdhListByYsdh(@Param("ysdh") String ysdh, @Param("xhfNsrsbh") String xhfNsrsbh);

    List<OrderInvoiceInfoEntity> selectListByOrderInvoiceInfoEntity(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    int updateKpztNoNeedToWaitByids(@Param("id") String id, @Param("baseNsrsbh") String baseNsrsbh);

    List<NoOrderInvoiceInfoListDTO> selectNoNeedList(Page page, @Param("noOrderInvoiceInfoListDTO") NoOrderInvoiceInfoListDTO noOrderInvoiceInfoListDTO);

    NoOrderInvoiceInfoListDTO selectNoNeedInfoByid(@Param("id") String id, @Param("baseNsrsbh") String baseNsrsbh);

    List<OrderInvoiceInfoEntity> getOrderInvoiceInfoByKPZT(Page page, @Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    List<OrderInvoiceInfoEntity> getOrderInvoiceInfoAllByKPZT(@Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    void updateToWaitByid(@Param("id") String id,@Param("baseNsrsbh") String baseNsrsbh, @Param("time") Date time);

    InvoiceRecordSumNumberDTO countSLbyNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    void updateBzById(@Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    OrderInvoiceInfoEntity queryStatusByYsdh(@Param("ysdh") String ysdh, @Param("xhfNsrsbh") String xhfNsrsbh);

    int updateEmailByqdhm(@Param("email") String email, @Param("qdfphm") String qdfphm);

    int updatePhoneByqdhm(@Param("phone") String phone, @Param("qdfphm") String qdfphm);

    void updateYxJfzt(@Param("id") String id);

    void updateSjJfzt(@Param("id") String id);

    List<String> selectAllListByDuplicate(Page page,@Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    void updateOrderInvoiceInfo(@Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity infoEntity);

    OrderInvoiceInfoEntity queryInvoiceInfoByDdh(@Param("djbh") String djbh,@Param("baseNsrsbh") String baseNsrsbh);

    List<OrderInvoiceInfoEntity> selectDdhListByYysdh(@Param("djbh") String djbh,@Param("baseNsrsbh") String baseNsrsbh);

    List<OrderInvoiceInfoEntity> queryDdhListByYsdh(@Param("ysdh")String ysdh,@Param("xhfNsrsbh") String xhfNsrsbh);

    /**
     * 开票记录通过id查询订单详情（包含对应红票字段）
     */
    OrderInvoiceInfoEntity selectRecordDetailByid(@Param("id") String id);

    List<OrderInvoiceInfoEntity> selectDdhListByYsdhIsDelete(@Param("ysdh") String djbh,@Param("xhfNsrsbh") String baseNsrsbh);

    /**
     * 通过开票状态和应收单号获取订单号列表
     */
    List<OrderInvoiceInfoEntity> selectDdhListByYsdhAndKpzt(@Param("ysdh") String ysdh, @Param("xhfNsrsbh") String xhfNsrsbh, @Param("kpzt") String kpzt);

    /**
     * 开票记录查应收单列表
     */
    List<String> selectRecordListByDuplicate(Page page,@Param("orderInvoiceInfoEntity") OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    List<OrderInvoiceInfoEntity> selectAllListByEntity(Page page, @Param("orderInvoiceInfoEntity")OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    List<OrderInvoiceInfoEntity> selectAllListDD(Page page, @Param("orderInvoiceInfoEntity")OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 开票记录通过开票状态和应收单号获取订单号列表
     */
    List<OrderInvoiceInfoEntity> selectRecordDdhListByYsdhAndKpzt(@Param("ysdh") String ysdh, @Param("xhfNsrsbh") String xhfNsrsbh, @Param("kpzt") String kpzt);

    /**
     * 通过全电发票号码找到被逻辑删除的慧企订单，解绑手工开票
     */
    OrderInvoiceInfoEntity seleInvoiceInfoByQdfphmIsDelete(@Param("qdfphm") String qdfphm);
}
