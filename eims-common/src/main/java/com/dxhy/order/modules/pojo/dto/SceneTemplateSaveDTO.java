package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 保存 场景模板DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("保存 场景模板DTO")
public class SceneTemplateSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 场景模板主键（此参数为空时表示新增 不为空时表示修改）
     */
    @ApiModelProperty("场景模板主键（此参数为空时表示新增 不为空时表示修改）")
    private String id;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 场景模板名称
     */
    @ApiModelProperty(name = "场景模板名称", required = true)
    private String name;

    /**
     * 附加信息ID 数组
     **/
    @ApiModelProperty(name = "附加信息ID 数组")
    private List<String> additionElementIdList;

    /**
     * uuid 第三方慧企返回uuid
     */
    @ApiModelProperty("uuid")
    private String uuid;
}
