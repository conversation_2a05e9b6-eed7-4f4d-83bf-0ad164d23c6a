package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.dao.AdditionElementDao;
import com.dxhy.order.modules.entity.AdditionElementEntity;
import com.dxhy.order.modules.entity.AdditionElementEntityRes;
import com.dxhy.order.modules.entity.AdditionElementSaveDTO;
import com.dxhy.order.modules.entity.AdditionElemrntQueryRes;
import com.dxhy.order.modules.pojo.dto.AdditionElementListDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.vo.AdditionElementVO;
import com.dxhy.order.modules.service.AdditionElementService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * 附加信息表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/28 16:26
 * @Version 1.0
 **/
@Service("additionElementService")
@Slf4j
@RefreshScope
public class AdditionElementServiceImpl extends ServiceImpl<AdditionElementDao, AdditionElementEntity> implements AdditionElementService {

    private static final String LOGGER = "(附加信息)";

    @Value("${order.encrypt.fjxxcxUrl}")
    private String fjxxcxUrl;
    @Value("${order.encrypt.fjxxxzUrl}")
    private String fjxxxzUrl;
    @Value("${order.encrypt.fjxxxgUrl}")
    private String fjxxxgUrl;
    @Value("${order.encrypt.fjxxscUrl}")
    private String fjxxscUrl;

    @Autowired
    private AdditionElementDao additionElementDao;
    @Resource
    private SsoUtil ssoUtil;

    @Override
    public PageUtils queryPage(AdditionElementListDTO additionElementListDTO) {
        Page page = new Page(additionElementListDTO.getCurrPage(), additionElementListDTO.getPageSize());
        List<AdditionElementEntity> list = additionElementDao.selectList(page, additionElementListDTO);
        List<AdditionElementVO> voList = new LinkedList<>();
        for (AdditionElementEntity tempData : list) {
            voList.add(new AdditionElementVO(tempData));
        }
        page.setRecords(voList);
        return new PageUtils(page);
    }

    @Override
    public List<AdditionElementVO> listAll(String nsrsbh) {
        List<AdditionElementEntity> list = additionElementDao.selectListByNsrsbh(nsrsbh);
        List<AdditionElementVO> voList = new LinkedList<>();
        for (AdditionElementEntity tempData : list) {
            voList.add(new AdditionElementVO(tempData));
        }
        return voList;
    }

    @Override
    public R listAdditionElementBySceneTemplateId(String id) {
        List<AdditionElementVO> voList = additionElementDao.listAdditionElementBySceneTemplateId(id);
        return R.ok().put("data", voList);
    }

    @Override
    public R queryDataById(String id) {
        if(StringUtils.isEmpty(id)){
            return R.error("数据不存在");
        }
        AdditionElementEntity additionElement = additionElementDao.queryDataById(id);
        AdditionElementVO voData = new AdditionElementVO(additionElement);
        return R.ok().put("data", voData);
    }

    @Override
    public R saveData(AdditionElementSaveDTO additionElementSaveDTO) {
        String checkStr = this.checkAdditionElementSaveDTO(additionElementSaveDTO);
        if(StringUtils.isNotEmpty(checkStr)){
            return R.error(checkStr);
        }
        if(StringUtils.isNotEmpty(additionElementSaveDTO.getId())){
            // 修改


            AdditionElementEntity additionElementEntity = additionElementDao.queryDataById(additionElementSaveDTO.getId());
            additionElementEntity.setFjxxmc(additionElementSaveDTO.getFjxxmc());
            additionElementEntity.setSjlx(additionElementSaveDTO.getSjlx());
            additionElementEntity.setUpdateTime(new Date());
            additionElementEntity.setUpdateBy(ssoUtil.getUserName());
            additionElementDao.updateById(additionElementEntity);
            log.info("{}，附加要素修改成功", LOGGER);
            return R.ok();
//            // 调用慧企接口后再更新
//            long begin = System.currentTimeMillis();
//            String reqString = JsonUtils.getInstance().toJsonString(additionElementSaveDTO);
//            log.info("{}，调用附加要素修改接口入参: {}", LOGGER, reqString);
//            String respString = HttpUtils.doPost(fjxxxgUrl, reqString);
//            log.info("{}，结束调用附加要素新增接口， 耗时: {}", System.currentTimeMillis() - begin);
//            log.info("{}，调用附加要素修改接口出参: {}", LOGGER, respString);
//            R res = JsonUtils.getInstance().parseObject(respString, R.class);
//            if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
//                AdditionElementEntity updateData = additionElementDao.queryDataById(additionElementSaveDTO.getId());
//                if(updateData == null){
//                    return R.error("数据不存在");
//                }
//                updateData.setFjxxmc(additionElementSaveDTO.getFjxxmc());
//                updateData.setSjlx(additionElementSaveDTO.getSjlx());
//                updateData.setUpdateTime(new Date());
//                updateData.setUpdateBy(ssoUtil.getUserName());
//                additionElementDao.updateById(updateData);
//                log.info("{}，附加要素修改成功", LOGGER);
//                return R.ok();
//            }
//            log.info("{}，附加要素修改失败", LOGGER);
//            return R.error(res.get(OrderManagementConstant.CODE).toString(), res.get(OrderManagementConstant.MESSAGE).toString());
        }else{
            // 新增
            AdditionElementEntity saveData = new AdditionElementEntity();
            saveData.setUuid(DistributedKeyMaker.generateShotKey());
            saveData.setId(DistributedKeyMaker.generateShotKey());
            saveData.setBaseNsrsbh(additionElementSaveDTO.getBaseNsrsbh());
            saveData.setFjxxmc(additionElementSaveDTO.getFjxxmc());
            saveData.setSjlx(additionElementSaveDTO.getSjlx());
            saveData.setSrfs("1");
            saveData.setYyzt("0");
            saveData.setIsDelete("0");
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(ssoUtil.getUserName());
            additionElementDao.insert(saveData);
            log.info("{}，附加要素新增入库成功", LOGGER);
            return R.ok().put("data", saveData.getId());
            // 调用慧企接口后再入库
//            long begin = System.currentTimeMillis();
//            String reqString = JsonUtils.getInstance().toJsonString(additionElementSaveDTO);
//            log.info("{}，调用附加要素新增接口入参: {}", LOGGER, reqString);
//            String respString = HttpUtils.doPost(fjxxxzUrl, reqString);
//            log.info("{}，结束调用附加要素新增接口， 耗时: {}", System.currentTimeMillis() - begin);
//            log.info("{}，调用附加要素新增接口出参: {}", LOGGER, respString);
//            R res = JsonUtils.getInstance().parseObject(respString, R.class);
//            if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
//                AdditionElementEntity saveData = new AdditionElementEntity();
//                Map map = JsonUtils.getInstance().parseObject(res.get("data").toString(), HashMap.class);
//                saveData.setUuid(map.get("uuid").toString());
//                saveData.setId(DistributedKeyMaker.generateShotKey());
//                saveData.setBaseNsrsbh(additionElementSaveDTO.getBaseNsrsbh());
//                saveData.setFjxxmc(additionElementSaveDTO.getFjxxmc());
//                saveData.setSjlx(additionElementSaveDTO.getSjlx());
//                saveData.setSrfs("1");
//                saveData.setYyzt("0");
//                saveData.setIsDelete("0");
//                saveData.setCreateTime(new Date());
//                saveData.setCreateBy(ssoUtil.getUserName());
//                additionElementDao.insert(saveData);
//                log.info("{}，附加要素新增入库成功", LOGGER);
//                return R.ok().put("data", saveData.getId());
        }
//            log.info("{}，附加要素新增入库失败", LOGGER);
//            return R.error(res.get(OrderManagementConstant.CODE).toString(), res.get(OrderManagementConstant.MESSAGE).toString());

    }

    private String checkAdditionElementSaveDTO(AdditionElementSaveDTO additionElementSaveDTO){
        if(additionElementSaveDTO == null){
            return "入参为空";
        }
        if(StringUtils.isEmpty(additionElementSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isEmpty(additionElementSaveDTO.getFjxxmc())){
            return "附加信息名称为空";
        }
        if(StringUtils.isEmpty(additionElementSaveDTO.getSjlx())){
            return "数据类型为空";
        }
        String sjlx = additionElementSaveDTO.getSjlx();
        if(sjlx.length() > 1 || (!"123".contains(sjlx))){
            return "数据类型格式有误";
        }
        List<AdditionElementEntity> additionElementEntityList = additionElementDao.selectListByNsrsbhAndName(additionElementSaveDTO.getBaseNsrsbh(), additionElementSaveDTO.getFjxxmc());
        if(CollectionUtils.isNotEmpty(additionElementEntityList)){
            if(additionElementEntityList.size() > 1){
                return "附加信息名称重复";
            }
            if(StringUtils.isEmpty(additionElementSaveDTO.getId())){
                return "附加信息名称重复.";
            }
            if(!additionElementSaveDTO.getId().equals(additionElementEntityList.get(0).getId())){
                return "附加信息名称重复..";
            }
        }
        return "";
    }

    @Override
    public R deleteData(IdsDTO idsDTO) {
        if(idsDTO == null || CollectionUtils.isEmpty(idsDTO.getIds())){
            return R.error("请选择要操作的数据");
        }
        if(StringUtils.isEmpty(idsDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        Integer yyztTureData = additionElementDao.countYyztTureDataByIdList(idsDTO.getIds());
        if(yyztTureData > 0){
            return R.error("被引用的数据不允许删除，请重新选择");
        }
        additionElementDao.deleteByIdList(idsDTO.getIds());
        return R.ok();




//        AdditionElementSaveDTO additionElementSaveDTO = new AdditionElementSaveDTO();
//        List<String> list = new ArrayList();
//        for (String id : idsDTO.getIds()) {
//            AdditionElementEntity additionElementEntity = additionElementDao.queryDataById(id);
//            list.add(additionElementEntity.getUuid());
//        }
//        additionElementSaveDTO.setBaseNsrsbh(idsDTO.getBaseNsrsbh());
//        additionElementSaveDTO.setUuidList(list);
//        long begin = System.currentTimeMillis();
//        String reqString = JsonUtils.getInstance().toJsonString(additionElementSaveDTO);
//        log.info("{}，调用附加要素删除接口入参: {}", LOGGER, reqString);
//        String respString = HttpUtils.doPost(fjxxscUrl, reqString);
//        log.info("{}，结束调用附加要素删除接口， 耗时: {}", System.currentTimeMillis() - begin);
//        log.info("{}，调用附加要素删除接口出参: {}", LOGGER, respString);
//        R res = JsonUtils.getInstance().parseObject(respString, R.class);
//        if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
//            additionElementDao.deleteByIdList(idsDTO.getIds());
//            log.info("{}，附加要素删除成功", LOGGER);
//            return R.ok();
//        }
//        log.info("{}，附加要素删除失败", LOGGER);
//        return R.error(res.get(OrderManagementConstant.CODE).toString(), res.get(OrderManagementConstant.MESSAGE).toString());
    }

    @Override
    public void refreshYyzt() {
        additionElementDao.refreshYyzt_0();
        additionElementDao.refreshYyzt_1();
    }

    /**
     * 查询附加信息并更新到数据库
     * @return
     */
    @Override
    public R getAdditionElementTask(String baseNsrsbh) {
        log.info("{}，调用附加要素查询接口入参: {}", LOGGER, baseNsrsbh);
        // 查询次数计算
        int times = 0;
        R res = sendFjysQuery("10", "1", "", baseNsrsbh);
        if ("0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
            AdditionElemrntQueryRes additionElemrntQueryRes = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(res.get("data")), AdditionElemrntQueryRes.class);
            times = Integer.valueOf(additionElemrntQueryRes.getPages());
        }
        log.info("{}，调用附加要素查询接口次数: {}", LOGGER, times);

        // 查询所有数据，更新或入库
        for (int i = 1; i <= times; i++) {
            log.info("{}，调用附加要素查询接口第{}次", LOGGER, i);
            sendFjysQuery("10", String.valueOf(i), "", baseNsrsbh);
        }
        log.info("{}，调用附加要素查询接口执行完成", LOGGER);
        return R.ok();
    }

    /**
     * 调用附加要素查询接口
     * @param size
     * @param current
     * @param fjxxmc
     * @return
     */
    public R sendFjysQuery(String size, String current, String fjxxmc, String nsrsbh) {
        AdditionElementSaveDTO additionElementSaveDTO = new AdditionElementSaveDTO();
        additionElementSaveDTO.setBaseNsrsbh(nsrsbh);
        additionElementSaveDTO.setCurrent(current);
        additionElementSaveDTO.setSize(size);
        additionElementSaveDTO.setFjxxmc(fjxxmc);
        long begin = System.currentTimeMillis();
        String reqString = JsonUtils.getInstance().toJsonString(additionElementSaveDTO);
        log.info("{}，调用附加要素查询接口入参: {}", LOGGER, reqString);
        String respString = HttpUtils.doPost(fjxxcxUrl, reqString);
        log.info("{}，结束调用附加要素查询接口， 耗时: {}", System.currentTimeMillis() - begin);
        log.info("{}，调用附加要素查询接口出参: {}", LOGGER, respString);
        R res = JsonUtils.getInstance().parseObject(respString, R.class);
        log.info("{}，调用附加要素查询方法返回: {}", LOGGER, res);
        AdditionElemrntQueryRes additionElemrntQueryRes = new AdditionElemrntQueryRes();
        if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
            additionElemrntQueryRes = JsonUtils.getInstance().parseObject(res.get("data").toString(), AdditionElemrntQueryRes.class);
            if (ObjectUtils.isNotEmpty(additionElemrntQueryRes) && ObjectUtils.isNotEmpty(additionElemrntQueryRes.getRecords())) {
                for (AdditionElementEntityRes element : additionElemrntQueryRes.getRecords()) {
                    AdditionElementEntity additionElementEntity = additionElementDao.queryDataByUUID(element.getUuid());

                    AdditionElementEntity additionElementEntity1 = new AdditionElementEntity();
                    additionElementEntity1.setUuid(element.getUuid());
                    additionElementEntity1.setBaseNsrsbh(element.getNsrsbh());
                    additionElementEntity1.setNsrmc(element.getNsrmc());
                    additionElementEntity1.setFjxxmc(element.getFjysxmmc());
                    if("1".equals(element.getSjlx1())){
                        additionElementEntity1.setSjlx("string");
                    }
                    if("2".equals(element.getSjlx1())){
                        additionElementEntity1.setSjlx("number");
                    }
                    if("3".equals(element.getSjlx1())){
                        additionElementEntity1.setSjlx("date");
                    }
                    additionElementEntity1.setYxbz(element.getYxbz());
                    try{
                        additionElementEntity1.setCreateTime(new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M_DH_M_S).parse(element.getLrrq()));
                        additionElementEntity1.setUpdateTime(new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M_DH_M_S).parse(element.getXgrq()));
                    }  catch (Exception e) {
                        log.error("{}，调用附加要素处理日期失败", LOGGER);
                    }
                    additionElementEntity1.setCreateBy(element.getLrrmc());
                    additionElementEntity1.setUpdateBy(element.getXgrmc());
                    additionElementEntity1.setYyzt(element.getYyzt());


                    // 更新
                    if (ObjectUtils.isNotEmpty(additionElementEntity)) {
                        additionElementEntity1.setId(additionElementEntity.getId());
                        int cnt = additionElementDao.updateById(additionElementEntity1);
                        if (cnt < 1) {
                            log.info("{}，调用附加要素查询接口更新失败", LOGGER);
                        } else {
                            log.info("{}，调用附加要素查询接口更新成功", LOGGER);
                        }
                    } else {
                        // 新增
                        additionElementEntity1.setIsDelete("0");
                        additionElementEntity1.setId(DistributedKeyMaker.generateShotKey());
                        int cnt = additionElementDao.insert(additionElementEntity1);
                        if (cnt < 1) {
                            log.info("{}，调用附加要素查询接口新增失败", LOGGER);
                        } else {
                            log.info("{}，调用附加要素查询接口新增成功", LOGGER);
                        }
                    }
                }
            }
            log.info("{}，调用附加要素查询接口执行成功", LOGGER);
            return R.ok().put("data", additionElemrntQueryRes);
        } else {
            log.info("{}，调用附加要素查询接口执行失败", LOGGER);
            return R.error();
        }
    }

}
