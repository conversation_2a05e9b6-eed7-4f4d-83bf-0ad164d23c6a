package com.dxhy.order.modules.service.impl;


import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.RespStatusEnum;
import com.dxhy.order.modules.service.CommonInterfaceService;
import com.dxhy.order.modules.service.OpenApiService;
import com.dxhy.order.pojo.ResponseStatus;
import com.dxhy.order.pojo.Result;
import com.dxhy.order.utils.Base64Encoding;
import com.dxhy.order.utils.GzipUtils;
import com.dxhy.order.utils.HmacSha1Util;
import com.dxhy.order.utils.TripleDesUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 订单对外接口业务实现类
 *
 * @author:
 * @date:
 */
@Service
@Slf4j
public class CommonInterfaceServiceImpl implements CommonInterfaceService {
    
    
    private static final String LOGGER_MSG = "(订单对外接口通用业务类)";

    @Resource
    private OpenApiService openApiService;
    
    
    /**
     * 校验接口入参数据,非空和数据校验
     *
     * @param interfaceVersion
     * @param interfaceName
     * @param timestamp
     * @param nonce
     * @param secretId
     * @param signature
     * @param encryptCode
     * @param zipCode
     * @param content
     * @return
     */
    @Override
    public Result checkInterfaceParam(String interfaceVersion, String interfaceName, String timestamp, String nonce, String secretId, String signature, String encryptCode, String zipCode, String content) {
        
        log.info("{},数据校验,请求的interfaceVersion:{},interfaceName:{},timestamp:{},nonce:{},secretId:{},signature:{},encryptCode:{},zipCode:{},content:{}", LOGGER_MSG, interfaceVersion, interfaceName, timestamp, nonce, secretId, signature, encryptCode, zipCode, content);
        
        if (StringUtils.isBlank(interfaceName)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_INTERFACENAME_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_INTERFACENAME_NULL.getCode(), RespStatusEnum.CHECK_INTERFACENAME_NULL.getDescribe()));
        } else if (StringUtils.isBlank(timestamp)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_TIMESTAMP_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_TIMESTAMP_NULL.getCode(), RespStatusEnum.CHECK_TIMESTAMP_NULL.getDescribe()));
        } else if (StringUtils.isBlank(nonce)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_NONCE_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_NONCE_NULL.getCode(), RespStatusEnum.CHECK_NONCE_NULL.getDescribe()));
        } else if (StringUtils.isBlank(secretId)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_SECRETID_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_SECRETID_NULL.getCode(), RespStatusEnum.CHECK_SECRETID_NULL.getDescribe()));
        } else if (StringUtils.isBlank(signature)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_SIGNATURE_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_SIGNATURE_NULL.getCode(), RespStatusEnum.CHECK_SIGNATURE_NULL.getDescribe()));
        } else if (StringUtils.isBlank(encryptCode)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_ENCRYPTCODE_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_ENCRYPTCODE_NULL.getCode(), RespStatusEnum.CHECK_ENCRYPTCODE_NULL.getDescribe()));
        } else if (StringUtils.isBlank(zipCode)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_ZIPCODE_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_ZIPCODE_NULL.getCode(), RespStatusEnum.CHECK_ZIPCODE_NULL.getDescribe()));
        } else if (StringUtils.isBlank(content)) {
            log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, RespStatusEnum.CHECK_CONTENT_NULL.getDescribe());
            return Result.error(new ResponseStatus(RespStatusEnum.CHECK_CONTENT_NULL.getCode(), RespStatusEnum.CHECK_CONTENT_NULL.getDescribe()));
        }
        

        
        return Result.ok(new ResponseStatus(RespStatusEnum.SUCCESS.getCode(), RespStatusEnum.SUCCESS.getDescribe()));
        
    }
    
    @Override
    public Result auth(HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentType("text/json;charset=UTF-8");
        String nonce = request.getParameter(ConfigurerInfo.NONCE);
        String secretId = request.getParameter(ConfigurerInfo.SECRETID);
        String timeStamp = request.getParameter(ConfigurerInfo.TIMESTAMP);
        String zipCode = request.getParameter(ConfigurerInfo.ZIPCODE);
        String encryptCode = request.getParameter(ConfigurerInfo.ENCRYPTCODE);
        String content = request.getParameter(ConfigurerInfo.CONTENT);
        String reqSign = request.getParameter(ConfigurerInfo.SIGNATURE);
        log.info("{},请求的secretId:{}，nonce:{},timestamp:{},zipCode:{},encryptCode:{},签名值sign:{}，内容content:{}", LOGGER_MSG, secretId, nonce, timeStamp, zipCode, encryptCode, reqSign, content);
        StringBuilder url = new StringBuilder();
        /*url.append(request.getMethod()).append(request.getServerName());
        if (ConfigureConstant.INT_80 != request.getServerPort() && ConfigureConstant.INT_443 != request.getServerPort()) {
            url.append(":").append(request.getServerPort());
        }*/
        url.append(request.getRequestURI()).append("?");
        log.info("{}生成签名的URL:{}", LOGGER_MSG, url);
        //特定排序
        TreeMap<String, String> sortMap = new TreeMap<>();
        sortMap.put(ConfigurerInfo.NONCE, nonce);
        sortMap.put(ConfigurerInfo.SECRETID, secretId);
        sortMap.put(ConfigurerInfo.TIMESTAMP, timeStamp);
        sortMap.put(ConfigurerInfo.CONTENT, content);
        sortMap.put(ConfigurerInfo.ENCRYPTCODE, encryptCode);
        sortMap.put(ConfigurerInfo.ZIPCODE, zipCode);
        //获取id对应的key
        Map<String, String> secret = openApiService.getSecret(secretId,"");
        String secretKey = "";
        //获取密钥key
        if (secret != null) {
            secretKey = secret.get("secretKey");
        }
        if(StringUtils.isBlank(secretKey)){
            log.error("{}根据secretId:{},获取对应的secretKey为空!", LOGGER_MSG, secretId);
            return Result.error(new ResponseStatus(RespStatusEnum.NOTAUTH.getCode(), RespStatusEnum.NOTAUTH.getDescribe()));
        }
        //校验产品是否已授权
        boolean flag = openApiService.validTenantProduct(secretId, "1");
        if(!flag){
            log.error("{}根据secretId:{},校验未开通该产品或授权已过期", LOGGER_MSG, secretId);
            return Result.error(new ResponseStatus(RespStatusEnum.NOTPRODUCT.getCode(), RespStatusEnum.NOTPRODUCT.getDescribe()));
        }

        log.info("{}通过secretId:{},获取的对应的secretKey:{}", LOGGER_MSG, secretId, secretKey);
        String localSign = "";
        try {
            localSign = HmacSha1Util.genSign(url.toString(), sortMap, secretKey);
        } catch (Exception e) {
            log.error("{}鉴权异常,鉴权URL为:{},secretId为:{},secretKey为:{},错误原因为:{}", LOGGER_MSG, url.toString(), secretId, secretKey, e);
            return Result.error(new ResponseStatus(RespStatusEnum.AUTHFAIL.getCode(), RespStatusEnum.AUTHFAIL.getDescribe()));
        }
        log.info("{}生成的本地签名值为local:{}，请求签名值:{}", LOGGER_MSG, localSign, reqSign);
        
        if (StringUtils.isNotBlank(localSign) && StringUtils.isNotBlank(reqSign)) {
            if (localSign.equals(reqSign)) {
                log.info("secretId:{},鉴权成功", secretId);
                Map<String, Object> result = new HashMap<>();
                result.put(ConfigurerInfo.RESPONSESTATUS,new ResponseStatus(RespStatusEnum.SUCCESS.getCode(), RespStatusEnum.SUCCESS.getDescribe()));
                result.put(ConfigurerInfo.SECRETKEY,secretKey);
                return Result.ok(result);
            } else {
                log.error("{}鉴权失败.请求鉴权值为:{},计算后鉴权值为:{}", LOGGER_MSG, reqSign, localSign);
                return Result.error(new ResponseStatus(RespStatusEnum.AUTHFAIL.getCode(), RespStatusEnum.AUTHFAIL.getDescribe()));
            }
        } else {
            log.error("{}鉴权失败.请求鉴权值和计算后鉴权值为空", LOGGER_MSG);
            return Result.error(new ResponseStatus(RespStatusEnum.AUTHFAIL.getCode(), RespStatusEnum.AUTHFAIL.getDescribe()));
        }
        
    }
    /**
     * 密钥处理，大于24为截取，不足24位补0至24位
     * @param key
     * @return
     */
    public static byte[] padKey(String key) {
        // 将字符串转换为字节数组
        byte[] keyBytes = key.getBytes();

        // 如果密钥长度不足 24 字节，则补 0
        if (keyBytes.length < 24) {
            byte[] paddedKey = new byte[24];
            System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
            // 剩余部分补 0
            for (int i = keyBytes.length; i < 24; i++) {
                paddedKey[i] = 0;
            }
            return paddedKey;
        } else if (keyBytes.length > 24) {
            // 如果密钥长度超过 24 字节，截取前 24 字节
            byte[] truncatedKey = new byte[24];
            System.arraycopy(keyBytes, 0, truncatedKey, 0, 24);
            return truncatedKey;
        } else {
            // 如果密钥长度正好是 24 字节，直接返回
            return keyBytes;
        }
    }


    /**
     * 参数压缩 加密
     */
    @Override
    public String commonEncrypt(String zipCode, String encryptCode, String content, String secretKey) {
        String json = content;
        byte[] de = null;
        // 加密
        if (ConfigurerInfo.ENCRYPTCODE_1.equals(encryptCode)) {
            try {
                // 获取秘钥
                String password = new String(padKey(secretKey));
                // 加密
                de = TripleDesUtil.encryptMode(password, json.getBytes());
            } catch (Exception e) {
                log.error("{}3DES加密出现异常:{}", LOGGER_MSG, e);
            }
        }
        if (ConfigurerInfo.ZIPCODE_1.equals(zipCode)) {
            // 压缩
            try {
                if (de != null) {
                    de = GzipUtils.compress(de);
                } else {
                    de = GzipUtils.compress(json.getBytes());
                }
            } catch (Exception e) {
                log.error("{}GZIP压缩出现异常:{}", LOGGER_MSG, e);
            }
        }
        try {
            if (de != null) {
                json = Base64Encoding.encodeToString(de);
            } else {
                json = Base64Encoding.encodeToString(content.getBytes());
            }
        } catch (Exception e) {
            log.error("{}base64压缩出现异常:{}", LOGGER_MSG, e);
        }
        return json;
    }

    /**
     * 参数解压缩 解密
     *
     * @return
     */
    @Override
    public String commonDecrypt(String zipCode, String encryptCode, String content, String secretKey) {

        String json = content;
        byte[] de = null;
        try {
            json = Base64Encoding.decodeToString(content);
            de = Base64Encoding.decode(content);
        } catch (Exception e) {
            log.error("{}base64解密出现异常:{}", LOGGER_MSG, e);
        }
        if (ConfigurerInfo.ZIPCODE_1.equals(zipCode)) {
            // 解压缩
            try {
                de = GzipUtils.decompress(de);
                json = new String(de, StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("{}解压缩出现异常:{}", LOGGER_MSG, e);
            }
        }
        // 解密
        if (ConfigurerInfo.ENCRYPTCODE_1.equals(encryptCode)) {
            try {
                // 获取秘钥
                String password = new String(padKey(secretKey));
                // 解密
                json = new String(TripleDesUtil.decryptMode(password, de), StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("{}3DES解密出现异常:{}", LOGGER_MSG, e);
            }
        }
        return json;
    }
    
}
