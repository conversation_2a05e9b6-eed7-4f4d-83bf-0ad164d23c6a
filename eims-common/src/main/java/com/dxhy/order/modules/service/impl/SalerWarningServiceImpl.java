package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.OrderInvoiceInfoDao;
import com.dxhy.order.modules.dao.SalerWarningDao;
import com.dxhy.order.modules.entity.FirstPagePortEntity;
import com.dxhy.order.modules.entity.SalerWarningInfo;
import com.dxhy.order.modules.service.OpenApiService;
import com.dxhy.order.modules.service.SalerWarningService;
import com.dxhy.order.utils.DistributedKeyMaker;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【saler_warning(发票预警表)】的数据库操作Service实现
 * @createDate 2022-07-21 13:39:13
 */
@Service("salerWarningService")
@Slf4j
public class SalerWarningServiceImpl extends ServiceImpl<SalerWarningDao, SalerWarningInfo>
        implements SalerWarningService {

    private static final String LOGGER_MSG = "(基础配置)";

    @Autowired
    private SalerWarningDao salerWarningDao;
    @Autowired
    private OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    private OpenApiService openApiService;


    @Override
    public Map queryWarnInfo(Map map) {
        log.info("{}，执行查询操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(map));

        int temp = 0;
        SalerWarningInfo salerWarningInfo = new SalerWarningInfo();
        // 是否已维护数据
        SalerWarningInfo salerWarningInfoForQuery = salerWarningDao.selectWarnInfoByNsrsbh(map.get("baseNsrsbh").toString());
        if (ObjectUtils.isEmpty(salerWarningInfoForQuery)) {
            // 如果是第一次查询，没有数据，则执行插入操作
            SalerWarningInfo salerWarningInfo1 = new SalerWarningInfo();
            salerWarningInfo1.setId(DistributedKeyMaker.generateShotKey());
            salerWarningInfo1.setXhfNsrsbh(map.get("baseNsrsbh").toString());
            salerWarningInfo1.setWarnFlag("0");
            salerWarningInfo1.setSaveInfo("0");
            salerWarningInfo1.setAutoBz("0");
            String createDate = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M_DH_M_S).format(Calendar.getInstance().getTime());
            salerWarningInfo1.setCreateTime(createDate);
            salerWarningInfo1.setUpdateTime(createDate);
            int i = salerWarningDao.insert(salerWarningInfo1);
            if (i > 0) {
                salerWarningInfo = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(salerWarningInfo1), SalerWarningInfo.class);
                log.info("{}，首次查询添加默认数据成功：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(salerWarningInfo1));
            }
            temp = 1;
        } else {
            salerWarningInfo = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(salerWarningInfoForQuery), SalerWarningInfo.class);
        }

        if (temp < 1) {
            map.put("id", salerWarningInfo.getId());
        } else {
            map.put("id", "");
        }
        map.put("baseNsrsbh", StringUtils.isBlank(salerWarningInfo.getXhfNsrsbh()) ? "" : salerWarningInfo.getXhfNsrsbh());
        map.put("monthUpper", StringUtils.isBlank(salerWarningInfo.getMonthUpper()) ? "" : salerWarningInfo.getMonthUpper());
        map.put("quarterUpper", StringUtils.isBlank(salerWarningInfo.getQuarterUpper()) ? "" : salerWarningInfo.getQuarterUpper());
        map.put("yearUpper", StringUtils.isBlank(salerWarningInfo.getYearUpper()) ? "" : salerWarningInfo.getYearUpper());
        map.put("saveInfo", StringUtils.isBlank(salerWarningInfo.getSaveInfo()) ? "" : salerWarningInfo.getSaveInfo());
        map.put("autoBz", StringUtils.isBlank(salerWarningInfo.getAutoBz()) ? "" : salerWarningInfo.getAutoBz());
        map.put("warnFlag", StringUtils.isBlank(salerWarningInfo.getWarnFlag()) ? "" : salerWarningInfo.getWarnFlag());
        String[] warnPhones = StringUtils.isBlank(salerWarningInfo.getWarnPhones()) ? new String[0] : salerWarningInfo.getWarnPhones().split(",");
        for (int i = 0; i < warnPhones.length; i++) {
            warnPhones[i] = warnPhones[i].trim();
        }
        String[] warnEmails = StringUtils.isBlank(salerWarningInfo.getWarnEmails()) ? new String[0] : salerWarningInfo.getWarnEmails().split(",");
        for (int i = 0; i < warnEmails.length; i++) {
            warnEmails[i] = warnEmails[i].trim();
        }
        map.put("warnPhones", warnPhones);
        map.put("warnEmails", warnEmails);


        log.info("{}，查询数据成功：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(map));
        return map;
    }

    @Override
    public R updateWarnInfo(Map map) {

        log.info("{}，执行更新操作入参：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(map));
        SalerWarningInfo salerWarningInfo = new SalerWarningInfo();
        salerWarningInfo.setId(map.get("id").toString());
        salerWarningInfo.setXhfNsrsbh(map.get("baseNsrsbh").toString());
        salerWarningInfo.setMonthUpper(map.get("monthUpper").toString());
        salerWarningInfo.setQuarterUpper(map.get("quarterUpper").toString());
        salerWarningInfo.setYearUpper(map.get("yearUpper").toString());
        salerWarningInfo.setWarnFlag(map.get("warnFlag").toString());
        salerWarningInfo.setSaveInfo(map.get("saveInfo").toString());
        salerWarningInfo.setAutoBz(map.get("autoBz").toString());

        String warnPhones = map.get("warnPhones").toString().replace("[", "").replace("]", "");
        salerWarningInfo.setWarnPhones(warnPhones);
        String warnEmails = map.get("warnEmails").toString().replace("[", "").replace("]", "");
        salerWarningInfo.setWarnEmails(warnEmails);

        salerWarningInfo.setUpdateTime(new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M_DH_M_S).format(Calendar.getInstance().getTime()));
        int i = salerWarningDao.updateWarnInfoById(salerWarningInfo);
        if (i < 1) {
            // 更新失败
            log.info("{}，执行更新操作失败：{}", LOGGER_MSG, salerWarningInfo.getXhfNsrsbh());
            return R.error();
        }
        log.info("{}，执行更新操作成功：{}", LOGGER_MSG, salerWarningInfo.getXhfNsrsbh());
        return R.ok();
    }


    /**
     * 发票预警 - 预警定时任务
     *
     * @return
     */
    @Override
    public void warningTask() {

        // 查询状态为启用的所有的税号
        List<SalerWarningInfo> list = salerWarningDao.selectWarnInfoQy();
        if (list.size() > 0) {
            for (SalerWarningInfo salerWarningInfo : list) {
                // 月度
                if (StringUtils.isNotBlank(salerWarningInfo.getMonthUpper()) && Integer.parseInt(salerWarningInfo.getMonthUpper()) > 0) {
                    // 计算当前月度数据 并 比较
                    String month = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M).format(new Date());
                    List<FirstPagePortEntity> listYkje = orderInvoiceInfoDao.selectMonthDataYkje(salerWarningInfo.getXhfNsrsbh(), month);
                    List<FirstPagePortEntity> listHcje = orderInvoiceInfoDao.selectMonthDataHcje(salerWarningInfo.getXhfNsrsbh(), month);
                    String cntYkje = getTotalNum(listYkje, "month");
                    String cntHcje = getTotalNum(listHcje, "month");
                    BigDecimal cnt = new BigDecimal(cntYkje).subtract(new BigDecimal(cntHcje));
                    log.info("warningTask 定时任务发票预警");
                    log.info("税号：{}，月度限额：{}，已开额度：{}", salerWarningInfo.getXhfNsrsbh(), new BigDecimal(salerWarningInfo.getMonthUpper()).multiply(new BigDecimal("10000")), cnt);
                    if (cnt.compareTo(new BigDecimal(salerWarningInfo.getMonthUpper()).multiply(new BigDecimal("10000"))) > 0) {
                        // 已超额，发送短信、邮箱提醒
                        String content = "税号：" + salerWarningInfo.getXhfNsrsbh() + "，本月度已开金额为：" + cnt + "，已超设置预警额度：" + salerWarningInfo.getMonthUpper() + "万元，请知悉！";
                        openApiService.sendMessageAndEmail(salerWarningInfo.getWarnPhones(), salerWarningInfo.getWarnEmails(), content);
                    }
                }
                // 季度
                if (StringUtils.isNotBlank(salerWarningInfo.getQuarterUpper()) && Integer.parseInt(salerWarningInfo.getQuarterUpper()) > 0) {
                    // 计算当前季度数据 并 比较
                    List<FirstPagePortEntity> listYkje = orderInvoiceInfoDao.selectTotalDataJdkjed(salerWarningInfo.getXhfNsrsbh(), DateUtil.beginOfQuarter(new Date()), DateUtil.endOfQuarter(new Date()));
                    BigDecimal cntJdje = new BigDecimal(getTotalNum(listYkje, "year"));
                    log.info("warningTask 定时任务发票预警");
                    log.info("税号：{}，季度限额：{}，已开额度：{}", salerWarningInfo.getXhfNsrsbh(), new BigDecimal(salerWarningInfo.getQuarterUpper()).multiply(new BigDecimal("10000")), cntJdje);
                    if (cntJdje.compareTo(new BigDecimal(salerWarningInfo.getQuarterUpper()).multiply(new BigDecimal("10000"))) > 0) {
                        // 已超额，发送短信、邮箱提醒
                        String content = "税号：" + salerWarningInfo.getXhfNsrsbh() + "，本季度已开金额为：" + cntJdje + "，已超设置预警额度：" + salerWarningInfo.getQuarterUpper() + "万元，请知悉！";
                        openApiService.sendMessageAndEmail(salerWarningInfo.getWarnPhones(), salerWarningInfo.getWarnEmails(), content);
                    }

                }
                // 年度
                if (StringUtils.isNotBlank(salerWarningInfo.getYearUpper()) && Integer.parseInt(salerWarningInfo.getYearUpper()) > 0) {
                    // 计算当前年度数据 并 比较
                    String year = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y).format(new Date());
                    List<FirstPagePortEntity> listKjje = orderInvoiceInfoDao.selectTotalDataLjkjje(salerWarningInfo.getXhfNsrsbh(), year);
                    BigDecimal cntYkje = new BigDecimal(getTotalNum(listKjje, "year"));
                    log.info("warningTask 定时任务发票预警");
                    log.info("税号：{}，年度限额：{}，已开额度：{}", salerWarningInfo.getXhfNsrsbh(), new BigDecimal(salerWarningInfo.getYearUpper()).multiply(new BigDecimal("10000")), cntYkje);
                    if (cntYkje.compareTo(new BigDecimal(salerWarningInfo.getYearUpper()).multiply(new BigDecimal("10000"))) > 0) {
                        // 已超额，发送短信、邮箱提醒
                        String content = "税号：" + salerWarningInfo.getXhfNsrsbh() + "，本年度已开金额为：" + cntYkje + "，已超设置预警额度：" + salerWarningInfo.getYearUpper() + "万元，请知悉！";
                        openApiService.sendMessageAndEmail(salerWarningInfo.getWarnPhones(), salerWarningInfo.getWarnEmails(), content);
                    }
                }
            }
        }
    }


    /**
     * 计算总数
     *
     * @param list
     * @param type 年度：year; 季度月度 month
     * @return
     */
    public String getTotalNum(List<FirstPagePortEntity> list, String type) {
        BigDecimal total = new BigDecimal("0");
        if ("year".equals(type)) {
            for (FirstPagePortEntity firstPagePortEntity : list) {
                BigDecimal monthNum = new BigDecimal(firstPagePortEntity.getMonthNum());
                total = total.add(monthNum);
            }
        } else {
            for (FirstPagePortEntity firstPagePortEntity : list) {
                BigDecimal dayNum = new BigDecimal(firstPagePortEntity.getDayNum());
                total = total.add(dayNum);
            }
        }
        return total.toString();
    }
}
