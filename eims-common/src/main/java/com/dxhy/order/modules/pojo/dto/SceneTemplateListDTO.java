package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 查询列表 场景模板DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("查询列表 场景模板DTO")
public class SceneTemplateListDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 场景模板名称
     */
    @ApiModelProperty(name = "场景模板名称")
    private String name;

}
