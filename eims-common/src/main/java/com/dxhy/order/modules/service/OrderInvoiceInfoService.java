package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.dto.IdDTO;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.dto.NoOrderInvoiceInfoListDTO;
import com.dxhy.order.pojo.QdInvalidRequest;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 订单和发票关系表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
public interface OrderInvoiceInfoService extends IService<OrderInvoiceInfoEntity> {

    /**
     * 查询
     *
     * @param orderInvoiceInfoEntity
     * @return
     */
    R queryPage(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 查询
     *
     * @param param
     * @return
     */
    R queryJxfpPage(Map<String, Object> param);

    /**
     * 暂存
     *
     * @param orderInvoiceInfoEntity
     * @return
     */
    R orderInvoiceTemporary(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    R deleteByInvoiceId(List<String> id, String baseNsrsbh);

    /**
     * 订单开票导入
     *
     * @param file
     * @return
     */
    R uploadOrderInvoiceInfo(MultipartFile file);

    /**
     * 开票记录 - 列表查询
     *
     * @param invoiceRecordQueryList
     * @return
     */
    PageUtils queryInvoiceRecordPage(InvoiceRecordQueryList invoiceRecordQueryList);

    /**
     * 开票记录 - 购货方查询
     *
     * @param nsrsbh
     * @return
     */
    List<String> queryGhfList(String nsrsbh);

    /**
     * 全电概览 - 月度数据概览查询
     *
     * @param nsrsbh
     * @return
     */
    FirstPageChartsEntity queryMonthData(String nsrsbh);

    /**
     * 全电概览 - 累计数据概览查询
     *
     * @param nsrsbh
     * @return
     */
    FirstPageChartsEntity queryYearsData(String nsrsbh);

    /**
     * 全电概览 - 待开发票清单 - 删除
     *
     * @param ID
     * @return
     */
    R updateReadyInvoiceStatus(String ID);

    /**
     * 全电概览 - 待开发票清单 - 删除
     *
     * @param ID
     * @return
     */
    R updateInvoicingStatus(String ID);

    /**
     * 全电概览 - 已开发票清单
     *
     * @param orderInvoiceInfoEntity
     * @return
     */
    PageUtils queryInvoicedList(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 数据统计 - 项目查询
     *
     * @param dataStatisticQueryList
     * @return
     */
    List<Map<String, Object>> queryXMStaisticList(DataStatisticQueryList dataStatisticQueryList);

    /**
     * 数据统计 - 种类查询
     *
     * @param dataStatisticQueryList
     * @return
     */
    List<DataStaticsQueryResultList> queryZLStaisticList(DataStatisticQueryList dataStatisticQueryList);

    /**
     * 合并订单
     *
     * @param ids
     * @param baseNsrsbh
     * @return
     */
    R mergeOrderInvoice(List<String> ids, String baseNsrsbh);

    /**
     * 订单退回
     *
     * @param id
     * @return
     */
    R orderInvoiceBack(List<String> id, String baseNsrsbh);

    /**
     * 发票填开-发票开具接口
     *
     * @param orderInvoiceInfoEntity
     * @return
     */
    R invoiceInputIssue(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 订单开票-发票开具接口
     *
     * @param id
     * @return
     */
    R orderInvoiceIssue(List<String> id);

    /**
     * 订单开票-详情
     *
     * @param idDTO
     * @return
     */
    R orderInvoiceDetail(IdDTO idDTO);

    /**
     * 发票信息保存
     *
     * @param orderInvoiceImportExcelEntities
     * @return
     */
    R saveOrderInvoice(List<OrderInvoiceImportExcelEntity> orderInvoiceImportExcelEntities, String xhfMc, String xhfNsrsbh, String xhfDz, String xhfDh, String xhfYh, String xhfZh);

    /**
     * 下载错误明细
     *
     * @param list
     * @return
     */
    void downLoadInvoiceItemDetail(List<OrderInvoiceImportExcelEntity> list, HttpServletResponse response);

    /**
     * 获取二维码信息
     *
     * @return
     */
    InvoiceRecordInfoEntity getEwmInfo(InvoiceRecordInfoEntity invoiceRecordInfoEntity);

    R uploadBatchOrderInvoiceInfo(MultipartFile file, String baseNsrsbh);

    void downLoadInvoiceFailDetail(InvoiceImportInfoResEntity orderInvoiceImportReqEntity, HttpServletResponse response) throws Exception;

    R saveBatchOrderInvoice(InvoiceImportInfoResEntity invoiceImportInfoResEntity);

    /**
     * 根据代码号码等参数获取对应png图片
     */
    List<String> getInvoicePng(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    BlueInvoicesIssueRes blueOrderInvoiceIssue(List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities);

    R getTaxClassCodeSpjc(TaxClassCodeEntity taxClassCodeEntity);

    /**
     * 发票抬头模糊查询
     */
    R getGhfmcByLike(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 智能赋码推荐的商品编码
     */
    R getSsbmByLike(OrderInvoiceItemEntity orderInvoiceItemEntity);

    /**
     * 获取实名认证二维码
     */
    R getQrcode(String nsrsbh);

    /**
     * 获取实名认证二维码
     */
    R getQrcodeStatus(String nsrsbh, String rzid);

    R noNeedOrderInvoiceInfo(IdsDTO idsDTO);

    R manualInvoiceInfo();

    R manualInvoiceInfoDeter(ManualInvoiceReq manualInvoiceReq);

    R editCompareJeInvoiceInfo(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    /**
     * 无需开票订单转为待处理
     */
    R noNeedToWaitInvoice(IdsDTO idsDTO);

    /**
     * 无需开票页签查询无需开票数据
     */

    R SelectnoNeedInvoiceInfo(NoOrderInvoiceInfoListDTO noOrderInvoiceInfoListDTO);

    /**
     * 无需开票页签查订单详细信息
     */
    R NoNeedDetails(IdsDTO ids);

    /**
     * 开票记录页面查询
     */
    R getOrderInvoiceInfoByKPZT(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R updateToWait(IdDTO id);

    R countSL(String baseNsrsbh);

    R batchDelivery(List<String> qdfpList);

    R updateNoNeedBzInfo(OrderInvoiceInfoEntity orderInvoiceInfoEntity);

    R getOrderDetail(IdDTO id);

    R getOrderItemDetail(IdDTO id);

    R orderInvoiceDetailInfo(IdDTO idDTO);

    R orderInvoiceYddhOrYysdh(DjbhReq djbhReq);

    int updateEmailByqdhm(String email, String qdfphm);

    int updatePhoneByqdhm(String phone, String qdfphm);

    R invalid(QdInvalidRequest qdInvalidRequest);

    R getXml(QdInvalidRequest qdInvalidRequest);

    /**
     * 获取特定业务详情
     * @param id
     * @return
     */
    R getOrderTdywDetail(IdDTO id);

    R getGhfmc(OrderInvoiceInfoEntity orderInvoiceInfoEntity);
}

