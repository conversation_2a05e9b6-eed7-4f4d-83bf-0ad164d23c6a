package com.dxhy.order.modules.entity;

import lombok.Data;

/**
 * 数据统计 - SQL查询实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-07-04 13:32:24
 */
@Data
public class DataStaticsQueryResultList {

    /**
     *  新版 - 税率
     */
    private String sl;

    /**
     *  新版 - 含税标志 ( 0 不含税 1含税 )
     */
    private String hsbz;

    /**
     *  新版 - 开票类型  (KPLX,0正票 1红票)
     */
    private String kplx;

    /**
     *  新版 - 金额
     */
    private String je;

    /**
     *  新版 - 税额
     */
    private String se;

    /**
     *  新版 - 开票状态
     */
    private String kpzt;

    /**
     *  新版 - 冲红标志
     */
    private String chbz;

    /**
     *  新版 - 发票种类
     */
    private String fpzldm;

    /**
     *  按种类查询 - 正数发票份数
     */
    private String zsfpfs;

    /**
     *  按种类查询 - 正废发票份数
     */
    private String zffpfs;

    /**
     *  按种类查询 - 负数发票份数
     */
    private String fsfpfs;

    /**
     *  按种类查询 - 负废发票份数
     */
    private String fffpfs;

    /**
     *  加工日期
     */
    private String jgrq;

}
