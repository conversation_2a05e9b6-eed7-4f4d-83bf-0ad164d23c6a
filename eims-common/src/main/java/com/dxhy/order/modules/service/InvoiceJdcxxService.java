package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceJdcxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 机动车信息的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceJdcxxService extends IService<InvoiceJdcxxEntity> {
    void saveBatch(List<InvoiceJdcxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
