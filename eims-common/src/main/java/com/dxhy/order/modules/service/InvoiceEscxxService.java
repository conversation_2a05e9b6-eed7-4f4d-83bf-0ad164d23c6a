package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceEscxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 二手车信息的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceEscxxService extends IService<InvoiceEscxxEntity> {
    void saveBatch(List<InvoiceEscxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
