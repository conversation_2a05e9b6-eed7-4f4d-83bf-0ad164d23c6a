package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceHwysxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 货物运输服务的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceHwysxxService extends IService<InvoiceHwysxxEntity> {
    void saveBatch(List<InvoiceHwysxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
