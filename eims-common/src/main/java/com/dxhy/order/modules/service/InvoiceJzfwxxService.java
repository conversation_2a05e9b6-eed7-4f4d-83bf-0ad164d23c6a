package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceJzfwxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 建筑服务的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceJzfwxxService extends IService<InvoiceJzfwxxEntity> {
    void saveBatch(List<InvoiceJzfwxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
