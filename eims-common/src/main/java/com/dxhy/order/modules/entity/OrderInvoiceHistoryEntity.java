package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票下载历史任务表
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 17:51:49
 */
@TableName("order_invoice_history")
@ApiModel("发票下载历史信息")
@Data
public class OrderInvoiceHistoryEntity extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /** 申请企业纳税人识别号 */
    @ApiModelProperty(name = "申请企业纳税人识别号",notes = "")
    private String sqqynsrsbh ;
    /** 申请企业名称 */
    @ApiModelProperty(name = "申请企业名称",notes = "")
    private String sqqymc ;
    /** 业务类型;销项 、进项 */
    @ApiModelProperty(name = "业务类型",notes = "销项 、进项")
    private String ywlx ;
    /** 下载通道;税航RPA、税航乐企、大象RPA、大象乐企 */
    @ApiModelProperty(name = "下载通道",notes = "税航RPA、税航乐企、大象RPA、大象乐企")
    private String xztd ;
    /** 开票日期起 */
    @ApiModelProperty(name = "开票日期起",notes = "")
    private String kprqq ;
    /** 开票日期止 */
    @ApiModelProperty(name = "开票日期止",notes = "")
    private String kprqz ;
    /** 发票种类代码 */
    @ApiModelProperty(name = "发票种类代码",notes = "")
    private String fpzldm ;
    /** 归集状态;未开始、归集中、归集失败、归集成功 */
    @ApiModelProperty(name = "归集状态",notes = "未开始、归集中、归集失败、归集成功")
    private String gjzt ;
    /** 状态描述;成功或具体异常信息 */
    @ApiModelProperty(name = "状态描述",notes = "成功或具体异常信息")
    private String ztms ;
    /** 是否删除标志;0：未删除  1:已删除 */
    @ApiModelProperty(name = "是否删除标志",notes = "0：未删除  1:已删除")
    private String isDelete ;
    /** 创建人 */
    @ApiModelProperty(name = "创建人",notes = "")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty(name = "创建时间",notes = "")
    private Date createdTime ;
    /** 更新人 */
    @ApiModelProperty(name = "更新人",notes = "")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty(name = "更新时间",notes = "")
    private Date updatedTime ;
}
