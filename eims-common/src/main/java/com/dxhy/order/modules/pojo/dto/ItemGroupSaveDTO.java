package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 新增&修改 项目分类DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("新增&修改 项目分类DTO")
public class ItemGroupSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 项目信息分类主键（此参数为空时表示新增 不为空时表示修改）
     */
    @ApiModelProperty("项目信息分类主键（此参数为空时表示新增 不为空时表示修改）")
    private String id;

    /**
     * 上级项目信息分类
     */
    @ApiModelProperty(name = "上级项目信息分类", required = true)
    private String parentId;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 项目分类名称
     */
    @ApiModelProperty(name = "项目分类名称", required = true)
    private String xmflmc;
}
