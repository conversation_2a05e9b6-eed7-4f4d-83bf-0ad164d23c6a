package com.dxhy.order.modules.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 查询列表 项目信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("查询列表 项目信息DTO")
public class ItemInfoListDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目分类主键
     */
    @ApiModelProperty(name = "项目分类主键")
    private String id;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String xmmc;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String dj;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String ggxh;

    /**
     * 租户内客户列表开关
     */
    @ApiModelProperty("开关判断")
    private String check = "ON";

    /**
     * 根据税号获取的集团下所有企业税号列表
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "根据税号获取的集团下所有企业税号列表", required = true)
    private List<String> baseNsrsbhList;

}
