package com.dxhy.order.modules.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 查询列表 客户信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("查询列表 客户信息DTO")
public class CustomerInfoListDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 客户分类主键
     */
    @ApiModelProperty(name = "客户分类主键", required = true)
    private String id;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String gsmc;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String lxr;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String sjhm;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String scode;

    /**
     * 开始时间 yyyyMMdd
     */
    @ApiModelProperty("开始时间 yyyyMMdd")
    private String timeStart;

    /**
     * 截止时间 yyyyMMdd
     */
    @ApiModelProperty("截止时间 yyyyMMdd")
    private String timeEnd;

    /**
     * 所属纳税人识别号
     */
    @TableField(exist = false)
    @ApiModelProperty(name = "所属纳税人识别号", required = true)
    private String baseNsrsbh;

}
