package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.pojo.dto.InvoiceRecordItemInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单开票明细表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-22 18:31:05
 */
@Mapper
public interface OrderInvoiceItemDao extends BaseMapper<OrderInvoiceItemEntity> {

    List<OrderInvoiceItemEntity> selectList(Page page, OrderInvoiceItemEntity orderInvoiceItemEntity);

    List<OrderInvoiceItemEntity> selectItemListById(@Param("id") String id);

    List<InvoiceRecordItemInfoDTO> selectRecordItemListById(Page page,@Param("id") String id);

    List<InvoiceRecordItemInfoDTO> selectRecordItemListByYsdh(Page page,@Param("ids") List<String> ids);
}
