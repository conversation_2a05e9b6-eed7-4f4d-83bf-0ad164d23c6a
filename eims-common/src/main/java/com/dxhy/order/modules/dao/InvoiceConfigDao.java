package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.InvoiceConfigInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【invoice_config】的数据库操作Mapper
* @createDate 2022-08-26 12:14:23
* @Entity generator.domain.InvoiceConfig
*/
@Mapper
public interface InvoiceConfigDao extends BaseMapper<InvoiceConfigInfo> {

    List<InvoiceConfigInfo> selectList(Page page, @Param("invoiceConfigInfo") InvoiceConfigInfo invoiceConfigInfo);

    /**
     *  根据税号检查是否存在【勾选默认】数据
     */
    InvoiceConfigInfo checkHadConfigByNsrsbh(@Param("invoiceConfigInfo") InvoiceConfigInfo invoiceConfigInfo);

    /**
     *  根据税号更新勾选默认状态
     */
    int updateZhlxByNsrsbh(@Param("invoiceConfigInfo") InvoiceConfigInfo invoiceConfigInfo);

    /**
     *  手机号重复的校验
     */
    int countForPhone(@Param("dh") String dh);

}




