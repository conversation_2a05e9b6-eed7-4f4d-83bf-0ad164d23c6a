package com.dxhy.order.modules.entity;

import lombok.Data;

@Data
public class OrderInvoiceImportExcelEntity extends OrderInvoiceItemEntity{
    private String ddh;
    private String fplx;
    private String tspz;
    private String ttlx;
    private String bmbbh;
    private String ssflbm;
    private String sfxsssyhzc;
    private String xsyhzenr;
    private String bz;
    private String businessType;
    private String jdcqylx;
    /**
     * 购货方名称
     */
    private String ghfMc;
    /**
     * 购货方识别号
     */
    private String ghfNsrsbh;
    /**
     * 购货方编码id
     */
    private String ghfId;
    /**
     * 购货方地址
     */
    private String ghfDz;
    /**
     * 购货方电话
     */
    private String ghfDh;
    /**
     * 购货方银行
     */
    private String ghfYh;
    /**
     * 购货方账号
     */
    private String ghfZh;
    /**
     * 购货方邮箱
     */
    private String ghfYx;
}
