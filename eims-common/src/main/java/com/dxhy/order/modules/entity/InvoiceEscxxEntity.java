package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-二手车信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_escxx")
@Data
public class InvoiceEscxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 二手车信息主键
	 */
	@ApiModelProperty(value = "二手车主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("开票方类型")
	private String kpflx;

	@ApiModelProperty("车牌照号")
	private String cpzh;

	@ApiModelProperty("登记证号")
	private String djzh;

	@ApiModelProperty("车辆类型")
	private String cllx;

	@ApiModelProperty("车架号/车辆识别代码")
	private String clsbdm;

	@ApiModelProperty("厂牌型号")
	private String cpxh;

	@ApiModelProperty("转入地车辆管理所名称")
	private String zrdclglsmc;

	@ApiModelProperty("经营、拍卖单位名称")
	@JsonProperty("jypmdw_mc")
	private String jypmdwMc;

	@ApiModelProperty("经营、拍卖单位税号")
	@JsonProperty("jypmdw_sh")
	private String jypmdwSh;

	@ApiModelProperty("经营、拍卖单位地址")
	@JsonProperty("jypmdw_dz")
	private String jypmdwDz;

	@ApiModelProperty("经营、拍卖单位电话")
	@JsonProperty("jypmdw_dh")
	private String jypmdwDh;

	@ApiModelProperty("经营、拍卖单位开户银行")
	@JsonProperty("jypmdw_khyh")
	private String jypmdwKhyh;

	@ApiModelProperty("经营、拍卖单位银行账号")
	@JsonProperty("jypmdw_yhzh")
	private String jypmdwYhzh;

	@ApiModelProperty("二手车市场名称")
	@JsonProperty("sc_mc")
	private String scMc;

	@ApiModelProperty("二手车市场税号")
	@JsonProperty("sc_sh")
	private String scSh;

	@ApiModelProperty("二手车市场地址")
	@JsonProperty("sc_dz")
	private String scDz;

	@ApiModelProperty("二手车市场电话")
	@JsonProperty("sc_dh")
	private String scDh;

	@ApiModelProperty("二手车市场开户银行")
	@JsonProperty("sc_khyh")
	private String scKhyh;

	@ApiModelProperty("二手车市场银行账号")
	@JsonProperty("sc_yhzh")
	private String scYhzh;

	@ApiModelProperty("二手车销货方代码")
	@JsonProperty("esc_xhfdm")
	private String escXhfdm;

	@ApiModelProperty("二手车销货方名称")
	@JsonProperty("esc_xhfmc")
	private String escXhfmc;

	@ApiModelProperty("二手车销货方地址")
	@JsonProperty("esc_xhfdz")
	private String escXhfdz;

	@ApiModelProperty("二手车销货方电话")
	@JsonProperty("esc_xhfdh")
	private String escXhfdh;

	@ApiModelProperty("二手车销货方自然人标识")
	@JsonProperty("esc_xhf_zrrbs")
	private String escXhfZrrbs;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
