package com.dxhy.order.modules.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.dao.OrderInvoiceConfigDao;
import com.dxhy.order.modules.dao.OrderInvoiceInfoDao;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.service.CommonInterfaceService;
import com.dxhy.order.modules.service.InvoiceTdywService;
import com.dxhy.order.modules.service.OpenApiService;
import com.dxhy.order.modules.service.SmsConfigService;
import com.dxhy.order.pojo.ResponseData;
import com.dxhy.order.pojo.ResponseStatus;
import com.dxhy.order.pojo.Result;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.net.Inet4Address;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service("openApiService")
@RefreshScope
public class OpenApiServiceImpl implements OpenApiService {

    private static final String LOGGER_MSG = "(短信邮件发送)";

    @Autowired
    private OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    private SmsConfigService smsConfigService;
    @Autowired
    private OrderInvoiceConfigDao orderInvoiceConfigDao;

    @Value("${order.sms.sendPhonesUrl}")
    private String sendPhonesUrl;
    @Value("${order.sms.sendPhoneUrl}")
    private String sendPhoneUrl;
    @Value("${order.sms.sendEmailUrl}")
    private String sendhEmailUrl;
    @Value("${order.sms.platformNo}")
    private String platformNo;
    @Value("${order.sms.platformKey}")
    private String platformKey;
    @Value("${dxKfpt.qd.secretUrl}")
    private String secretUrl;
    @Value("${dxKfpt.qd.validTenantProductUrl}")
    private String validTenantProductUrl;
    @Autowired
    private  MessageSenderUtil messageSenderUtil;
    @Autowired
    private  CommonInterfaceService commonInterfaceService;
    @Autowired
    private InvoiceTdywService invoiceTdywService;

    /**
     * 发送短信 发票信息推送
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> sendShortMessage(String phone, String qdfphm) {
        log.error("{}，发送短信入参：phone: {}，qdfphm：{}", LOGGER_MSG, phone, qdfphm);
        //定义回调接收实体
        Map<String, Object> returnMap = new HashMap<>(10);
        try {

            // 组装短信内容
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(qdfphm);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
            String kprq = simpleDateFormat.format(invoiceInfoEntity.getKprq());
            String content = "您收到一张来自" + invoiceInfoEntity.getXhfMc() + "的电子发票，开票时间为：" + kprq +
                    "， 发票号: " + qdfphm + "，请打开： " + invoiceInfoEntity.getPdfUrl() + " 进行查看。";
            log.info("{}，短信内容，返回 ：{}", LOGGER_MSG, content);
            SendMsgReqContext sendMsgReqContext = generateSessionContext();
            SendMsgReq sendMsgRep = new SendMsgReq();
            sendMsgRep.setSessionContext(sendMsgReqContext);
            sendMsgRep.setPlatformNo(platformNo);
            sendMsgRep.setPlatformKey(platformKey);
            sendMsgRep.setPhone(phone);
            sendMsgRep.setIpAddress(Inet4Address.getLocalHost().getHostAddress());
            sendMsgRep.setContent(content);
            sendMsgRep.setMsgType("2");
            sendMsgRep.setChannel("");
            String req = JsonUtils.getInstance().toJsonString(sendMsgRep);
            SendMsgResp response = sendRequest(req, sendPhoneUrl);
            if ("0".equals(response.getErrorCode())) {
                log.info("{}，发送短信成功: {}：{}", LOGGER_MSG, response.getReplyCode(), response.getReplyText());
                //  单据层级判断
                OrderInvoiceConfigEntity invoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(invoiceInfoEntity.getXhfNsrsbh());
                if (!StringUtils.isEmpty(invoiceConfigEntity.getDjcjsz()) && "1".equals(invoiceConfigEntity.getDjcjsz())) {
                    List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(invoiceInfoEntity.getYsdh(), invoiceInfoEntity.getXhfNsrsbh(), null);
                    for (OrderInvoiceInfoEntity orderInvoiceInfo : orderInvoiceInfoEntities) {
                        orderInvoiceInfoDao.updateSjJfzt(orderInvoiceInfo.getId());
                    }
                } else {
                    orderInvoiceInfoDao.updateSjJfzt(invoiceInfoEntity.getId());
                }
                returnMap.put(OrderManagementConstant.CODE, "0000");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "短信发送成功");
            } else {
                log.error("{}，发送短信失败: {}：{}", LOGGER_MSG, response.getReplyCode(), response.getReplyText());
                returnMap.put(OrderManagementConstant.CODE, "9999");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "短信发送失败");
            }

        } catch (Exception e) {
            returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
            returnMap.put(OrderManagementConstant.ALL_MESSAGE, e.getMessage());
        }
        log.info("{}，发送短信，返回 ：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(returnMap));
        return returnMap;
    }


    /**
     * 发送邮箱 发票信息推送
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> sendEmailOld(String email, String qdfphm) {
        log.error("{}，发送邮件入参：email: {}，qdfphm：{}", LOGGER_MSG, email, qdfphm);
        //定义回调接收实体
        Map<String, Object> returnMap = new HashMap<>(10);
        try {
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(qdfphm);
            if (ObjectUtils.isEmpty(invoiceInfoEntity)) {
                returnMap.put(OrderManagementConstant.CODE, "9999");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "未查询到该数据");
                return returnMap;
            }
            String content = generateEmailContentNew(invoiceInfoEntity);
            log.info("{}，邮件内容，返回 ：{}", LOGGER_MSG, content);
            List<String> receiverList = new ArrayList<>();
            receiverList.add(email);

            List<SendEmailReqFile> list = new ArrayList<>();
            SendEmailReqFile sendEmailReqFile = new SendEmailReqFile();
            String fjmc = invoiceInfoEntity.getGhfMc();
            if (!StringUtils.isEmpty(invoiceInfoEntity.getQdfphm())) {
                fjmc = invoiceInfoEntity.getQdfphm() + "_" + fjmc + ".pdf";
            } else {
                fjmc = invoiceInfoEntity.getFpdm() + "_" + invoiceInfoEntity.getFphm() + "_" + fjmc + ".pdf";
            }
            sendEmailReqFile.setFileName(fjmc);
            sendEmailReqFile.setFileUrl(invoiceInfoEntity.getPdfUrl());
            list.add(sendEmailReqFile);

            SendMsgReqContext sendMsgReqContext = generateSessionContext();
            SendEmailReq sendEmailReq = new SendEmailReq();
            sendEmailReq.setSessionContext(sendMsgReqContext);
            sendEmailReq.setPlatformNo(platformNo);
            sendEmailReq.setPlatformKey(platformKey);
            sendEmailReq.setIpAddress(Inet4Address.getLocalHost().getHostAddress());
            sendEmailReq.setTitle("您收到一张来自" + invoiceInfoEntity.getXhfMc() + "的电子发票");
            sendEmailReq.setContent(content);
            sendEmailReq.setReceiverList(receiverList);
            sendEmailReq.setCcList(new ArrayList<>());
            sendEmailReq.setBccList(new ArrayList<>());
            sendEmailReq.setAttachmentList(list);
            sendEmailReq.setChannel("");

            String req = JsonUtils.getInstance().toJsonString(sendEmailReq);
            SendMsgResp response = sendRequest(req, sendhEmailUrl);

            if ("0".equals(response.getErrorCode())) {
                log.info("{}，发送邮件成功: {}：{}", LOGGER_MSG, response.getReplyCode(), response.getReplyText());
                //  单据层级判断
                OrderInvoiceConfigEntity invoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(invoiceInfoEntity.getXhfNsrsbh());
                if (!StringUtils.isEmpty(invoiceConfigEntity.getDjcjsz()) && "1".equals(invoiceConfigEntity.getDjcjsz())) {
                    List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(invoiceInfoEntity.getYsdh(), invoiceInfoEntity.getXhfNsrsbh(), null);
                    for (OrderInvoiceInfoEntity orderInvoiceInfo : orderInvoiceInfoEntities) {
                        orderInvoiceInfoDao.updateYxJfzt(orderInvoiceInfo.getId());
                    }
                } else {
                    orderInvoiceInfoDao.updateYxJfzt(invoiceInfoEntity.getId());
                }
                returnMap.put(OrderManagementConstant.CODE, "0000");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "邮件发送成功");
            } else {
                log.error("{}，发送邮件失败: {}：{}", LOGGER_MSG, response.getReplyCode(), response.getReplyText());
                returnMap.put(OrderManagementConstant.CODE, "9999");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "邮件发送失败");
            }
        } catch (Exception e) {
            returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
            returnMap.put(OrderManagementConstant.ALL_MESSAGE, e.getMessage());
        }
        log.info("{}，发送邮件，返回 ：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(returnMap));
        return returnMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> sendEmail(String email, String qdfphm) {
        log.error("{}，发送邮件入参：email: {}，qdfphm：{}", LOGGER_MSG, email, qdfphm);
        //定义回调接收实体
        Map<String, Object> returnMap = new HashMap<>(10);
        try {
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(qdfphm);


            if (ObjectUtils.isEmpty(invoiceInfoEntity)) {
                returnMap.put(OrderManagementConstant.CODE, "9999");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "未查询到该数据");
                return returnMap;
            }
            invoiceInfoEntity.setFphm(qdfphm);
            log.info("发票详情："+invoiceInfoEntity.toString());
            List<String> emailList = new ArrayList<>();
            emailList.add(email);
            log.info("pdf地址：");
            byte[] ofd_byte = HttpsUtils.doGet(invoiceInfoEntity.getPdfUrl());

            EmailContent emailContent = buildEmailContent(invoiceInfoEntity, emailList,  new String(Base64Encoding.encodeToString(ofd_byte)));
            Map<String, Object> sendEmail = sendEmail(emailContent);

            if ("0000".equals(String.valueOf(sendEmail.get("code")))) {
                log.info("发票代码:{},发票号码:{},邮箱:{},发送成功", invoiceInfoEntity.getFpdm(), invoiceInfoEntity.getFphm(), email);

                //  单据层级判断
                OrderInvoiceConfigEntity invoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(invoiceInfoEntity.getXhfNsrsbh());
                if (!StringUtils.isEmpty(invoiceConfigEntity.getDjcjsz()) && "1".equals(invoiceConfigEntity.getDjcjsz())) {
                    List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(invoiceInfoEntity.getYsdh(), invoiceInfoEntity.getXhfNsrsbh(), null);
                    for (OrderInvoiceInfoEntity orderInvoiceInfo : orderInvoiceInfoEntities) {
                        orderInvoiceInfoDao.updateYxJfzt(orderInvoiceInfo.getId());
                    }
                } else {
                    orderInvoiceInfoDao.updateYxJfzt(invoiceInfoEntity.getId());
                }
                returnMap.put(OrderManagementConstant.CODE, "0000");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "邮件发送成功");
            } else {
//                log.error("{}，发送邮件失败: {}：{}", LOGGER_MSG, response.getReplyCode(), response.getReplyText());
                log.error("发票代码:{},发票号码:{},邮箱:{},发送失败,底层错误信息:{}", invoiceInfoEntity.getFpdm(), invoiceInfoEntity.getFphm(), email,sendEmail.get(OrderManagementConstant.ALL_MESSAGE));
                returnMap.put(OrderManagementConstant.CODE, "9999");
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "邮件发送失败");
            }
        } catch (Exception e) {
            returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
            returnMap.put(OrderManagementConstant.ALL_MESSAGE, e.getMessage());
        }
        log.info("{}，发送邮件，返回 ：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(returnMap));
        return returnMap;
    }


    /**
     * 发送短信、邮箱 - 预警
     *
     * @param phones
     * @param emails
     * @param content
     * @return
     */
    public void sendMessageAndEmail(String phones, String emails, String content) {
        log.info("{}预警提醒，phones：{}，emails:{}，content:{}", LOGGER_MSG, phones, emails, content);
        SendMsgReqContext sendMsgReqContext = generateSessionContext();
        // 短信
        String[] phoneStr = phones.split(",");
        for (String phone : phoneStr) {
            // 组装短信内容
            SendMsgReq sendMsgRep = new SendMsgReq();
            sendMsgRep.setSessionContext(sendMsgReqContext);
            sendMsgRep.setPlatformNo(platformNo);
            sendMsgRep.setPlatformKey(platformKey);
            sendMsgRep.setPhone(phone);
            try {
                sendMsgRep.setIpAddress(Inet4Address.getLocalHost().getHostAddress());
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
            sendMsgRep.setContent(content);
            sendMsgRep.setMsgType("2");
            sendMsgRep.setChannel("");
            String reqPhone = JsonUtils.getInstance().toJsonString(sendMsgRep);
            SendMsgResp respPhone = sendRequest(reqPhone, sendPhoneUrl);
            log.info("{}预警提醒，短信返回:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(respPhone));
        }


        // 邮箱
        String[] emailStr = emails.split(",");
        List<String> list = new ArrayList<>();
        for (String email : emailStr) {
            list.add(email);
        }
        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setSessionContext(sendMsgReqContext);
        sendEmailReq.setPlatformNo(platformNo);
        sendEmailReq.setPlatformKey(platformKey);
        try {
            sendEmailReq.setIpAddress(Inet4Address.getLocalHost().getHostAddress());
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        sendEmailReq.setTitle("深圳标普云科技有限公司");
        sendEmailReq.setContent(content);
        sendEmailReq.setReceiverList(list);
        sendEmailReq.setCcList(new ArrayList<>());
        sendEmailReq.setBccList(new ArrayList<>());
        sendEmailReq.setAttachmentList(new ArrayList<>());
        sendEmailReq.setChannel("");
        String reqEmail = JsonUtils.getInstance().toJsonString(sendEmailReq);
        SendMsgResp respEmail = sendRequest(reqEmail, sendhEmailUrl);
        log.info("{}，预警发送，邮件返回:{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(respEmail));

    }


    public SendMsgResp sendRequest(String content, String url) {
        log.debug("{},请求地址为:{},请求参数为:{}", LOGGER_MSG, url, content);
        String doPost = HttpUtils.doPost(url, content);
        log.debug("{}请求地址为:{},返回参数为:{}", LOGGER_MSG, url, doPost);
        Map map = JsonUtils.getInstance().parseObject(doPost, HashMap.class);
        return JsonUtils.getInstance().parseObject(map.get("transactionStatus").toString(), SendMsgResp.class);
    }


    public SendMsgReqContext generateSessionContext() {
        SendMsgReqContext sendMsgReqContext = new SendMsgReqContext();
        sendMsgReqContext.setEntityCode("1");
        sendMsgReqContext.setChannel("1");
        sendMsgReqContext.setServiceCode("FLEX0001");
        sendMsgReqContext.setPostingDateText((new SimpleDateFormat("yyyyMMddhhmmss").format(new Date())));
        sendMsgReqContext.setValueDateText((new SimpleDateFormat("yyyyMMddhhmmss").format(new Date())));
        sendMsgReqContext.setLocalDateTimeText((new SimpleDateFormat("yyyyMMddhhmmss").format(new Date())));
        sendMsgReqContext.setTransactionBranch("0100001");
        sendMsgReqContext.setUserId("sysadmin");
        sendMsgReqContext.setPassword("1");
        sendMsgReqContext.setSuperUserId("sysadmin");
        sendMsgReqContext.setSuperPassword("1");
        sendMsgReqContext.setAuthorizationReason("1");
        sendMsgReqContext.setExternalReferenceNo(String.valueOf(org.apache.commons.lang3.RandomUtils.nextLong(0, 31)));
        sendMsgReqContext.setUserReferenceNumber(String.valueOf(org.apache.commons.lang3.RandomUtils.nextLong(0, 31)));
        sendMsgReqContext.setOriginalReferenceNo(String.valueOf(org.apache.commons.lang3.RandomUtils.nextLong(0, 31)));
        sendMsgReqContext.setMarketCode("1");
        sendMsgReqContext.setStepCode("1");
        sendMsgReqContext.setAccessSource("1");
        sendMsgReqContext.setAccessSourceType("1");
        sendMsgReqContext.setRiskMessage("1");
        return sendMsgReqContext;
    }


    public String generateEmailContentNew(OrderInvoiceInfoEntity invoiceInfoEntity) {
        Map model = new HashMap();
        model.put("fphm", StringUtils.isEmpty(invoiceInfoEntity.getFphm()) ? "" : invoiceInfoEntity.getFphm());
        model.put("fpdm", StringUtils.isEmpty(invoiceInfoEntity.getFpdm()) ? "" : invoiceInfoEntity.getFpdm());
        model.put("ghfmc", invoiceInfoEntity.getGhfMc());
        model.put("xhfmc", invoiceInfoEntity.getXhfMc());
        model.put("kprq", new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(invoiceInfoEntity.getKprq()));
        model.put("fpje", invoiceInfoEntity.getJshj());
        model.put("qdfphm", invoiceInfoEntity.getQdfphm());
        BigDecimal allJshj = new BigDecimal(0.00);
        OrderInvoiceConfigEntity orderInvoiceConfig = orderInvoiceConfigDao.selectByNsrsbh(invoiceInfoEntity.getXhfNsrsbh());
        if ("1".equals(orderInvoiceConfig.getDjcjsz())) {
            List<OrderInvoiceInfoEntity> orderInvoiceInfoEntityList = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(invoiceInfoEntity.getYsdh(), invoiceInfoEntity.getXhfNsrsbh(), null);
            for (OrderInvoiceInfoEntity infoEntity : orderInvoiceInfoEntityList) {
                allJshj = allJshj.add(new BigDecimal(infoEntity.getJshj()));
            }
            model.put("fpje", allJshj.setScale(2).toPlainString());
        }

        if ("0".equals(invoiceInfoEntity.getFpzlDm()) || "1".equals(invoiceInfoEntity.getFpzlDm())) {
            model.put("fpdmCss", "display:none");
            model.put("fphmCss", "display:none");
            model.put("qdfphmCss", "display:default");
        } else {
            model.put("qdfphmCss", "display:none");
            model.put("fpdmCss", "display:default");
            model.put("fphmCss", "display:default");
        }

        log.info("{}，替换html内容：{}", LOGGER_MSG, JsonUtils.getInstance().toJsonString(model));
        String templateName = "email_fpts.ftl";
        SmsConfig smsConfig = smsConfigService.selectByNsrsbh(invoiceInfoEntity.getXhfNsrsbh());
        if (!ObjectUtils.isEmpty(smsConfig) && !StringUtils.isEmpty(smsConfig.getFile())) {
            templateName = smsConfig.getFile() + ".ftl";
        }
        log.info("{}，税号“{}，对应邮件模板为：{}", LOGGER_MSG, invoiceInfoEntity.getXhfNsrsbh(), templateName);
        FreeMarkerUtil freeMarkerUtil = new FreeMarkerUtil();
        String content = freeMarkerUtil.processTemplate(templateName, model);
        return content;
    }

    public Map<String, String> getSecret(String secretId,String tenantCode) {
        Map requestMap = new HashMap();
        requestMap.put("secretId", secretId);
        requestMap.put("tenantCode", tenantCode);
        String response = HttpUtils.doPost(secretUrl, JsonUtils.getInstance().toJsonString(requestMap));
        Map<String, Object> responseMap = JSONObject.parseObject(response, HashMap.class);
        if (!"0000".equals(responseMap.get("code").toString())) {
            return null;
        }
        Map<String, String> map = JSONObject.parseObject(JsonUtils.getInstance().toJsonString(responseMap.get("data")), HashMap.class);
        return map;
    }

    @Override
    public boolean validTenantProduct(String secretId, String productId) {
        Map requestMap = new HashMap();
        requestMap.put("secretId", secretId);
        requestMap.put("productId", productId);
        String response = HttpUtils.doPost(validTenantProductUrl, JsonUtils.getInstance().toJsonString(requestMap));
        Map<String, Object> responseMap = JSONObject.parseObject(response, HashMap.class);
        if ("0000".equals(responseMap.get("code").toString()) && Boolean.valueOf(responseMap.get("data").toString())) {
            return true;
        }
        return false;
    }


    /**
     * 根据发送方式发送邮件
     * 邮件发送方式
     *
     * @param emailContent 邮件内容
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR> href="<EMAIL>">yaoxuguang</a>
     * @date 2021-12-13
     */
    private Map<String, Object> sendEmail(EmailContent emailContent) {
        Map<String, Object> resultMap = new HashMap<>(3);

        MailContentVo mailContent = new MailContentVo();
        //模板id
        mailContent.setTemplateId(emailContent.getTemplateId());
        //邮件标题
        mailContent.setSubject(emailContent.getSubjects());
        //邮件内容
        mailContent.setContent(emailContent.getContents());
        //接收方邮箱
        mailContent.setTo(emailContent.getTo());
        //抄送方邮箱
        mailContent.setCc(emailContent.getCc());
        //附件
        Attachments[] attachments = emailContent.getAttachments();
        if (ArrayUtil.isNotEmpty(attachments)) {
            List<AttachmentVo> attachmentVoList = new ArrayList<>(attachments.length);
            Arrays.stream(attachments).forEach(attachments1 -> {
                AttachmentVo attachmentVo = new AttachmentVo();
//                BeanUtils.copyProperties(attachments1, attachmentVo);
                attachmentVo.setName(attachments1.getName());
                attachmentVo.setType(attachments1.getType());
                attachmentVo.setFileBytes(Base64Encoding.decode(attachments1.getContent()));
                attachmentVoList.add(attachmentVo);
            });
            mailContent.setAttachmentVos(attachmentVoList);
        }
        //发件方信息
        SendMailVo sendVo = new SendMailVo();
        //发件方邮箱
        sendVo.setSendAddress("<EMAIL>");
        //发件方第三方授权码
        sendVo.setAuthPassword("YDGAPKZOQNACIUKD");
        //发件方名称
//            sendVo.setSendName(invoiceConfig.getSendName());
        sendVo.setSendName("全数通");
        //发件方邮件服务器
        sendVo.setSmtpServer("smtp.163.com");
        //邮件服务器端口
        sendVo.setPort("25");
        //发送邮件
        try {
//            MessageSenderUtil messageSenderUtil1 = new MessageSenderUtil();
//            String errorMsg = messageSenderUtil1.sendMailExecutor(mailContent, sendVo);
            String errorMsg = messageSenderUtil.sendMailExecutor(mailContent, sendVo);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(errorMsg)) {
                resultMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
                resultMap.put(OrderManagementConstant.ALL_MESSAGE, errorMsg);
                return resultMap;
            }
        } catch (Exception e) {
            log.error("邮件发送失败：" + e.getMessage());
            resultMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
            resultMap.put(OrderManagementConstant.ALL_MESSAGE, "邮件发送失败：" + e.getMessage());
            return resultMap;
        }
        resultMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_0000);
        resultMap.put(OrderManagementConstant.ALL_MESSAGE, "邮件发送成功");

        return resultMap;
    }

    private EmailContent buildEmailContent(OrderInvoiceInfoEntity orderInvoiceInfo, List<String> emails, String pdfFile) {
        //获取Calendar的方法
        Calendar c = Calendar.getInstance();
        //将Calendar的时间改成日期的时间
        c.setTime(orderInvoiceInfo.getKprq());
        //获取当前年份
        int mYear = c.get(Calendar.YEAR);
        //获取当前月份
        int mMonth = c.get(Calendar.MONTH) + 1;
        //获取当前月份的日期号码
        int mDay = c.get(Calendar.DAY_OF_MONTH);
        //主题
        EmailContent emailContent = new EmailContent();
        emailContent.setTemplateId("53");
        //新增查询销项后台获取企业是否配置过定制模板,如果说配置使用新的模板id
        emailContent.setSerialNum(RandomUtil.randomNumbers(ConfigureConstant.INT_25));
//        emailContent.setSubjects(new String[]{orderInvoiceInfo.getFphm()});
        emailContent.setSubjects(new String[]{"您收到一张【" + orderInvoiceInfo.getXhfMc() + "】开具的发票【发票号码：" + orderInvoiceInfo.getFphm() + "】"});
//        emailContent.setContents(new String[]{sdf1.format(orderInvoiceInfo.getDdscrq()), "", String.valueOf(orderInvoiceInfo.getFphm()),
//                sdf1.format(orderInvoiceInfo.getKprq()), orderInvoiceInfo.getGhfMc(), orderInvoiceInfo.getEndJshj()});
        emailContent.setContents(new String[]{String.valueOf(mYear), String.valueOf(mMonth), String.valueOf(mDay),orderInvoiceInfo.getFphm(), orderInvoiceInfo.getXhfMc(),orderInvoiceInfo.getGhfMc(), orderInvoiceInfo.getJshj()});
        //添加邮件地址
        emailContent.setTo(Convert.toStrArray(emails));

        /**
         * 生成邮件附件
         */
        List<Attachments> attachmentsList = createFileAttachementsList(orderInvoiceInfo,pdfFile );

        emailContent.setAttachments(attachmentsList.toArray(new Attachments[0]));
        return emailContent;
    }


    static List<Attachments> createFileAttachementsList(OrderInvoiceInfoEntity orderInvoiceInfo, String pdfFile) {

        //添加邮件附件,支持多个附件
        List<Attachments> attachmentsList = new ArrayList<>();

        String suffix = ConfigureConstant.STRING_SUFFIX_PDF;
        String applicationType = ConfigureConstant.STRING_APPLICATION_PDF;

        log.info("文件类型:{},文件后缀:{},applicationType:{}", "PDF", suffix, applicationType);
        Attachments attachments = new Attachments();
        attachments.setContent(pdfFile);
        String attachFileName = orderInvoiceInfo.getFphm() + suffix;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(orderInvoiceInfo.getFpdm())) {
            attachFileName = orderInvoiceInfo.getFphm() + suffix;
        }
        attachments.setName(attachFileName);
        attachments.setType(applicationType);
        attachmentsList.add(attachments);


        return attachmentsList;
    }


//    public static void main(String[] args) {
//        OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
//        orderInvoiceInfoEntity.setKprq(new Date());
//        orderInvoiceInfoEntity.setDdscrq(new Date());
//        orderInvoiceInfoEntity.setFphm("11001001010");
//        orderInvoiceInfoEntity.setGhfMc("购房名称");
//        orderInvoiceInfoEntity.setXhfMc("我开的");
//        orderInvoiceInfoEntity.setEndJshj("999.99");
//        ArrayList<String> objects = new ArrayList<>();
//        objects.add("<EMAIL>");
//        EmailContent emailContent = buildEmailContent(orderInvoiceInfoEntity, objects, "7923913274937249731294871392");

//        Map<String, Object> sendEmail = sendEmail(emailContent);
//    }

    /**
     * 推送数据到指定URL - 发票推送
     *
     * @param pushUrl 推送地址
     * @param orderInvoiceInfo 发票信息
     * @return map
     */
    @Override
    public Map<String, Object> sendPushData(String pushUrl, OrderInvoiceInfoEntity orderInvoiceInfo,String tenantCode) {
        log.info("开票完成自动回推，推送数据入参：pushUrl: {}，orderInvoiceInfo：{}", pushUrl, orderInvoiceInfo);
        //定义回调接收实体
        Map<String, Object> returnMap = new HashMap<>(10);
        String qdfphm = orderInvoiceInfo.getQdfphm();
        try {
            InvoiceIssueInfoParam invoiceIssueInfoParam = new InvoiceIssueInfoParam();
            //对象转换
            convertOrderInvoiceInfo(orderInvoiceInfo,invoiceIssueInfoParam);
            // 将 invoiceIssueInfoParam 对象转换为JSON字符串
            String invoiceInfoJson = JacksonUtils.toJsonString(invoiceIssueInfoParam);
            log.info("开票完成自动回推，推送数据-原始数据: {}", invoiceInfoJson);
            
            // 加密数据
            String zipCode = "0"; // 不使用压缩
            String encryptCode = "0"; // 不使用加密
            Map<String, String> secret = getSecret("",tenantCode);
            String secretKey = "";
            //获取密钥key
            if (secret != null) {
                secretKey = secret.get("secretKey");
            }
            // 获取CommonInterfaceService
            String encryptedData = commonInterfaceService.commonEncrypt(zipCode, encryptCode, invoiceInfoJson, secretKey);
            log.info("开票完成自动回推，推送数据-加密后数据: {}", encryptedData);
            
            // 构造请求参数
            TreeMap<String, String> sortMap = new TreeMap<>();
            sortMap.put(ConfigurerInfo.NONCE, UUID.randomUUID().toString().replaceAll("-", ""));
            sortMap.put(ConfigurerInfo.SECRETID, UUID.randomUUID().toString().replaceAll("-", ""));
            sortMap.put(ConfigurerInfo.TIMESTAMP, String.valueOf(System.currentTimeMillis()));
            sortMap.put(ConfigurerInfo.CONTENT, encryptedData);
            sortMap.put(ConfigurerInfo.ENCRYPTCODE, encryptCode);
            sortMap.put(ConfigurerInfo.ZIPCODE, zipCode);
            String signStr = HmacSha1Util.genSign(pushUrl, sortMap, secretKey);
            log.info("开票完成自动回推，最终生成签名为:{}",signStr);
            sortMap.put(ConfigurerInfo.SIGNATURE,signStr);
            //请求头
            Map<String,String> requestHeaders = new HashMap<>();
            requestHeaders.put("Content-Type","application/x-www-form-urlencoded");
            requestHeaders.put("endId",orderInvoiceInfo.getNsrsbh());
            // 发送请求
            log.info("1、发票号码: {},开票完成自动回推，请求地址: {},请求数据: {}",qdfphm,pushUrl, sortMap);
            String pushResponse = HttpUtils.doPostFormWithHeader(pushUrl, requestHeaders,sortMap);
            log.info("2、开票完成自动回推，响应数据: {}", pushResponse);
            //解密响应数据
            Result result = JacksonUtils.parseObject(pushResponse, Result.class);
            ResponseStatus responseStatus = (ResponseStatus)result.get(ConfigurerInfo.RESPONSESTATUS);
            if(responseStatus.getCode().equals(ConfigureConstant.STRING_0000)){
                log.debug("3.1、发票号码:{},开票完成自动回推，接收成功",qdfphm);
                ResponseData responseData = (ResponseData)result.get(ConfigurerInfo.RESPONSEDATA);
                String commonDecrypt = commonInterfaceService.commonDecrypt(zipCode,encryptCode,responseData.getContent(),secretKey);
                log.debug("4、开票完成自动回推，解密content结果：{}", commonDecrypt);
                InvoiceIssueRes invoiceIssueRes = JacksonUtils.parseObject(commonDecrypt, InvoiceIssueRes.class);
                if(invoiceIssueRes.getZtdm().equals(ConfigureConstant.STRING_0000)){
                    log.debug("5.1、发票号码:{},开票完成自动回推，响应成功",qdfphm);
                    returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_0000);
                    returnMap.put(OrderManagementConstant.ALL_MESSAGE, "推送数据成功");
                }else {
                    log.debug("5.2、发票号码:{},开票完成自动回推，响应失败",qdfphm);
                    returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
                    returnMap.put(OrderManagementConstant.ALL_MESSAGE, "推送响应数据失败: "+invoiceIssueRes.getZtxx());
                }
            }else {
                log.debug("3.2、发票号码:{},开票完成自动回推，接收失败",qdfphm);
                returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
                returnMap.put(OrderManagementConstant.ALL_MESSAGE, "推送接收数据失败: "+responseStatus.getMessage());
            }
        } catch (Exception e) {
            log.error("发票号码:{},开票完成自动回推，推送数据异常: {}",qdfphm,e);
            returnMap.put(OrderManagementConstant.CODE, ConfigureConstant.STRING_9999);
            returnMap.put(OrderManagementConstant.ALL_MESSAGE, "推送数据异常：" + e.getMessage());
        }
        log.info("开票完成自动回推结束，返回为：{}", JacksonUtils.toJsonString(returnMap));
        return returnMap;
    }

    /**
     * 对象转换
     * @param orderInvoiceInfo
     * @param invoiceIssueInfoParam
     */
    private void convertOrderInvoiceInfo(OrderInvoiceInfoEntity orderInvoiceInfo, InvoiceIssueInfoParam invoiceIssueInfoParam) {
        //主对象转换
        BeanUtils.copyProperties(orderInvoiceInfo,invoiceIssueInfoParam);
        invoiceIssueInfoParam.setDdqqlsh(orderInvoiceInfo.getFpqqlsh());
        invoiceIssueInfoParam.setTdywys(orderInvoiceInfo.getTdyw());
        invoiceIssueInfoParam.setFplxdm(orderInvoiceInfo.getFpzlDm());
        invoiceIssueInfoParam.setGmfsbh(orderInvoiceInfo.getGhfNsrsbh());
        invoiceIssueInfoParam.setGmfmc(orderInvoiceInfo.getGhfMc());
        invoiceIssueInfoParam.setGmfdz(orderInvoiceInfo.getGhfDz());
        invoiceIssueInfoParam.setGmfdh(orderInvoiceInfo.getGhfDh());
        invoiceIssueInfoParam.setGmfyh(orderInvoiceInfo.getGhfYh());
        invoiceIssueInfoParam.setGmfzh(orderInvoiceInfo.getGhfZh());
        invoiceIssueInfoParam.setGmjfsjh(orderInvoiceInfo.getGhfSj());
        invoiceIssueInfoParam.setGmjfyx(orderInvoiceInfo.getGhfYx());
        invoiceIssueInfoParam.setJbrxm(orderInvoiceInfo.getGmfjbrxm());
        invoiceIssueInfoParam.setHjje(orderInvoiceInfo.getHjbhsje());
        invoiceIssueInfoParam.setHjse(orderInvoiceInfo.getKpse());
        invoiceIssueInfoParam.setDdsj(DateUtil.format(orderInvoiceInfo.getDdscrq(), DatePattern.NORM_DATETIME_FORMAT));
        //附加信息转换
        List<InvoiceAdditionInfoEntity> infoEntityList = orderInvoiceInfo.getInfoEntityList();
        if(CollectionUtils.isNotEmpty(infoEntityList)){
            List<InvoiceIssueAdditionParam> additionParams = new ArrayList<>();
            for (InvoiceAdditionInfoEntity additionInfoEntity : infoEntityList) {
                InvoiceIssueAdditionParam additionParam = new InvoiceIssueAdditionParam();
                BeanUtils.copyProperties(additionInfoEntity,additionParam);
                additionParams.add(additionParam);
            }
            invoiceIssueInfoParam.setDdfjxx(additionParams);
        }
        //订单明细转换
        List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfo.getItemEntityList();
        if(CollectionUtils.isNotEmpty(itemEntityList)){
            List<InvoiceIssueItemParam> itemParams = new ArrayList<>();
            for (OrderInvoiceItemEntity itemEntity : itemEntityList) {
                InvoiceIssueItemParam itemParam = new InvoiceIssueItemParam();
                BeanUtils.copyProperties(itemEntity,itemParam);
                itemParam.setSpsl(itemEntity.getXmsl());
                itemParams.add(itemParam);
            }
            invoiceIssueInfoParam.setDdmxxx(itemParams);
        }
        //特定业务转换
        InvoiceTdywEntity tdywEntity = orderInvoiceInfo.getInvoiceTdywEntity();
        InvoiceIssueTdywParam tdywParam = new InvoiceIssueTdywParam();
        invoiceTdywService.copyTdywEntityToParam(tdywEntity,tdywParam);
        invoiceIssueInfoParam.setInvoiceIssueTdywParam(tdywParam);
        //差额征收转换
        List<InvoiceCezsEntity> cezslist = orderInvoiceInfo.getCezslist();
        if(CollectionUtils.isNotEmpty(cezslist)){
            List<InvoiceIssueCezsParam> cezsParams = new ArrayList<>();
            for (InvoiceCezsEntity cezsEntity : cezslist) {
                InvoiceIssueCezsParam cezsParam = new InvoiceIssueCezsParam();
                BeanUtils.copyProperties(cezsEntity,cezsParam);
                cezsParams.add(cezsParam);
            }
            invoiceIssueInfoParam.setCezslist(cezsParams);
        }
    }

}