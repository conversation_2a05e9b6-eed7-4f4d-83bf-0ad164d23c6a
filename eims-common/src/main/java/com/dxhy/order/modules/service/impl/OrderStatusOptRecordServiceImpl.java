package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.OrderStatusOptRecordDao;
import com.dxhy.order.modules.entity.OrderStatusOptRecordEntity;
import com.dxhy.order.modules.service.OrderStatusOptRecordService;
import com.dxhy.order.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("orderStatusOptRecordService")
public class OrderStatusOptRecordServiceImpl extends ServiceImpl<OrderStatusOptRecordDao, OrderStatusOptRecordEntity> implements OrderStatusOptRecordService {

    @Autowired
    OrderStatusOptRecordDao orderStatusOptRecordDao;

    @Override
    public PageUtils queryPage(OrderStatusOptRecordEntity orderStatusOptRecordEntity) {
        Page page = new Page(orderStatusOptRecordEntity.getCurrPage(),orderStatusOptRecordEntity.getPageSize());
        List<OrderStatusOptRecordEntity> list = orderStatusOptRecordDao.selectList(page,orderStatusOptRecordEntity);
        page.setRecords(list);
        return new PageUtils(page);
    }

}
