package com.dxhy.order.modules.pojo.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 树形结构BO 用于客户信息分类树和项目信息分类树
 * <AUTHOR>
 * @Date 2022/6/28 16:34
 * @Version 1.0
 **/
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("树形结构BO")
public class GroupTreeBO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private String id;

	/**
	 * 上级ID
	 */
	@ApiModelProperty("上级ID")
	private String parentId;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String label;

	/**
	 * 子节点
	 */
	@ApiModelProperty("子节点")
	private List<GroupTreeBO> children;

	/**
	 * 生成根节点时候用
	 * @param
	 * @return
	 * <AUTHOR>
	 **/
	public GroupTreeBO(String _label){
		this.id = "0";
		this.parentId = "-1";
		this.label = _label;
		this.children = new ArrayList<>();
	}
}
