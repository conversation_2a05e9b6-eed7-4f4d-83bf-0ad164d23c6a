package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.modules.dao.OrderInvoiceHistoryDao;
import com.dxhy.order.modules.dao.OrderInvoiceInfoDao;
import com.dxhy.order.modules.entity.OrderInvoiceHistoryEntity;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.service.OrderInvoiceHistoryService;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.modules.service.OrderInvoiceItemService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.pojo.InvoiceBaseInfoRes;
import com.dxhy.order.pojo.InvoiceInfoReq;
import com.dxhy.order.pojo.InvoiceInfoRes;
import com.dxhy.order.pojo.InvoiceItemInfoRes;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;


@Service("orderInvoiceHistoryService")
@Slf4j
public class OrderInvoiceHistoryServiceImpl extends ServiceImpl<OrderInvoiceHistoryDao, OrderInvoiceHistoryEntity> implements OrderInvoiceHistoryService{

    @Autowired
    OrderInvoiceHistoryDao orderInvoiceHistoryDao;
    @Autowired
    OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    OrderInvoiceInfoService orderInvoiceInfoService;
    @Autowired
    OrderInvoiceItemService orderInvoiceItemService;
    @Resource
    private SsoUtil ssoUtil;
    @Resource(name = ConfigurerInfo.COMMONTHREADPOOL)
    private Executor executor;
    @Value("${order.encrypt.invoiceInfosAndItemsUrl}")
    private String invoiceInfosAndItemsUrl;
    @Override
    public R queryPage(OrderInvoiceHistoryEntity orderInvoiceHistoryEntity) {
        log.info("历史发票下载列表请求参数为:{}", JacksonUtils.toJsonPrettyString(orderInvoiceHistoryEntity));
        String sqqynsrsbh = orderInvoiceHistoryEntity.getSqqynsrsbh();
        if(StringUtils.isBlank(sqqynsrsbh)){
            return R.error("请输入查询的纳税人识别号");
        }
        Page page = new Page(orderInvoiceHistoryEntity.getCurrPage(), orderInvoiceHistoryEntity.getPageSize());
        String kprqqStr = orderInvoiceHistoryEntity.getKprqq();
        String kprqzStr = orderInvoiceHistoryEntity.getKprqz();
        LambdaQueryWrapper<OrderInvoiceHistoryEntity> historyWrapper = Wrappers.lambdaQuery();
        List<String> nsrsbhList = Arrays.asList(StringUtils.split(sqqynsrsbh, ","));
        //申请企业纳税人识别号 必传
        historyWrapper.in(OrderInvoiceHistoryEntity::getSqqynsrsbh, nsrsbhList);
        //发票种类代码 非必传
        historyWrapper.eq(StringUtils.isNotBlank(orderInvoiceHistoryEntity.getFpzldm()),  OrderInvoiceHistoryEntity::getFpzldm, orderInvoiceHistoryEntity.getFpzldm());
        //业务类型 非必传 进项、销项
        historyWrapper.eq(StringUtils.isNotBlank(orderInvoiceHistoryEntity.getYwlx()),  OrderInvoiceHistoryEntity::getYwlx, orderInvoiceHistoryEntity.getYwlx());
        //归集状态 非必传 归集中、归集成功、归集失败
        historyWrapper.eq(StringUtils.isNotBlank(orderInvoiceHistoryEntity.getGjzt()),  OrderInvoiceHistoryEntity::getGjzt, orderInvoiceHistoryEntity.getGjzt());
        if(StringUtils.isNotBlank(kprqqStr)){
            //开票日期起
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate kprqq = LocalDate.parse(kprqqStr, formatter);
            historyWrapper.ge(OrderInvoiceHistoryEntity::getKprqq, kprqq);
        }
        if (StringUtils.isNotBlank(kprqzStr)){
            //开票日期止
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate kprqz = LocalDate.parse(kprqzStr, formatter);
            historyWrapper.le(OrderInvoiceHistoryEntity::getKprqz, kprqz);
        }
        historyWrapper.orderByDesc(OrderInvoiceHistoryEntity::getCreatedTime);
        Page resultPage = orderInvoiceHistoryDao.selectPage(page, historyWrapper);
        return R.ok().put("page", new PageUtils(resultPage));
    }

    @Override
    public void downLoad(OrderInvoiceHistoryEntity orderInvoiceHistoryEntity) {
        //获取当前登录用户名
        String userName = ssoUtil.getUserName();
        //不管归集成功与否先落库数据
        if(StringUtils.isNotBlank(orderInvoiceHistoryEntity.getId())){
            //历史任务重新触发
            OrderInvoiceHistoryEntity oldInvoiceHistory = orderInvoiceHistoryDao.selectById(orderInvoiceHistoryEntity.getId());
            oldInvoiceHistory.setUpdatedBy(userName);
            oldInvoiceHistory.setUpdatedTime(new Date());
            orderInvoiceHistoryDao.updateById(oldInvoiceHistory);
            BeanUtils.copyProperties(oldInvoiceHistory, orderInvoiceHistoryEntity);
        }else {
            //新任务落库
            String id = DistributedKeyMaker.generateShotKey();
            orderInvoiceHistoryEntity.setId(id);
            orderInvoiceHistoryEntity.setUpdatedBy(userName);
            orderInvoiceHistoryEntity.setCreatedBy(userName);
            orderInvoiceHistoryEntity.setCreatedTime(new Date());
            orderInvoiceHistoryEntity.setUpdatedTime(new Date());
            orderInvoiceHistoryEntity.setIsDelete("0");
            orderInvoiceHistoryEntity.setGjzt("未开始");
            orderInvoiceHistoryEntity.setZtms("");
            orderInvoiceHistoryEntity.setXztd("税航RPA");
            orderInvoiceHistoryDao.insert(orderInvoiceHistoryEntity);
        }
        //启用线程池异步执行下载任务，不必等待任务执行完毕
        CompletableFuture.supplyAsync(() -> {
            String result = syncInvoiceList(orderInvoiceHistoryEntity,userName);
            return result;
        },executor).exceptionally(ex -> {
            // 捕获异常并封装失败信息
            log.error("请求流水号为:【"+orderInvoiceHistoryEntity.getId()+"】的任务下载历史发票失败，原因: " + ex.getMessage());
            return "线程池下载历史发票信息发生异常";
        });
    }

    /**
     * 同步发票列表 返回成功或失败原因
     * @param orderInvoiceHistoryEntity
     * @return
     */
    private String syncInvoiceList(OrderInvoiceHistoryEntity orderInvoiceHistoryEntity,String userName){
        log.info("发票历史下载入参：{}", JsonUtils.getInstance().toJsonString(orderInvoiceHistoryEntity));
        String result = ConfigurerInfo.SUCCSSCODE;
        //先更新任务状态为进行中
        orderInvoiceHistoryEntity.setGjzt("归集中");
        orderInvoiceHistoryEntity.setUpdatedBy(StringUtils.isNotBlank(userName) ? userName : "admin");
        orderInvoiceHistoryEntity.setUpdatedTime(new Date());
        orderInvoiceHistoryDao.updateById(orderInvoiceHistoryEntity);
        try {
            InvoiceInfoReq invoiceInfoReq = new InvoiceInfoReq();
            invoiceInfoReq.setGjbq("销项".equals(orderInvoiceHistoryEntity.getYwlx()) ? "1" : "2");
            invoiceInfoReq.setNsrsbh(orderInvoiceHistoryEntity.getSqqynsrsbh());
            invoiceInfoReq.setFpztDm(new String[]{"01"});
            invoiceInfoReq.setFplyDm("0");
            invoiceInfoReq.setFplxDm(orderInvoiceHistoryEntity.getFpzldm().split(","));
            invoiceInfoReq.setKprqq(orderInvoiceHistoryEntity.getKprqq());
            invoiceInfoReq.setKprqz(orderInvoiceHistoryEntity.getKprqz());
            invoiceInfoReq.setCurrent("1");
            invoiceInfoReq.setSize("100");

            String jsonString = JsonUtils.getInstance().toJsonString(invoiceInfoReq);
            log.info("历史发票查询 invoiceInfoReq 远程调用入参: {}", jsonString);
            long lo = System.currentTimeMillis();
            String res = HttpUtils.doPost(invoiceInfosAndItemsUrl, jsonString);
            log.info("历史发票查询 invoiceInfoReq 远程调用 耗时: {}", System.currentTimeMillis() - lo);
            log.info("历史发票查询invoiceInfoReq远程调用出参res: {}", res);
            R r = JsonUtils.getInstance().fromJson(res, R.class);
            log.info("历史发票查询远程调用出参-R: {}", r);
            if ("0000".equals(r.get("code").toString())) {
                String data = r.get("data").toString();
                log.info("历史发票查询远程调用出参data: {}", data);
                InvoiceInfoRes invoiceInfoRes = JsonUtils.getInstance().parseObject(data, InvoiceInfoRes.class);
                log.info("历史发票查询远程调用出参invoiceInfoRes: {}", invoiceInfoRes);
                String totalRows = invoiceInfoRes.getTotal();
                String perSize = invoiceInfoRes.getSize();
                String totalPages = invoiceInfoRes.getPages();
                log.info("第一次分页查询，总数据量{},总页数{},每页数量{}", totalRows,totalPages,perSize);
                //遍历分页查询
                for (int i = 1; i <= Integer.parseInt(totalPages); i++) {
                    if(i > 1){
                        //第一次不用查，上边已经查过了
                        invoiceInfoReq.setCurrent(String.valueOf(i));
                        jsonString = JsonUtils.getInstance().toJsonString(invoiceInfoReq);
                        log.info("第{}次历史发票分页查询 invoiceInfoReq 远程调用入参: {}",i,jsonString);
                        res = HttpUtils.doPost(invoiceInfosAndItemsUrl, jsonString);
                        r = JsonUtils.getInstance().fromJson(res, R.class);
                        if ("0000".equals(r.get("code").toString())) {
                            data = r.get("data").toString();
                            invoiceInfoRes = JsonUtils.getInstance().parseObject(data, InvoiceInfoRes.class);
                            log.info("后续分页查询，页数{},每页数量{}",i,perSize);
                        }else {
                            result = r.get("msg").toString();
                        }
                    }
                    if (Objects.nonNull(invoiceInfoRes) && !CollectionUtils.isEmpty(invoiceInfoRes.getRecords())) {
                        List<InvoiceBaseInfoRes> records = invoiceInfoRes.getRecords();
                        List<OrderInvoiceInfoEntity> invoiceInfoEntities = new ArrayList<>();
                        List<OrderInvoiceItemEntity> invoiceItemEntities = new ArrayList<>();
                        for (InvoiceBaseInfoRes invoiceBaseInfoRes : records) {
                            // 查询数据库 是否存在相同全电号码数据 不存在则入库
                            OrderInvoiceInfoEntity info = orderInvoiceInfoDao.seleInvoiceInfoByQdfphmIsDele(invoiceBaseInfoRes.getQdfphm());
                            if (Objects.isNull(info)) {
                                OrderInvoiceInfoEntity invoiceInfoEntity = new OrderInvoiceInfoEntity();
                                String id = DistributedKeyMaker.generateShotKey();
                                invoiceInfoEntity.setId(id);
                                invoiceInfoEntity.setPch(DistributedKeyMaker.generateShotKey());
                                invoiceInfoEntity.setFpqqlsh(DistributedKeyMaker.generateShotKey());
                                invoiceInfoEntity.setDdh(DistributedKeyMaker.generateShotKey());
                                invoiceInfoEntity.setGhfMc(invoiceBaseInfoRes.getGmfmc());
                                invoiceInfoEntity.setGhfNsrsbh(invoiceBaseInfoRes.getGmfnsrsbh());
                                invoiceInfoEntity.setGhfQylx("01");
                                invoiceInfoEntity.setKplx("0");
                                invoiceInfoEntity.setChBz("0");
                                // 1-销项发票； 2-进项发票
                                invoiceInfoEntity.setGjbq(orderInvoiceHistoryEntity.getYwlx());
                                invoiceInfoEntity.setXhfMc(invoiceBaseInfoRes.getXsfmc());
                                invoiceInfoEntity.setXhfNsrsbh(invoiceBaseInfoRes.getXsfnsrsbh());
                                invoiceInfoEntity.setSflzfp(invoiceBaseInfoRes.getSflzfp());
                                invoiceInfoEntity.setHjbhsje(org.springframework.util.StringUtils.isEmpty(invoiceBaseInfoRes.getHjje()) ? "0.00" : invoiceBaseInfoRes.getHjje());
                                invoiceInfoEntity.setKpse(org.springframework.util.StringUtils.isEmpty(invoiceBaseInfoRes.getHjse()) ? "0.00" : invoiceBaseInfoRes.getHjse());
                                BigDecimal hjje = new BigDecimal(invoiceInfoEntity.getHjbhsje());
                                BigDecimal hjse = new BigDecimal(invoiceInfoEntity.getKpse());
                                // 不含税考虑
                                invoiceInfoEntity.setJshj(hjje.add(hjse).toString());
                                invoiceInfoEntity.setKpzt("2");
                                Date date = transDate(invoiceBaseInfoRes.getKprq());
                                invoiceInfoEntity.setKprq(date);
                                invoiceInfoEntity.setDdly("2");
                                invoiceInfoEntity.setBz(invoiceBaseInfoRes.getBz());
                                invoiceInfoEntity.setDdzt("0");
                                invoiceInfoEntity.setHsbz("0");
                                invoiceInfoEntity.setQdfphm(invoiceBaseInfoRes.getQdfphm());
                                invoiceInfoEntity.setFpdm(invoiceBaseInfoRes.getFpdm());
                                invoiceInfoEntity.setFphm(invoiceBaseInfoRes.getFphm());
                                invoiceInfoEntity.setFpzlDm(invoiceBaseInfoRes.getFplxDm());
                                invoiceInfoEntity.setEwm(invoiceBaseInfoRes.getEwm());
                                invoiceInfoEntity.setKpr(invoiceBaseInfoRes.getKpr());
                                invoiceInfoEntity.setFply(invoiceBaseInfoRes.getFplyDm());
                                invoiceInfoEntity.setFpzt(invoiceBaseInfoRes.getFpztDm());
                                if ("01".equals(invoiceBaseInfoRes.getFpztDm())) {
                                    invoiceInfoEntity.setChBz("0");
                                } else if ("02".equals(invoiceBaseInfoRes.getFpztDm())) {
                                    invoiceInfoEntity.setZfBz("1");
                                } else if ("03".equals(invoiceBaseInfoRes.getFpztDm())) {
                                    invoiceInfoEntity.setChBz("1");
                                } else if ("04".equals(invoiceBaseInfoRes.getFpztDm())) {
                                    invoiceInfoEntity.setChBz("4");
                                }
                                invoiceInfoEntity.setTdyw(invoiceBaseInfoRes.getTdyslxDm());
                                invoiceInfoEntity.setKpfnsrsbh(invoiceBaseInfoRes.getKpfnsrsbh());
                                invoiceInfoEntity.setFppzdm(invoiceBaseInfoRes.getFppzDm());
                                invoiceInfoEntity.setCezslxdm(invoiceBaseInfoRes.getCezslxDm());
                                invoiceInfoEntity.setIsDelete("0");
                                invoiceInfoEntity.setCreateTime(new Date());
                                invoiceInfoEntities.add(invoiceInfoEntity);
                                //插入明细表
                                List<InvoiceItemInfoRes> items = invoiceBaseInfoRes.getItems();
                                for (InvoiceItemInfoRes invoiceItemInfoRes : items) {
                                    OrderInvoiceItemEntity invoiceInfoItemEntity = new OrderInvoiceItemEntity();
                                    invoiceInfoItemEntity.setId(DistributedKeyMaker.generateShotKey());
                                    invoiceInfoItemEntity.setOrderInvoiceId(id);
                                    invoiceInfoItemEntity.setSpid(invoiceItemInfoRes.getSSFLBM());
                                    invoiceInfoItemEntity.setSpbm(invoiceItemInfoRes.getSSFLBM());
                                    invoiceInfoItemEntity.setXmmc(invoiceItemInfoRes.getHWHYSLWMC());
                                    invoiceInfoItemEntity.setGgxh(invoiceItemInfoRes.getGGXH());
                                    invoiceInfoItemEntity.setDw(invoiceItemInfoRes.getDW());
                                    invoiceInfoItemEntity.setXmsl(invoiceItemInfoRes.getSL());
                                    invoiceInfoItemEntity.setDj(invoiceItemInfoRes.getDJ());
                                    invoiceInfoItemEntity.setJe(invoiceItemInfoRes.getJE());
                                    invoiceInfoItemEntity.setSl(invoiceItemInfoRes.getSLV());
                                    invoiceInfoItemEntity.setSe(invoiceItemInfoRes.getSE());
                                    invoiceInfoItemEntity.setQdfphm(invoiceBaseInfoRes.getQdfphm());
                                    invoiceItemEntities.add(invoiceInfoItemEntity);
                                }
                            }
                        }
                        //批量保存发票信息
                        orderInvoiceInfoService.saveBatch(invoiceInfoEntities);
                        //批量保存发票明细信息
                        orderInvoiceItemService.saveBatch(invoiceItemEntities);
                    } else {
                        result = "第"+i+"次分页查询获取数据结果为空！";
                        break;
                    }
                }
            } else {
                result = r.get("msg").toString();
            }
        } catch (Exception e) {
            log.error("历史发票查询 getInvoiceBaseInfo异常: {}", e);
            result = e.getMessage();
        }
        if(ConfigurerInfo.SUCCSSCODE.equals(result)){
            //更新为归集成功
            orderInvoiceHistoryEntity.setGjzt("归集成功");
            orderInvoiceHistoryEntity.setZtms("success");
            orderInvoiceHistoryEntity.setUpdatedTime(new Date());
            orderInvoiceHistoryDao.updateById(orderInvoiceHistoryEntity);
        }else {
            //更新为归集失败
            orderInvoiceHistoryEntity.setGjzt("归集失败");
            orderInvoiceHistoryEntity.setZtms(result);
            orderInvoiceHistoryEntity.setUpdatedTime(new Date());
            orderInvoiceHistoryDao.updateById(orderInvoiceHistoryEntity);
        }
        return result;
    }
    private Date transDate(String kprqStr) {
        // String time = "2020-02-13 16:01:30";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(kprqStr);
        } catch (ParseException e) {
            log.error("日期转换失败");
        }
        return date;
    }
}
