package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceLkysfwxxDao;
import com.dxhy.order.modules.entity.InvoiceLkysfwxxEntity;
import com.dxhy.order.modules.service.InvoiceLkysfwxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 旅客运输服务信息的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceLkysfwxxService")
@Slf4j
@RefreshScope
public class InvoiceLkysfwxxServiceImpl extends ServiceImpl<InvoiceLkysfwxxDao, InvoiceLkysfwxxEntity>
        implements InvoiceLkysfwxxService {

    @Override
    public void saveBatch(List<InvoiceLkysfwxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceLkysfwxxEntity> lkysfwxxWrapper = Wrappers.lambdaUpdate();
        lkysfwxxWrapper.set(InvoiceLkysfwxxEntity::getIsDelete, "1");
        lkysfwxxWrapper.eq(InvoiceLkysfwxxEntity::getOrderInvoiceInfoId, invoiceId);
        lkysfwxxWrapper.eq(InvoiceLkysfwxxEntity::getInvoiceTdywId, tdywId);
        this.update(lkysfwxxWrapper);
    }
}




