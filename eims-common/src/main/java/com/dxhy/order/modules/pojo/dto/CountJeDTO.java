package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/12/10 9:26
 */
@Data
@ApiModel("金额合计DTO")
public class CountJeDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty(name = "所属纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 开票状态
     */
    @ApiModelProperty(name = "开票状态")
    private String kpzt;

    /**
     * 总不含税金额
     */
    @ApiModelProperty(name = "总不含税金额")
    private String allBhsje;

    /**
     * 总税额
     */
    @ApiModelProperty(name = "总税额")
    private String allSe;

    /**
     * 总价税合计
     */
    @ApiModelProperty(name = "总价税合计")
    private String allJshj;

    /**
     * 总数
     */
    @ApiModelProperty(name = "总数")
    private String allZs;

}
