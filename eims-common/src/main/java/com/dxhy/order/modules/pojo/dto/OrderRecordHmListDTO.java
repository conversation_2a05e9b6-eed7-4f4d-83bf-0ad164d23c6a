package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/12/15 17:09
 */

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("开票记录-批量交付 DTO")
public class OrderRecordHmListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 纳税人识别号
     **/
    @ApiModelProperty(name = "baseNsrsbh")
    private String baseNsrsbh ;

    /**
     * qdfphm数组
     **/
    @ApiModelProperty(name = "qdfphm")
    private List<String> qdfphm;
}
