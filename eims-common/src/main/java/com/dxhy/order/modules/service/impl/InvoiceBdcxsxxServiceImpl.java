package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceBdcxsxxDao;
import com.dxhy.order.modules.entity.InvoiceBdcxsxxEntity;
import com.dxhy.order.modules.service.InvoiceBdcxsxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 不动产销售信息的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceBdcxsxxService")
@Slf4j
@RefreshScope
public class InvoiceBdcxsxxServiceImpl extends ServiceImpl<InvoiceBdcxsxxDao, InvoiceBdcxsxxEntity>
        implements InvoiceBdcxsxxService {

    @Override
    public void saveBatch(List<InvoiceBdcxsxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceBdcxsxxEntity> bdcxsxxWrapper = Wrappers.lambdaUpdate();
        bdcxsxxWrapper.set(InvoiceBdcxsxxEntity::getIsDelete, "1");
        bdcxsxxWrapper.eq(InvoiceBdcxsxxEntity::getOrderInvoiceInfoId, invoiceId);
        bdcxsxxWrapper.eq(InvoiceBdcxsxxEntity::getInvoiceTdywId, tdywId);
        this.update(bdcxsxxWrapper);
    }
}




