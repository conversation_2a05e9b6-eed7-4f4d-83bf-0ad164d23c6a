package com.dxhy.order.modules.service;

import com.dxhy.order.exception.OrderReceiveException;
import com.dxhy.order.model.OrderQrcodeExtendInfo;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.utils.R;

/**
 * 订单发票通用接口
 *
 * <AUTHOR>
 */
public interface OrderCommonService {
    
    /**
     * 调用主键生成器生成主键 generate primary key
     *
     * @return
     */
    String getGenerateShotKey();
    
    /**
     * 初始化发票数据
     *
     * @param fpqqlsh
     * @param kplsh
     * @param xhfNsrsbh
     * @return
     */
    R initRepeatInvoice(String fpqqlsh, String kplsh, String xhfNsrsbh);
    
    /**
     * 保存订单信息
     */
    void saveQrCodeData(OrderInvoiceInfoEntity orderInvoiceInfo, OrderQrcodeExtendInfo qrcodeExtendInfo) throws OrderReceiveException;
    
}
