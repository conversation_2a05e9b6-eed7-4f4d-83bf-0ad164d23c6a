package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 差额征税信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceIssueCezsParam implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	private String xh;
	/**
	 * 凭证类型
	 */
	private String pzlx;
	/**
	 * 发票代码
	 */
	private String fpdm;
	/**
	 * 发票号码
	 */
	private String fphm;
	/**
	 * 纸质发票号码
	 */
	private String zzfphm;
	/**
	 * 凭证号码
	 */
	private String pzhm;
	/**
	 * 开具日期
	 */
	private String kjrq;
	/**
	 * 合计金额
	 */
	private String hjje;
	/**
	 * 扣除额
	 */
	private String kce;
	/**
	 * 备注
	 */
	private String bz;
	/**
	 * 录入方式
	 */
	private String lrfs;
	/**
	 * 本次扣除金额
	 */
	private String bckcje;
	/**
	 * 凭证合计金额
	 */
	private String pzhjje;
}
