package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.config.OrderConfig;
import com.dxhy.order.constant.ExcelReadContext;
import com.dxhy.order.constant.ExportExcelItemInfoEnum;
import com.dxhy.order.constant.ExportItemInfoEnum;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.ItemGroupDao;
import com.dxhy.order.modules.dao.ItemInfoDao;
import com.dxhy.order.modules.dao.TaxClassCodeDao;
import com.dxhy.order.modules.entity.ItemGroupEntity;
import com.dxhy.order.modules.entity.ItemInfoEntity;
import com.dxhy.order.modules.entity.TaxClassCodeEntity;
import com.dxhy.order.modules.pojo.bo.GroupTreeBO;
import com.dxhy.order.modules.pojo.bo.ItemInfoUpdateExcelBO;
import com.dxhy.order.modules.pojo.dto.*;
import com.dxhy.order.modules.pojo.vo.ExportItemInfoVO;
import com.dxhy.order.modules.pojo.vo.ItemInfoUpdateExcelVO;
import com.dxhy.order.modules.service.ItemInfoService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;

/**
 * 商品信息表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/27 12:13
 * @Version 1.0
 **/
@Service("itemInfoService")
@Slf4j
@RefreshScope
public class ItemInfoServiceImpl extends ServiceImpl<ItemInfoDao, ItemInfoEntity> implements ItemInfoService {

    @Autowired
    private ItemInfoDao itemInfoDao;
    @Autowired
    private ItemGroupDao itemGroupDao;
    @Resource
    private SsoUtil ssoUtil;
    @Resource
    private OrderConfig orderConfig;
    @Value("${order.invoice.rootName.item}")
    private String itemRootName;
    @Autowired
    private TaxClassCodeDao taxClassCodeDao;

    private final static BigDecimal param100 = new BigDecimal("100");
    private final static BigDecimal param0 = new BigDecimal("0");

    @Override
    public GroupTreeBO getGroupTree(String nsrsbh) {
        // 1 根节点不保存到数据库 查询时需要重新生成
        GroupTreeBO baseGroupTreeBO = new GroupTreeBO(itemRootName);
        // 2 判断nsrsbh是否为空 有没有数据  为空或没有数据的时候只返回根节点对象
        if(StringUtils.isEmpty(nsrsbh)){
            return baseGroupTreeBO;
        }
        List<ItemGroupEntity> itemGroupEntityList = itemGroupDao.listByNsrsbh(nsrsbh);
        if(CollectionUtils.isEmpty(itemGroupEntityList)){
            return baseGroupTreeBO;
        }
        // 3 组装树形结构
        Map<String, GroupTreeBO> treeMap = new HashMap<>();
        treeMap.put(baseGroupTreeBO.getId(), baseGroupTreeBO);
        List<GroupTreeBO> groupTreeList = new ArrayList<>();
        for (ItemGroupEntity itemGroupEntity : itemGroupEntityList) {
            GroupTreeBO tempGroupTreeBO = new GroupTreeBO(itemGroupEntity.getId(), itemGroupEntity.getParentId(), itemGroupEntity.getXmflmc(), new ArrayList<>());
            groupTreeList.add(tempGroupTreeBO);
            treeMap.put(itemGroupEntity.getId(), tempGroupTreeBO);
        }
        for (GroupTreeBO groupTreeBO : groupTreeList) {
            GroupTreeBO tempBO = treeMap.get(groupTreeBO.getParentId());
            if(tempBO != null){
                tempBO.getChildren().add(groupTreeBO);
            }
        }
        return treeMap.get(baseGroupTreeBO.getId());
    }

    @Override
    public R saveItemGroup(ItemGroupSaveDTO itemGroupSaveDTO) {
        String checkStr = this.checkItemGroupSaveDTO(itemGroupSaveDTO);
        if(StringUtils.isNotEmpty(checkStr)){
            return R.error(checkStr);
        }
        if(StringUtils.isNotEmpty(itemGroupSaveDTO.getId())){
            // 修改
            ItemGroupEntity updateData = itemGroupDao.selectById(itemGroupSaveDTO.getId());
            if(updateData == null){
                return R.error("数据不存在");
            }
            updateData.setParentId(itemGroupSaveDTO.getParentId());
            updateData.setXmflmc(itemGroupSaveDTO.getXmflmc());
            updateData.setUpdateTime(new Date());
            updateData.setUpdateBy(ssoUtil.getUserName());
            itemGroupDao.updateById(updateData);
            return R.ok();
        }else{
            // 新增
            ItemGroupEntity saveData = new ItemGroupEntity();
            saveData.setId(DistributedKeyMaker.generateShotKey());
            saveData.setParentId(itemGroupSaveDTO.getParentId());
            saveData.setBaseNsrsbh(itemGroupSaveDTO.getBaseNsrsbh());
            saveData.setXmflmc(itemGroupSaveDTO.getXmflmc());
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(ssoUtil.getUserName());
            itemGroupDao.insert(saveData);
            return R.ok().put("data", saveData.getId());
        }
    }

    private String checkItemGroupSaveDTO(ItemGroupSaveDTO itemGroupSaveDTO){
        if(itemGroupSaveDTO == null){
            return "入参为空";
        }
        if(StringUtils.isNotEmpty(itemGroupSaveDTO.getId()) && "0".equals(itemGroupSaveDTO.getId())){
            return "根节点不允许修改";
        }
        if(StringUtils.isEmpty(itemGroupSaveDTO.getParentId())){
            return "上级客户分类为空";
        }
        if(StringUtils.isEmpty(itemGroupSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isEmpty(itemGroupSaveDTO.getXmflmc())){
            return "项目分类名称为空";
        }
        List<ItemGroupEntity> itemGroupEntityList = itemGroupDao.selectListByItemGroupSaveDTO(itemGroupSaveDTO);
        if(CollectionUtils.isEmpty(itemGroupEntityList)){
            return "";
        }
        for (ItemGroupEntity itemGroupEntity : itemGroupEntityList) {
            if(!itemGroupEntity.getParentId().equals(itemGroupSaveDTO.getParentId())){
                continue;
            }
            if(StringUtils.isEmpty(itemGroupSaveDTO.getId())){
                return "项目分类名称重复";
            }
            if(!itemGroupSaveDTO.getId().equals(itemGroupEntity.getId())){
                return "项目分类名称重复.";
            }
        }
        return "";
    }

    @Override
    public R deleteItemGroup(String id) {
        if(StringUtils.isEmpty(id)){
            return R.error("请选择要删除的数据");
        }
        ItemGroupEntity baseData = itemGroupDao.selectById(id);
        if(baseData == null){
            return R.error("请选择要删除的数据.");
        }
        // 1 递归清理项目分类数据
        List<String> itemGroupIdList = itemGroupDao.listIdByParentId(id);
        if(CollectionUtils.isNotEmpty(itemGroupIdList)){
            deleteItemGroup(itemGroupIdList);
        }
        itemGroupDao.deleteById(id);
        // 2 清理项目信息数据
        itemInfoDao.clearDataByNsrsbh(baseData.getBaseNsrsbh());
        return R.ok();
    }

    /**
     * 递归清理项目分类数据
     * @param idList
     * @return void
     * <AUTHOR>
     **/
    private void deleteItemGroup(List<String> idList){
        for (String id : idList) {
            List<String> customerGroupIdList = itemGroupDao.listIdByParentId(id);
            if(CollectionUtils.isNotEmpty(customerGroupIdList)){
                deleteItemGroup(customerGroupIdList);
            }
            itemGroupDao.deleteById(id);
        }
    }

    @Override
    public R queryPage(ItemInfoListDTO itemInfoListDTO) {
        if(StringUtils.isEmpty(itemInfoListDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        Page page = new Page(itemInfoListDTO.getCurrPage(), itemInfoListDTO.getPageSize());
        List<ItemInfoEntity> list = itemInfoDao.selectList(page, itemInfoListDTO);

        list.forEach(tempData -> {
            tempData.setDj(StringUtil.numberFormat2(tempData.getDj()));
            String sl = tempData.getSl();
            if (!sl.contains("%") && !"免税".equals(sl) && !"不征税".equals(sl)){
                sl = this.getPercent(sl, 1);
                tempData.setSl(sl);
            }
        });
        page.setRecords(list);
        PageUtils pageUtil = new PageUtils(page);
        return R.ok().put("data", pageUtil);
    }

    @Override
    public R queryPageWithoutNsrsbh(ItemInfoListDTO itemInfoListDTO) {
        // 开关判断，开启查所有税号
        if(itemInfoListDTO.getCheck().equals("ON")) {
            // 查询税号所属集团的所有企业税号
            long lo = System.currentTimeMillis();
            // 拼接调用参数，为{taxNo: "baseNsrsbh"}
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taxNo", itemInfoListDTO.getBaseNsrsbh());
            log.info("listWithoutId 调用集团所属企业税号查询接口入参: {}", jsonObject);
            // 获取token
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            // 请求头加入token
            String auth = request.getHeader("Authorization");
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", auth);
            log.info("listWithoutId 开始调用集团所属企业税号查询接口");
            String queryAllTaxListByTaxResponseBodyStr = "";
            String url = orderConfig.getQueryAllTaxListByTaxNo() + itemInfoListDTO.getBaseNsrsbh();
//            String url = "http://172.16.60.111:8501/user-base/dept/queryAllTaxListByTax?taxNo=" + itemInfoListDTO.getBaseNsrsbh();
            try {
                ResponseEntity<String> queryAllTaxListByTaxResponseEntity = restTemplate.exchange(
                        url,
                        HttpMethod.POST,
                        new HttpEntity<>(jsonObject, httpHeaders),
                        String.class);
                queryAllTaxListByTaxResponseBodyStr = queryAllTaxListByTaxResponseEntity.getBody();
                log.info("listWithoutId 结束调用集团所属企业税号查询接口 耗时: {}", System.currentTimeMillis() - lo);
                log.info("listWithoutId 调用集团所属企业税号查询接口出参: {}", queryAllTaxListByTaxResponseBodyStr);
                // 将返回的Json转换成查询用的税号list
                JSONObject queryAllTaxListByTaxResponseBody = JSONObject.parseObject(queryAllTaxListByTaxResponseBodyStr);
                String data = queryAllTaxListByTaxResponseBody.getString("data");
                List<String> res = JsonUtils.getInstance().fromJson(data, List.class);
                // 如果获取列表为空，则把当前企业税号加入列表
                if(CollectionUtils.isEmpty(res)){
                    res.add(itemInfoListDTO.getBaseNsrsbh());
                }
                // 设置查询用的税号列表
                itemInfoListDTO.setBaseNsrsbhList(res);
            } catch (Exception e) {
                log.error("queryAllTaxListByTax error: {}", queryAllTaxListByTaxResponseBodyStr, e);
            }
        }
        Page page = new Page(itemInfoListDTO.getCurrPage(), itemInfoListDTO.getPageSize());
        List<ItemInfoEntity> list = itemInfoDao.selectList(page, itemInfoListDTO);
        //list.forEach(tempData -> tempData.setDj(StringUtil.numberFormat2(tempData.getDj())));
        for (ItemInfoEntity item : list) {
            item.setDj(StringUtil.numberFormat2(item.getDj()));
            String sl = item.getSl();
            if (!sl.contains("%")) {
                if (!"免税".equals(sl) && !"不征税".equals(sl)) {
                    sl = this.getPercent(sl, 1);
                    item.setSl(sl);
                }
            }
        }
        page.setRecords(list);
        PageUtils pageUtil = new PageUtils(page);
        return R.ok().put("data", pageUtil);
    }

    private String getPercent(String data, int digit) {
        String format = "";
        if (data.length() == 5) {
            NumberFormat numberFormat = NumberFormat.getPercentInstance();
            digit = 1;
            numberFormat.setMinimumFractionDigits(digit);
            Float aFloat = Float.valueOf(data);
            format = numberFormat.format(aFloat);
        } else {
            NumberFormat numberFormat = NumberFormat.getPercentInstance();
            digit = 0;
            numberFormat.setMinimumFractionDigits(digit);
            Float aFloat = Float.valueOf(data);
            format = numberFormat.format(aFloat);
        }
        return format;
    }

    @Override
    public R listByNameWithoutPage(ItemInfoListByNameWithoutPageDTO itemInfoListByNameWithoutPageDTO) {
        if(StringUtils.isEmpty(itemInfoListByNameWithoutPageDTO.getBaseNsrsbh())){
            R.error("请选择公司主体");
        }
        List<ItemInfoEntity> list = itemInfoDao.listByNameWithoutPage(itemInfoListByNameWithoutPageDTO);
        list.forEach(tempData -> tempData.setDj(StringUtil.numberFormat2(tempData.getDj())));
        return R.ok().put("data", list);
    }

    @Override
    public R saveData(ItemInfoSaveDTO itemInfoSaveDTO) {
        String checkStr = this.checkItemInfoSaveDTO(itemInfoSaveDTO);
        if(StringUtils.isNotEmpty(checkStr)){
            return R.error(checkStr);
        }
        // 查询税收分类编码
        TaxClassCodeEntity classCodeEntity = taxClassCodeDao.getTaxClassCodeSpjc(itemInfoSaveDTO.getSphssflbm());
        if (Objects.isNull(classCodeEntity)){
            return R.error("商品编码不存在！");
        }
        if(StringUtils.isNotEmpty(itemInfoSaveDTO.getId())){
            // 修改
            ItemInfoEntity updateData = itemInfoDao.selectById(itemInfoSaveDTO.getId());
            if(updateData == null){
                return R.error("数据不存在");
            }
            updateData.setParentId(itemInfoSaveDTO.getParentId());
            updateData.setXmmc(itemInfoSaveDTO.getXmmc());
            updateData.setSphssflbm(itemInfoSaveDTO.getSphssflbm());
            updateData.setSphssfljc(itemInfoSaveDTO.getSphssfljc());
            updateData.setYhzcbs(itemInfoSaveDTO.getYhzcbs());
            updateData.setYhzclx(itemInfoSaveDTO.getYhzclx());
            updateData.setSl(itemInfoSaveDTO.getSl());
            updateData.setJm(itemInfoSaveDTO.getJm());
            updateData.setHsbs(itemInfoSaveDTO.getHsbs());
            updateData.setDj(itemInfoSaveDTO.getDj());
            updateData.setDw(itemInfoSaveDTO.getDw());
            updateData.setGgxh(itemInfoSaveDTO.getGgxh());
            updateData.setUpdateTime(new Date());
            updateData.setUpdateBy(ssoUtil.getUserName());
            itemInfoDao.updateById(updateData);
            return R.ok();
        }else{
            // 新增  调用

            ItemInfoEntity saveData = new ItemInfoEntity();
            saveData.setId(DistributedKeyMaker.generateShotKey());
            saveData.setParentId(itemInfoSaveDTO.getParentId());
            saveData.setBaseNsrsbh(itemInfoSaveDTO.getBaseNsrsbh());
            saveData.setXmmc(itemInfoSaveDTO.getXmmc());
            saveData.setSphssflbm(itemInfoSaveDTO.getSphssflbm());
            saveData.setSphssfljc(itemInfoSaveDTO.getSphssfljc());
            saveData.setYhzcbs(itemInfoSaveDTO.getYhzcbs());
            saveData.setYhzclx(itemInfoSaveDTO.getYhzclx());
            saveData.setSl(itemInfoSaveDTO.getSl());
            saveData.setJm(itemInfoSaveDTO.getJm());
            saveData.setHsbs(itemInfoSaveDTO.getHsbs());
            saveData.setDj(itemInfoSaveDTO.getDj());
            saveData.setDw(itemInfoSaveDTO.getDw());
            saveData.setGgxh(itemInfoSaveDTO.getGgxh());
            saveData.setIsDelete("0");
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(ssoUtil.getUserName());
//            String addInvoiceItemUrl = orderConfig.getAddInvoiceItemUrl();
//            String res = HttpUtils.doPost(addInvoiceItemUrl, JsonUtils.getInstance().toJsonString(saveData));
//            R r = JsonUtils.getInstance().parseObject(res, R.class);
//            if ("0000".equals(r.get(OrderManagementConstant.CODE))){
//                itemInfoDao.insert(saveData);
//                return R.ok().put("data", saveData);
//            }else{
//                return R.error("新增商品失败,请联系管理员");
//            }
            itemInfoDao.insert(saveData);
            return R.ok().put("data", saveData);
        }
    }

    private String checkItemInfoSaveDTO(ItemInfoSaveDTO itemInfoSaveDTO){
        if(itemInfoSaveDTO == null){
            return "入参为空";
        }
        if(StringUtils.isEmpty(itemInfoSaveDTO.getParentId())){
            return "项目分类为空";
        }
        if(StringUtils.isEmpty(itemInfoSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isEmpty(itemInfoSaveDTO.getXmmc())){
            return "项目名称为空";
        }
        if(StringUtils.isEmpty(itemInfoSaveDTO.getSphssflbm())){
            return "商品和服务税收分类编码为空";
        }
        if(StringUtils.isEmpty(itemInfoSaveDTO.getSphssfljc())){
            return "商品和服务税收分类简码为空";
        }
        if(StringUtils.isEmpty(itemInfoSaveDTO.getSl())){
            return "税率为空";
        }
        if(StringUtils.isNotEmpty(itemInfoSaveDTO.getYhzcbs())){
            if(itemInfoSaveDTO.getYhzcbs().length() > 1){
                return "优惠政策标识不合法";
            }
            if(!"01".contains(itemInfoSaveDTO.getYhzcbs())){
                return "优惠政策标识不合法.";
            }
        }
        if(StringUtils.isNotEmpty(itemInfoSaveDTO.getHsbs())){
            if(itemInfoSaveDTO.getHsbs().length() > 1){
                return "含税标识不合法";
            }
            if(!"01".contains(itemInfoSaveDTO.getHsbs())){
                return "含税标识不合法.";
            }
        }
        List<ItemInfoEntity> itemInfoEntityList = itemInfoDao.selectListByItemInfoSaveDTO(itemInfoSaveDTO);
        if(CollectionUtils.isNotEmpty(itemInfoEntityList)){
            if(itemInfoEntityList.size() > 1){
                return "项目信息重复";
            }
            if(StringUtils.isEmpty(itemInfoSaveDTO.getId())){
                return "项目信息重复.";
            }
            if(!itemInfoSaveDTO.getId().equals(itemInfoEntityList.get(0).getId())){
                return "项目信息重复..";
            }
        }
        return "";
    }

    @Override
    public R deleteData(String id) {
        if(StringUtils.isEmpty(id)){
            return R.error("请选择要删除的数据");
        }
        ItemInfoEntity itemInfoEntity = itemInfoDao.selectById(id);
        if(itemInfoEntity == null){
            return R.error("数据不存在");
        }
        itemInfoEntity.setIsDelete("1");
        itemInfoEntity.setUpdateTime(new Date());
        itemInfoDao.updateById(itemInfoEntity);
        return R.ok();
    }

    @Override
    public void exportData(List<String> idList, HttpServletResponse response) {
        List<ItemInfoEntity> itemInfoEntityList = itemInfoDao.selectBatchIds(idList);
        if(CollectionUtils.isEmpty(itemInfoEntityList)){
            return ;
        }
        String fileName = "项目信息";
        log.debug("开始导出{}excel", fileName);
        OutputStream out = null;
        String filePrefix = DateUtil.format(new Date(), OrderInfoContentEnum.DATE_FORMAT_DATE_YMDHMS);
        try {
            response.setContentType("octets/stream");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + filePrefix + ".xlsx");
            out = response.getOutputStream();
            List<ExportItemInfoVO> exportItemInfoVOList = this.genExportItemInfoVOList(idList, itemInfoEntityList);
            exportExportItemInfoList(out, exportItemInfoVOList, fileName);
        } catch (Exception e) {
            log.error("(导出{}excel)，出现异常", fileName, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

            } catch (Exception e) {
                log.error("(导出{}excel)，流关闭异常", fileName, e);
            }
        }
    }

    /**
     * 将 List<ItemInfoEntity> 转为 List<ExportItemInfoVO>
     **/
    private List<ExportItemInfoVO> genExportItemInfoVOList(List<String> idList, List<ItemInfoEntity> itemInfoEntityList){
        List<ExportItemInfoVO> exportExportItemInfoVOList = new LinkedList<>();
        List<Map<String, String>> idAndParentNameMapList = itemInfoDao.listParentNameByIdList(idList);
        Map<String, String> idAneParentNameMap = new HashMap<>();
        idAndParentNameMapList.forEach(tempData -> idAneParentNameMap.put(tempData.get("id"), tempData.get("xmflmc")));
        for (ItemInfoEntity itemInfoEntity : itemInfoEntityList) {
            ExportItemInfoVO tempExportItemInfoVO = new ExportItemInfoVO();
            tempExportItemInfoVO.setXmmc(itemInfoEntity.getXmmc());
            tempExportItemInfoVO.setSphssfljc(itemInfoEntity.getSphssfljc());
            tempExportItemInfoVO.setSphssflbm(itemInfoEntity.getSphssflbm());
            tempExportItemInfoVO.setDj(StringUtil.numberFormat2(itemInfoEntity.getDj()));
            if("1".equals(itemInfoEntity.getHsbs())){
                tempExportItemInfoVO.setHsbs("含税");
            }else{
                tempExportItemInfoVO.setHsbs("不含税");
            }
            tempExportItemInfoVO.setGgxh(itemInfoEntity.getGgxh());
            tempExportItemInfoVO.setSl(itemInfoEntity.getSl());
            tempExportItemInfoVO.setDw(itemInfoEntity.getDw());
            tempExportItemInfoVO.setJm(itemInfoEntity.getJm());
            if("1".equals(itemInfoEntity.getYhzcbs())){
                tempExportItemInfoVO.setYhzcbs("是");
            }else{
                tempExportItemInfoVO.setYhzcbs("否");
            }
            tempExportItemInfoVO.setYhzclx(itemInfoEntity.getYhzclx());
            if("0".equals(itemInfoEntity.getParentId())){
                tempExportItemInfoVO.setItemGroupName(itemRootName);
            }else{
                tempExportItemInfoVO.setItemGroupName(idAneParentNameMap.get(itemInfoEntity.getId()));
            }
            tempExportItemInfoVO.setItemGroupCode(itemInfoEntity.getSphssflbm().substring(0, 2));
            exportExportItemInfoVOList.add(tempExportItemInfoVO);
        }
        return exportExportItemInfoVOList;
    }

    /**
     * 导出excel
     **/
    private void exportExportItemInfoList(OutputStream out, List<ExportItemInfoVO> list, String fileName) {
        Long startTime = System.currentTimeMillis();
        // 创建一个工作簿
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(-1);
        try {
            //设置单个sheet最大容量,是个约值,暂定为2000
            int limitSheetSize = list.size() + 10;
            int columnSize = 13;
            Map<String, String> headToProperty = new HashMap<>(columnSize);
            Map<String, String> headerToColumnMap = new HashMap<>(columnSize);
            for (ExportItemInfoEnum exportItemInfoEnum : ExportItemInfoEnum.values()) {
                headToProperty.put(exportItemInfoEnum.getValue(), exportItemInfoEnum.getCellName());
                headerToColumnMap.put(exportItemInfoEnum.getValue(), exportItemInfoEnum.getKey());
            }
            ExcelReadContext context = new ExcelReadContext(headToProperty, true, headerToColumnMap);
            context.setFilePrefix(".xlsx");
            context.setSheetIndex(0);
            context.setHeadRow(0);
            context.setSheetLimit(limitSheetSize);
            ExcelExportUtils handle = new ExcelExportUtils(context);
            sxssfWorkbook = handle.exportExcel(sxssfWorkbook, list);
            sxssfWorkbook.write(out);
            Long endTime = System.currentTimeMillis();
            log.debug("(导出{}excel)结束,耗时:{}", fileName, endTime - startTime);
        } catch (Exception e) {
            log.error("(导出{}excel)异常", fileName, e);
        } finally {
            try {
                sxssfWorkbook.close();
            } catch (IOException e) {
                log.error("(导出{}excel)输出流关闭异常:{}", fileName, e);
            }
        }
    }

    @Override
    public R uploadItemInfoExcel(MultipartFile file) {
        try{
            if(file == null){
                return R.error("请上传文件");
            }
            // 1 获取数据
            Set<ItemInfoUpdateExcelBO> dataSet = getDataFromExcel(file);
            // 2 校验数据
            List<ItemInfoUpdateExcelBO> rightList = new ArrayList<>();
            List<ItemInfoUpdateExcelBO> errorList = new ArrayList<>();
            this.dealCheckExcelCustomerDataResult(dataSet, rightList, errorList);
            // 3 响应数据
            return R.ok().put("data", new ItemInfoUpdateExcelVO(rightList, String.valueOf(rightList.size()), errorList, String.valueOf(errorList.size())));
        }catch (Exception e){
            return R.error();
        }
    }

    /**
     * 从excel中获取ItemInfoUpdateExcelBO的Set集合
     * @param file
     * @return java.util.Set<com.dxhy.order.modules.pojo.bo.ItemInfoUpdateExcelBO>
     * <AUTHOR>
     **/
    private Set<ItemInfoUpdateExcelBO> getDataFromExcel(MultipartFile file) throws Exception{
        Set<ItemInfoUpdateExcelBO> dataSet = new HashSet<>();
        EasyExcel.read(file.getInputStream(), ItemInfoUpdateExcelBO.class, new AnalysisEventListener<ItemInfoUpdateExcelBO>() {
            @Override
            public void invoke(ItemInfoUpdateExcelBO o, AnalysisContext analysisContext) {
                dataSet.add(o);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
        }).doReadAll();
        dataSet.forEach(ItemInfoUpdateExcelBO::clearNull);
        return dataSet;
    }

    /**
     * 处理Excel传入的参数
     * @param dataSet
     * @param rightList
     * @param errorList
     * @return void
     * <AUTHOR>
     **/
    private void dealCheckExcelCustomerDataResult(Set<ItemInfoUpdateExcelBO> dataSet, List<ItemInfoUpdateExcelBO> rightList, List<ItemInfoUpdateExcelBO> errorList){
        if(CollectionUtils.isEmpty(dataSet)){
            return;
        }
        for (ItemInfoUpdateExcelBO itemInfoUpdateExcelBO : dataSet) {

            String checkResult = checkExcelCustomerData(itemInfoUpdateExcelBO);
            if(StringUtils.isEmpty(checkResult)){
                rightList.add(itemInfoUpdateExcelBO);
            }else{
                itemInfoUpdateExcelBO.setErrorMsg(checkResult);
                errorList.add(itemInfoUpdateExcelBO);
            }
        }
    }

    /**
     * 校验Excel传入的参数
     * @param itemInfoUpdateExcelBO
     * @return String
     * <AUTHOR>
     **/
    private String checkExcelCustomerData(ItemInfoUpdateExcelBO itemInfoUpdateExcelBO){
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getXmflmc())){
            return "项目分类名称不能为空";
        }
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getXmmc())){
            return "项目名称不能为空";
        }
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getSphssflbm())){
            return "商品和税收分类编码不能为空";
        }
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getSphfffljc())){
            return "商品和服务分类简称不能为空";
        }
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getSl())){
            return "税率不能为空";
        }else{
            BigDecimal bigDecimal = new BigDecimal(itemInfoUpdateExcelBO.getSl());
            String money = "0.00";
            if(bigDecimal.compareTo(param0) > 0){
                BigDecimal moneyBigDecimal = bigDecimal.multiply(param100);
                money = moneyBigDecimal.toString();
            }
            itemInfoUpdateExcelBO.setSl(money.substring(0, money.indexOf(".")) + "%");
        }
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getSfsyyhzc())){
            return "是否使用优惠政策不能为空";
        }
        if(StringUtils.isEmpty(itemInfoUpdateExcelBO.getHsbs())){
            return "含税标志不能为空";
        }
        return "";
    }

    @Override
    public R saveDataFromExcel(ItemInfoExcelSaveDTO itemInfoExcelSaveDTO) {
        String baseNsrsbh = itemInfoExcelSaveDTO.getBaseNsrsbh();
        Date dateNow = new Date();
        String userNameNow = ssoUtil.getUserName();
        List<ItemInfoUpdateExcelBO> dataList = itemInfoExcelSaveDTO.getDataList();
        // 1 查询当前已存在的二级分类
        List<ItemGroupEntity> levelTwoCustomerGroupEntity = itemGroupDao.listLevelTwoDataByNsrsbh(baseNsrsbh);
        Map<String, ItemGroupEntity> itemGroupMap = new HashMap<>();
        levelTwoCustomerGroupEntity.forEach(e -> itemGroupMap.put(e.getXmflmc(), e));
        ItemGroupEntity rootItemGroupEntity = new ItemGroupEntity();
        rootItemGroupEntity.setId("0");
        rootItemGroupEntity.setParentId("-1");
        rootItemGroupEntity.setBaseNsrsbh(itemInfoExcelSaveDTO.getBaseNsrsbh());
        rootItemGroupEntity.setXmflmc(itemRootName);
        itemGroupMap.put(itemRootName, rootItemGroupEntity);
        // 2.1 格式化分类数据
        List<ItemGroupEntity> saveGroupDataList = new ArrayList<>();
        String groupParentId = "0";
        if(CollectionUtils.isNotEmpty(dataList)){
            for (ItemInfoUpdateExcelBO tempData : dataList) {
                if(itemGroupMap.get(tempData.getXmflmc()) != null){
                    continue;
                }
                ItemGroupEntity saveData = new ItemGroupEntity();
                saveData.setId(DistributedKeyMaker.generateShotKey());
                saveData.setParentId(groupParentId);
                saveData.setBaseNsrsbh(baseNsrsbh);
                saveData.setXmflmc(tempData.getXmflmc());
                saveData.setCreateTime(dateNow);
                saveData.setCreateBy(userNameNow);
                saveGroupDataList.add(saveData);
                itemGroupMap.put(tempData.getXmflmc(), saveData);
            }
        }
        // 2.2 保存分类数据
        if(CollectionUtils.isNotEmpty(saveGroupDataList)){
            itemGroupDao.insertList(saveGroupDataList);
        }
        // 3.1 格式化业务数据
        List<ItemInfoEntity> saveItemDataList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dataList)){
            for (ItemInfoUpdateExcelBO itemInfoUpdateExcelBO : dataList) {
                ItemInfoEntity saveData = new ItemInfoEntity();
                saveData.setId(DistributedKeyMaker.generateShotKey());
                saveData.setParentId(itemGroupMap.get(itemInfoUpdateExcelBO.getXmflmc()).getId());
                saveData.setBaseNsrsbh(baseNsrsbh);
                saveData.setXmmc(itemInfoUpdateExcelBO.getXmmc());
                saveData.setSphssflbm(itemInfoUpdateExcelBO.getSphssflbm());
                saveData.setSphssfljc(itemInfoUpdateExcelBO.getSphfffljc());
                saveData.setYhzcbs("是".equals(itemInfoUpdateExcelBO.getSfsyyhzc()) ? "1" : "0");
                saveData.setYhzclx(itemInfoUpdateExcelBO.getYhzclx());
                saveData.setSl(itemInfoUpdateExcelBO.getSl());
                saveData.setJm(itemInfoUpdateExcelBO.getJm());
                saveData.setHsbs("含税".equals(itemInfoUpdateExcelBO.getHsbs()) ? "1" : "0");
                saveData.setDj(itemInfoUpdateExcelBO.getDj());
                saveData.setDw(itemInfoUpdateExcelBO.getDw());
                saveData.setGgxh(itemInfoUpdateExcelBO.getGgxh());
                saveData.setIsDelete("0");
                saveData.setCreateTime(dateNow);
                saveData.setCreateBy(userNameNow);
                saveItemDataList.add(saveData);
            }
        }
        // 3.2 保存业务数据
        if(CollectionUtils.isNotEmpty(saveItemDataList)){
            itemInfoDao.insertList(saveItemDataList);
        }
        return R.ok();
    }

    @Override
    public void genExcelForErrorData(ItemInfoExcelSaveDTO itemInfoExcelSaveDTO, HttpServletResponse response) {
        List<ItemInfoUpdateExcelBO> customerInfoList = itemInfoExcelSaveDTO.getDataList();
        if(CollectionUtils.isEmpty(customerInfoList)){
            return ;
        }
        String fileName = "客户信息";
        log.debug("开始导出{}excel", fileName);
        OutputStream out = null;
        String filePrefix = DateUtil.format(new Date(), OrderInfoContentEnum.DATE_FORMAT_DATE_YMDHMS);
        try {
            response.setContentType("octets/stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + filePrefix + ".xlsx");
            out = response.getOutputStream();
            exportExcelItemInfoList(out, customerInfoList, fileName);
        } catch (Exception e) {
            log.error("(导出{}excel)，出现异常", fileName, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

            } catch (Exception e) {
                log.error("(导出{}excel)，流关闭异常", fileName, e);
            }
        }
    }

    /**
     * 导出excel
     **/
    private void exportExcelItemInfoList(OutputStream out, List<ItemInfoUpdateExcelBO> list, String fileName) {
        Long startTime = System.currentTimeMillis();
        // 创建一个工作簿
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(-1);
        try {
            //设置单个sheet最大容量,是个约值,暂定为2000
            int limitSheetSize = list.size() + 10;
            int columnSize = 10;
            Map<String, String> headToProperty = new HashMap<>(columnSize);
            Map<String, String> headerToColumnMap = new HashMap<>(columnSize);
            for (ExportExcelItemInfoEnum exportExcelItemInfoEnum : ExportExcelItemInfoEnum.values()) {
                headToProperty.put(exportExcelItemInfoEnum.getValue(), exportExcelItemInfoEnum.getCellName());
                headerToColumnMap.put(exportExcelItemInfoEnum.getValue(), exportExcelItemInfoEnum.getKey());
            }
            ExcelReadContext context = new ExcelReadContext(headToProperty, true, headerToColumnMap);
            context.setFilePrefix(".xlsx");
            context.setSheetIndex(0);
            context.setHeadRow(0);
            context.setSheetLimit(limitSheetSize);
            ExcelExportUtils handle = new ExcelExportUtils(context);
            sxssfWorkbook = handle.exportExcel(sxssfWorkbook, list);
            sxssfWorkbook.write(out);
            Long endTime = System.currentTimeMillis();
            log.debug("(导出{}excel)结束,耗时:{}", fileName, endTime - startTime);
        } catch (Exception e) {
            log.error("(导出{}excel)异常", fileName, e);
        } finally {
            try {
                sxssfWorkbook.close();
            } catch (IOException e) {
                log.error("(导出{}excel)输出流关闭异常:{}", fileName, e);
            }
        }
    }

}
