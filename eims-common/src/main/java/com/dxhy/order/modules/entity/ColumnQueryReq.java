package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ColumnQueryReq implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 页面标识
     */
    private String pageMark;
    /**
     * 筛选条件标识
     */
    private String pageMarkInfo;
    /**
     * 列配置信息
     */
    private String configInfo;
    /**
     * 筛选条件配置信息
     */
    private String screenConditionInfo;
    /**
     * 用户标识token
     */
    private String token;

}
