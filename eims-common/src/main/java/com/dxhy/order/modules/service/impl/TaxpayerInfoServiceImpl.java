package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.config.OrderConfig;
import com.dxhy.order.modules.dao.DbTenantDao;
import com.dxhy.order.modules.dao.TaxpayerInfoDao;
import com.dxhy.order.modules.entity.DbTenantEntity;
import com.dxhy.order.modules.entity.NsrsbhTenantRelationEntity;
import com.dxhy.order.modules.entity.TaxpayerInfo;
import com.dxhy.order.modules.service.OrderInvoiceConfigService;
import com.dxhy.order.modules.service.TaxpayerInfoService;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.pojo.DeptInfo;
import com.dxhy.order.utils.DistributedKeyMaker;
import com.dxhy.order.utils.HttpUtils;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【taxpayer_info(销方企业信息表)】的数据库操作Service实现
 * @createDate 2022-06-29 17:13:17
 */
@Slf4j
@Service("taxpayerInfoService")
public class TaxpayerInfoServiceImpl extends ServiceImpl<TaxpayerInfoDao, TaxpayerInfo>
        implements TaxpayerInfoService {

    private static final String LOGGER_MSG = "(销方信息表)";

    @Resource
    TaxpayerInfoDao taxpayerInfoDao;

    @Resource
    private DbTenantDao dbTenantDao;

    @Resource
    private NsrsbhTenantRelationServiceImpl nsrsbhTenantRelationService;

    @Resource
    private OrderConfig orderConfig;

    @Resource
    private OrderInvoiceConfigService orderInvoiceConfigService;


    @Override
    public TaxpayerInfo selectZsxedByNsrsbh(String nsrsbh) {
        TaxpayerInfo cnt = taxpayerInfoDao.selectZsxedByNsrsbh(nsrsbh);
        if (ObjectUtils.isEmpty(cnt)) {
            log.info("{}，该税号需初始化：{}", LOGGER_MSG, nsrsbh);
            TaxpayerInfo taxpayerInfo = new TaxpayerInfo();
            taxpayerInfo.setId(DistributedKeyMaker.generateShotKey());
            taxpayerInfo.setXhfNsrsbh(nsrsbh);
            taxpayerInfo.setZsxed("1000000");
            taxpayerInfo.setSecretId("b552d9a9bbf14c1e905922a4fda7da681534");
            taxpayerInfo.setSecretKey("e023090b13f94a3c9ff00aafc11f4adf");

            int i = taxpayerInfoDao.insert(taxpayerInfo);
            if (i < 1) {
                log.error("{}，初始化税号失败：{}", LOGGER_MSG, nsrsbh);
            } else {
                return taxpayerInfo;
            }
        }
        log.info("{}，税号已初始化：{}", LOGGER_MSG, nsrsbh);
        return cnt;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R insertDept(DeptInfo deptInfo) throws Exception {
        //切换到主数据源
        DynamicDataSource.setDataSourceDefault();
        //tenantId   改为税号  2023-01-04
        DbTenantEntity rdsConfig = new DbTenantEntity();
        rdsConfig.setTenantCode(deptInfo.getTenantId());
        Map map = new HashMap();
        map.put("tenant_code", deptInfo.getTenantId());
        List list = dbTenantDao.selectByMap(map);
        if (CollectionUtils.isEmpty(list)) {
            rdsConfig.setDbUrl(orderConfig.getDbUrl());
            rdsConfig.setDbPort(orderConfig.getDbPort());
            rdsConfig.setDbName(orderConfig.getDbName());
            rdsConfig.setDbAccount(orderConfig.getDbAccount());
            rdsConfig.setDbPassword(orderConfig.getDbPassword());
            dbTenantDao.insert(rdsConfig);
        }
        QueryWrapper<NsrsbhTenantRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nsrsbh", deptInfo.getTaxpayerCode());
        NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity = nsrsbhTenantRelationService.getBaseMapper().selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(nsrsbhTenantRelationEntity)) {
            NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity1 = new NsrsbhTenantRelationEntity();
            nsrsbhTenantRelationEntity1.setNsrmc(deptInfo.getName());
            nsrsbhTenantRelationEntity1.setNsrsbh(deptInfo.getTaxpayerCode());
            nsrsbhTenantRelationEntity1.setTenantCode(deptInfo.getTenantId());
            int insert = nsrsbhTenantRelationService.getBaseMapper().insert(nsrsbhTenantRelationEntity1);
            log.info("税号:{} 租户信息新增结果:{}", deptInfo.getTaxpayerCode(), insert == 1 ? true : false);
        } else {
            nsrsbhTenantRelationEntity.setNsrmc(deptInfo.getName());
            nsrsbhTenantRelationEntity.setNsrsbh(deptInfo.getTaxpayerCode());
            nsrsbhTenantRelationEntity.setTenantCode(deptInfo.getTenantId());
            int i = nsrsbhTenantRelationService.getBaseMapper().updateById(nsrsbhTenantRelationEntity);
            log.info("税号:{} 租户信息更新结果:{}", deptInfo.getTaxpayerCode(), i == 1 ? true : false);
        }

        //调用invoice
        HttpUtils.doPost(orderConfig.getAddDeptUrl(), JsonUtils.getInstance().toJsonString(deptInfo));

        return R.ok();
    }

}
