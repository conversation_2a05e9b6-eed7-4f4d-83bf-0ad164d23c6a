package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子税务局账号表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-23 10:56:08
 */
@Data
@TableName("e_tax_account")
public class ETaxAccountEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 
	 */
	private String xfmc;
	/**
	 * 
	 */
	private String nsrsbh;
	/**
	 * 
	 */
	private String yhm;
	/**
	 * 
	 */
	private String mm;
	/**
	 * 责任人类型
	 */
	private String zzrlx;
	/**
	 * 激活状态  0未激活   1已激活 
	 */
	private String status;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
	private Date updateTime;

	/**
	 * 登录接口的方法名
	 */
	@TableField(exist = false)
	private String  ffm;

	/**
	 * 短信验证码
	 */
	@TableField(exist = false)
	private String  dxyzm;

	/**
	 * rzid
	 */
	@TableField(exist = false)
	private String  rzid;
	/**
	 * current
	 */
	@TableField(exist = false)
	private Long  current;
	/**
	 * size
	 */
	@TableField(exist = false)
	private Long  size;

}
