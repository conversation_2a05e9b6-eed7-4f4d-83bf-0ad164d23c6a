package com.dxhy.order.modules.service;

import com.dxhy.order.modules.entity.*;

import java.util.List;

public interface OpenOderInvoiceIssueService {
    String allocateInvoices(String secretId, String timestamp, String nonce, String signature, String encryptCode, String zipCode, String content) throws Exception;

    String getOrderInfoAndInvoiceInfo(String secretId, String timestamp, String nonce, String signature, String encryptCode, String zipCode, String content);

    void getTaskInvoiceInfo();

    void taxGenerateRedTableResult();

    BlueInvoicesIssueRes blueInvoiceIssue(List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities);

    InvoiceIssueRes invoiceIssue(InvoiceIssueInfoParam issueInfoParam);

    InvoiceIssueRes getToken(AccessTokeReq accessTokeReq);


    InvoiceIssueRes queryInvoiceInfo(DdqqlshParam ddqqlshParam);
}
