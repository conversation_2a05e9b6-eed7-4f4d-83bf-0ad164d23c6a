package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceBdczlxxDao;
import com.dxhy.order.modules.entity.InvoiceBdczlxxEntity;
import com.dxhy.order.modules.service.InvoiceBdczlxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 不动产租赁信息的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceBdczlxxService")
@Slf4j
@RefreshScope
public class InvoiceBdczlxxServiceImpl extends ServiceImpl<InvoiceBdczlxxDao, InvoiceBdczlxxEntity>
        implements InvoiceBdczlxxService {

    @Override
    public void saveBatch(List<InvoiceBdczlxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceBdczlxxEntity> bdczlxxWrapper = Wrappers.lambdaUpdate();
        bdczlxxWrapper.set(InvoiceBdczlxxEntity::getIsDelete, "1");
        bdczlxxWrapper.eq(InvoiceBdczlxxEntity::getOrderInvoiceInfoId, invoiceId);
        bdczlxxWrapper.eq(InvoiceBdczlxxEntity::getInvoiceTdywId, tdywId);
        this.update(bdczlxxWrapper);
    }
}




