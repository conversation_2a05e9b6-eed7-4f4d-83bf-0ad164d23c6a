package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SceneTemplateEntityRes implements Serializable {
    /** uuid **/
    private String uuid;
    /** 纳税人识别号 **/
    private String nsrsbh;
    /** 纳税人名称 **/
    private String nsrmc;
    /** 场景模板名称 **/
    private String cjmbmc;
    /** 有效标志 **/
    private String yxbz;
    /** 录入时间 **/
    private String lrrq;
    /** 修改日期 **/
    private String xgrq;
    /** 录入人名称 **/
    private String lrrmc;
    /** 修改人名称 **/
    private String xgrmc;
    /** 附加要素信息列表 **/
    private List<SceneTemplateEntityFjusRes> jcxxFpfjysxxList;
}
