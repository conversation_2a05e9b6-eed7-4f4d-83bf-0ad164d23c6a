package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.ItemInfoEntity;
import com.dxhy.order.modules.pojo.bo.GroupTreeBO;
import com.dxhy.order.modules.pojo.dto.*;
import com.dxhy.order.utils.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品信息表 service
 * <AUTHOR>
 * @Date 2022/6/27 12:10
 * @Version 1.0
 **/
public interface ItemInfoService extends IService<ItemInfoEntity> {

    /**
     * 获取项目分类树
     * @param nsrsbh
     * @return com.dxhy.order.modules.pojo.bo.GroupTreeBO
     * <AUTHOR>
     **/
    GroupTreeBO getGroupTree(String nsrsbh);

    /**
     * 保存项目分类（新增 修改）
     * @param itemGroupSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveItemGroup(ItemGroupSaveDTO itemGroupSaveDTO);

    /**
     * 删除项目分类
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R deleteItemGroup(String id);

    /**
     * 查询列表
     * @param itemInfoListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R queryPage(ItemInfoListDTO itemInfoListDTO);

    /**
     * 查询列表
     * @param itemInfoListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R queryPageWithoutNsrsbh(ItemInfoListDTO itemInfoListDTO);

    /**
     * 查询列表 - 不分页
     * @param itemInfoListByNameWithoutPageDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listByNameWithoutPage(ItemInfoListByNameWithoutPageDTO itemInfoListByNameWithoutPageDTO);

    /**
     * 保存数据
     * @param itemInfoSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveData(ItemInfoSaveDTO itemInfoSaveDTO);

    /**
     * 删除数据
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R deleteData(String id);

    /**
     * 导出数据
     * @param idList
     * @param response
     * @return void
     * <AUTHOR>
     **/
    void exportData(List<String> idList, HttpServletResponse response);

    /**
     * 上传项目信息
     * @param file
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R uploadItemInfoExcel(MultipartFile file);

    /**
     * 保存Excel上传的项目信息
     * @param itemInfoExcelSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveDataFromExcel(ItemInfoExcelSaveDTO itemInfoExcelSaveDTO);

    /**
     * 将Excel错误的数据导出
     * @param itemInfoExcelSaveDTO
     * @return void
     * <AUTHOR>
     **/
    void genExcelForErrorData(ItemInfoExcelSaveDTO itemInfoExcelSaveDTO, HttpServletResponse response);

}

