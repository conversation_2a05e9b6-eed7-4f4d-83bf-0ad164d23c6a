package com.dxhy.order.modules.service;

import com.dxhy.order.modules.entity.*;

import java.util.List;

public interface InvoiceIssueService {


    /**
     * 3.1 蓝字发票开具接口
     *
     * @param invoiceIssueInfoParamList
     * @return
     */
    InvoiceIssueRes invoiceIssue(List<InvoiceIssueInfoParam> invoiceIssueInfoParamList);

    /**
     * 3.2 蓝字发票结果查询接口
     *
     * @return
     */
    InvoiceIssueRes queryInvoiceInfo(ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO);

    /**
     * 3.3 发票基础信息查询
     *
     * @return
     */
    InvoiceIssueRes getInvoiceBaseInfo(InvoiceBaseInfoParam invoiceBaseInfoParam);

    /**
     * 3.4 单张发票信息查询
     *
     * @return
     */
    InvoiceIssueRes getSingleInvoiceInfo(SingleInvoiceInfoParam singleInvoiceInfoParam);


    /**
     * 3.5 红字开票确认信息录入
     *
     * @param apiHzkpqrxxlrReqBO
     * @return
     */
    InvoiceIssueRes redInvoiceConfirmInfoEnter(ApiHzkpqrxxlrReqBO apiHzkpqrxxlrReqBO);

    /**
     * 3.6 红字开票确认信息受理结果查询
     *
     * @param apiHzkpqrxxsljgcxReqBO
     * @return
     */
    InvoiceIssueRes getRedInvoiceConfirmInfo(ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO);

    /**
     * 3.7 红字发票确认信息处理
     *
     * @param apiHzfpqrxxclReqBO
     * @return
     */
    InvoiceIssueRes getRedInvoiceConfirmInfoDeal(ApiHzfpqrxxclReqBO apiHzfpqrxxclReqBO);

    /**
     * 3.8 红字发票确认信息列表查询
     *
     * @param apiHzfpqrxxlbcxReqBO
     * @return
     */
    InvoiceIssueRes getRedInvoiceConfirmInfoList(ApiHzfpqrxxlbcxReqBO apiHzfpqrxxlbcxReqBO);

    /**
     * 3.9 红字发票确认明细信息查询
     *
     * @param apiHzfpqrmxxxcxReqBO
     * @return
     */
    InvoiceIssueRes getRedInvoiceConfirmInfoDetail(ApiHzfpqrmxxxcxReqBO apiHzfpqrmxxxcxReqBO);

    /**
     * 3.10 红字发票开具受理
     *
     * @param apiHzfpkjslReqBO
     * @return
     */
    InvoiceIssueRes redInvoiceIssue(ApiHzfpkjslReqBO apiHzfpkjslReqBO);

    /**
     * 3.11 红字发票开具受理结果查询
     *
     * @param apiHzfpkjsljgcxReqBO
     * @return
     */
    InvoiceIssueRes getRedInvoiceResult(ApiHzfpkjsljgcxReqBO apiHzfpkjsljgcxReqBO);


}
