package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.ItemInfoEntity;
import com.dxhy.order.modules.pojo.dto.ItemInfoListByNameWithoutPageDTO;
import com.dxhy.order.modules.pojo.dto.ItemInfoListDTO;
import com.dxhy.order.modules.pojo.dto.ItemInfoSaveDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 商品信息表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface ItemInfoDao extends BaseMapper<ItemInfoEntity> {

    /**
     * 清理未和项目分类关联的数据
     * @param baseNsrsbh
     * @return void
     * <AUTHOR>
     **/
    void clearDataByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 查询项目信息列表
     * @param page
     * @param itemInfoListDTO
     * @return java.util.List<com.dxhy.order.modules.entity.ItemInfoEntity>
     * <AUTHOR>
     **/
    List<ItemInfoEntity> selectList(Page page, @Param("itemInfoListDTO") ItemInfoListDTO itemInfoListDTO);

    /**
     * 查询列表 - 不分页
     * @param itemInfoListByNameWithoutPageDTO
     * @return java.util.List<com.dxhy.order.modules.entity.ItemInfoEntity>
     * <AUTHOR>
     **/
    List<ItemInfoEntity> listByNameWithoutPage(ItemInfoListByNameWithoutPageDTO itemInfoListByNameWithoutPageDTO);

    /**
     * 新增、修改时候查重用
     * @param itemInfoSaveDTO
     * @return java.util.List<com.dxhy.order.modules.entity.ItemInfoEntity>
     * <AUTHOR>
     **/
    List<ItemInfoEntity> selectListByItemInfoSaveDTO(ItemInfoSaveDTO itemInfoSaveDTO);

    /**
     * 查询实体
     * @param itemInfoEntity
     * <AUTHOR>
     **/
    ItemInfoEntity selectItemInfoByName(@Param("itemInfoEntity") ItemInfoEntity itemInfoEntity);

    /**
     * 根据项目ID查询项目分类名称列表
     * @param idList
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
     * <AUTHOR>
     **/
    List<Map<String, String>> listParentNameByIdList(@Param("idList") List<String> idList);

    /**
     * 批量保存数据
     * @param itemInfoEntityList
     * @return void
     * <AUTHOR>
     **/
    void insertList(@Param("itemInfoEntityList") List<ItemInfoEntity> itemInfoEntityList);
}
