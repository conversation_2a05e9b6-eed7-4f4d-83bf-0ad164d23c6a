package com.dxhy.order.modules.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单开票明细表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-22 18:31:05
 */
@TableName("order_invoice_item")
@Data
public class OrderInvoiceItemEntity extends BasePage implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单开票明细主键
	 */
	@TableId(type = IdType.INPUT)
	@JsonProperty("ID")
	private String id;

	/**
	 * 商品ID
	 */
	@ApiModelProperty("商品ID")
	@JsonProperty("SPID")
	private String spid;
	/**
	 * 订单开票主表ID
	 */
	@ApiModelProperty("订单开票主表ID")
	@JsonProperty("ORDERINVOICEID")
	@JSONField(name = "ORDERINVOICEID")
	private String orderInvoiceId;
	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	@JSONField(name = "XH")
	@JsonProperty("XH")
	private String xh;
	/**
	 * 项目名称
	 */
	@ApiModelProperty("项目名称")
	@JSONField(name = "XMMC")
	@JsonProperty("XMMC")
	private String xmmc;
	/**
	 * 规格型号
	 */
	@ApiModelProperty("规格型号")
	@JSONField(name = "GGXH")
	@JsonProperty("GGXH")
	private String ggxh;
	/**
	 * 单位
	 */
	@ApiModelProperty("单位")
	@JSONField(name = "DW")
	@JsonProperty("DW")
	private String dw;
	/**
	 * 数量
	 */
	@ApiModelProperty("数量")
	@JSONField(name = "SPSL")
	@JsonProperty("SPSL")
	private String xmsl;
	/**
	 * 含税标志 0 不含税 1含税
	 */
	@ApiModelProperty("含税标志 0 不含税 1含税")
	@JSONField(name = "HSBZ")
	@JsonProperty("HSBZ")
	private String hsbz;
	/**
	 * 单价
	 */
	@ApiModelProperty("单价")
	@JSONField(name = "DJ")
	@JsonProperty("DJ")
	private String dj;
	/**
	 * 金额
	 */
	@ApiModelProperty("金额")
	@JSONField(name = "JE")
	@JsonProperty("JE")
	private String je;
	/**
	 * 税率
	 */
	@ApiModelProperty("税率")
	@JSONField(name = "SL")
	@JsonProperty("SL")
	private String sl;
	/**
	 * 税额
	 */
	@ApiModelProperty("税额")
	@JSONField(name = "SE")
	@JsonProperty("SE")
	private String se;
	/**
	 * 发票行性质 0正常商品行  1折扣行  2被折扣行
	 */
	@ApiModelProperty("发票行性质 0正常商品行  1折扣行  2被折扣行")
	@JSONField(name = "FPHXZ")
	@JsonProperty("FPHXZ")
	private String 	fphxz;
	/**
	 * 商品编码
	 */
	@ApiModelProperty("商品编码")
	@JSONField(name = "SPBM")
	@JsonProperty("SPBM")
	private String spbm;
	/**
	 * 自行编码
	 */
	@ApiModelProperty("自行编码")
	@JSONField(name = "ZXBM")
	@JsonProperty("ZXBM")
	private String zxbm;
	/**
	 * 优惠政策标识
	 */
	@ApiModelProperty("优惠政策标识 0 否  1是")
	@JSONField(name = "YHZCBS")
	@JsonProperty("YHZCBS")
	private String yhzcbs;
	/**
	 * 零税率标识
	 */
	@ApiModelProperty("零税率标识")
	@JSONField(name = "LSLBS")
	@JsonProperty("LSLBS")
	private String lslbs;
	/**
	 * 增值税特殊管理
	 */
	@ApiModelProperty("增值税特殊管理")
	@JSONField(name = "ZZSTSGL")
	@JsonProperty("ZZSTSGL")
	private String zzstsgl;
	/**
	 * 扣除额
	 */
	@ApiModelProperty("扣除额")
	@JSONField(name = "KCE")
	@JsonProperty("KCE")
	private String kce;

	/**
	 * 全电发票号码
	 */
	@JsonProperty("QDFPHM")
	private String qdfphm;

	/**
	 * 税率集合
	 */
	@JsonProperty("SLARRAY")
	private String slArray;
	/**
	 * 折扣额
	 */
	@TableField(exist = false)
	private String zke;

	@TableField(exist = false)
	List<String> msgList;

	@TableField(exist = false)
	private String msg;

	/**
	 * 逻辑删除
	 */
	@JsonProperty("ISDELETE")
	private String isDelete;
	/**
	 * 优惠政策类型
	 */
	@JsonProperty("BYZD1")
	@JSONField(name = "BYZD1")
	private String byzd1;
	/**
	 * 折扣方式 1-按金额   2-按比例
	 */
	@JsonProperty("BYZD2")
	@JSONField(name = "BYZD2")
	private String byzd2;
	/**
	 * 比例数据 -----已被占用
	 * 存储折扣金额
	 */
	@JsonProperty("BYZD3")
	@JSONField(name = "BYZD3")
	private String byzd3;
	/**
	 * 备用字段4-------货物或应税劳务名称
	 */
	@JsonProperty("BYZD4")
	private String byzd4;
	/**
	 * 备用字段5 ---------商品服务简称
	 */
	@JsonProperty("BYZD5")
	private String byzd5;
	/**
	 * 备用字段6
	 */
	@JsonProperty("BYZD6")
	private String byzd6;
	/**
	 * 备用字段7
	 */
	@JsonProperty("BYZD7")
	private String byzd7;
	/**
	 * 备用字段8
	 */
	@JsonProperty("BYZD8")
	private String byzd8;
	/**
	 * 创建时间
	 */
	@JsonProperty("CREATETIME")
	private Date createTime;
	/**
	 * 创建人
	 */
	@JsonProperty("CREATEBY")
	private String createBy;
	/**
	 * 更新时间
	 */
	@JsonProperty("UPDATETIME")
	private Date updateTime;
	/**
	 * 更新人
	 */
	@JsonProperty("UPDATEBY")
	private String updateBy;

	/**
	 * 标识商品信息查询的接口，0 商品信息查询界面  1 商品信息新增界面
	 */
	@TableField(exist = false)
	private String interfaceFlag;

	/**
	 * 智能赋码 多个税率集合  提供给前台  做编辑用
	 */
	@TableField(exist = false)
	@JsonProperty("SLList")
	private List<String> SLList;

	@TableField(exist = false)
	private String hsje;

	@TableField(exist = false)
	private String ddh;

	/**
	 * 剩余可冲红金额
	 */
	@ApiModelProperty("剩余可冲红金额")
	@JsonProperty("SYKCHJE")
	private String sykchje;
	/**
	 * 剩余可冲红数量
	 */
	@ApiModelProperty("剩余可冲红数量")
	@JsonProperty("SYKCHSL")
	private String sykchsl;
	/**
	 * 剩余可冲红税额
	 */
	@ApiModelProperty("剩余可冲红税额")
	@JsonProperty("SYKCHSE")
	private String sykchse;

}
