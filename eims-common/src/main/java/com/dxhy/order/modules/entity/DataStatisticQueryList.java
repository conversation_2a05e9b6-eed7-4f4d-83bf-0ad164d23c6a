package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据统计 - 查询实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-28 15:19:24
 */
@Data
public class DataStatisticQueryList implements Serializable {

    /**
     * 销货方纳税人识别号
     */
    private String baseNsrsbh;

    /**
     * 发票种类代码
     */
    private String fpzldm;

    /**
     * 统计方式： 1 月度；2 季度；3 年度
     */
    private String tjfs;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 时间
     */
    private String time;

    /**
     * 季度： 1 第一季度 2 第二季度 3 第三季度 4 第四季度
     */
    private String jd;

}
