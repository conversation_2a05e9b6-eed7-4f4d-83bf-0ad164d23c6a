package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 发票回推配置表
 *
 * <AUTHOR> @email
 * @date 2024-05-09
 */
@TableName("invoice_backpush_config")
@Data
public class InvoiceBackpushConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    
    /**
     * 开票配置表ID
     */
    private String orderConfigId;
    
    /**
     * 系统来源
     */
    private String xtly;
    
    /**
     * 回推地址
     */
    private String backpushUrl;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 