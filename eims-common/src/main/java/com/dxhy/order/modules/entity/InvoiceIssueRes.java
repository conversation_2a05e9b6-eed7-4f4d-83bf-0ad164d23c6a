package com.dxhy.order.modules.entity;

import com.dxhy.order.model.OrderInfoContentEnum;
import lombok.Data;

import java.util.HashMap;

@Data
public class InvoiceIssueRes<A> extends HashMap<String, Object> {
    private static final long serialVersionUID = -662172649495000241L;
    private String ztdm;
    private String ztxx;
    private A data;


    public static InvoiceIssueRes ok(Object data) {
        InvoiceIssueRes invoiceIssueRes = new InvoiceIssueRes();
        invoiceIssueRes.setZtdm(OrderInfoContentEnum.API_RETURN_ERROR_0000.getKey());
        invoiceIssueRes.setZtxx(OrderInfoContentEnum.API_RETURN_ERROR_0000.getMessage());
        invoiceIssueRes.setData(data);
        invoiceIssueRes.put("ztdm", OrderInfoContentEnum.API_RETURN_ERROR_0000.getKey());
        invoiceIssueRes.put("ztxx", OrderInfoContentEnum.API_RETURN_ERROR_0000.getMessage());
        invoiceIssueRes.put("data", data);
        return invoiceIssueRes;
    }

    public static InvoiceIssueRes error(String ztdm, String ztxx) {
        InvoiceIssueRes invoiceIssueRes = new InvoiceIssueRes();
        invoiceIssueRes.setZtdm(ztdm);
        invoiceIssueRes.setZtxx(ztxx);
        invoiceIssueRes.put("ztdm", ztdm);
        invoiceIssueRes.put("ztxx", ztxx);
        invoiceIssueRes.put("data", "");
        return invoiceIssueRes;
    }

    public static InvoiceIssueRes res(String ztdm, String ztxx,Object data) {
        InvoiceIssueRes invoiceIssueRes = new InvoiceIssueRes();
        invoiceIssueRes.setZtdm(ztdm);
        invoiceIssueRes.setZtxx(ztxx);
        invoiceIssueRes.setData(data);
        invoiceIssueRes.put("ztdm", ztdm);
        invoiceIssueRes.put("ztxx", ztxx);
        invoiceIssueRes.put("data", data);
        return invoiceIssueRes;
    }
    public static InvoiceIssueRes res(String ztdm, String ztxx) {
        InvoiceIssueRes invoiceIssueRes = new InvoiceIssueRes();
        invoiceIssueRes.setZtdm(ztdm);
        invoiceIssueRes.setZtxx(ztxx);
        invoiceIssueRes.put("ztdm", ztdm);
        invoiceIssueRes.put("ztxx", ztxx);
        return invoiceIssueRes;
    }
}
