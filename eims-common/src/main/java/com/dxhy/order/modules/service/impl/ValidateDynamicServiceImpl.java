package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.constant.PatternConstant;
import com.dxhy.order.model.OrderInfoContentEnum;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.service.ValidateDynamicService;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ：VerifyOrderInfoImpl
 * @Description ：
 * @date 创建时间: 2022-06-29 09:42
 */
@Service
@Slf4j
public class ValidateDynamicServiceImpl implements ValidateDynamicService {


    @Override
    public Map<String, String> verifyDynamicEwmInfo(OrderInvoiceInfoEntity orderInvoiceInfo) {

        // 基础信息校验
        Map<String, String> resultMap = checkCommonOrderHead(orderInvoiceInfo);
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(resultMap.get(OrderManagementConstant.ERRORCODE))) {
            return resultMap;
        }
        // 订单主体-发票种类代码合法性(只能为0:专票;2:普票;41:卷票;51:电子票)

        //订单请求发票类型合法性
        if (CommonUtils.checkFpzldm(orderInvoiceInfo.getFpzlDm())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107004);
        }

        return resultMap;

    }

    private Map<String, String> checkCommonOrderHead(OrderInvoiceInfoEntity orderInvoiceInfo) {
        // 声明校验结果map
        Map<String, String> checkResultMap = new HashMap<>(10);
        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.SUCCESS.getKey());
        // 1.数据非空和长度校验
        if (orderInvoiceInfo == null) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.HANDLE_ISSUE_202004);
        }
        List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfo.getItemEntityList();
        if (CollectionUtils.isEmpty(itemEntityList)) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.HANDLE_ISSUE_202009);
        }
        /**
         * 订单主体-订单请求流水号
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107014, orderInvoiceInfo.getFpqqlsh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-纳税人识别号
         */
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.CHECK_ISS7PRI_107016,
                OrderInfoContentEnum.CHECK_ISS7PRI_107017, OrderInfoContentEnum.CHECK_ISS7PRI_107163,
                orderInvoiceInfo.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-开票类型
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107020, orderInvoiceInfo.getKplx());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        // 订单主体-开票类型合法性(开票类型只能为0和1：0蓝字发票；1红字发票)
        if (!OrderInfoEnum.ORDER_BILLING_INVOICE_TYPE_0.getKey().equals(orderInvoiceInfo.getKplx())
                && !OrderInfoEnum.ORDER_BILLING_INVOICE_TYPE_1.getKey().equals(orderInvoiceInfo.getKplx())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107021);
        }

        /**
         * 订单主体-销售方纳税人识别号
         */
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.CHECK_ISS7PRI_107022,
                OrderInfoContentEnum.CHECK_ISS7PRI_107017, OrderInfoContentEnum.CHECK_ISS7PRI_107163,
                orderInvoiceInfo.getXhfNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-销售方纳税人名称
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107024, orderInvoiceInfo.getXhfMc());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        /**
         * 订单主体-销售方地址
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107282, orderInvoiceInfo.getXhfDz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-销售方电话
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107283, orderInvoiceInfo.getXhfDh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-销售方银行
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107284, orderInvoiceInfo.getXhfYh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-销售方帐号
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107285, orderInvoiceInfo.getXhfZh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-销售方地址和电话总长度 由于企业区分不开地址电话,所以校验支持地址电话总长度100,默认应该是85
         */
        String dzDh = StringUtils.isBlank(orderInvoiceInfo.getXhfDz()) ? "" : orderInvoiceInfo.getXhfDz()
                + (StringUtils.isBlank(orderInvoiceInfo.getXhfDh()) ? "" : orderInvoiceInfo.getXhfDh());
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107267, dzDh);
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-销售方银行和帐号总长度 由于企业区分不开银行帐号,所以校验支持银行帐号总长度100,默认应该是85
         */
        String yhZh = StringUtils.isBlank(orderInvoiceInfo.getXhfYh()) ? "" : orderInvoiceInfo.getXhfYh()
                + (StringUtils.isBlank(orderInvoiceInfo.getXhfZh()) ? "" : orderInvoiceInfo.getXhfZh());
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107268, yhZh);
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-开票人
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107044, orderInvoiceInfo.getKpr());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-收款人
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107046, orderInvoiceInfo.getSkr());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-复核人
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107048, orderInvoiceInfo.getFhr());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-红字信息表编号
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107045, orderInvoiceInfo.getHzxxbbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-订单号
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107067, orderInvoiceInfo.getDdh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-订单日期
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107068,
                orderInvoiceInfo.getDdscrq() == null ? "" : DateUtil.format(orderInvoiceInfo.getDdscrq(), "yyyy-MM-dd HH:mm:ss"));
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单主体-价税合计
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107141, orderInvoiceInfo.getJshj());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 价税合计金额不能为0或者0.00
        if (ConfigureConstant.STRING_0.equals(orderInvoiceInfo.getJshj())
                || ConfigureConstant.STRING_000.equals(orderInvoiceInfo.getJshj())
                || ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceInfo.getJshj())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107066);
        }
        // 开票类型为0(蓝票)时,金额必须大于0
        boolean result5 = (StringUtils.isNotBlank(orderInvoiceInfo.getKplx())
                && !OrderInfoEnum.ORDER_BILLING_INVOICE_TYPE_0.getKey().equals(orderInvoiceInfo.getKplx()))
                || ConfigureConstant.DOUBLE_PENNY_ZERO >= new BigDecimal(orderInvoiceInfo.getJshj()).doubleValue();
        if (result5) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107080);
        }

        /**
         * 订单主体-合计金额
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107142, orderInvoiceInfo.getJshj());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 合计金额为不为0时,需要保证金额为小数点后两位
        if (ConfigureConstant.DOUBLE_PENNY_ZERO != new BigDecimal(orderInvoiceInfo.getJshj()).doubleValue()
                && ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceInfo.getJshj())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107135);
        }

        if (ConfigureConstant.MAX_ITEM_LENGTH <= itemEntityList.size()) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.INVOICE_AUTO_NUMBER1999);
        }

        /**
         * 金额关系合法性校验
         */
        if (!StringUtils.isBlank(orderInvoiceInfo.getJshj()) && !StringUtils.isBlank(orderInvoiceInfo.getHjbhsje())
                && !StringUtils.isBlank(orderInvoiceInfo.getKpse())) {

            double differ = MathUtil.sub(orderInvoiceInfo.getJshj(),
                    String.valueOf(MathUtil.add(orderInvoiceInfo.getHjbhsje(), orderInvoiceInfo.getKpse())));
            // 如果误差值等于含税金额,说明是含税金额不作校验,如果是尾插不等于0,校验返回
            if (DecimalCalculateUtil.decimalFormatToString(orderInvoiceInfo.getJshj(), ConfigureConstant.INT_2).equals(
                    DecimalCalculateUtil.decimalFormatToString(String.valueOf(differ), ConfigureConstant.INT_2))) {
            } else if (ConfigureConstant.DOUBLE_PENNY_ZERO != differ) {
                checkResultMap = CheckParamUtil.generateErrorMap(OrderInfoContentEnum.INVOICE_JSHJ_ERROR);
                return checkResultMap;
            }
        }

        /**
         * 明细行数据与发票头数据进行校验
         */
        BigDecimal kphjje = new BigDecimal(orderInvoiceInfo.getJshj());
        BigDecimal sumKphjje = BigDecimal.ZERO;
        for (int j = 0; j < itemEntityList.size(); j++) {
            Map<String, String> checkItemResultMap = checkCommonOrderItemsV3(itemEntityList.get(j), itemEntityList.size(), orderInvoiceInfo.getFpzlDm());
            if (!OrderInfoContentEnum.SUCCESS.getKey()
                    .equals(checkItemResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkItemResultMap;
            }

            if (OrderInfoEnum.HSBZ_1.getKey().equals(itemEntityList.get(j).getHsbz())) {
                sumKphjje = sumKphjje.add(new BigDecimal(itemEntityList.get(j).getJe()));
            } else {
                sumKphjje = sumKphjje.add(new BigDecimal(itemEntityList.get(j).getJe())).add(new BigDecimal(itemEntityList.get(j).getSe()));
            }
        }

        if (kphjje.subtract(sumKphjje).abs().compareTo(BigDecimal.ZERO) > 0) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.PRICE_TAX_SEPARATION_NE_KPHJJE);
        }
        return checkResultMap;
    }

    private Map<String, String> checkCommonOrderItemsV3(OrderInvoiceItemEntity orderInvoiceItem, int itemLength, String fpzldm) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.SUCCESS.getKey());

        /**
         * 校验包含金额的数据格式问题,如果格式不合法赋值为空,继续后续操作
         */
        //校验单价
        if (StringUtils.isNotBlank(orderInvoiceItem.getDj())) {
            if (!orderInvoiceItem.getDj().matches(PatternConstant.PATTERN_XMDJ)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107152);
            }
        }
        //校验数量
        if (StringUtils.isNotBlank(orderInvoiceItem.getXmsl())) {
            if (!orderInvoiceItem.getXmsl().matches(PatternConstant.PATTERN_XMSL)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107153);
            }
        }
        //校验金额
        if (StringUtils.isNotBlank(orderInvoiceItem.getJe())) {
            if (!orderInvoiceItem.getJe().matches(PatternConstant.PATTERN_XMJE)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107160);
            }
        }
        //校验税额
        if (StringUtils.isNotBlank(orderInvoiceItem.getSe())) {
            if (!orderInvoiceItem.getSe().matches(PatternConstant.PATTERN_XMJE)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107161);
            }
        }
        //校验扣除额
        if (StringUtils.isNotBlank(orderInvoiceItem.getKce())) {
            if (!orderInvoiceItem.getKce().matches(PatternConstant.PATTERN_XMJE)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107167);
            }
        }
        //校验税率
        if (StringUtils.isNotBlank(orderInvoiceItem.getSl())) {
            if (!orderInvoiceItem.getSl().matches(PatternConstant.PATTERN_SL)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107168);
            }
        }

        /**
         * 订单明细信息-规格型号
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107059, orderInvoiceItem.getGgxh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单明细信息-发票行性质
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107055, orderInvoiceItem.getFphxz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 发票行性质只能为:0正常行、1折扣行、2被折扣行、6清单红字发票
        if (!OrderInfoEnum.FPHXZ_CODE_0.getKey().equals(orderInvoiceItem.getFphxz())
                && !OrderInfoEnum.FPHXZ_CODE_1.getKey().equals(orderInvoiceItem.getFphxz())
                && !OrderInfoEnum.FPHXZ_CODE_2.getKey().equals(orderInvoiceItem.getFphxz())
                && !OrderInfoEnum.FPHXZ_CODE_6.getKey().equals(orderInvoiceItem.getFphxz())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107056);
        }

        // 商品编码非必传
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107100, orderInvoiceItem.getSpbm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 税率必传
        if (StringUtils.isBlank(orderInvoiceItem.getSl())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107146);
        }

        /**
         * 订单明细信息-项目名称
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107057, orderInvoiceItem.getXmmc());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单明细信息-项目单位
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107060, orderInvoiceItem.getDw());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单明细信息-扣除额
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107061, orderInvoiceItem.getKce());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单明细信息-项目金额
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107145, orderInvoiceItem.getJe());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 项目金额不能为0或者0.00
        if (ConfigureConstant.STRING_0.equals(orderInvoiceItem.getJe())
                || ConfigureConstant.STRING_000.equals(orderInvoiceItem.getJe())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107081);
        }
        // 合计金额为不为0时,需要保证金额为小数点后两位
        if (ConfigureConstant.DOUBLE_PENNY_ZERO != new BigDecimal(orderInvoiceItem.getJe()).doubleValue()
                && ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceItem.getJe())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107062);
        }

        /**
         * 订单明细信息-项目税额
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107134, orderInvoiceItem.getSe());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 合计税额为不为0时,需要保证税额为小数点后两位
        if (!StringUtils.isBlank(orderInvoiceItem.getSe())
                && ConfigureConstant.DOUBLE_PENNY_ZERO != new BigDecimal(orderInvoiceItem.getSe()).doubleValue()
                && ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceItem.getSe())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107133);
        }

        /**
         * 订单明细信息-项目数量
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107051, orderInvoiceItem.getXmsl());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (StringUtils.isNotBlank(orderInvoiceItem.getXmsl())) {
            if (!orderInvoiceItem.getXmsl().matches(PatternConstant.PATTERN_XMSL)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107153);
            }

            if (DecimalCalculateUtil.stringCompare(orderInvoiceItem.getXmsl(), ConfigureConstant.STRING_000) == 0) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107156);
            }
        }

        /**
         * 订单明细信息-项目单价
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107149, orderInvoiceItem.getDj());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (StringUtils.isNotBlank(orderInvoiceItem.getDj()) && !orderInvoiceItem.getDj().matches(PatternConstant.PATTERN_XMDJ)) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107152);
        }
        if (StringUtils.isNotBlank(orderInvoiceItem.getDj())) {
            if (!orderInvoiceItem.getDj().matches(PatternConstant.PATTERN_XMDJ)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107152);
            }

            if (DecimalCalculateUtil.stringCompare(orderInvoiceItem.getDj(), ConfigureConstant.STRING_000) == 0) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107159);
            }
        }


        /**
         * 订单明细信息-自行编码
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107063, orderInvoiceItem.getZxbm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单明细信息-含税标志
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107064, orderInvoiceItem.getHsbz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 含税标志只能为0和1：0表示都不含税,1表示都含税
        if (!OrderInfoEnum.HSBZ_1.getKey().equals(orderInvoiceItem.getHsbz())
                && !OrderInfoEnum.HSBZ_0.getKey().equals(orderInvoiceItem.getHsbz())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107065);
        }
        // 含税标志为0时,税额不能为空
        if (OrderInfoEnum.HSBZ_0.getKey().equals(orderInvoiceItem.getHsbz())
                && StringUtils.isBlank(orderInvoiceItem.getHsbz())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107139);
        }

        /**
         * 订单明细信息-商品编码
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107100, orderInvoiceItem.getSpbm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 商品编码必须为19位数字
        if (StringUtils.isNotBlank(orderInvoiceItem.getSpbm())) {
            boolean spbm = false;
            for (int j = 0; j < orderInvoiceItem.getSpbm().length(); j++) {
                char c = orderInvoiceItem.getSpbm().charAt(j);
                if ((c < '0' || c > '9')) {
                    spbm = true;
                    break;
                }
            }
            if (spbm) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107101);
            }
        }

        /**
         * 订单明细信息-增值税特殊管理
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107105, orderInvoiceItem.getZzstsgl());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 订单明细信息-优惠政策标识
         */
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107103, orderInvoiceItem.getYhzcbs());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 优惠政策标识只能为0或1,0:不使用,1:使用
        if (!OrderInfoEnum.YHZCBS_0.getKey().equals(orderInvoiceItem.getYhzcbs())
                && !OrderInfoEnum.YHZCBS_1.getKey().equals(orderInvoiceItem.getYhzcbs())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107102);
        }
        // 优惠政策标识为1时;
        if (ConfigureConstant.STRING_1.equals(orderInvoiceItem.getYhzcbs())) {
            if (StringUtils.isBlank(orderInvoiceItem.getZzstsgl())) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107104);
            }
            // 订单明细信息中YHZCBS(优惠政策标识)为1, 且税率为0, 则LSLBS只能根据实际情况选择"0或1或2"中的一种,
            // 不能选择3, 且ZZSTSGL内容也只能写与0/1/2对应的"出口零税/免税/不征税
            if (!StringUtils.isBlank(orderInvoiceItem.getSl()) && ConfigureConstant.STRING_0.equals(orderInvoiceItem.getSl())
                    && !OrderInfoEnum.LSLBS_0.getKey().equals(orderInvoiceItem.getLslbs())
                    && !OrderInfoEnum.LSLBS_1.getKey().equals(orderInvoiceItem.getLslbs())
                    && !OrderInfoEnum.LSLBS_2.getKey().equals(orderInvoiceItem.getLslbs())
                    && (StringUtils.isBlank(orderInvoiceItem.getZzstsgl()))) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107132);
            }

        }
        if (OrderInfoEnum.YHZCBS_0.getKey().equals(orderInvoiceItem.getYhzcbs())) {
            if (StringUtils.isNotBlank(orderInvoiceItem.getZzstsgl())) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107106);
            }
        }

        /**
         * 订单明细信息-零税率标识
         */
        if (!StringUtils.isBlank(orderInvoiceItem.getLslbs())
                && !OrderInfoEnum.LSLBS_0.getKey().equals(orderInvoiceItem.getLslbs())
                && !OrderInfoEnum.LSLBS_1.getKey().equals(orderInvoiceItem.getLslbs())
                && !OrderInfoEnum.LSLBS_2.getKey().equals(orderInvoiceItem.getLslbs())
                && !OrderInfoEnum.LSLBS_3.getKey().equals(orderInvoiceItem.getLslbs())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107138);
        }

        /**
         * 税率非空时,逻辑判断
         */
        if (!StringUtils.isBlank(orderInvoiceItem.getSl())) {
            /**
             * 增值税特殊管理不为空,不为不征税,不为免税,不为出口零税逻辑处理 如果是按5%简易征收需要保证税率为0.05
             * 如果是按3%简易征收需要保证税率为0.03 如果是简易征收需要保证税率为0.03或0.04或0.05
             * 如果是按5%简易征收减按1.5%计征需要保证税率为0.015
             */
            if ((!StringUtils.isBlank(orderInvoiceItem.getZzstsgl()))
                    && (!ConfigureConstant.STRING_BZS.equals(orderInvoiceItem.getZzstsgl()))
                    && (!ConfigureConstant.STRING_MS.equals(orderInvoiceItem.getZzstsgl()))
                    && (!ConfigureConstant.STRING_CKLS.equals(orderInvoiceItem.getZzstsgl()))) {
                if (orderInvoiceItem.getZzstsgl().contains(ConfigureConstant.STRING_ERROR_PERCENT)) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.COMMODITY_MESSAGE_SYNC_ERROR_173033);
                }
                switch (orderInvoiceItem.getZzstsgl()) {
                    case ConfigureConstant.STRING_JYZS5:
                        if (!ConfigureConstant.STRING_005.equals(orderInvoiceItem.getSl())) {
                            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107108);
                        }
                        break;
                    case ConfigureConstant.STRING_JYZS3:
                        if (!ConfigureConstant.STRING_003.equals(orderInvoiceItem.getSl())) {
                            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107109);
                        }
                        break;
                    case ConfigureConstant.STRING_JYZS:
                        if (!ConfigureConstant.STRING_003.equals(orderInvoiceItem.getSl())
                                && !ConfigureConstant.STRING_004.equals(orderInvoiceItem.getSl())
                                && !ConfigureConstant.STRING_005.equals(orderInvoiceItem.getSl())) {
                            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107110);
                        }
                        break;
                    case ConfigureConstant.STRING_JYZS5_1:
                        if (!ConfigureConstant.STRING_0015.equals(orderInvoiceItem.getSl())) {
                            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107111);
                        }

                        break;
                    default:
                        break;
                }

            }

            // 零税率标识不为空,税率必须为0
            if ((!StringUtils.isBlank(orderInvoiceItem.getLslbs()))
                    && (ConfigureConstant.DOUBLE_PENNY_ZERO != new BigDecimal(orderInvoiceItem.getSl()).doubleValue())) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107112);
            }
            // 零税率标识为空,税率不能为0
            if ((StringUtils.isBlank(orderInvoiceItem.getLslbs())) && (DecimalCalculateUtil.stringCompare(orderInvoiceItem.getSl(), ConfigureConstant.STRING_000) == 0)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107113);
            }
            /**
             * 税率不为空时,如果是专票,并且税率为0,提示错误,专票不可以开具0税率发票
             */
            boolean result = (OrderInfoEnum.ORDER_INVOICE_TYPE_0.getKey().equals(fpzldm) || OrderInfoEnum.ORDER_INVOICE_TYPE_52.getKey().equals(fpzldm)) && ConfigureConstant.STRING_000.equals(new BigDecimal(orderInvoiceItem.getSl()).setScale(ConfigureConstant.INT_2, RoundingMode.HALF_UP).toPlainString());

            if (result) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107118);
            }

        }

        // 订单明细信息中零税率标识为0/1/2, 但增值税特殊管理内容不为'出口零税/免税/不征税';
        boolean result3 = StringUtils.isBlank(orderInvoiceItem.getZzstsgl())
                && (OrderInfoEnum.LSLBS_0.getKey().equals(orderInvoiceItem.getLslbs())
                || OrderInfoEnum.LSLBS_1.getKey().equals(orderInvoiceItem.getLslbs())
                || OrderInfoEnum.LSLBS_2.getKey().equals(orderInvoiceItem.getLslbs()));
        if (result3) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107114);
        }

        if (OrderInfoEnum.LSLBS_0.getKey().equals(orderInvoiceItem.getLslbs())
                && !ConfigureConstant.STRING_CKLS.equals(orderInvoiceItem.getLslbs())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107114);
        }
        if (OrderInfoEnum.LSLBS_1.getKey().equals(orderInvoiceItem.getLslbs())
                && !ConfigureConstant.STRING_MS.equals(orderInvoiceItem.getZzstsgl())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107114);
        }
        if (OrderInfoEnum.LSLBS_2.getKey().equals(orderInvoiceItem.getLslbs())
                && !ConfigureConstant.STRING_BZS.equals(orderInvoiceItem.getZzstsgl())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107114);
        }
        boolean result4 = OrderInfoEnum.LSLBS_3.getKey().equals(orderInvoiceItem.getLslbs())
                && (!StringUtils.isBlank(orderInvoiceItem.getZzstsgl())
                || !(OrderInfoEnum.YHZCBS_0.getKey().equals(orderInvoiceItem.getYhzcbs())));
        if (result4) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.CHECK_ISS7PRI_107140);
        }

        return checkResultMap;
    }

}
