package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApiHzfpqrmxxxcxRspBO implements Serializable {

    private static final long serialVersionUID = 3136144163260510722L;

    /**
     *
     */
    private String total;
    /**
     *
     */
    private String size;
    /**
     *
     */
    private String current;
    /**
     * 主键
     */
    private String uuid;
    /**
     *
     */
    private String hzfpxxqrdbh;

    /**
     * 录入方身份 0 销售方 1 购买方
     */
    private String lrfsf;
    /**
     * 销售方纳税人识别号
     */
    private String xsfnsrsbh;
    /**
     * 销售方名称
     */
    private String xsfmc;
    /**
     * 购买方纳税人识别号
     */
    private String gmfnsrsbh;
    /**
     * 购买方名称
     */
    private String gmfmc;
    /**
     * 蓝字全电号码
     */
    private String lzqdhm;
    /**
     * 蓝字开票日期 格式: yyyy-mm-dd hh:mm:ss
     */
    private String lzkprq;
    /**
     * 蓝字发票金额
     */
    private String lzhjje;
    /**
     * 蓝字发票税额
     */
    private String lzhjse;
    /**
     * 蓝字发票票种代码
     */
    private String lzfppzdm;
    /**
     * 增值税用途代码 00 未勾选 01 已确认 03
     */
    private String zzsytdm;
    /**
     * 消费税用途代码 00 未勾选 01 已确认 03已勾选未确认
     */
    private String xfsytdm;
    /**
     * 发票入账状态代码 00 未入账 01 已入账
     */
    private String fprzztdm;
    /**
     * 红字查询金额
     */
    private String hzcxje;
    /**
     * 红字查询税额
     */
    private String hzcxse;
    /**
     * 冲红原因代码
     */
    private String chyydm;
    /**
     * 红字确认信息状态代码
     */
    private String hzqrxxztdm;
    /**
     * 已开具红字发票标志 y 已开具  n未开具
     */
    private String ykjhzfpbz;
    /**
     *
     */
    private List<ApiHzfpqrmxxxcxListRspBO> hzqrxxmxlist;
}
