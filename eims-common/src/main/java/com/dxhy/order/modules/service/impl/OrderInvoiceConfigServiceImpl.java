package com.dxhy.order.modules.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.OrderInvoiceConfigDao;
import com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity;
import com.dxhy.order.modules.entity.OrderInvoiceConfigEntity;
import com.dxhy.order.modules.service.InvoiceBackpushConfigService;
import com.dxhy.order.modules.service.OrderInvoiceConfigService;
import com.dxhy.order.utils.DistributedKeyMaker;
import com.dxhy.order.utils.HttpUtils;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@Service("orderInvoiceConfigService")
public class OrderInvoiceConfigServiceImpl extends ServiceImpl<OrderInvoiceConfigDao, OrderInvoiceConfigEntity> implements OrderInvoiceConfigService {

    @Autowired
    OrderInvoiceConfigDao orderInvoiceConfigDao;
    
    @Autowired
    InvoiceBackpushConfigService invoiceBackpushConfigService;

    @Value("${sso.deptByNameAndCodeUrl}")
    private String deptByNameAndCodeUrl;

    @Override
    public R saveOrderInvoiceConfigEntity(OrderInvoiceConfigEntity orderInvoiceConfig) {
        try {
            //查询主表数据是否存在
            OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(orderInvoiceConfig.getBaseNsrsbh());
            if (Objects.nonNull(orderInvoiceConfigEntity)) {
                orderInvoiceConfig.setUpdateTime(new Date());
                orderInvoiceConfig.setId(orderInvoiceConfigEntity.getId());
                orderInvoiceConfigDao.updateById(orderInvoiceConfig);
            }else {
                orderInvoiceConfig.setId(DistributedKeyMaker.generateShotKey());
                orderInvoiceConfig.setCode(DistributedKeyMaker.generateShotKey());
                orderInvoiceConfig.setCreateTime(new Date());
                //保存配置主表
                orderInvoiceConfigDao.insert(orderInvoiceConfig);
            }
            //发票回推配置，先删后插
            List<InvoiceBackpushConfigEntity> invoiceBackpushConfigList = orderInvoiceConfig.getBackpushConfigList();
            invoiceBackpushConfigService.deleteInvoiceBackpushConfigByOrderConfigId(orderInvoiceConfigEntity.getId());
            if(!CollectionUtils.isEmpty(invoiceBackpushConfigList)){
                invoiceBackpushConfigList.forEach(invoiceBackpushConfig -> {
                    invoiceBackpushConfig.setOrderConfigId(orderInvoiceConfig.getId());
                    invoiceBackpushConfigService.saveInvoiceBackpushConfig(invoiceBackpushConfig);
                });
            }
            return R.ok();
        } catch (Exception e) {
            log.error("saveOrderInvoiceConfigEntity 异常: {}", e);
            return R.error("9999", "保存失败！");
        }
    }

    @Override
    public R saveChgz(OrderInvoiceConfigEntity orderInvoiceConfig) {
        log.info("拆合规则，保存入参：{}", JSONObject.toJSONString(orderInvoiceConfig));
        if (ObjectUtils.isEmpty(orderInvoiceConfig)) {
            log.info("拆合规则，入参为空");
            return R.error();
        }
        try {
            OrderInvoiceConfigEntity orderInvoiceConfigEntity = new OrderInvoiceConfigEntity();
            // 订单合并规则 - 0 不合并同类明细 1 合并同类明细
            orderInvoiceConfigEntity.setHbfsBhbtlmx(orderInvoiceConfig.getHbfsBhbtlmx());
            // 订单合并规则 - 合并同类明细 1商品税率；2单价；3商品名称；4规格型号；5计量单位',
            if (!StringUtils.isEmpty(orderInvoiceConfig.getHbfsHbtlmx())) {
                String hbtlmx = "hbtlmx";
                if (orderInvoiceConfig.getHbfsHbtlmx().contains("商品税率")) {
                    hbtlmx = hbtlmx + "," + "1";
                }
                if (orderInvoiceConfig.getHbfsHbtlmx().contains("单价一致时合并")) {
                    hbtlmx = hbtlmx + "," + "2";
                }
                if (orderInvoiceConfig.getHbfsHbtlmx().contains("名称")) {
                    hbtlmx = hbtlmx + "," + "3";
                }
                if (orderInvoiceConfig.getHbfsHbtlmx().contains("规格型号")) {
                    hbtlmx = hbtlmx + "," + "4";
                }
                if (orderInvoiceConfig.getHbfsHbtlmx().contains("计量单位")) {
                    hbtlmx = hbtlmx + "," + "5";
                }
                orderInvoiceConfigEntity.setHbfsHbtlmx(hbtlmx.replace("hbtlmx,", ""));
            }
            // 订单合并规则 - 负数行合并
            orderInvoiceConfigEntity.setHbfsFshhb(orderInvoiceConfig.getHbfsFshhb());
            // 订单合并规则 - 商品行排序
            orderInvoiceConfigEntity.setQtgzSphpx(orderInvoiceConfig.getQtgzSphpx());
            // 订单合并规则 - 发票行备注规则
            orderInvoiceConfigEntity.setQtgzFpbzhgz(orderInvoiceConfig.getQtgzFpbzhgz());
            // 订单合并规则 - 发票行备注规则 - 符号 1；  2，  3 换行
            orderInvoiceConfigEntity.setQtgzFpbzhgzFh(orderInvoiceConfig.getQtgzFpbzhgzFh());

            //OrderInvoiceConfigEntity entity = orderInvoiceConfigDao.selectAllList();
            OrderInvoiceConfigEntity entity = orderInvoiceConfigDao.selectByNsrsbh(orderInvoiceConfig.getBaseNsrsbh());

            // 更新
            if (Objects.nonNull(entity)) {
                entity.setHbfsBhbtlmx(orderInvoiceConfigEntity.getHbfsBhbtlmx());
                entity.setHbfsHbtlmx(orderInvoiceConfigEntity.getHbfsHbtlmx());
                entity.setHbfsFshhb(StringUtils.isEmpty(orderInvoiceConfigEntity.getHbfsFshhb()) ? "" : orderInvoiceConfigEntity.getHbfsFshhb());
                entity.setQtgzSphpx(orderInvoiceConfigEntity.getQtgzSphpx());
                entity.setQtgzFpbzhgz(orderInvoiceConfigEntity.getQtgzFpbzhgz());
                entity.setQtgzFpbzhgzFh(orderInvoiceConfigEntity.getQtgzFpbzhgzFh());
                entity.setUpdateTime(new Date());
                log.info("拆合规则，更新操作：{}", JSONObject.toJSONString(entity));
                orderInvoiceConfigDao.updateById(entity);
            } else {
                // 插入
                orderInvoiceConfigEntity.setId(DistributedKeyMaker.generateShotKey());
                orderInvoiceConfigEntity.setCode(DistributedKeyMaker.generateShotKey());
                orderInvoiceConfigEntity.setCreateTime(new Date());
                log.info("拆合规则，插入操作：{}", JSONObject.toJSONString(orderInvoiceConfigEntity));
                orderInvoiceConfigDao.insert(orderInvoiceConfigEntity);
            }
        } catch (Exception e) {
            log.error("拆合规则，保存处理异常: {}", e);
            return R.error();
        }
        log.info("拆合规则，保存操作成功");
        return R.ok();
    }

    @Override
    public R selectOrderInvoiceConfigEntity(OrderInvoiceConfigEntity orderInvoiceConfig) {
        log.info("配置管理，查询开票配置，入参：{}", JsonUtils.getInstance().toJsonString(orderInvoiceConfig));
        OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(orderInvoiceConfig.getBaseNsrsbh());
        if (ObjectUtils.isEmpty(orderInvoiceConfigEntity)) {
            OrderInvoiceConfigEntity entity = new OrderInvoiceConfigEntity();
            entity.setId(DistributedKeyMaker.generateShotKey());
            entity.setNsrsbh(orderInvoiceConfig.getBaseNsrsbh());
            entity.setCode(DistributedKeyMaker.generateShotKey());
            entity.setNsrlx(orderInvoiceConfig.getNsrlx());
            entity.setQykxsl(orderInvoiceConfig.getQykxsl());
            entity.setGfjfsj("1");
            entity.setGfjhyx("1");
            entity.setKpzdjf("1");
            entity.setJezdkp("");
            entity.setKhxxzdbc("0");
            entity.setHzfpzdkj("1");
            entity.setZnppssflbm("0");
            entity.setDjcjsz("0");
            entity.setSpmxhsz("1");

            entity.setHbfsBhbtlmx("0");
            entity.setHbfsHbtlmx("商品税率,单价一致时合并");
            entity.setHbfsFshhb("1");
            entity.setQtgzSphpx("0");
            entity.setQtgzFpbzhgz("1");
            entity.setQtgzFpbzhgzFh("1");
            entity.setCreateTime(new Date());
            entity.setBackpushConfigList(null);
            int i = orderInvoiceConfigDao.insert(entity);
            if (i < 1) {
                log.info("配置管理，初始化开票配置失败");
            }
            log.info("配置管理，初始化开票配置成功：{}", JsonUtils.getInstance().toJsonString(entity));
            return R.ok().put("data", entity);
        }
        log.info("配置管理，开票配置已存在：{}", JsonUtils.getInstance().toJsonString(orderInvoiceConfigEntity));
        // 1商品税率、2单价一致时合并、3名称、4规则型号、5计量单位
        if (!StringUtils.isEmpty(orderInvoiceConfigEntity.getHbfsHbtlmx())) {
            String s = orderInvoiceConfigEntity.getHbfsHbtlmx().replace("1", "商品税率").replace("2", "单价一致时合并").replace("3", "名称").replace("4", "规则型号").replace("5", "计量单位");
            orderInvoiceConfigEntity.setHbfsHbtlmx(s);
        } else {
            orderInvoiceConfigEntity.setHbfsHbtlmx("商品税率,单价一致时合并");
        }
        List<InvoiceBackpushConfigEntity> backpushList = invoiceBackpushConfigService.getInvoiceBackpushConfigByOrderConfigId(orderInvoiceConfigEntity.getId());
        orderInvoiceConfigEntity.setBackpushConfigList(backpushList);
        orderInvoiceConfigEntity.setHbfsFshhb(StringUtils.isEmpty(orderInvoiceConfigEntity.getHbfsFshhb()) ? "" : orderInvoiceConfigEntity.getHbfsFshhb());
        orderInvoiceConfigEntity.setQtgzFpbzhgzFh(StringUtils.isEmpty(orderInvoiceConfigEntity.getQtgzFpbzhgzFh()) ? "1" : orderInvoiceConfigEntity.getQtgzFpbzhgzFh());
        log.info("配置管理，开票配置已存在，返回：{}", JsonUtils.getInstance().toJsonString(orderInvoiceConfigEntity));
        return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, orderInvoiceConfigEntity);
    }

    @Override
    public R taxpayerNsrsbh(OrderInvoiceConfigEntity orderInvoiceConfig) {
        // 同步小规模纳税人或者一般纳税人
        R r = null;
        try {

            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            // 获取token
            String auth = request.getHeader("Authorization");
            log.info("taxpayerNsrsbh get Authorization: {}", auth);
            Map token = new HashMap();
            token.put("Authorization", auth);
            log.info("taxpayerNsrsbh token: {}", JsonUtils.getInstance().toJsonString(token));

            Map req = new HashMap();
            req.put("name", "");
            req.put("taxpayerCode", orderInvoiceConfig.getBaseNsrsbh());
            // String res = HttpUtils.doPost(deptByNameAndCodeUrl, JsonUtils.getInstance().toJsonString(req));
            String res = HttpUtils.doPostWithHeader(deptByNameAndCodeUrl, JsonUtils.getInstance().toJsonString(req), token);

            Map resp = JsonUtils.getInstance().parseObject(res, HashMap.class);
            log.info("taxpayerNsrsbh 是否小规模实体返回: {}", JsonUtils.getInstance().toJsonString(resp));
            // 一般纳税人-1  小规模-0
            String taxpayerType = "1";
            if ("0000".equals(resp.get("code"))) {
                Map data = JsonUtils.getInstance().parseObject(resp.get("data").toString(), HashMap.class);
                taxpayerType = data.get("taxpayerType").toString();
                log.info("taxpayerNsrsbh taxpayerType小规模纳税人标识: {}", taxpayerType);
            }
            r = R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, taxpayerType);
        } catch (Exception e) {
            log.error("执行taxpayerNsrsbh异常: {}", e);
            r = R.error();
        }
        return r;
    }


    @Override
    public int insertDefaultInfo(String nsrsbh) {
        log.info("配置管理，开票配置税号：{}，初始化入", nsrsbh);
        int i = -1;
        OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(nsrsbh);
        if (ObjectUtils.isEmpty(orderInvoiceConfigEntity)) {

            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            // 获取token
            String auth = request.getHeader("Authorization");
            log.info("配置管理 get Authorization: {}", auth);
            Map token = new HashMap();
            token.put("Authorization", auth);
            log.info("配置管理 token: {}", JsonUtils.getInstance().toJsonString(token));


            Map req = new HashMap();
            req.put("name", "");
            req.put("taxpayerCode", nsrsbh);
            // String res = HttpUtils.doPost(deptByNameAndCodeUrl, JsonUtils.getInstance().toJsonString(req));
            String res = HttpUtils.doPostWithHeader(deptByNameAndCodeUrl, JsonUtils.getInstance().toJsonString(req), token);

            Map resp = JsonUtils.getInstance().parseObject(res, HashMap.class);
            log.info("配置管理，税号：{}，开票配置初始化企业数据拉取返回:{}", nsrsbh, JsonUtils.getInstance().toJsonString(resp));
            if ("0000".equals(resp.get("code").toString())) {
                Map data = JsonUtils.getInstance().parseObject(resp.get("data").toString(), HashMap.class);
                String taxpayerType = data.get("taxpayerType").toString();

                OrderInvoiceConfigEntity entity = new OrderInvoiceConfigEntity();
                entity.setId(DistributedKeyMaker.generateShotKey());
                entity.setNsrsbh(nsrsbh);
                entity.setCode(DistributedKeyMaker.generateShotKey());
                entity.setNsrlx(taxpayerType);
                // 纳税人类型(1:一般纳税人 0:小规模纳税人)
                if ("0".equals(taxpayerType)) {
                    entity.setQykxsl("17%、16%、13%、11%、10%、9%、6%、5%、4%、3%、1.5%、1%、0%");
                } else {
                    entity.setQykxsl("5%、4%、3%、1.5%、1%");
                }
                entity.setGfjfsj("1");
                entity.setGfjhyx("1");
                entity.setKpzdjf("1");
                entity.setJezdkp("");
                entity.setKhxxzdbc("0");
                entity.setHzfpzdkj("1");
                entity.setZnppssflbm("0");
                entity.setDjcjsz("0");
                entity.setSpmxhsz("1");

                entity.setHbfsBhbtlmx("0");
                entity.setHbfsHbtlmx("商品税率,单价一致时合并");
                entity.setHbfsFshhb("1");
                entity.setQtgzSphpx("0");
                entity.setQtgzFpbzhgz("1");
                entity.setQtgzFpbzhgzFh("1");
                entity.setCreateTime(new Date());
                i = orderInvoiceConfigDao.insert(entity);
                if (i < 1) {
                    log.info("配置管理，开票配置税号：{}，初始化失败", nsrsbh);
                } else {
                    log.info("配置管理，开票配置税号：{}，初始化完成:{}", nsrsbh, JsonUtils.getInstance().toJsonString(entity));
                }
            }
        }
        return i;
    }
}
