package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.constant.InvoiceItemLinePropertyEnum;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.ItemInfoDao;
import com.dxhy.order.modules.dao.OrderInvoiceConfigDao;
import com.dxhy.order.modules.dao.OrderInvoiceItemDao;
import com.dxhy.order.modules.dao.TaxClassCodeDao;
import com.dxhy.order.modules.entity.InvoiceImportItemEntity;
import com.dxhy.order.modules.entity.OrderInvoiceConfigEntity;
import com.dxhy.order.modules.entity.OrderInvoiceItemEntity;
import com.dxhy.order.modules.entity.TaxClassCodeEntity;
import com.dxhy.order.modules.service.OrderInvoiceItemService;
import com.dxhy.order.utils.EasyExcels;
import com.dxhy.order.utils.PageUtils;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service("orderInvoiceItemService")
@Slf4j
public class OrderInvoiceItemServiceImpl extends ServiceImpl<OrderInvoiceItemDao, OrderInvoiceItemEntity> implements OrderInvoiceItemService {

    @Autowired
    OrderInvoiceItemDao orderInvoiceItemDao;
    @Autowired
    ItemInfoDao itemInfoDao;
    @Resource
    private OrderInvoiceConfigDao orderInvoiceConfigDao;
    @Resource
    private TaxClassCodeDao taxClassCodeDao;

    @Override
    public PageUtils queryPage(OrderInvoiceItemEntity orderInvoiceItemEntity) {
        Page page = new Page(orderInvoiceItemEntity.getCurrPage(), orderInvoiceItemEntity.getPageSize());
        List<OrderInvoiceItemEntity> list = orderInvoiceItemDao.selectList(page, orderInvoiceItemEntity);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public R uploadInvoiceItemInfo(MultipartFile file, String hsbs, String baseNsrsbh) {
        InvoiceImportItemEntity invoiceImportItemEntity = null;
        try {

            List<Map<String, String>> excelContentList = EasyExcels.getExcelContent(file, OrderInvoiceItemEntity.class);
            List<OrderInvoiceItemEntity> successInvoiceItemList = new ArrayList<>();
            List<OrderInvoiceItemEntity> failInvoiceItemList = new ArrayList<>();
            invoiceImportItemEntity = new InvoiceImportItemEntity();
            for (int i = 2; i < excelContentList.size(); i++) {
                List<String> msgList = new ArrayList<>();
                OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                // i代表行数
                // 每一列数据 组成map
                Map<String, String> list = excelContentList.get(i);
                if (list.size() > 0) {
                    if (StringUtils.isEmpty(list.get(0))) {
                        msgList.add(new String("项目名称不能为空"));
                    } else {
                        if (list.get(0).contains("*")) {
                            msgList.add(new String("项目名称输入不合法"));
                        }
                    }
                    // 查询配置 是否 智能匹配税收分类编码
                    //OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectAllList();
                    OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(baseNsrsbh);
                    if (ObjectUtils.isEmpty(orderInvoiceConfigEntity)) {
                        return R.error("9999", "未配置开票设置！");
                    }

                    // 如果项目信息已经在【项目信息维护】功能中维护,则在开票明细导入模板中“商品和税收编码”非必填,以系统内保存内容为准
//                    ItemInfoEntity itemInfoEntity1 = new ItemInfoEntity();
//                    itemInfoEntity1.setXmmc(list.get(0));
//                    ItemInfoEntity itemInfoEntity = itemInfoDao.selectItemInfoByName(itemInfoEntity1);
                    TaxClassCodeEntity taxClassCodeEntity = taxClassCodeDao.getTaxClassCodeSpjc(list.get(1));
                    // TaxClassCodeEntity taxClassCodeEntity = taxClassCodeDao.getTaxClassCodeXmmc(list.get(0));
                    if (Objects.nonNull(taxClassCodeEntity)) {
                        orderInvoiceItemEntity.setXmmc((StringUtils.isEmpty(list.get(0)) ? "*" + taxClassCodeEntity.getSpjc() + "*" + taxClassCodeEntity.getSpmc() : "*" + taxClassCodeEntity.getSpjc() + "*" + list.get(0).trim()));
                        // 智能匹配税收分类编码
                        if ("0".equals(orderInvoiceConfigEntity.getZnppssflbm())) {
                            // 根据商品名称去 匹配 税收分类编码
                            orderInvoiceItemEntity.setSpbm(taxClassCodeEntity.getSpbm());
                            //orderInvoiceItemEntity.setSpbm(taxClassCodeEntity.getSpjc());
                        } else {
                            orderInvoiceItemEntity.setSpbm(list.get(1));
                        }
                    } else {
                        TaxClassCodeEntity taxClassCodeEntity1 = taxClassCodeDao.getTaxClassCodeXmmc(list.get(0));
                        if (!ObjectUtils.isEmpty(taxClassCodeEntity1)) {
                            orderInvoiceItemEntity.setXmmc(StringUtils.isEmpty(list.get(0)) ? "" : list.get(0).trim());
                            orderInvoiceItemEntity.setSpbm(taxClassCodeEntity1.getSpbm());
                        } else {
                            msgList.add(new String("服务税收分类编码不存在"));
                            // 如系统内没有改项目的信息,则在开票明细导入模板中“商品和税收编码必填,以表内导入为准
                            if (StringUtils.isEmpty(list.get(1))) {
                                msgList.add(new String("商品和服务税收分类编码不为空"));
                            }

                            orderInvoiceItemEntity.setXmmc(StringUtils.isEmpty(list.get(0)) ? "" : list.get(0).trim());
                            orderInvoiceItemEntity.setSpbm(StringUtils.isEmpty(list.get(1)) ? "" : list.get(1).trim());
                        }

//                        orderInvoiceItemEntity.setXmmc(StringUtils.isEmpty(list.get(0)) ? "" : list.get(0).trim());
//                        orderInvoiceItemEntity.setSpbm(StringUtils.isEmpty(list.get(1)) ? "" : list.get(1).trim());

                    }
                    orderInvoiceItemEntity.setGgxh(StringUtils.isEmpty(list.get(2)) ? "" : list.get(2).trim());
                    orderInvoiceItemEntity.setDw(StringUtils.isEmpty(list.get(3)) ? "" : list.get(3).trim());
                    // 小数位数总长度不超过13位
                    if (!StringUtils.isEmpty(list.get(4))) {
                        String s = list.get(4).trim();
                        String[] split = s.split("\\.");
                        if (split.length == 2) {
                            String bit = split[1];
                            if (bit.length() > 13) {
                                msgList.add(new String("数量不得超过13位"));
                            }
                        }
                    }
                    // 小数位数总长度不超过13位
                    if (!StringUtils.isEmpty(list.get(5))) {
                        String s = list.get(5).trim();
                        String[] split = s.split("\\.");
                        if (split.length == 2) {
                            String bit = split[1];
                            if (bit.length() > 13) {
                                msgList.add(new String("单价不得超过13位"));
                            }
                        }
                    }
                    // 小数位数不超过2位
                    if (!StringUtils.isEmpty(list.get(6))) {
                        String s = list.get(6).trim();
                        String[] split = s.split("\\.");
                        if (split.length == 2) {
                            String bit = split[1];
                            if (bit.length() > 2) {
                                msgList.add(new String("金额不得超过2位"));
                            }
                        }
                    }
                    // 如果数量 单价 金额 都为空则提示数量 单价 金额都不能为空
                    if (StringUtils.isEmpty(list.get(4)) && StringUtils.isEmpty(list.get(5)) && StringUtils.isEmpty(list.get(6))) {
                        msgList.add(new String("数量、单价、金额都不能为空"));
                    }
                    // 金额不能为空
                    if (StringUtils.isEmpty(list.get(6))) {
                        msgList.add(new String("金额不能为空"));
                    }
                    // 如果三项均填写，导入后系统将默认以填写的数量和单价为准重算金额
                    if ((!StringUtils.isEmpty(list.get(4)) && !StringUtils.isEmpty(list.get(5)) && !StringUtils.isEmpty(list.get(6)))
                            || (!StringUtils.isEmpty(list.get(4)) && !StringUtils.isEmpty(list.get(5)) && StringUtils.isEmpty(list.get(6)))) {
                        /*BigDecimal xmsl = new BigDecimal(list.get(4).trim());
                        BigDecimal dj = new BigDecimal(list.get(5).trim());
                        BigDecimal je = xmsl.multiply(dj).setScale(2, BigDecimal.ROUND_HALF_UP);//截取结果两位
                        orderInvoiceItemEntity.setJe(je.toString());
                        orderInvoiceItemEntity.setXmsl(xmsl.toString());
                        // 单价0.00
                        String s = formatDecimal(dj);
                        orderInvoiceItemEntity.setDj(s);*/
                        BigDecimal xmsl = new BigDecimal(list.get(4).trim());
                        BigDecimal dj = new BigDecimal(list.get(5).trim());
                        // 单价0.00
                        String s = formatDecimal(dj);
                        orderInvoiceItemEntity.setDj(s);
                        BigDecimal bigDecimalDj = new BigDecimal(s);
                        BigDecimal je = xmsl.multiply(bigDecimalDj).setScale(2, BigDecimal.ROUND_HALF_UP);;//截取结果两位
                        orderInvoiceItemEntity.setJe(je.toString());
                        orderInvoiceItemEntity.setXmsl(xmsl.toString());

                    }
                    // 数量 金额 不空 ，单价为空
                    if (!StringUtils.isEmpty(list.get(4)) && StringUtils.isEmpty(list.get(5)) && !StringUtils.isEmpty(list.get(6))) {
                        BigDecimal xmsl = new BigDecimal(list.get(4).trim());
                        BigDecimal je = new BigDecimal(list.get(6).trim());
                        BigDecimal dj = je.divide(xmsl, 2, RoundingMode.HALF_UP);
                        orderInvoiceItemEntity.setDj(dj.toString());
                        orderInvoiceItemEntity.setXmsl(xmsl.toString());
                        // 金额0.00
                        String s = formatDecimal(je);
                        orderInvoiceItemEntity.setJe(s);
                    }
                    // 数量为空 ，单价和金额不为空
                    if (StringUtils.isEmpty(list.get(4)) && !StringUtils.isEmpty(list.get(5)) && !StringUtils.isEmpty(list.get(6))) {
                        BigDecimal je = new BigDecimal(list.get(6).trim());
                        BigDecimal dj = new BigDecimal(list.get(5).trim());
                        BigDecimal xmsl = je.divide(dj, 2, RoundingMode.HALF_UP);
                        orderInvoiceItemEntity.setXmsl(xmsl.toString());
                        // 单价0.00
                        String s1 = formatDecimal(dj);
                        orderInvoiceItemEntity.setDj(s1);
                        // 金额0.00
                        String s = formatDecimal(je);
                        orderInvoiceItemEntity.setJe(s);
                    }
                    // 税率不能为空
                    if (StringUtils.isEmpty(list.get(7))) {
                        msgList.add(new String("税率不能为空"));
                    } else {
                        String sl = list.get(7).trim();
                        Float aFloat = Float.valueOf(sl);
                        if (aFloat >= 1) {
                            String spsl = new String("税率不得超过100%");
                            msgList.add(spsl);
                        } else {
                            Float aFloat1 = slTranst(sl);
                            String s = String.valueOf(aFloat1);
                            orderInvoiceItemEntity.setSl(s);
                        }
                    }
                    orderInvoiceItemEntity.setZke(StringUtils.isEmpty(list.get(8)) ? "" : list.get(8).trim());
                    if (!CollectionUtils.isEmpty(msgList)) {
                        // 每一行的失败信息
                        String msg = StringUtils.join(msgList, ",");
                        orderInvoiceItemEntity.setMsg(msg);
                        OrderInvoiceItemEntity orderInvoiceItemFailZKEEntity = null;
                        if (!StringUtils.isEmpty(list.get(8))) {
                            String trim = list.get(8).trim();
                            String s1 = formatDecimal(new BigDecimal(trim));
                            orderInvoiceItemFailZKEEntity = new OrderInvoiceItemEntity();
                            orderInvoiceItemFailZKEEntity.setSl(orderInvoiceItemEntity.getSl());
                            orderInvoiceItemFailZKEEntity.setJe("-" + s1);
                            orderInvoiceItemFailZKEEntity.setFphxz(InvoiceItemLinePropertyEnum.INVOICE_ITEM_LINE_PROPERTY_ENUM_1.getKey());
                            orderInvoiceItemFailZKEEntity.setXmmc(list.get(0) + "-折扣");
                            orderInvoiceItemFailZKEEntity.setDj("");
                            orderInvoiceItemFailZKEEntity.setDw("");
                            orderInvoiceItemFailZKEEntity.setGgxh("");
                            orderInvoiceItemFailZKEEntity.setSpbm("");
                            orderInvoiceItemFailZKEEntity.setZke("");
                            orderInvoiceItemFailZKEEntity.setXmsl("");
                            // 失败信息折扣行 不含税 税额
                            if (StringUtils.isEmpty(orderInvoiceItemFailZKEEntity.getSl())) {
                                orderInvoiceItemFailZKEEntity.setSe(BigDecimal.ZERO.toString());
                            } else {
                                if ("0".equals(hsbs)) {
                                    BigDecimal zkje = new BigDecimal(list.get(8).trim());
                                    BigDecimal sl = new BigDecimal(orderInvoiceItemFailZKEEntity.getSl());
                                    BigDecimal se = zkje.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                    String s = formatDecimal(se);
                                    orderInvoiceItemFailZKEEntity.setSe("-" + s);
                                } else {
                                    // 含税计算税额
                                    // 含税金额
                                    BigDecimal je = new BigDecimal(list.get(8).trim());
                                    // 税率
                                    BigDecimal bigDecimalSL = new BigDecimal(orderInvoiceItemFailZKEEntity.getSl());
                                    BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                    BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                    // 税额
                                    BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    String s = se.toString();
                                    orderInvoiceItemFailZKEEntity.setSe("-" + s);
                                }
                            }

                        }
                        orderInvoiceItemEntity.setFphxz("2");
                        orderInvoiceItemEntity.setHsbz(hsbs);
                        if (StringUtils.isEmpty(orderInvoiceItemEntity.getJe()) || StringUtils.isEmpty(orderInvoiceItemEntity.getSl())) {
                            orderInvoiceItemEntity.setSe(BigDecimal.ZERO.toString());
                        } else {
                            // 失败信息 计算税额 不含税
                            if ("0".equals(hsbs)) {
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                BigDecimal sl = new BigDecimal(orderInvoiceItemEntity.getSl());
                                BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                String s = formatDecimal(se);
                                orderInvoiceItemEntity.setSe(s);
                            } else {
                                // 含税计算税额
                                // 含税金额
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                // 税率
                                BigDecimal bigDecimalSL = new BigDecimal(orderInvoiceItemEntity.getSl());
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                                orderInvoiceItemEntity.setSe(se.toString());
                            }
                        }
                        if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSl()) && !orderInvoiceItemEntity.getSl().contains("%")) {
                            String percent = this.getPercent(Float.valueOf(orderInvoiceItemEntity.getSl()), 0);
                            orderInvoiceItemEntity.setSl(percent);
                        }
                        failInvoiceItemList.add(orderInvoiceItemEntity);
                        orderInvoiceItemEntity.setFphxz("0");
                        if (Objects.nonNull(orderInvoiceItemFailZKEEntity)) {
                            orderInvoiceItemEntity.setFphxz("2");
                            if (!StringUtils.isEmpty(orderInvoiceItemFailZKEEntity.getSl()) && !orderInvoiceItemFailZKEEntity.getSl().contains("%")) {
                                String percent = this.getPercent(Float.valueOf(orderInvoiceItemFailZKEEntity.getSl()), 0);
                                orderInvoiceItemFailZKEEntity.setSl(percent);
                            }
                            failInvoiceItemList.add(orderInvoiceItemFailZKEEntity);
                        }
                    } else {
                        // 正确数据
                        // 折扣行
                        OrderInvoiceItemEntity orderInvoiceItemZKEEntity = null;
                        if (!StringUtils.isEmpty(list.get(8))) {
                            orderInvoiceItemZKEEntity = new OrderInvoiceItemEntity();
                            orderInvoiceItemZKEEntity.setSl(orderInvoiceItemEntity.getSl());
                            orderInvoiceItemZKEEntity.setJe("-" + list.get(8).trim());
                            orderInvoiceItemZKEEntity.setFphxz("1");
                            orderInvoiceItemZKEEntity.setXmmc(orderInvoiceItemEntity.getXmmc() + "-折扣");
                            //orderInvoiceItemZKEEntity.setXmmc(list.get(0) + "-折扣");
                            orderInvoiceItemZKEEntity.setDj("");
                            orderInvoiceItemZKEEntity.setDw("");
                            orderInvoiceItemZKEEntity.setGgxh("");
                            orderInvoiceItemZKEEntity.setSpbm("");
                            orderInvoiceItemZKEEntity.setZke("");
                            orderInvoiceItemZKEEntity.setXmsl("");
                            if (StringUtils.isEmpty(orderInvoiceItemZKEEntity.getSl())) {
                                orderInvoiceItemZKEEntity.setSe(BigDecimal.ZERO.toString());
                            } else {
                                // 成功信息折扣行 不含税 税额
                                if ("0".equals(hsbs)) {
                                    BigDecimal zkje = new BigDecimal(list.get(8).trim());
                                    BigDecimal sl = new BigDecimal(orderInvoiceItemZKEEntity.getSl());
                                    BigDecimal se = zkje.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                    String s = formatDecimal(se);
                                    orderInvoiceItemZKEEntity.setSe("-" + s);
                                } else {
                                    // 含税计算税额
                                    // 含税金额
                                    BigDecimal je = new BigDecimal(list.get(8).trim());
                                    // 税率
                                    BigDecimal bigDecimalSL = new BigDecimal(orderInvoiceItemZKEEntity.getSl());
                                    BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                    BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                    // 税额
                                    BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    String s = se.toString();
                                    orderInvoiceItemZKEEntity.setSe("-" + s);
                                }
                            }

                        }
                        orderInvoiceItemEntity.setFphxz("2");
                        orderInvoiceItemEntity.setHsbz(hsbs);
                        if (StringUtils.isEmpty(orderInvoiceItemEntity.getJe()) || StringUtils.isEmpty(orderInvoiceItemEntity.getSl())) {
                            orderInvoiceItemEntity.setSe(BigDecimal.ZERO.toString());
                        } else {
                            // 成功信息 计算税额 不含税
                            if ("0".equals(hsbs)) {
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                BigDecimal sl = new BigDecimal(orderInvoiceItemEntity.getSl());
                                BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                String s = formatDecimal(se);
                                orderInvoiceItemEntity.setSe(s);
                            } else {
                                // 含税计算税额
                                // 含税金额
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                // 税率
                                BigDecimal bigDecimalSL = new BigDecimal(orderInvoiceItemEntity.getSl());
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
//                                BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);

                                orderInvoiceItemEntity.setSe(je.subtract(divide).toPlainString());
                            }
                        }
                        if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSl()) && !orderInvoiceItemEntity.getSl().contains("%")) {
                            String percent = this.getPercent(Float.valueOf(orderInvoiceItemEntity.getSl()), 0);
                            orderInvoiceItemEntity.setSl(percent);
                        }
                        successInvoiceItemList.add(orderInvoiceItemEntity);
                        orderInvoiceItemEntity.setFphxz("0");
                        if (Objects.nonNull(orderInvoiceItemZKEEntity)) {
                            orderInvoiceItemEntity.setFphxz("2");
                            if (!StringUtils.isEmpty(orderInvoiceItemZKEEntity.getSl()) && !orderInvoiceItemZKEEntity.getSl().contains("%")) {
                                String percent = this.getPercent(Float.valueOf(orderInvoiceItemZKEEntity.getSl()), 0);
                                orderInvoiceItemZKEEntity.setSl(percent);
                            }
                            successInvoiceItemList.add(orderInvoiceItemZKEEntity);
                        }
                    }
                }
            }
            if (!CollectionUtils.isEmpty(successInvoiceItemList)) {
                // 被折扣行
                List<BigDecimal> bzkhs = new ArrayList<>();
                List<BigDecimal> zkhs = new ArrayList<>();
                for (OrderInvoiceItemEntity orderInvoiceItemEntity : successInvoiceItemList) {
                    if (orderInvoiceItemEntity.getJe().contains("-")) {
                        String replace = orderInvoiceItemEntity.getJe().replace("-", "");
                        zkhs.add(new BigDecimal(replace));
                    } else {
                        bzkhs.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                    }
                }
                // 被折扣行总和
                BigDecimal bzkhSum = bzkhs.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 折扣行总和
                BigDecimal zkhSum = zkhs.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                invoiceImportItemEntity.setSuccessAmount(bzkhSum.subtract(zkhSum).toString());
                invoiceImportItemEntity.setSuccessNum("" + successInvoiceItemList.size());
            }
            if (!CollectionUtils.isEmpty(failInvoiceItemList)) {
                // 被折扣行
                List<BigDecimal> bzkhs = new ArrayList<>();
                List<BigDecimal> zkhs = new ArrayList<>();
                for (OrderInvoiceItemEntity orderInvoiceItemEntity : failInvoiceItemList) {
                    if (orderInvoiceItemEntity.getJe().contains("-")) {
                        zkhs.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                    } else {
                        bzkhs.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                    }
                }
                // 被折扣行总和
                BigDecimal bzkhSum = bzkhs.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 折扣行总和
                BigDecimal zkhSum = zkhs.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                invoiceImportItemEntity.setFailAmount(bzkhSum.subtract(zkhSum).toString());
                invoiceImportItemEntity.setFailNumber("" + failInvoiceItemList.size());

            }
            invoiceImportItemEntity.setSuccessInvoiceItemList(successInvoiceItemList);
            invoiceImportItemEntity.setFailInvoiceItemList(failInvoiceItemList);
            invoiceImportItemEntity.setHsbs(hsbs);
        } catch (Exception e) {
            log.error("uploadInvoiceItemInfo 明细导入出现异常: {}", e);
            return R.setCodeAndMsg(OrderInfoContentEnum.ERROR, null);
        }
        return R.setCodeAndMsg(OrderInfoContentEnum.SUCCESS, invoiceImportItemEntity);
    }

    // 不足两位小数补0
    private String formatDecimal(BigDecimal bc) {
        // 不足两位小数补0
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(bc);
    }

    // 小数转百分数
    private String getPercent(double data, int digit) {
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMinimumFractionDigits(digit);
        return numberFormat.format(data);
    }

    // 百分水转小数
    private Float slTranst(String sl) {
        Float f = 0.00f;
        if (!org.springframework.util.StringUtils.isEmpty(sl)) {
            if (sl.contains("%")) {
                sl = sl.replace("%", "");
                f = Float.valueOf(sl) / 100;
            } else {
                return Float.valueOf(sl);
            }
        }
        return f;
    }

}
