package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.modules.pojo.bo.ItemInfoUpdateExcelBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * excel新增 项目信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("excel新增 项目信息DTO")
public class ItemInfoExcelSaveDTO extends BaseNsrsbhDTO{

    @ApiModelProperty(name = "项目信息数组", required = true)
    private List<ItemInfoUpdateExcelBO> dataList;

}
