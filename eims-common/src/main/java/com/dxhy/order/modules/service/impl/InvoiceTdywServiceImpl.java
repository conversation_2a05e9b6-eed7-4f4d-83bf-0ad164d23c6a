package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.modules.dao.InvoiceTdywDao;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.utils.DistributedKeyMaker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 特定业务表 serviceImpl
 * <AUTHOR>
 * @Date 2024/12/27 11:26
 * @Version 1.0
 **/
@Service("invoiceTdywService")
@Slf4j
@RefreshScope
public class InvoiceTdywServiceImpl implements InvoiceTdywService {
    @Autowired
    InvoiceTdywDao invoiceTdywDao;
    @Autowired
    InvoiceBdczlxxService invoiceBdczlxxService;
    @Autowired
    InvoiceBdcxsxxService invoiceBdcxsxxService;
    @Autowired
    InvoiceJzfwxxService invoiceJzfwxxService;
    @Autowired
    InvoiceLkysfwxxService invoiceLkysfwxxService;
    @Autowired
    InvoiceHwysxxService invoiceHwysxxService;
    @Autowired
    InvoiceJdcxxService invoiceJdcxxService;
    @Autowired
    InvoiceEscxxService invoiceEscxxService;

    @Override
    public void saveTdywRelation(InvoiceTdywEntity tdywEntity, String invoiceId) {
        if (tdywEntity != null) {
            String tdys = tdywEntity.getTdys();
            //特定业务id
            String tdywId = DistributedKeyMaker.generateShotKey();
            tdywEntity.setTdys(tdys);
            tdywEntity.setId(tdywId);
            tdywEntity.setOrderInvoiceInfoId(invoiceId);
            if (StringUtils.isNotBlank(tdys)) {
                if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(), tdys)) {
                    //建筑服务
                    InvoiceJzfwxxEntity jzfwxxEntity = tdywEntity.getJzfwxx();
                    if (ObjectUtils.isNotEmpty(jzfwxxEntity)) {
                        jzfwxxEntity.setId(DistributedKeyMaker.generateShotKey());
                        jzfwxxEntity.setInvoiceTdywId(tdywId);
                        jzfwxxEntity.setOrderInvoiceInfoId(invoiceId);
                        invoiceJzfwxxService.save(jzfwxxEntity);
                        log.info("特定业务-建筑服务入库成功");
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(), tdys)) {
                    //货物运输
                    List<InvoiceHwysxxEntity> hwysxxEntityList = tdywEntity.getHwysxx();
                    if (CollectionUtils.isNotEmpty(hwysxxEntityList)) {
                        for (InvoiceHwysxxEntity hwysxxEntity : hwysxxEntityList) {
                            hwysxxEntity.setId(DistributedKeyMaker.generateShotKey());
                            hwysxxEntity.setInvoiceTdywId(tdywId);
                            hwysxxEntity.setOrderInvoiceInfoId(invoiceId);
                        }
                        invoiceHwysxxService.saveBatch(hwysxxEntityList);
                        log.info("特定业务-货物运输入库成功");
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(), tdys)) {
                    //不动产销售
                    List<InvoiceBdcxsxxEntity> bdcxsxxEntityList = tdywEntity.getBdcxsxx();
                    if (CollectionUtils.isNotEmpty(bdcxsxxEntityList)) {
                        for (InvoiceBdcxsxxEntity bdcxsxxEntity : bdcxsxxEntityList) {
                            bdcxsxxEntity.setId(DistributedKeyMaker.generateShotKey());
                            bdcxsxxEntity.setInvoiceTdywId(tdywId);
                            bdcxsxxEntity.setOrderInvoiceInfoId(invoiceId);
                        }
                        invoiceBdcxsxxService.saveBatch(bdcxsxxEntityList);
                        log.info("特定业务-不动产销售入库成功");
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(), tdys)) {
                    //不动产租赁
                    List<InvoiceBdczlxxEntity> bdczlxxEntityList = tdywEntity.getBdczlxx();
                    if (CollectionUtils.isNotEmpty(bdczlxxEntityList)) {
                        for (InvoiceBdczlxxEntity bdczlxxEntity : bdczlxxEntityList) {
                            bdczlxxEntity.setId(DistributedKeyMaker.generateShotKey());
                            bdczlxxEntity.setInvoiceTdywId(tdywId);
                            bdczlxxEntity.setOrderInvoiceInfoId(invoiceId);
                        }
                        invoiceBdczlxxService.saveBatch(bdczlxxEntityList);
                        log.info("特定业务-不动产租赁入库成功");
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(), tdys)) {
                    //旅客运输
                    List<InvoiceLkysfwxxEntity> lkysfwxxEntityList = tdywEntity.getLkysfwxx();
                    if (CollectionUtils.isNotEmpty(lkysfwxxEntityList)) {
                        for (InvoiceLkysfwxxEntity lkysfwxxEntity : lkysfwxxEntityList) {
                            lkysfwxxEntity.setId(DistributedKeyMaker.generateShotKey());
                            lkysfwxxEntity.setInvoiceTdywId(tdywId);
                            lkysfwxxEntity.setOrderInvoiceInfoId(invoiceId);
                        }
                        invoiceLkysfwxxService.saveBatch(lkysfwxxEntityList);
                        log.info("特定业务-旅客运输入库成功");
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(), tdys)) {
                    //机动车
                    InvoiceJdcxxEntity jdcxxEntity = tdywEntity.getJdcxx();
                    if (jdcxxEntity != null) {
                        jdcxxEntity.setId(DistributedKeyMaker.generateShotKey());
                        jdcxxEntity.setInvoiceTdywId(tdywId);
                        jdcxxEntity.setOrderInvoiceInfoId(invoiceId);
                        invoiceJdcxxService.save(jdcxxEntity);
                        log.info("特定业务-机动车入库成功");
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(), tdys)) {
                    //二手车
                    InvoiceEscxxEntity escxxEntity = tdywEntity.getEscxx();
                    if (escxxEntity != null) {
                        escxxEntity.setId(DistributedKeyMaker.generateShotKey());
                        escxxEntity.setInvoiceTdywId(tdywId);
                        escxxEntity.setOrderInvoiceInfoId(invoiceId);
                        invoiceEscxxService.save(escxxEntity);
                        log.info("特定业务-二手车入库成功");
                    }
                } else {
                    log.error("不支持的特定业务类型，{}", tdys);
                    throw new RuntimeException("不支持的特定业务类型：" + tdys);
                }
                invoiceTdywDao.insert(tdywEntity);
                log.info("特定业务信息入库成功，特定要素：{}", tdys);
            }
        }
    }

    @Override
    public void deleteByInvoiceId(String invoiceId) {
        LambdaQueryWrapper<InvoiceTdywEntity> tdywWrapper = Wrappers.lambdaQuery();
        tdywWrapper.eq(InvoiceTdywEntity::getOrderInvoiceInfoId, invoiceId);
        tdywWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceTdywEntity::getIsDelete).or().eq(InvoiceTdywEntity::getIsDelete,"0"));
        InvoiceTdywEntity tdywEntity = invoiceTdywDao.selectOne(tdywWrapper);
        if (tdywEntity != null) {
            String tdys = tdywEntity.getTdys();
            String tdywId = tdywEntity.getId();
            if (StringUtils.isNotBlank(tdys)) {
                if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(), tdys)) {
                    //建筑服务
                    invoiceJzfwxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-建筑服务逻辑删除成功");
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(), tdys)) {
                    //货物运输
                    invoiceHwysxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-货物运输逻辑删除成功");
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(), tdys)) {
                    //不动产销售
                    invoiceBdcxsxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-不动产销售逻辑删除成功");
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(), tdys)) {
                    //不动产租赁
                    invoiceBdczlxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-不动产租赁逻辑删除成功");
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(), tdys)) {
                    //旅客运输
                    invoiceLkysfwxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-旅客运输逻辑删除成功");
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(), tdys)) {
                    //机动车
                    invoiceJdcxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-机动车逻辑删除成功");
                } else if (org.apache.commons.lang3.StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(), tdys)) {
                    //二手车
                    invoiceEscxxService.deleteByInvoiceId(invoiceId,tdywId);
                    log.info("特定业务-二手车逻辑删除成功");
                } else {
                    log.error("不支持的特定业务类型，{}", tdys);
                    throw new RuntimeException("不支持的特定业务类型：" + tdys);
                }
            }
            tdywEntity.setIsDelete("1");
            invoiceTdywDao.updateById(tdywEntity);
            log.info("特定业务逻辑删除成功");
        }
    }

    @Override
    public void copyTdywRelation(InvoiceIssueTdywParam tdywParam, InvoiceTdywEntity tdywEntity) {
        if (tdywParam != null) {
            String tdys = tdywParam.getTdys();
            BeanUtils.copyProperties(tdywParam,tdywEntity);
            if (StringUtils.isNotBlank(tdys)) {
                if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(), tdys)) {
                    //建筑服务
                    InvoiceIssueJzfwxxParam jzfwxxParam = tdywParam.getJzfwxx();
                    if(jzfwxxParam != null){
                        InvoiceJzfwxxEntity jzfwxxEntity = new InvoiceJzfwxxEntity();
                        BeanUtils.copyProperties(jzfwxxParam,jzfwxxEntity);
                        tdywEntity.setJzfwxx(jzfwxxEntity);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(), tdys)) {
                    //货物运输
                    List<InvoiceIssueHwysxxParam> hwysxxParamList = tdywParam.getHwysxx();
                    List<InvoiceHwysxxEntity> hwysxxEntityList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(hwysxxParamList)){
                        for(InvoiceIssueHwysxxParam hwysxxParam : hwysxxParamList){
                            InvoiceHwysxxEntity hwysxxEntity = new InvoiceHwysxxEntity();
                            BeanUtils.copyProperties(hwysxxParam,hwysxxEntity);
                            hwysxxEntityList.add(hwysxxEntity);
                        }
                        tdywEntity.setHwysxx(hwysxxEntityList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(), tdys)) {
                    //不动产销售
                    List<InvoiceIssueBdcxsxxParam> bdcxsxxParamList = tdywParam.getBdcxsxx();
                    List<InvoiceBdcxsxxEntity> bdcxsxxEntityList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(bdcxsxxParamList)){
                        for(InvoiceIssueBdcxsxxParam bdcxsxxParam : bdcxsxxParamList){
                            InvoiceBdcxsxxEntity bdcxsxxEntity = new InvoiceBdcxsxxEntity();
                            BeanUtils.copyProperties(bdcxsxxParam,bdcxsxxEntity);
                            bdcxsxxEntityList.add(bdcxsxxEntity);
                        }
                        tdywEntity.setBdcxsxx(bdcxsxxEntityList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(), tdys)) {
                    //不动产租赁
                    List<InvoiceIssueBdczlxxParam> bdczlxxParamList = tdywParam.getBdczlxx();
                    List<InvoiceBdczlxxEntity> bdczlxxEntityList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(bdczlxxParamList)){
                        for(InvoiceIssueBdczlxxParam bdczlxxParam : bdczlxxParamList){
                            InvoiceBdczlxxEntity bdczlxxEntity = new InvoiceBdczlxxEntity();
                            BeanUtils.copyProperties(bdczlxxParam,bdczlxxEntity);
                            bdczlxxEntityList.add(bdczlxxEntity);
                        }
                        tdywEntity.setBdczlxx(bdczlxxEntityList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(), tdys)) {
                    //旅客运输
                    List<InvoiceIssueLkysfwxxParam> lkysfwxxParamList = tdywParam.getLkysfwxx();
                    List<InvoiceLkysfwxxEntity> lkysfwxxEntityList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(lkysfwxxParamList)){
                        for(InvoiceIssueLkysfwxxParam lkysfwxxParam : lkysfwxxParamList){
                            InvoiceLkysfwxxEntity lkysfwxxEntity = new InvoiceLkysfwxxEntity();
                            BeanUtils.copyProperties(lkysfwxxParam,lkysfwxxEntity);
                            lkysfwxxEntityList.add(lkysfwxxEntity);
                        }
                        tdywEntity.setLkysfwxx(lkysfwxxEntityList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(), tdys)) {
                    //机动车
                    InvoiceIssueJdcxxParam jdcxxParam = tdywParam.getJdcxx();
                    if(jdcxxParam != null){
                        InvoiceJdcxxEntity jdcxxEntity = new InvoiceJdcxxEntity();
                        BeanUtils.copyProperties(jdcxxParam,jdcxxEntity);
                        tdywEntity.setJdcxx(jdcxxEntity);
                    }
                } else if (org.apache.commons.lang3.StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(), tdys)) {
                    //二手车
                    InvoiceIssueEscxxParam escxxParam = tdywParam.getEscxx();
                    if(escxxParam != null){
                        InvoiceEscxxEntity escxxEntity = new InvoiceEscxxEntity();
                        BeanUtils.copyProperties(escxxParam,escxxEntity);
                        tdywEntity.setEscxx(escxxEntity);
                    }
                } else {
                    log.error("不支持的特定业务类型，{}", tdys);
                    throw new RuntimeException("不支持的特定业务类型：" + tdys);
                }
            }
        }
    }

    @Override
    public void copyTdywEntityToParam(InvoiceTdywEntity tdywEntity, InvoiceIssueTdywParam tdywParam) {
        if (tdywEntity != null) {
            String tdys = tdywEntity.getTdys();
            BeanUtils.copyProperties(tdywEntity,tdywParam);
            if (StringUtils.isNotBlank(tdys)) {
                if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(), tdys)) {
                    //建筑服务
                    InvoiceJzfwxxEntity jzfwxxEntity = tdywEntity.getJzfwxx();
                    if(jzfwxxEntity != null){
                        InvoiceIssueJzfwxxParam jzfwxxParam = new InvoiceIssueJzfwxxParam();
                        BeanUtils.copyProperties(jzfwxxEntity,jzfwxxParam);
                        tdywParam.setJzfwxx(jzfwxxParam);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(), tdys)) {
                    //货物运输
                    List<InvoiceHwysxxEntity> hwysxxEntityList = tdywEntity.getHwysxx();
                    List<InvoiceIssueHwysxxParam> hwysxxParamList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(hwysxxEntityList)){
                        for(InvoiceHwysxxEntity hwysxxEntity : hwysxxEntityList){
                            InvoiceIssueHwysxxParam hwysxxParam = new InvoiceIssueHwysxxParam();
                            BeanUtils.copyProperties(hwysxxEntity,hwysxxParam);
                            hwysxxParamList.add(hwysxxParam);
                        }
                        tdywParam.setHwysxx(hwysxxParamList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(), tdys)) {
                    //不动产销售
                    List<InvoiceBdcxsxxEntity> bdcxsxxEntityList = tdywEntity.getBdcxsxx();
                    List<InvoiceIssueBdcxsxxParam> bdcxsxxParamList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(bdcxsxxEntityList)){
                        for(InvoiceBdcxsxxEntity bdcxsxxEntity : bdcxsxxEntityList){
                            InvoiceIssueBdcxsxxParam bdcxsxxParam = new InvoiceIssueBdcxsxxParam();
                            BeanUtils.copyProperties(bdcxsxxEntity,bdcxsxxParam);
                            bdcxsxxParamList.add(bdcxsxxParam);
                        }
                        tdywParam.setBdcxsxx(bdcxsxxParamList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(), tdys)) {
                    //不动产租赁
                    List<InvoiceBdczlxxEntity> bdczlxxEntityList = tdywEntity.getBdczlxx();
                    List<InvoiceIssueBdczlxxParam> bdczlxxParamList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(bdczlxxEntityList)){
                        for(InvoiceBdczlxxEntity bdczlxxEntity : bdczlxxEntityList){
                            InvoiceIssueBdczlxxParam bdczlxxParam = new InvoiceIssueBdczlxxParam();
                            BeanUtils.copyProperties(bdczlxxEntity,bdczlxxParam);
                            bdczlxxParamList.add(bdczlxxParam);
                        }
                        tdywParam.setBdczlxx(bdczlxxParamList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(), tdys)) {
                    //旅客运输
                    List<InvoiceLkysfwxxEntity> lkysfwxxEntityList = tdywEntity.getLkysfwxx();
                    List<InvoiceIssueLkysfwxxParam> lkysfwxxParamList = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(lkysfwxxEntityList)){
                        for(InvoiceLkysfwxxEntity lkysfwxxEntity : lkysfwxxEntityList){
                            InvoiceIssueLkysfwxxParam lkysfwxxParam = new InvoiceIssueLkysfwxxParam();
                            BeanUtils.copyProperties(lkysfwxxEntity,lkysfwxxParam);
                            lkysfwxxParamList.add(lkysfwxxParam);
                        }
                        tdywParam.setLkysfwxx(lkysfwxxParamList);
                    }
                } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(), tdys)) {
                    //机动车
                    InvoiceJdcxxEntity jdcxxEntity = tdywEntity.getJdcxx();
                    if(jdcxxEntity != null){
                        InvoiceIssueJdcxxParam jdcxxParam = new InvoiceIssueJdcxxParam();
                        BeanUtils.copyProperties(jdcxxEntity,jdcxxParam);
                        tdywParam.setJdcxx(jdcxxParam);
                    }
                } else if (org.apache.commons.lang3.StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(), tdys)) {
                    //二手车
                    InvoiceEscxxEntity escxxEntity = tdywEntity.getEscxx();
                    if(escxxEntity != null){
                        InvoiceIssueEscxxParam escxxParam = new InvoiceIssueEscxxParam();
                        BeanUtils.copyProperties(escxxEntity,escxxParam);
                        tdywParam.setEscxx(escxxParam);
                    }
                } else {
                    log.error("不支持的特定业务类型，{}", tdys);
                    throw new RuntimeException("不支持的特定业务类型：" + tdys);
                }
            }
        }
    }

    @Override
    public List<InvoiceTdywEntity> queryTdywRelation(Collection<String> invoiceIds) {
        List<InvoiceTdywEntity> tdywEntityList = new ArrayList<>();
        for (String invoiceId: invoiceIds){
            LambdaQueryWrapper<InvoiceTdywEntity> tdywWrapper = Wrappers.lambdaQuery();
            tdywWrapper.eq(InvoiceTdywEntity::getOrderInvoiceInfoId, invoiceId);
            tdywWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceTdywEntity::getIsDelete).or().eq(InvoiceTdywEntity::getIsDelete,"0"));
            InvoiceTdywEntity tdywEntity = invoiceTdywDao.selectOne(tdywWrapper);
            if (tdywEntity != null) {
                String tdys = tdywEntity.getTdys();
                if (StringUtils.isNotBlank(tdys)) {
                    if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_03.getKey(), tdys)) {
                        //建筑服务
                        LambdaQueryWrapper<InvoiceJzfwxxEntity> jzfwxxWrapper = Wrappers.lambdaQuery();
                        jzfwxxWrapper.eq(InvoiceJzfwxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        jzfwxxWrapper.eq(InvoiceJzfwxxEntity::getOrderInvoiceInfoId, invoiceId);
                        jzfwxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceJzfwxxEntity::getIsDelete).or().eq(InvoiceJzfwxxEntity::getIsDelete,"0"));
                        tdywEntity.setJzfwxx(invoiceJzfwxxService.getOne(jzfwxxWrapper));
                        log.info("特定业务-建筑服务逻辑查询成功");
                    } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_04.getKey(), tdys)) {
                        //货物运输
                        LambdaQueryWrapper<InvoiceHwysxxEntity> hwysxxWrapper = Wrappers.lambdaQuery();
                        hwysxxWrapper.eq(InvoiceHwysxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        hwysxxWrapper.eq(InvoiceHwysxxEntity::getOrderInvoiceInfoId, invoiceId);
                        hwysxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceHwysxxEntity::getIsDelete).or().eq(InvoiceHwysxxEntity::getIsDelete,"0"));
                        tdywEntity.setHwysxx(invoiceHwysxxService.list(hwysxxWrapper));
                        log.info("特定业务-货物运输逻辑查询成功");
                    } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_05.getKey(), tdys)) {
                        //不动产销售
                        LambdaQueryWrapper<InvoiceBdcxsxxEntity> bdcxsxxWrapper = Wrappers.lambdaQuery();
                        bdcxsxxWrapper.eq(InvoiceBdcxsxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        bdcxsxxWrapper.eq(InvoiceBdcxsxxEntity::getOrderInvoiceInfoId, invoiceId);
                        bdcxsxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceBdcxsxxEntity::getIsDelete).or().eq(InvoiceBdcxsxxEntity::getIsDelete,"0"));
                        tdywEntity.setBdcxsxx(invoiceBdcxsxxService.list(bdcxsxxWrapper));
                        log.info("特定业务-不动产销售逻辑查询成功");
                    } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_06.getKey(), tdys)) {
                        //不动产租赁
                        LambdaQueryWrapper<InvoiceBdczlxxEntity> bdczlxxWrapper = Wrappers.lambdaQuery();
                        bdczlxxWrapper.eq(InvoiceBdczlxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        bdczlxxWrapper.eq(InvoiceBdczlxxEntity::getOrderInvoiceInfoId, invoiceId);
                        bdczlxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceBdczlxxEntity::getIsDelete).or().eq(InvoiceBdczlxxEntity::getIsDelete,"0"));
                        tdywEntity.setBdczlxx(invoiceBdczlxxService.list(bdczlxxWrapper));
                        log.info("特定业务-不动产租赁逻辑查询成功");
                    } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_09.getKey(), tdys)) {
                        //旅客运输
                        LambdaQueryWrapper<InvoiceLkysfwxxEntity> lkysfwxxWrapper = Wrappers.lambdaQuery();
                        lkysfwxxWrapper.eq(InvoiceLkysfwxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        lkysfwxxWrapper.eq(InvoiceLkysfwxxEntity::getOrderInvoiceInfoId, invoiceId);
                        lkysfwxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceLkysfwxxEntity::getIsDelete).or().eq(InvoiceLkysfwxxEntity::getIsDelete,"0"));
                        tdywEntity.setLkysfwxx(invoiceLkysfwxxService.list(lkysfwxxWrapper));
                        log.info("特定业务-旅客运输逻辑查询成功");
                    } else if (StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_14.getKey(), tdys)) {
                        //机动车
                        LambdaQueryWrapper<InvoiceJdcxxEntity> jdcxxWrapper = Wrappers.lambdaQuery();
                        jdcxxWrapper.eq(InvoiceJdcxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        jdcxxWrapper.eq(InvoiceJdcxxEntity::getOrderInvoiceInfoId, invoiceId);
                        jdcxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceJdcxxEntity::getIsDelete).or().eq(InvoiceJdcxxEntity::getIsDelete,"0"));
                        tdywEntity.setJdcxx(invoiceJdcxxService.getOne(jdcxxWrapper));
                        log.info("特定业务-机动车逻辑查询成功");
                    } else if (org.apache.commons.lang3.StringUtils.equals(OrderInfoEnum.ORDER_QD_TDYS_15.getKey(), tdys)) {
                        //二手车
                        LambdaQueryWrapper<InvoiceEscxxEntity> escxxWrapper = Wrappers.lambdaQuery();
                        escxxWrapper.eq(InvoiceEscxxEntity::getInvoiceTdywId, tdywEntity.getId());
                        escxxWrapper.eq(InvoiceEscxxEntity::getOrderInvoiceInfoId, invoiceId);
                        escxxWrapper.and(tempWrapper -> tempWrapper.isNull(InvoiceEscxxEntity::getIsDelete).or().eq(InvoiceEscxxEntity::getIsDelete,"0"));
                        tdywEntity.setEscxx(invoiceEscxxService.getOne(escxxWrapper));
                        log.info("特定业务-二手车逻辑查询成功");
                    } else {
                        log.error("不支持的特定业务类型，{}", tdys);
                        throw new RuntimeException("不支持的特定业务类型：" + tdys);
                    }
                }
                tdywEntityList.add(tdywEntity);
            }
        }
        return tdywEntityList;
    }
}
