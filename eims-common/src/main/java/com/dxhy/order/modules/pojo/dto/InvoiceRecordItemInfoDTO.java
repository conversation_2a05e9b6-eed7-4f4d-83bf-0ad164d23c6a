package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.utils.BasePage;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/12/14 15:28
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("开票记录详情商品信息 DTO")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class InvoiceRecordItemInfoDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目名称
     */
    @ApiModelProperty(name = "项目名称")
    private String xmmc;

    /**
     * 规格型号
     */
    @ApiModelProperty(name = "规格型号")
    private String ggxh;

    /**
     * 单位
     */
    @ApiModelProperty(name = "单位")
    private String dw;

    /**
     * 数量
     */
    @ApiModelProperty(name = "数量")
    private String xmsl;

    /**
     * 单价
     */
    @ApiModelProperty(name = "单价")
    private String dj;

    /**
     * 金额
     */
    @ApiModelProperty(name = "金额")
    private String je;

    /**
     * 税率
     */
    @ApiModelProperty(name = "税率")
    private String sl;

    /**
     * 税额
     */
    @ApiModelProperty(name = "税额")
    private String se;

    /**
     * 含税标志
     */
    @ApiModelProperty(name = "含税标志")
    private String hsbz;

}
