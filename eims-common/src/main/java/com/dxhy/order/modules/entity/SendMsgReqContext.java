package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信发送 sessionContext
 */
@Data
public class SendMsgReqContext implements Serializable {

    private String entityCode;
    private String channel;
    private String serviceCode;
    private String postingDateText;
    private String valueDateText;
    private String localDateTimeText;
    private String transactionBranch;
    private String userId;
    private String password;
    private String superUserId;
    private String superPassword;
    private String authorizationReason;
    private String externalReferenceNo;
    private String userReferenceNumber;
    private String originalReferenceNo;
    private String marketCode;
    private String stepCode;
    private String accessSource;
    private String accessSourceType;
    private String riskMessage;
}
