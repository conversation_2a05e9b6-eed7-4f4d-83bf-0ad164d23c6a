package com.dxhy.order.modules.entity;

/**
 * @author: zhangjinjing
 * @Date: 2022/7/7 11:51
 * @Version 1.0
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.io.Serializable;

/**
 * 租户数据源配置表
 * <AUTHOR>
 * @Date 2022/6/28 16:34
 * @Version 1.0
 **/
@TableName("db_tenant")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("租户数据源配置表")
public class DbTenantEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 租户编码
     */
    @TableId(type = IdType.INPUT)
    private String tenantCode;

    /**
     * 数据库url
     */
    private String dbUrl;

    /**
     * 数据库端口
     */
    private String dbPort;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 数据库账号
     */
    private String dbAccount;

    /**
     * 数据库密码
     */
    private String dbPassword;
}
