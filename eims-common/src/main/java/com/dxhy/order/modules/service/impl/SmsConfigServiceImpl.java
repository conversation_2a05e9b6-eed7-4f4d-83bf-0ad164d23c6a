package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.SmsConfigDao;
import com.dxhy.order.modules.entity.SmsConfig;
import com.dxhy.order.modules.service.SmsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【sms_config】的数据库操作Service实现
* @createDate 2022-09-14 14:09:53
*/
@Service("smsConfigService")
public class SmsConfigServiceImpl extends ServiceImpl<SmsConfigDao, SmsConfig> implements SmsConfigService {

    @Autowired
    private SmsConfigDao smsConfigDao;

    @Override
    public SmsConfig selectByNsrsbh(String nsrsbh) {
        return smsConfigDao.selectByNsrsbh(nsrsbh);
    }

}




