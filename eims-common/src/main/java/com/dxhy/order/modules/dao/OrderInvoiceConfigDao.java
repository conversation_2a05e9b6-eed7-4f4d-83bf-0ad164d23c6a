package com.dxhy.order.modules.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.OrderInvoiceConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 开票配置表
 *
 * <AUTHOR>
 * @email
 * @date 2022-12-06 20:00:18
 */
@Mapper
public interface OrderInvoiceConfigDao extends BaseMapper<OrderInvoiceConfigEntity> {

    OrderInvoiceConfigEntity selectAllList();

    OrderInvoiceConfigEntity selectByNsrsbh(@Param("nsrsbh") String nsrsbh);
}
