package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.OrderStatusOptRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单状态操作记录表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-23 11:36:51
 */
@Mapper
public interface OrderStatusOptRecordDao extends BaseMapper<OrderStatusOptRecordEntity> {

    List<OrderStatusOptRecordEntity> selectList(Page page, OrderStatusOptRecordEntity orderStatusOptRecordEntity);

    List<OrderStatusOptRecordEntity> selectInfoByFreshId(@Param("id") String id);
}
