package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ApiHzkpqrxxsljgcxRspBO implements Serializable {

    private static final long serialVersionUID = 1112164287255986561L;

    /**
     * 红字确认信息状态代码
     */
    private String hzqrxxztdm;
    /**
     * 销售方名称
     */
    private String xsfmc;
    /**
     * 红字发票确认单编号
     */
    private String hzfpxxqrdbh;
    /**
     * 开单时间
     */
    private String kdsj;
    /**
     * 销售方纳税人识别号
     */
    private String xsfnsrsbh;
    /**
     * 全电发票号码
     */
    private String qdfphm;
    /**
     * 购买方名称
     */
    private String gmfmc;
    /**
     * 价税合计
     */
    private String jshj;
    /**
     * 购买方纳税人识别号
     */
    private String gmfnsrsbh;

}
