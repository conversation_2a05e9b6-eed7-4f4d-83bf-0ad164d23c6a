package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-不动产销售信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceIssueBdcxsxxParam implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	private String xh;

	/**
	 * 网签合同标志
	 */
	private String wqhtbz;

	/**
	 * 不动产单元代码/网签合同备案编号
	 */
	private String bdcwqhtbh;

	/**
	 * 不动产地区
	 */
	private String bdcdq;

	/**
	 * 不动产地址
	 */
	private String bdcdz;

	/**
	 * 跨地(市)标志
	 */
	private String kdsbz;

	/**
	 * 土地增值税项目编号
	 */
	private String tdzzsxmbh;

	/**
	 * 核定计税价格
	 */
	private String hdjsjg;

	/**
	 * 实际成交含税金额
	 */
	private String sjcjhsje;

	/**
	 * 房屋产权证书号/不动产权证号
	 */
	private String cqzsh;

	/**
	 * 面积单位
	 */
	private String mjdw;
}
