package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品信息表
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@TableName("item_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("商品信息表")
public class ItemInfoEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 开票项目主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty("开票项目主键")
    private String id;

    /**
     * 项目分类主键
     */
    @ApiModelProperty("项目分类主键")
    private String parentId;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String baseNsrsbh;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String xmmc;

    /**
     * 商品和服务税收分类编码
     */
    @ApiModelProperty("商品和服务税收分类编码")
    private String sphssflbm;

    /**
     * 商品和服务税收分类简称
     */
    @ApiModelProperty("商品和服务税收分类简称")
    private String sphssfljc;

    /**
     * 优惠政策标识 0 否 1 是
     */
    @ApiModelProperty("优惠政策标识 0 否 1 是")
    private String yhzcbs;

    /**
     * 优惠政策类型
     */
    @ApiModelProperty("优惠政策类型")
    private String yhzclx;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private String sl;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String jm;

    /**
     * 含税标识 0 否 1 是
     */
    @ApiModelProperty("含税标识 0 否 1 是")
    private String hsbs;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String dj;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String dw;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String ggxh;

    /**
     * 逻辑删除 0 正常 1 已删除
     */
    @ApiModelProperty("逻辑删除 0 正常 1 已删除")
    private String isDelete;

    /**
     * 备用字段1
     */
    @ApiModelProperty("备用字段1")
    private String byzd1;

    /**
     * 备用字段2
     */
    @ApiModelProperty("备用字段2")
    private String byzd2;

    /**
     * 备用字段3
     */
    @ApiModelProperty("备用字段3")
    private String byzd3;

    /**
     * 备用字段4
     */
    @ApiModelProperty("备用字段4")
    private String byzd4;

    /**
     * 备用字段5
     */
    @ApiModelProperty("备用字段5")
    private String byzd5;

    /**
     * 备用字段6
     */
    @ApiModelProperty("备用字段6")
    private String byzd6;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
}
