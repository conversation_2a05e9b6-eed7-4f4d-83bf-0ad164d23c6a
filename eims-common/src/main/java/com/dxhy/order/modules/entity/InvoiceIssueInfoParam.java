package com.dxhy.order.modules.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class InvoiceIssueInfoParam {
    private String ddqqlsh;
    private String fplxdm;
    private String kpfs;
    private String gmfsbh;
    private String gmfmc;
    private String gmfdz;
    private String gmfdh;
    private String gmfyh;
    private String gmfzh;
    private String gmjfsjh;
    private String gmjfyx;
    private String jbrxm;
    private String jbrzjlx;
    private String jbrzjhm;
    private String kpr;
    private String hsbz;
    private String jshj;
    private String hjje;
    private String hjse;
    private String bz;
    private String ddh;
    private String nsrsbh;
    // 应收单号
    private String ysdh;
    // 交付手机号
    private String jfsjh;
    // 交付邮箱地址
    private String jfyx;
    //附加信息
    private List<InvoiceIssueAdditionParam> ddfjxx;
    //明细信息
    private List<InvoiceIssueItemParam> ddmxxx;
    // 小规模纳税人放弃优惠原因
    private String giveUpReason;
    //房间号
    private String fjh;
    /**
     * 是否展示购方地址电话 Y、N
     */
    private String sfzsgfdzdh;
    /**
     * 是否展示购方银行账号 Y、N
     */
    private String sfzsgfyhzh;
    /**
     * 是否展示销方地址电话 Y、N
     */
    private String sfzsxfdzdh;
    /**
     * 是否展示销方银行账号 Y、N
     */
    private String sfzsxfyhzh;
    /**
     * 特定业务要素，某些特定业务没有详细信息，没有特定业务（如农产品收购、自产农产品销售），所以需要本字段传递数据
     * 取值为 OrderInfoEnum.ORDER_QD_TDYS_00 相关枚举，表字段存储在 order_invoice_info.tdyw
     * 前端页面传的同样也是大写TDYW，对外接口区分开，统一用小写
     */
    private String tdywys;
    /**
     * 特定业务详细信息
     */
    @JsonProperty("tdyw")
    private InvoiceIssueTdywParam invoiceIssueTdywParam;
    /**
     * 差额征税集合
     */
    private List<InvoiceIssueCezsParam> cezslist;
    //订单时间
    private String ddsj;
    //是否拆分
    private String sfcf;
    /**
     * （是要存表还是有特殊逻辑处理）
     * 特殊票证标识
     * 空 非收购发票
     * 01 农产品收购发票
     * 02 光伏收购发票
     * 03 二手车收购发票
     */
    private String tspzbz;
    /**
     * 征收方式
     * 空 非差额发票
     * 01 全额开票
     * 02 差额开票
     */
    private String zsfs;
    //邮箱 （什么邮箱？销货方还是购货方）
    private String yx;
    //电话（什么电话？销货方还是购货方）
    private String dh;
    //销售方自然人标识
    private String xsfzrrbs;
    //经办人联系电话
    private String jbrlxdh;
    //结算方式代码
    private String jsfsdm;
    //开票人证件号码
    private String kprzjhm;
    //开票人证件类型
    private String kprzjlx;
    //红字确认单uuid（是要存表还是有特殊逻辑处理）
    private String hzqrduuid;
    //受票方自然人标识
    private String spfzrrbs;
    //收款人
    private String skr;
    //复核人
    private String fhr;
    //购买方证件类型
    private String gmfzjlx;
    //购买方证件号码
    private String gmfzjhm;
    //购买方国籍
    private String gmfgj;
    //电商平台原始订单编号
    private String dsptysddbh;
    //电商平台类型
    private String dsptlx;
    //系统来源
    private String xtly;

}
