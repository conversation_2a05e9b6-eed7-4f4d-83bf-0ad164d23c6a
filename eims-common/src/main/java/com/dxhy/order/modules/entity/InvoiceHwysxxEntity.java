package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-货物运输信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_hwysxx")
@Data
public class InvoiceHwysxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 货物运输信息主键
	 */
	@ApiModelProperty(value = "货物运输信息主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("明细序号")
	private String xmxh;

	@ApiModelProperty("起运地")
	private String qyd;

	@ApiModelProperty("到达地")
	private String ddd;

	@ApiModelProperty("运输工具种类")
	private String ysgjzl;

	@ApiModelProperty("运输工具牌号")
	private String ysgjph;

	@ApiModelProperty("运输货物名称")
	private String yshwmc;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
