package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ApiHzfpkjslReqBO implements Serializable {

    private static final long serialVersionUID = 5010279751501280066L;

    /**
     * 蓝字全电发票号码
     */
    private String lzqdfphm;
    /**
     * 开票日期 格式yyyy-mm-dd
     */
    private String kprq;
    /**
     * 开单时间 格式yyyy-mm-dd
     */
    private String kdsj;
    /**
     * 对方纳税人名称
     */
    private String dfnsrmc;
    /**
     * 红字发票确认单编号
     */
    private String hzfpxxqrdbh;
    /**
     * 红字确认信息状态代码 【01 无需确认，04 购销双方已确认】
     */
    private String hzqrxxztdm;
    /**
     * 纳税人识别号
     */
    private String nsrsbh;

}
