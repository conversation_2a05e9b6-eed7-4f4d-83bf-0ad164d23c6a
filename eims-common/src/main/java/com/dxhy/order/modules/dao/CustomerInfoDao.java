package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.CustomerInfoEntity;
import com.dxhy.order.modules.pojo.dto.CustomerInfoListByNameWithoutPageDTO;
import com.dxhy.order.modules.pojo.dto.CustomerInfoListDTO;
import com.dxhy.order.modules.pojo.dto.CustomerInfoListWithoutIdDTO;
import com.dxhy.order.modules.pojo.dto.CustomerInfoSaveDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户信息表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface CustomerInfoDao extends BaseMapper<CustomerInfoEntity> {

    /**
     * 查询客户信息列表
     * @param page
     * @param customerInfoListDTO
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerInfoEntity>
     * <AUTHOR>
     **/
    List<CustomerInfoEntity> selectList(Page page, @Param("customerInfoListDTO") CustomerInfoListDTO customerInfoListDTO);

    /**
     * 列表 - 不选择客户分类
     * @param customerInfoListWithoutIdDTO
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerInfoEntity>
     * <AUTHOR>
     **/
    List<CustomerInfoEntity> listWithoutId(Page page, @Param("customerInfoListWithoutIdDTO") CustomerInfoListWithoutIdDTO customerInfoListWithoutIdDTO);

    /**
     * 根据名称查询列表 - 不分页
     * @param customerInfoListByNameWithoutPageDTO
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerInfoEntity>
     * <AUTHOR>
     **/
    List<CustomerInfoEntity> listByNameWithoutPage(CustomerInfoListByNameWithoutPageDTO customerInfoListByNameWithoutPageDTO);

    /**
     * 新增、修改时候查重用
     * @param customerInfoSaveDTO
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerInfoEntity>
     * <AUTHOR>
     **/
    List<CustomerInfoEntity> selectListByCustomerInfoSaveDTO(CustomerInfoSaveDTO customerInfoSaveDTO);

    /**
     * 清理未和客户分类关联的数据
     * @param baseNsrsbh
     * @return void
     * <AUTHOR>
     **/
    void clearDataByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 根据客户ID查询客户分类名称列表
     * @param idList
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
     * <AUTHOR>
     **/
    List<Map<String, String>> listParentNameByIdList(@Param("idList") List<String> idList);

    /**
     * 批量保存数据
     * @param customerInfoEntityList
     * @return void
     * <AUTHOR>
     **/
    void insertList(@Param("customerInfoEntityList") List<CustomerInfoEntity> customerInfoEntityList);

    /**
     *  查询购货方信息（去重）
     *
     */
    List<String> selectGhfmcList(@Param("baseNsrsbh") String baseNsrsbh);

    List<String> selectGhfmcListByGhfsbh(@Param("baseNsrsbh") String baseNsrsbh, @Param("nsrsbh") String nsrsbh);
}
