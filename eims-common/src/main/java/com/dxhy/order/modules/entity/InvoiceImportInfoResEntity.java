package com.dxhy.order.modules.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 发票明细导入实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceImportInfoResEntity {

    @JsonProperty("XHFMC")
    private String xhfMc;
    @JsonProperty("XHFSBH")
    private String xhfNsrsbh;
    @JsonProperty("XHFDZ")
    private String xhfDz;
    @JsonProperty("XHFDH")
    private String xhfDh;
    @JsonProperty("XHFYH")
    private String xhfYh;
    @JsonProperty("XHFZH")
    private String xhfZh;
    //  纳税人识别号
    @JsonProperty("baseNsrsbh")
    private String baseNsrsbh;

    private String successNum;
    private String successAmount;
    // 页面的成功信息集合
    private List<OrderInvoiceInfoExcelEntity> successInvoiceItemList;
    private String failNumber;
    private String failAmount;
    // 页面的失败信息集合
    private List<OrderInvoiceInfoExcelEntity> failInvoiceItemList;
    // 成功保存的集合
    private List<OrderInvoiceInfoExcelEntity> successOrderInvoiceImportExcelEntities;
    // 下载错误明细传入的集合
    private List<OrderInvoiceInfoExcelEntity> failOrderInvoiceImportExcelEntities;
    /**
     * 单据层级设置  0-显示订单    1-显示应收单+订单
     */
    @JsonProperty("DDORYSD")
    private String ddhOrYsd;

}
