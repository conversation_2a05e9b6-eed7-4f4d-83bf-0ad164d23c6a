package com.dxhy.order.modules.entity;

import lombok.Data;

import java.util.List;

/**
 * 发票明细导入实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceImportItemEntity {

    private String successNum;
    private String successAmount;
    private List<OrderInvoiceItemEntity> successInvoiceItemList;
    private String failNumber;
    private String failAmount;
    private List<OrderInvoiceItemEntity> failInvoiceItemList;
    private String hsbs;
    private List<OrderInvoiceImportExcelEntity> successOrderInvoiceImportExcelEntities;
    private List<OrderInvoiceImportExcelEntity> failOrderInvoiceImportExcelEntities;

}
