package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.CustomerInfoQrcodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户扫码信息表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface CustomerInfoQrcodeDao extends BaseMapper<CustomerInfoQrcodeEntity> {

    /**
     * 扫码客户列表
     * @param page
     * @param baseNsrsbh
     * @return java.util.List<com.dxhy.order.modules.entity.CustomerInfoQrcodeEntity>
     * <AUTHOR>
     **/
    List<CustomerInfoQrcodeEntity> listData(Page page, @Param("baseNsrsbh") String baseNsrsbh);

}
