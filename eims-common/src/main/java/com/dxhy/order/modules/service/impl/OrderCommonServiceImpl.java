package com.dxhy.order.modules.service.impl;

import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.exception.OrderReceiveException;
import com.dxhy.order.model.OrderQrcodeExtendInfo;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.service.OrderCommonService;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.modules.service.OrderQrcodeExtendService;
import com.dxhy.order.utils.DistributedKeyMaker;
import com.dxhy.order.utils.JsonUtils;
import com.dxhy.order.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单发票通用服务接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderCommonServiceImpl implements OrderCommonService {
    
    private static final String LOGGER_MSG = "(订单接口通用业务类)";
    @Autowired
    OrderInvoiceInfoService orderInvoiceInfoService;
    @Autowired
    OrderQrcodeExtendService orderQrcodeExtendService;

    @Override
    public String getGenerateShotKey() {
        return DistributedKeyMaker.generateShotKey();
    }

    @Override
    public R initRepeatInvoice(String fpqqlsh, String kplsh, String xhfNsrsbh) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQrCodeData(OrderInvoiceInfoEntity orderInvoiceInfo,OrderQrcodeExtendInfo qrcodeExtendInfo) throws OrderReceiveException {
        String jsonString;

        //调用发票暂存接口
        R r = orderInvoiceInfoService.orderInvoiceTemporary(orderInvoiceInfo);
        if (!StringUtils.equals("0000",(String)r.get("code"))) {
            jsonString = JsonUtils.getInstance().toJsonString(orderInvoiceInfo);
            log.error("{}存数电发票表失败,请求数据:{}", LOGGER_MSG, jsonString);
            throw new OrderReceiveException(OrderInfoContentEnum.RECEIVE_FAILD);
        }
        //保存二维码扩展表
        boolean i = orderQrcodeExtendService.saveQrcodeInfo(qrcodeExtendInfo);
        if (!i) {
            log.error("插入订单二维码扩展表失败");
            throw new OrderReceiveException(OrderInfoContentEnum.RECEIVE_FAILD);
        }
    }


}
