package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.dxhy.order.config.OpenApiConfig;
import com.dxhy.order.constant.ConfigureConstant;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.exception.OrderReceiveException;
import com.dxhy.order.model.*;
import com.dxhy.order.model.page.PageEwmConfigInfo;
import com.dxhy.order.model.page.PageEwmItem;
import com.dxhy.order.model.page.PageOrderItemInfo;
import com.dxhy.order.model.page.QrcodeOrderInfo;
import com.dxhy.order.modules.entity.InvoiceIssueRes;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.modules.entity.PageQrcodeOrderInfo;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 静态码业务处理
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
@Service
@Slf4j
public class QrcodeServiceImpl implements QrcodeService {
	private static final String LOGGER_MSG = "(静态码业务类)";
	@Resource
	QuickCodeInfoService quickCodeInfoService;
	
	@Resource
	OrderCommonService apiInvoiceCommonService;
	
	@Resource
	OrderQrcodeExtendService orderQrcodeExtendService;
	
	@Resource
	ValidateDynamicService validateDynamicService;
	
	@Resource
	private OpenApiConfig openApiConfig;
	
	
	@Override
	public boolean saveQrcodeInfo(QrcodeOrderInfo qrcodeOrderInfo) {
		
		log.info("二维码信息保存接口，入参:{}", JsonUtils.getInstance().toJsonString(qrcodeOrderInfo));
		QuickResponseCodeInfo qrCodeInfo = qrcodeOrderInfoToQuickRsponseInfo(qrcodeOrderInfo);
		qrCodeInfo.setId(apiInvoiceCommonService.getGenerateShotKey());
		
		//生成提取码
		qrCodeInfo.setTqm(qrcodeOrderInfo.getTqm());
		qrCodeInfo.setQuickResponseCodeType(ConfigureConstant.STRING_0);
		List<QuickResponseCodeItemInfo> itemList = qrcodeOrderInfoToQuickRsponseItem(qrcodeOrderInfo.getOrderItemList(), qrCodeInfo.getId(), qrCodeInfo.getXhfNsrsbh());
		
		List<InvoiceTypeCodeExt> extList = buildInvoiceTypeExt(qrcodeOrderInfo, qrCodeInfo.getId(), qrCodeInfo.getXhfNsrsbh());
		
		return quickCodeInfoService.saveQrcodeInfo(qrCodeInfo, itemList, extList);
	}
	
	@Override
	public PageUtils queryQrCodeList(Map map, List<String> shList) {
		return quickCodeInfoService.queryQrCodeList(map, shList);
	}
	
	@Override
	public Map<String, Object> queryQrCodeDetail(String qrCodeId, List<String> xhfNsrsbh) {
		log.info("{}二维码详情查询接口，入参:{}", LOGGER_MSG, qrCodeId);
		Map<String, Object> returnMap = new HashMap<>(5);
		//查询二维码基础信息
		QuickResponseCodeInfo info = quickCodeInfoService.queryQrCodeDetail(qrCodeId, xhfNsrsbh);
		//查询二维码项目明细信息
		List<QuickResponseCodeItemInfo> itemList = quickCodeInfoService.queryQrCodeItemListByQrcodeId(qrCodeId, xhfNsrsbh);
		//查询二维码发票种类代码信息
		List<InvoiceTypeCodeExt> invoiceTypeList = quickCodeInfoService.queryInvoiceTypeByQrcodeId(qrCodeId, xhfNsrsbh);
		
		returnMap.put("qrcodeInfo", info);
		returnMap.put("qrcodeItemList", itemList);
        returnMap.put("invoiceTypeList", invoiceTypeList);

		return returnMap;
	}
	
	@Override
	public Map<String, Object> queryQrCodeImg(String qrcodeId, String type, List<String> xhfNsrsbh, String backGround) {
		Map<String, Object> returnMap = new HashMap<>(5);
		
		if (OrderInfoEnum.QR_TYPE_0.getKey().equals(type)) {
			//静态码
			QuickResponseCodeInfo info = quickCodeInfoService.queryQrCodeDetail(qrcodeId, xhfNsrsbh);
			if (ObjectUtils.isEmpty(info)) {
				return null;
			}
			// 生成qrcode的base64流
			String qrCodeString = QrCodeUtil.drawLogoQrCode(null,
					String.format(openApiConfig.configQrCodeShortUrl(), info.getTqm()), "", backGround);
			returnMap.put("ywxl", info.getYwlx());
			returnMap.put("ewm", qrCodeString);
			returnMap.put("qrcodeUrl", String.format(openApiConfig.configQrCodeShortUrl(), info.getTqm()));
			returnMap.put("qrcodeId", info.getId());
		} else {
			//动态码
			OrderQrcodeExtendInfo orderQrcodeExtendInfo = orderQrcodeExtendService.queryQrcodeDetailById(qrcodeId, xhfNsrsbh);
			if (orderQrcodeExtendInfo == null) {
				log.warn("动态码不存在,id:{}", qrcodeId);
				return null;
			}
			// 生成qrcode的base64流
			String qrCodeString = QrCodeUtil.drawLogoQrCode(null,
					String.format(openApiConfig.configQrCodeShortUrl(), orderQrcodeExtendInfo.getTqm()), "", backGround);
			returnMap.put("ewm", qrCodeString);
			returnMap.put("qrcodeUrl", String.format(openApiConfig.configQrCodeShortUrl(), orderQrcodeExtendInfo.getTqm()));
			returnMap.put("qrcodeId", orderQrcodeExtendInfo.getId());
		}
		return returnMap;
	}
	
	private List<InvoiceTypeCodeExt> buildInvoiceTypeExt(QrcodeOrderInfo qrcodeOrderInfo, String invoiceTypeCodeId, String xhfNsrsbh) {
		
		List<InvoiceTypeCodeExt> extList = new ArrayList<>();
		String fpzldm = qrcodeOrderInfo.getFpzldm();
		if (StringUtils.isNotEmpty(fpzldm)) {
			/*String[] fpzldmArr = fpzldm.split("\\|");*/
			String[] fpzldmArr = JsonUtils.getInstance().fromJson(fpzldm, String[].class);
			for (String fpzl : fpzldmArr) {
				InvoiceTypeCodeExt invoiceTypeCodeExt = new InvoiceTypeCodeExt();
				invoiceTypeCodeExt.setFpzlDm(fpzl);
				invoiceTypeCodeExt.setCreateTime(new Date());
				invoiceTypeCodeExt.setFpzlDmMc(CommonUtils.getFpzlDmMc(fpzl));
				invoiceTypeCodeExt.setId(apiInvoiceCommonService.getGenerateShotKey());
				invoiceTypeCodeExt.setInvoiceTypeCodeId(invoiceTypeCodeId);
				invoiceTypeCodeExt.setXhfNsrsbh(xhfNsrsbh);
				extList.add(invoiceTypeCodeExt);
			}
		}
	    return extList;
	}
	
	private List<QuickResponseCodeItemInfo> qrcodeOrderInfoToQuickRsponseItem(List<PageOrderItemInfo> orderItemList,
	                                                                          String quickResponseCodeInfoId, String xhfNsrsbh) {
		
		List<QuickResponseCodeItemInfo> itemList = new ArrayList<>();
		int i = 1;
		for (PageOrderItemInfo item : orderItemList) {
			QuickResponseCodeItemInfo itemInfo = new QuickResponseCodeItemInfo();
			itemInfo.setXmmc(item.getXmmc());
			itemInfo.setZzstsgl(item.getZzstsgl());
			itemInfo.setByzd1(item.getByzd1());
			itemInfo.setByzd2(item.getByzd2());
			itemInfo.setByzd3(item.getByzd3());
			itemInfo.setByzd4(item.getByzd4());
			itemInfo.setByzd5(item.getByzd5());
			itemInfo.setCreateTime(new Date());
			itemInfo.setFphxz(item.getFphxz());
			itemInfo.setGgxh(item.getGgxh());
			itemInfo.setYhzcbs(item.getYhzcbs());
			itemInfo.setHsbz(item.getHsbz());
			itemInfo.setId(apiInvoiceCommonService.getGenerateShotKey());
			itemInfo.setKce(item.getKce());
			itemInfo.setLslbs(item.getLslbs());
			itemInfo.setQuickResponseCodeInfoId(quickResponseCodeInfoId);
			itemInfo.setSe(item.getSe());
			itemInfo.setSl(item.getSl());
			itemInfo.setSpbm(item.getSpbm());
			itemInfo.setSphxh(String.valueOf(i));
			itemInfo.setWcje(item.getWcje());
			itemInfo.setXmdj(item.getXmdj());
			itemInfo.setXmdw(item.getXmdw());
			itemInfo.setXmje(item.getXmje());
			itemInfo.setXmsl(item.getXmsl());
			itemInfo.setZxbm(item.getZxbm());
			itemInfo.setXhfNsrsbh(xhfNsrsbh);
			itemList.add(itemInfo);
			i++;
		}
		return itemList;
	}

	private QuickResponseCodeInfo qrcodeOrderInfoToQuickRsponseInfo(QrcodeOrderInfo qrcodeOrderInfo) {
		QuickResponseCodeInfo quickResponseCodeInfo = new QuickResponseCodeInfo();
		quickResponseCodeInfo.setCreateTime(new Date());
		quickResponseCodeInfo.setUpdateTime(new Date());
		quickResponseCodeInfo.setYwlxId(qrcodeOrderInfo.getYwlxId());
		quickResponseCodeInfo.setYwlx(qrcodeOrderInfo.getYwlx());
		quickResponseCodeInfo.setSkr(qrcodeOrderInfo.getSkr());
		quickResponseCodeInfo.setFhr(qrcodeOrderInfo.getFhr());
		quickResponseCodeInfo.setKpr(qrcodeOrderInfo.getKpr());
		quickResponseCodeInfo.setXhfMc(qrcodeOrderInfo.getXhfmc());
		quickResponseCodeInfo.setXhfNsrsbh(qrcodeOrderInfo.getXhfNsrsbh());
		quickResponseCodeInfo.setXhfDz(qrcodeOrderInfo.getXhfdz());
		quickResponseCodeInfo.setXhfYh(qrcodeOrderInfo.getXhfyh());
		quickResponseCodeInfo.setXhfDh(qrcodeOrderInfo.getXhfdh());
		quickResponseCodeInfo.setXhfZh(qrcodeOrderInfo.getXhfzh());
		quickResponseCodeInfo.setQuickResponseCodeType(qrcodeOrderInfo.getQrCodeType());
		quickResponseCodeInfo.setQuickResponseCodeUrl(qrcodeOrderInfo.getQrCodeUrl());
		quickResponseCodeInfo.setEwmzt(ConfigureConstant.STRING_0);
		return quickResponseCodeInfo;
	}
	
	
	
    /**
     * 动态码列表接口
     */
    @Override
    public PageUtils queryDynamicQrcodeList(Map<String, Object> paramMap, List<String> shList) {
	
	    PageUtils page = orderQrcodeExtendService.queryDynamicQrCodeList(paramMap, shList);
	
	
	    List<Map> list = (List<Map>) page.getList();
	    for (Map map : list) {
		    //开票状态处理
		    String kpzt = map.get("kpzt") == null ? "" : String.valueOf(map.get("kpzt"));
		    if (StringUtils.isBlank(kpzt)) {
			    map.put("kpzt", "0");
		    }
		    //二维码状处理
		    String ewmzt = map.get("ewmzt") == null ? "" : String.valueOf(map.get("ewmzt"));
		
		    if (OrderInfoEnum.EWM_STATUS_0.getKey().equals(ewmzt)) {
			    String zfzt = map.get("zfzt") == null ? "" : String.valueOf(map.get("zfzt"));
			    if ("1".equals(zfzt)) {
				    map.put("ewmzt", OrderInfoEnum.EWM_STATUS_3.getKey());
			    } else {
				    String validTime = map.get("quickResponseCodeValidTime") == null ? "" : String.valueOf(map.get("quickResponseCodeValidTime"));
				    if (!StringUtils.isBlank(validTime)) {
					
					    Date validDate = DateUtil.parse(validTime, "yyyy-MM-dd HH:mm:ss");
					    if (new Date().after(validDate)) {
						    //二维码失效
						    map.put("ewmzt", OrderInfoEnum.EWM_STATUS_2.getKey());
					    }else {
						    map.put("ewmzt", OrderInfoEnum.EWM_STATUS_0.getKey());
					    }
					}else {
					    map.put("ewmzt", OrderInfoEnum.EWM_STATUS_0.getKey());
				    }
				}
			}else{
				String zfzt = map.get("zfzt") == null ? "" : String.valueOf(map.get("zfzt"));

				if("1".equals(zfzt)) {
					map.put("ewmzt", OrderInfoEnum.EWM_STATUS_3.getKey());
				}else {
					map.put("ewmzt", OrderInfoEnum.EWM_STATUS_1.getKey());
					
				}
			}
			
		}
		page.setList(list);
		//列表数据处理
		
		return page;
	}
	
	
	@Override
	public Map<String,Object> queryEwmConfigInfo(Map<String,Object> paramMap) {
		
		EwmConfigInfo queryEwmConfigInfo = quickCodeInfoService.queryEwmConfigInfo(paramMap);
		if (queryEwmConfigInfo == null) {
			return null;
		}
		List<EwmConfigItemInfo> ewmConfigItemList = quickCodeInfoService.queryEwmConfigItemInfoById(queryEwmConfigInfo.getId());
		Map<String, Object> resultMap = new HashMap<>(5);
		resultMap.put("ewmConfigInfo", queryEwmConfigInfo);
		resultMap.put("ewmConfigItemList", ewmConfigItemList);
		return resultMap;
	}
	
	
	@Override
	public boolean addEwmConfigInfo(PageEwmConfigInfo pageEwmConfigInfo) {
		EwmConfigInfo ewmConfig = new EwmConfigInfo();
		ewmConfig.setInvalidTime(pageEwmConfigInfo.getInvalidTime());
		ewmConfig.setXhfMc(pageEwmConfigInfo.getXhfMc());
		ewmConfig.setXhfNsrsbh(pageEwmConfigInfo.getXhfNsrsbh());
		ewmConfig.setCreateTime(new Date());
		ewmConfig.setId(apiInvoiceCommonService.getGenerateShotKey());
		
		List<PageEwmItem> itemList = pageEwmConfigInfo.getItemList();
		List<EwmConfigItemInfo> ewmConfigItemList = new ArrayList<>();
		for (PageEwmItem item : itemList) {
			EwmConfigItemInfo ewmItem = new EwmConfigItemInfo();
			ewmItem.setCreateTime(new Date());
			ewmItem.setEwmCoinfgId(ewmConfig.getId());
			ewmItem.setFpzldm(item.getFpzlDm());
			ewmItem.setId(apiInvoiceCommonService.getGenerateShotKey());
			ewmItem.setSld(item.getSld());
			ewmItem.setSldMc(item.getSldMc());
			ewmConfigItemList.add(ewmItem);
			
		}
		
		return quickCodeInfoService.addEwmConfigInfo(ewmConfig,ewmConfigItemList);
	}
	
	
	@Override
	public boolean updateEwmConfigInfo(PageEwmConfigInfo pageEwmConfigInfo) {
		
		EwmConfigInfo ewmConfig = new EwmConfigInfo();
		ewmConfig.setInvalidTime(pageEwmConfigInfo.getInvalidTime());
		ewmConfig.setXhfMc(pageEwmConfigInfo.getXhfMc());
		ewmConfig.setXhfNsrsbh(pageEwmConfigInfo.getXhfNsrsbh());
		ewmConfig.setId(pageEwmConfigInfo.getId());
		
		List<PageEwmItem> itemList = pageEwmConfigInfo.getItemList();
		List<EwmConfigItemInfo> ewmConfigItemList = new ArrayList<>();
		for (PageEwmItem item : itemList) {
			EwmConfigItemInfo ewmItem = new EwmConfigItemInfo();
			ewmItem.setCreateTime(new Date());
			ewmItem.setEwmCoinfgId(ewmConfig.getId());
			ewmItem.setFpzldm(item.getFpzlDm());
			ewmItem.setId(apiInvoiceCommonService.getGenerateShotKey());
			ewmItem.setSld(item.getSld());
			ewmItem.setSldMc(item.getSldMc());
			ewmConfigItemList.add(ewmItem);
			
		}
		return quickCodeInfoService.updateEwmConfigInfo(ewmConfig,ewmConfigItemList);
	}
	
	
	@Override
	public Map<String, Object> queryEwmDetailByFpqqlsh(String fpqqlsh, List<String> shList) {
		
		Map<String, Object> resultMap = new HashMap<>(5);
		Map<String, Object> queryEwmDetailByFpqqlsh = orderQrcodeExtendService.queryEwmDetailByFpqqlsh(fpqqlsh, shList);
		resultMap.put("qrCodeInfo", queryEwmDetailByFpqqlsh);
		
		if (queryEwmDetailByFpqqlsh != null) {
			String orderInfoId = queryEwmDetailByFpqqlsh.get("orderInfoId") == null ? "" : String.valueOf(queryEwmDetailByFpqqlsh.get("orderInfoId"));
			//todo zsc 等待处理明细表时进行操作
//			List<OrderItemInfo> orderItemInfos = orderItemInfoService.selectOrderItemInfoByOrderId(orderInfoId, shList);
//			PageDataDealUtil.dealOrderItemInfo(orderItemInfos);
//			if (ObjectUtil.isNotEmpty(orderItemInfos)) {
//				resultMap.put("qrCodeItemList", orderItemInfos);
//			}
		}
		return resultMap;
	}
	
	
	@Override
	public boolean updateEwmDetailInfo(List<Map> idList) {
		
		return orderQrcodeExtendService.updateEwmDetailInfoByIds(idList);
	}
	
	@Override
	public R updateStaticEwmInfo(QrcodeOrderInfo qrcodeOrderInfo) {
		
		QuickResponseCodeInfo qrCodeInfo = qrcodeOrderInfoToQuickRsponseInfo(qrcodeOrderInfo);
		qrCodeInfo.setId(qrcodeOrderInfo.getId());
		List<QuickResponseCodeItemInfo> itemList = qrcodeOrderInfoToQuickRsponseItem(qrcodeOrderInfo.getOrderItemList(), qrCodeInfo.getId(), qrCodeInfo.getXhfNsrsbh());
		List<InvoiceTypeCodeExt> extList = buildInvoiceTypeExt(qrcodeOrderInfo, qrCodeInfo.getId(), qrCodeInfo.getXhfNsrsbh());
		
		return quickCodeInfoService.updateStaticEwmInfo(qrCodeInfo, itemList, extList);
	}
	
	@Override
	public R deleteStaticEwmInfo(List<String> qrcodeIds) {
		
		for (String qrcodeId : qrcodeIds) {
			List<String> shList = new ArrayList<>();
			QuickResponseCodeInfo quickResponseCodeInfo = new QuickResponseCodeInfo();
			quickResponseCodeInfo.setId(qrcodeId);
			quickResponseCodeInfo.setEwmzt("1");
			quickResponseCodeInfo.setUpdateTime(new Date());
			boolean b = quickCodeInfoService.updateEwmDetailInfo(quickResponseCodeInfo, shList);
			if (b) {
				log.info("二维码："+qrcodeId+",更新ewmzt为删除状态成功");
			} else {
				return R.error("二维码id为："+qrcodeId+"的数据删除失败");
			}
		}
		return R.ok();
		
	}

	@Override
	public InvoiceIssueRes generateDynamicQrCode(PageQrcodeOrderInfo pageQrcodeOrderInfo) {

		try {
			OrderInvoiceInfoEntity pageToFpkjInfo = PageDataDealUtil.pageToFpkjInfo(pageQrcodeOrderInfo);
			
			//校验二维码信息
			Map<String, String> resultMap = validateDynamicService.verifyDynamicEwmInfo(pageToFpkjInfo);
			if (!OrderInfoContentEnum.SUCCESS.getKey()
					.equals(resultMap.get(OrderManagementConstant.ERRORCODE))) {
				log.error("动态码生成，订单信息校验失败，请求流水号:{},错误信息:{}", pageToFpkjInfo.getFpqqlsh(), resultMap.get(OrderManagementConstant.ERRORMESSAGE));
				return InvoiceIssueRes.error(resultMap.get(OrderManagementConstant.ERRORCODE),resultMap.get(OrderManagementConstant.ERRORMESSAGE));
			}
			//查询当前税号下的配置信息
			Map<String, Object> paramMap = new HashMap<>(2);
			paramMap.put("xhfNsrsbh", pageToFpkjInfo.getXhfNsrsbh());
			EwmConfigInfo queryEwmConfigInfo = quickCodeInfoService.queryEwmConfigInfo(paramMap);
			//保存订单二维码扩展表
			OrderQrcodeExtendInfo orderQrcodeExtendInfo = PageDataDealUtil.buildOrderQrcodeInfo(pageToFpkjInfo);
			//设置失效时间
			int invalidDays = 30;
			if (queryEwmConfigInfo != null) {
				invalidDays = StringUtils.isBlank(queryEwmConfigInfo.getInvalidTime()) ? 30 : Integer.parseInt(queryEwmConfigInfo.getInvalidTime());
			}
			//订单没有过期时间，永不失效 数据库中过期时间设置为2099 01 01 00：00：00
			if(invalidDays == 0){
				Date validDate = DateUtil.parse("2099-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
				orderQrcodeExtendInfo.setQuickResponseCodeValidTime(validDate);
			} else {
				Date validDate = DateUtil.offsetDay(pageToFpkjInfo.getDdscrq(), invalidDays);
				orderQrcodeExtendInfo.setQuickResponseCodeValidTime(validDate);
			}
			orderQrcodeExtendInfo.setTqm(apiInvoiceCommonService.getGenerateShotKey());
			orderQrcodeExtendInfo.setId(apiInvoiceCommonService.getGenerateShotKey());
			orderQrcodeExtendInfo.setQuickResponseCodeType(OrderInfoEnum.QR_TYPE_1.getKey());
			orderQrcodeExtendInfo.setOrderInfoId(pageToFpkjInfo.getId());
			String qrcodeUrl = String.format(openApiConfig.configQrCodeScanUrl(), orderQrcodeExtendInfo.getTqm(), orderQrcodeExtendInfo.getXhfNsrsbh(), ConfigureConstant.STRING_1);
			orderQrcodeExtendInfo.setQuickResponseCodeUrl(qrcodeUrl);
			apiInvoiceCommonService.saveQrCodeData(pageToFpkjInfo, orderQrcodeExtendInfo);
			
			String qrCodeString = QrCodeUtil.drawLogoQrCode(null,
					String.format(openApiConfig.configQrCodeShortUrl(), orderQrcodeExtendInfo.getTqm()), "", pageQrcodeOrderInfo.getBackGround());
			Map<String, Object> returnMap = new HashMap<>();
			returnMap.put("ewm", qrCodeString);
			returnMap.put("qrcodeInfo", orderQrcodeExtendInfo);
			return InvoiceIssueRes.ok(returnMap);
		} catch (OrderReceiveException e) {
			log.error("生成动态码异常:{}",e.getMessage());
			return InvoiceIssueRes.error(e.getCode(),e.getMessage());
		}
	}

	@Override
	public Map<String, Object> queryQrcodeAndInvoiceDetail(String qrcodeId, String type, List<String> shList, String backGround) {
		
		Map<String, Object> resultMap = orderQrcodeExtendService.queryQrcodeAndInvoiceDetail(qrcodeId, shList);
		String kpzt = resultMap.get("kpzt") == null ? "" : String.valueOf(resultMap.get("kpzt"));
		if (StringUtils.isBlank(kpzt)) {
			resultMap.put("kpzt", "0");
		}
		//二维码状处理
		String ewmzt = resultMap.get("ewmzt") == null ? "" : String.valueOf(resultMap.get("ewmzt"));
		
		if (OrderInfoEnum.EWM_STATUS_0.getKey().equals(ewmzt)) {
			String zfzt = resultMap.get("zfzt") == null ? "" : String.valueOf(resultMap.get("zfzt"));
			if (ConfigureConstant.STRING_1.equals(zfzt)) {
				resultMap.put("ewmzt", OrderInfoEnum.EWM_STATUS_3.getKey());
			} else {
				String validTime = resultMap.get("quickResponseCodeValidTime") == null ? "" : String.valueOf(resultMap.get("quickResponseCodeValidTime"));
				if (!StringUtils.isBlank(validTime)) {
					
					Date validDate = DateUtil.parse(validTime, "yyyy-MM-dd HH:mm:ss");
					if (new Date().after(validDate)) {
						//二维码失效
						resultMap.put("ewmzt", OrderInfoEnum.EWM_STATUS_2.getKey());
					} else {
						resultMap.put("ewmzt", OrderInfoEnum.EWM_STATUS_0.getKey());
					}
				} else {
					resultMap.put("ewmzt", OrderInfoEnum.EWM_STATUS_0.getKey());
				}
			}
		} else {
			resultMap.put("ewmzt", OrderInfoEnum.EWM_STATUS_1.getKey());
		}
		
		String qrCodeString = QrCodeUtil.drawLogoQrCode(null,
				String.format(openApiConfig.configQrCodeShortUrl(), resultMap.get("tqm") == null ? "" : String.valueOf(resultMap.get("tqm"))), "", backGround);
		resultMap.put("ewm", qrCodeString);
		
		return resultMap;
	}

	@Override
	public boolean isExistNoAuditOrder(Map<String,Object> paramMap,List<String> shList) {
		
		return orderQrcodeExtendService.isExistNoAuditOrder(paramMap, shList);
	}

}
