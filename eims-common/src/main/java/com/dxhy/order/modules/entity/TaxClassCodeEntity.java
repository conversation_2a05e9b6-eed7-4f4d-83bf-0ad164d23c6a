package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 税收分类编码表
 * <AUTHOR>
 * @Date 2022/6/27 12:04
 * @Version 1.0
 **/
@TableName("tax_class_code")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("税收分类编码表")
public class TaxClassCodeEntity implements Serializable {
   private static final long serialVersionUID = 1L;
   /**
    * 税收分类编码id
    */
   @ApiModelProperty("税收分类编码id")
   @TableId(type = IdType.INPUT)
   private String id;

   /**
    * 商品编码
    */
   @ApiModelProperty("商品编码")
   private String spbm;

   /**
    * 商品名称
    */
   @ApiModelProperty("商品名称")
   private String spmc;

   /**
    * 商品简称
    */
   @ApiModelProperty("商品简称")
   private String spjc;

   /**
    * 说明
    */
   @ApiModelProperty("说明")
   private String sm;

   /**
    * 增值税税率
    */
   @ApiModelProperty("增值税税率")
   private String zzssl;

   /**
    * 关键字
    */
   @ApiModelProperty("关键字")
   private String gjz;

   /**
    * 汇总项 Y 是 N 不是
    */
   @ApiModelProperty("汇总项 Y 是 N 不是")
   private String hzx;

   /**
    * 可用状态 Y 可用 N不可用
    */
   @ApiModelProperty("可用状态 Y 可用 N不可用")
   private String kyzt;

   /**
    * 增值税特殊管理
    */
   @ApiModelProperty("增值税特殊管理")
   private String zzstsgl;

   /**
    * 增值税政策依据
    */
   @ApiModelProperty("增值税政策依据")
   private String zzszcyj;

   /**
    * 增值税特殊内容代码
    */
   @ApiModelProperty("增值税特殊内容代码")
   private String zzstsnrdm;

   /**
    * 消费税管理
    */
   @ApiModelProperty("消费税管理")
   private String xfsgl;

   /**
    * 消费税政策依据
    */
   @ApiModelProperty("消费税政策依据")
   private String xfszcyj;

   /**
    * 消费税特殊内容代码
    */
   @ApiModelProperty("消费税特殊内容代码")
   private String xfstsnrdm;

   /**
    * 统计局编码
    */
   @ApiModelProperty("统计局编码")
   private String tjjbm;

   /**
    * 海关进出口商品名称
    */
   @ApiModelProperty("海关进出口商品名称")
   private String hgjcksppm;

   /**
    * pid
    */
   @ApiModelProperty("pid")
   private Long pid;

   /**
    * 优惠政策名称
    */
   @ApiModelProperty("优惠政策名称")
   private String yhzcmc;

   /**
    * 税率
    */
   @ApiModelProperty("税率")
   private String sl;

   /**
    * 创建时间
    */
   @ApiModelProperty("创建时间")
   @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
   private Date createTime;

   /**
    * 编码表版本号
    */
   @ApiModelProperty("编码表版本号")
   private String bbh;

   /**
    * 是否成品油(Y:成品油;N:非成品油)
    */
   @ApiModelProperty("是否成品油(Y:成品油;N:非成品油)")
   private String cpy;

   /**
    * 是否机动车税编(Y:机动车;N:非机动车)
    */
   @ApiModelProperty("是否机动车税编(Y:机动车;N:非机动车)")
   private String jdc;

   /**
    * 税编启用时间
    */
   @ApiModelProperty("税编启用时间")
   @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
   private Date enablingTime;

   /**
    * 税编更新时间
    */
   @ApiModelProperty("税编更新时间")
   @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
   private Date updateTime;

   /**
    * 免税类型（0：正常税率；1-出口免税率或其他免税优惠政策；2-不征增值税；3-普通零税率）
    */
   @ApiModelProperty("免税类型（0：正常税率；1-出口免税率或其他免税优惠政策；2-不征增值税；3-普通零税率）")
   private String mslx;
}
