package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.TaxClassCodeDao;
import com.dxhy.order.modules.entity.TaxClassCodeEntity;
import com.dxhy.order.modules.service.TaxClassCodeService;
import com.dxhy.order.utils.R;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 税收分类编码表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/27 12:13
 * @Version 1.0
 **/
@Service("taxClassCodeService")
public class TaxClassCodeServiceImpl extends ServiceImpl<TaxClassCodeDao, TaxClassCodeEntity> implements TaxClassCodeService {

    @Autowired
    private TaxClassCodeDao taxClassCodeDao;

    private Long basePid = 0L;

    @Override
    public R listByName(String spmc) {
        if(StringUtils.isEmpty(spmc)){
            return R.ok().put("data", taxClassCodeDao.listById(basePid));
        }
        return R.ok().put("data", taxClassCodeDao.listByName(spmc));
    }

    @Override
    public R listById(String pid) {
        if(StringUtils.isEmpty(pid)){
            return R.ok().put("data", taxClassCodeDao.listById(basePid));
        }
        Long _pid = Long.valueOf(pid);
        return R.ok().put("data", taxClassCodeDao.listById(_pid));
    }
}
