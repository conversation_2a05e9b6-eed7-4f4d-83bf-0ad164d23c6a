package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.InvoiceAdditionInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发票附加要素表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-22 19:47:06
 */
@Mapper
public interface InvoiceAdditionInfoDao extends BaseMapper<InvoiceAdditionInfoEntity> {

    List<InvoiceAdditionInfoEntity> selectList(Page page, InvoiceAdditionInfoEntity invoiceAdditionInfoEntity);

    List<InvoiceAdditionInfoEntity> selectAdditionListById(@Param("id") String id);

    void deleteByOrderId(String id);
}
