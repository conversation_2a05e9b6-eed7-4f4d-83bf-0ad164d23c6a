package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.CustomerInfoEntity;
import com.dxhy.order.modules.pojo.dto.*;
import com.dxhy.order.utils.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户信息表 service
 * <AUTHOR>
 * @Date 2022/6/27 12:10
 * @Version 1.0
 **/
public interface CustomerInfoService extends IService<CustomerInfoEntity> {

    /**
     * 获取客户分类树
     * @param nsrsbh
     * @return com.dxhy.order.modules.pojo.bo.GroupTreeBO
     * <AUTHOR>
     **/
    R getGroupTree(String nsrsbh);

    /**
     * 保存客户分类（新增 修改）
     * @param customerGroupSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveCustomerGroup(CustomerGroupSaveDTO customerGroupSaveDTO);

    /**
     * 删除客户分类
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R deleteCustomerGroup(String id);

    /**
     * 查询列表
     * @param customerInfoListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R queryPage(CustomerInfoListDTO customerInfoListDTO);

    /**
     * 列表 - 不选择客户分类
     * @param customerInfoListWithoutIdDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listWithoutId(CustomerInfoListWithoutIdDTO customerInfoListWithoutIdDTO);

    /**
     * 根据名称查询列表 - 不分页
     * @param customerInfoListByNameWithoutPageDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listByNameWithoutPage(CustomerInfoListByNameWithoutPageDTO customerInfoListByNameWithoutPageDTO);

    /**
     * 新增&修改 客户信息
     * @param customerInfoSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveData(CustomerInfoSaveDTO customerInfoSaveDTO);

    /**
     * 删除数据
     * @param id
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R deleteData(String id);

    /**
     * 导出数据
     * @param idList
     * @param response
     * @return void
     * <AUTHOR>
     **/
    void exportData(List<String> idList, HttpServletResponse response);

    /**
     * 上传项目信息
     * @param file
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R uploadCustomerExcel(MultipartFile file);

    /**
     * 保存Excel上传的客户信息
     * @param customerInfoExcelSaveDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R saveDataFromExcel(CustomerInfoExcelSaveDTO customerInfoExcelSaveDTO);

    /**
     * 将Excel错误的数据导出
     * @param customerInfoExcelSaveDTO
     * @return void
     * <AUTHOR>
     **/
    void genExcelForErrorData(CustomerInfoExcelSaveDTO customerInfoExcelSaveDTO, HttpServletResponse response);

    /**
     * 查询购货方名称
     * @return
     */
    List<String> selectGhfmcList(String nsrsbh);

    List<String> selectGhfmcListByGhfsbh(String basensrsbh, String ghfsbh);
}

