package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName invoice_config
 */
@TableName(value ="invoice_config")
@Data
public class InvoiceConfigInfo extends BasePage implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 企业名称
     */
    private String qymc;

    /**
     * 纳税人识别号
     */
    @JsonProperty("baseNsrsbh")
    private String nsrsbh;

    /**
     * 地址
     */
    private String dz;

    /**
     * 电话
     */
    private String dh;

    /**
     * 开户银行
     */
    private String khyh;

    /**
     * 银行账户
     */
    private String yhzh;

    /**
     * 开票人
     */
    private String kpr;

    /**
     * 账户类型（0默认账户，1普通账户）
     */
    private String zhlx;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}