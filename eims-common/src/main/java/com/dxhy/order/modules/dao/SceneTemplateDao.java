package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.SceneTemplateEntity;
import com.dxhy.order.modules.pojo.dto.SceneTemplateListDTO;
import com.dxhy.order.modules.pojo.vo.SceneTemplateListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景模板表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface SceneTemplateDao extends BaseMapper<SceneTemplateEntity> {

    /**
     * 场景模板列表
     * @param sceneTemplateListDTO
     * @param page
     * @return java.util.List<com.dxhy.order.modules.entity.SceneTemplateVO>
     * <AUTHOR>
     **/
    List<SceneTemplateListVO> selectList(Page page, @Param("sceneTemplateListDTO") SceneTemplateListDTO sceneTemplateListDTO);

    /**
     * 场景模板列表 - 不分页
     * @param sceneTemplateListDTO
     * @return java.util.List<com.dxhy.order.modules.entity.SceneTemplateEntity>
     * <AUTHOR>
     **/
    List<SceneTemplateEntity> listWithoutPage(SceneTemplateListDTO sceneTemplateListDTO);

    /**
     * 根据税号和场景模板名称查询列表
     * @param baseNsrsbh
     * @param name
     * @return java.util.List<com.dxhy.order.modules.entity.SceneTemplateEntity>
     * <AUTHOR>
     **/
    List<SceneTemplateEntity> selectListByNsrsbhAndName(@Param("baseNsrsbh") String baseNsrsbh, @Param("name") String name);

    /**
     * 根据ID查询数据
     * @param id
     * @return com.dxhy.order.modules.entity.SceneTemplateEntity
     * <AUTHOR>
     **/
    SceneTemplateEntity queryDataById(@Param("id") String id);

    /**
     * 根据ID删除数据（逻辑删除）
     * @param idList
     * @return void
     * <AUTHOR>
     **/
    void deleteByIdList(@Param("idList") List<String> idList);

    /**
     * 根据UUID查询数据
     * @param uuid
     * @return com.dxhy.order.modules.entity.SceneTemplateEntity
     * <AUTHOR>
     **/
    SceneTemplateEntity queryDataByUUID(String uuid);
}
