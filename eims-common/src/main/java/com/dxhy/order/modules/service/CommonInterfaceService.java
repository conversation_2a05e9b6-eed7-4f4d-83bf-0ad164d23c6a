package com.dxhy.order.modules.service;


import com.dxhy.order.pojo.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description: 订单对外接口业务层接口
 * @author:
 * @date:
 */
public interface CommonInterfaceService {
    
    /**
     * 统一校验
     *
     * @param interfaceVersion
     * @param interfaceName
     * @param timestamp
     * @param nonce
     * @param secretId
     * @param signature
     * @param encryptCode
     * @param zipCode
     * @param content
     * @return
     */
    Result checkInterfaceParam(String interfaceVersion, String interfaceName, String timestamp, String nonce, String secretId, String signature, String encryptCode, String zipCode, String content);
    
    /**
     * 统一鉴权方法
     *
     * @param request
     * @param response
     * @return
     */
    Result auth(HttpServletRequest request, HttpServletResponse response);


    /**
     * 数据解密
     *
     * @param zipCode 压缩标识
     * @param encryptCode 加密标识
     * @param content 待加密数据
     * @param secretKey 密钥
     * @return
     */
    String commonDecrypt(String zipCode, String encryptCode, String content, String secretKey);


    /**
     * 数据加密
     *
     * @param zipCode 压缩标识
     * @param encryptCode 加密标识
     * @param content 待加密数据
     * @param secretKey 密钥
     * @return
     */
    String commonEncrypt(String zipCode, String encryptCode, String content, String secretKey);
    

}
