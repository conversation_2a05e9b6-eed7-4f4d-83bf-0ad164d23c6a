package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.*;
import com.dxhy.order.model.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.*;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.pojo.*;
import com.dxhy.order.pojo.single.*;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("invoiceIssueService")
@Slf4j
@RefreshScope
public class InvoiceIssueServiceImpl implements InvoiceIssueService {

    private static final String LOGGER = "(对外API)";

    @Autowired
    private RedInvoiceConfirmService redInvoiceConfirmService;
    @Autowired
    private RedInvoiceConfirmDao redInvoiceConfirmDao;
    @Autowired
    OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    OrderInvoiceInfoService orderInvoiceInfoService;
    @Autowired
    private SalerWarningDao salerWarningDao;
    @Autowired
    OrderInvoiceItemDao orderInvoiceItemDao;
    @Autowired
    TaxClassCodeDao taxClassCodeDao;
    @Autowired
    InvoiceAdditionInfoDao invoiceAdditionInfoDao;
    @Resource
    private InvoiceConfigDao invoiceConfigDao;
    @Resource
    private SsoUtil ssoUtil;
    @Resource
    private OrderInvoiceConfigService orderInvoiceConfigService;

    @Value("${order.encrypt.redInvoiceConfirmSldhUrl}")
    private String redInvoiceConfirmSldhUrl;
    @Value("${order.encrypt.queryHzqrxxUrl}")
    private String queryHzqrxxUrl;
    @Value("${order.encrypt.queryHzqrxxStatusUrl}")
    private String queryHzqrxxStatusUrl;
    @Value("${order.encrypt.redInvoiceIssueUrl}")
    private String redInvoiceIssueUrl;
    @Value("${order.encrypt.redInvoiceResultInfoUrl}")
    private String redInvoiceResultInfoUrl;
    @Value("${order.encrypt.invoiceGenerateRedUrl}")
    private String invoiceGenerateRedUrl;
    @Value("${order.encrypt.invoiceInfosAndItemsUrl}")
    private String invoiceInfosAndItemsUrl;
    @Value("${order.encrypt.singelInvoiceInfosAndItemsUrl}")
    private String singelInvoiceInfosAndItemsUrl;
    @Value("${order.encrypt.hzqrxxUpdateUrl}")
    private String hzqrxxUpdateUrl;
    @Resource
    private OrderInvoiceConfigDao orderInvoiceConfigDao;
    @Autowired
    private InvoiceTdywService invoiceTdywService;
    @Autowired
    private InvoiceCezsService invoiceCezsService;
    @Resource(name = ConfigurerInfo.COMMONTHREADPOOL)
    private Executor executor;

    @Override
    public InvoiceIssueRes invoiceIssue(List<InvoiceIssueInfoParam> invoiceIssueInfoParamList) {
        if(CollectionUtils.isEmpty(invoiceIssueInfoParamList)){
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9514.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9514.getKey());
        }else {
            // 提交任务到线程池
            List<CompletableFuture<InvoiceIssueRes>> futures = new ArrayList<>();
            for(InvoiceIssueInfoParam invoiceIssueInfoParam : invoiceIssueInfoParamList){
                CompletableFuture<InvoiceIssueRes> future = CompletableFuture.supplyAsync(() -> {
                    InvoiceIssueRes invoiceIssueRes = invoiceIssue(invoiceIssueInfoParam);
                    return invoiceIssueRes;
                },executor).exceptionally(ex -> {
                    // 捕获异常并封装失败信息
                    log.error("请求流水号为:【"+invoiceIssueInfoParam.getDdqqlsh()+"】的数据开票失败，原因: " + ex.getMessage());
                    return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
                });
                //添加所有任务
                futures.add(future);
            }
            // 处理多个任务
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            try {
                //等待所有任务完成
                allFutures.get(300L, TimeUnit.SECONDS);
                List<DxInvoiceIssueResponse.InvoiceIssueSuccessRes> successResList = new ArrayList<>();
                List<DxInvoiceIssueResponse.InvoiceIssueErrorRes> errorResList = new ArrayList<>();
                for (CompletableFuture<InvoiceIssueRes> future : futures) {
                    //单任务运行结果
                    InvoiceIssueRes invoiceIssueRes = future.get();
                    if(OrderInfoContentEnum.API_RETURN_ERROR_0000.getKey().equals(invoiceIssueRes.getZtdm())){
                        //开具成功
                        DxInvoiceIssueResponse.InvoiceIssueSuccessRes data = (DxInvoiceIssueResponse.InvoiceIssueSuccessRes) invoiceIssueRes.getData();
                        successResList.add(data);
                    }else {
                        //开具失败
                        DxInvoiceIssueResponse.InvoiceIssueErrorRes errorRes = new DxInvoiceIssueResponse.InvoiceIssueErrorRes();
                        errorRes.setErrorCode(invoiceIssueRes.getZtdm());
                        errorRes.setErrorMsg(invoiceIssueRes.getZtxx());
                        errorRes.setDdh(invoiceIssueRes.getData().toString());
                        errorResList.add(errorRes);
                    }
                }
                DxInvoiceIssueResponse dxInvoiceIssueResponse = new DxInvoiceIssueResponse();
                dxInvoiceIssueResponse.setSuccessList(successResList);
                dxInvoiceIssueResponse.setErrorList(errorResList);
                return InvoiceIssueRes.ok(dxInvoiceIssueResponse);
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException("获取多线程任务结果失败："+e.getMessage());
            } catch (TimeoutException e) {
                throw new RuntimeException("获取多线程任务结果超时");
            }
        }
    }
    /**
     * 3.1 蓝字发票开具接口
     *
     * @param invoiceIssueInfoParam
     * @return
     */
    private InvoiceIssueRes invoiceIssue(InvoiceIssueInfoParam invoiceIssueInfoParam) {
        log.info("{}，蓝字发票开具接口，入参：{}", LOGGER, JsonUtils.getInstance().toJsonString(invoiceIssueInfoParam));
        try {
            Map<String, String> invoiceIssueMap = checkInvoiceIssueInfoParam(invoiceIssueInfoParam);
            if (!CollectionUtils.isEmpty(invoiceIssueMap)) {
                String errorCode = invoiceIssueMap.get(OrderManagementConstant.ERRORCODE);
                String errorMsg = invoiceIssueMap.get(OrderManagementConstant.ERRORMESSAGE);
                return InvoiceIssueRes.res(errorCode, errorMsg,invoiceIssueInfoParam.getDdh());
            }
        } catch (Exception e) {
            log.error("checkInvoiceIssueInfoParam-invoiceIssue 校验异常: {}", e);
            return InvoiceIssueRes.res(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage(),invoiceIssueInfoParam.getDdh());
        }
        DxInvoiceIssueResponse.InvoiceIssueSuccessRes invoiceIssueSuccessRes = new DxInvoiceIssueResponse.InvoiceIssueSuccessRes();
        InvoiceIssueRes res = null;
        // 手动开票  入库
        try {
            // 配置 是否智能匹配税收分类编码
            OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(invoiceIssueInfoParam.getNsrsbh());
            if (ObjectUtils.isEmpty(orderInvoiceConfigEntity)) {
                int i = orderInvoiceConfigService.insertDefaultInfo(invoiceIssueInfoParam.getNsrsbh());
                if (i < 1) {
                    log.info("{}，蓝字发票开具接口，未配置规则：{}", LOGGER, JsonUtils.getInstance().toJsonString(invoiceIssueInfoParam));
                    return InvoiceIssueRes.res(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage(),invoiceIssueInfoParam.getDdh());
                }
            }

            if ("1".equals(invoiceIssueInfoParam.getKpfs())) {
                OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
                String invoiceId = DistributedKeyMaker.generateShotKey();
                orderInvoiceInfoEntity.setId(invoiceId);
                orderInvoiceInfoEntity.setPch(DistributedKeyMaker.generateShotKey());
                orderInvoiceInfoEntity.setFpqqlsh(invoiceIssueInfoParam.getDdqqlsh());
                orderInvoiceInfoEntity.setDdh(invoiceIssueInfoParam.getDdh());
                orderInvoiceInfoEntity.setYsdh(invoiceIssueInfoParam.getYsdh());
                //保存电商平台信息
                orderInvoiceInfoEntity.setDsptlx(invoiceIssueInfoParam.getDsptlx());
                orderInvoiceInfoEntity.setDsptysddbh(invoiceIssueInfoParam.getDsptysddbh());
                //保存购买方证件及国籍信息
                orderInvoiceInfoEntity.setGmfzjlx(invoiceIssueInfoParam.getGmfzjlx());
                orderInvoiceInfoEntity.setGmfzjhm(invoiceIssueInfoParam.getGmfzjhm());
                orderInvoiceInfoEntity.setGmfgj(invoiceIssueInfoParam.getGmfgj());
                //结算方式代码
                orderInvoiceInfoEntity.setJsfsdm(invoiceIssueInfoParam.getJsfsdm());
                //开票人证件类型及号码
                orderInvoiceInfoEntity.setKprzjhm(invoiceIssueInfoParam.getKprzjhm());
                orderInvoiceInfoEntity.setKprzjlx(invoiceIssueInfoParam.getKprzjlx());
                //销售方/受票方自然人标志
                orderInvoiceInfoEntity.setSpfzrrbs(invoiceIssueInfoParam.getSpfzrrbs());
                orderInvoiceInfoEntity.setXsfzrrbs(invoiceIssueInfoParam.getXsfzrrbs());
                //征收方式
                orderInvoiceInfoEntity.setZsfs(invoiceIssueInfoParam.getZsfs());
                //系统来源
                orderInvoiceInfoEntity.setXtly(invoiceIssueInfoParam.getXtly());
                if (Objects.nonNull(orderInvoiceConfigEntity)) {
                    if ("0".equals(orderInvoiceConfigEntity.getDjcjsz())) {
                        if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getDdh())) {
                            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.queryStatusByDdh(orderInvoiceInfoEntity.getDdh(), orderInvoiceInfoEntity.getXhfNsrsbh());
                            if (Objects.nonNull(invoiceInfoEntity)) {
                                return InvoiceIssueRes.res("9999", "订单号重复",invoiceIssueInfoParam.getDdh());
                            }
                        } else {
                            String ddh = this.getDdh();
                            orderInvoiceInfoEntity.setDdh(ddh);
                        }
                    } else if ("1".equals(orderInvoiceConfigEntity.getDjcjsz())) {
                        List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdh(orderInvoiceInfoEntity.getYsdh(), orderInvoiceInfoEntity.getXhfNsrsbh());
                        if (orderInvoiceInfoEntities.size() > 0) {
                            return InvoiceIssueRes.res("9999", "应收单号重复",invoiceIssueInfoParam.getDdh());
                        }

                    }
                }
                //发票类型代码直接取
                orderInvoiceInfoEntity.setFpzlDm(invoiceIssueInfoParam.getFplxdm());
                // 销方信息
                InvoiceConfigInfo invoiceConfigInfo = new InvoiceConfigInfo();
                invoiceConfigInfo.setNsrsbh(invoiceIssueInfoParam.getNsrsbh());
                InvoiceConfigInfo configInfo = invoiceConfigDao.checkHadConfigByNsrsbh(invoiceConfigInfo);
                if (!Objects.isNull(configInfo)) {
                    orderInvoiceInfoEntity.setXhfMc(configInfo.getQymc());
                    orderInvoiceInfoEntity.setXhfNsrsbh(configInfo.getNsrsbh());
                    orderInvoiceInfoEntity.setXhfZh(configInfo.getYhzh());
                    orderInvoiceInfoEntity.setXhfYh(configInfo.getKhyh());
                    orderInvoiceInfoEntity.setXhfDh(configInfo.getDh());
                    orderInvoiceInfoEntity.setXhfDz(configInfo.getDz());
                }
                orderInvoiceInfoEntity.setGhfNsrsbh(invoiceIssueInfoParam.getGmfsbh());
                orderInvoiceInfoEntity.setGhfMc(invoiceIssueInfoParam.getGmfmc());
                orderInvoiceInfoEntity.setGhfDz(invoiceIssueInfoParam.getGmfdz());
                orderInvoiceInfoEntity.setGhfDh(invoiceIssueInfoParam.getGmfdh());
                orderInvoiceInfoEntity.setGhfYh(invoiceIssueInfoParam.getGmfyh());
                orderInvoiceInfoEntity.setGhfZh(invoiceIssueInfoParam.getGmfzh());
                orderInvoiceInfoEntity.setGhfSj(invoiceIssueInfoParam.getGmjfsjh());
                orderInvoiceInfoEntity.setGhfYx(invoiceIssueInfoParam.getGmjfyx());
                orderInvoiceInfoEntity.setJbrzjhm(invoiceIssueInfoParam.getJbrzjhm());
                orderInvoiceInfoEntity.setJbrzjlx(invoiceIssueInfoParam.getJbrzjlx());
                orderInvoiceInfoEntity.setJbrlxdh(invoiceIssueInfoParam.getJbrlxdh());
                orderInvoiceInfoEntity.setGmfjbrxm(invoiceIssueInfoParam.getJbrxm());
                orderInvoiceInfoEntity.setKpr(invoiceIssueInfoParam.getKpr());
                orderInvoiceInfoEntity.setHsbz(invoiceIssueInfoParam.getHsbz());
                orderInvoiceInfoEntity.setJshj(invoiceIssueInfoParam.getJshj());
                orderInvoiceInfoEntity.setHjbhsje(invoiceIssueInfoParam.getHjje());
                orderInvoiceInfoEntity.setKpse(invoiceIssueInfoParam.getHjse());
                orderInvoiceInfoEntity.setBz(invoiceIssueInfoParam.getBz());
                orderInvoiceInfoEntity.setDdh(invoiceIssueInfoParam.getDdh());
                orderInvoiceInfoEntity.setFjh(invoiceIssueInfoParam.getFjh());
                orderInvoiceInfoEntity.setIsDelete("0");
                orderInvoiceInfoEntity.setCreateTime(new Date());
                //订单时间
                orderInvoiceInfoEntity.setDdscrq(DateUtil.parse(invoiceIssueInfoParam.getDdsj()));
                //是否拆分
                orderInvoiceInfoEntity.setSfcf(invoiceIssueInfoParam.getSfcf());
                // 手动开票
                orderInvoiceInfoEntity.setKpfs("1");
                orderInvoiceInfoEntity.setPushStatus("0");
                orderInvoiceInfoEntity.setEmailPushStatus("0");
                orderInvoiceInfoEntity.setShortMsgPushStatus("0");
                // 订单状态 0: 正常
                orderInvoiceInfoEntity.setDdzt("0");
                orderInvoiceInfoEntity.setKplx("0");
                // 订单来源 2 外部接口
                orderInvoiceInfoEntity.setDdly("2");
                orderInvoiceInfoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_0.getKey());
                orderInvoiceInfoEntity.setChBz("0");
                // 特定业务入库
                orderInvoiceInfoEntity.setTdyw(invoiceIssueInfoParam.getTdywys());
                InvoiceIssueTdywParam tdywParam = invoiceIssueInfoParam.getInvoiceIssueTdywParam();
                if(tdywParam != null){
                    InvoiceTdywEntity tdywEntity = new InvoiceTdywEntity();
                    invoiceTdywService.copyTdywRelation(tdywParam,tdywEntity);
                    invoiceTdywService.saveTdywRelation(tdywEntity,invoiceId);
                }
                //差额征税入库
                List<InvoiceIssueCezsParam> cezslist = invoiceIssueInfoParam.getCezslist();
                if(!CollectionUtils.isEmpty(cezslist)){
                    List<InvoiceCezsEntity> list = new ArrayList<>();
                    for(InvoiceIssueCezsParam cezsParam : cezslist){
                        InvoiceCezsEntity cezsEntity = new InvoiceCezsEntity();
                        BeanUtils.copyProperties(cezsParam,cezsEntity);
                        list.add(cezsEntity);
                    }
                    invoiceCezsService.saveBatch(list,invoiceId);
                }
                // 附加信息入库
                if (!CollectionUtils.isEmpty(invoiceIssueInfoParam.getDdfjxx())) {
                    for (InvoiceIssueAdditionParam invoiceIssueAdditionParam : invoiceIssueInfoParam.getDdfjxx()) {
                        InvoiceAdditionInfoEntity invoiceAdditionInfoEntity = new InvoiceAdditionInfoEntity();
                        invoiceAdditionInfoEntity.setId(DistributedKeyMaker.generateShotKey());
                        invoiceAdditionInfoEntity.setOrderInvoiceInfoId(invoiceId);
                        invoiceAdditionInfoEntity.setFjxxmc(invoiceIssueAdditionParam.getFjxxmc());
                        invoiceAdditionInfoEntity.setFjxxz(invoiceIssueAdditionParam.getFjxxz());
                        invoiceAdditionInfoEntity.setSjnr(invoiceIssueAdditionParam.getSjnr());
                        invoiceAdditionInfoDao.insert(invoiceAdditionInfoEntity);
                    }
                    log.info("蓝字发票开具接口 invoiceIssue 发票附加信息入库成功");
                }
                List<BigDecimal> bje = new ArrayList<>();
                List<BigDecimal> bse = new ArrayList<>();
                // 明细信息入库
                if (!CollectionUtils.isEmpty(invoiceIssueInfoParam.getDdmxxx())) {
                    List<InvoiceIssueItemParam> ddmxxx = invoiceIssueInfoParam.getDdmxxx();
                    List<OrderInvoiceItemEntity> invoiceItemEntities = new ArrayList<>();
                    for (int i = 0; i < ddmxxx.size(); i++) {
                        InvoiceIssueItemParam invoiceIssueItemParam = ddmxxx.get(i);
                        OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                        orderInvoiceItemEntity.setXh(i + 1 + "");
                        orderInvoiceItemEntity.setOrderInvoiceId(invoiceId);
                        orderInvoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                        orderInvoiceItemEntity.setSpid(invoiceIssueItemParam.getSpid());
                        orderInvoiceItemEntity.setSpbm(invoiceIssueItemParam.getSpbm());
                        // 项目名称 *简称*项目名称
                        String xmc = "";
                        TaxClassCodeEntity taxClassCodeSpjc = taxClassCodeDao.getTaxClassCodeSpjc(invoiceIssueItemParam.getSpbm());
                        if (Objects.nonNull(taxClassCodeSpjc)) {
                            xmc = "*" + taxClassCodeSpjc.getSpjc() + "*" + taxClassCodeSpjc.getSpmc();
                            orderInvoiceItemEntity.setXmmc(xmc);
                            // 智能匹配税收分类编码
                            if ("0".equals(orderInvoiceConfigEntity.getZnppssflbm())) {
                                orderInvoiceItemEntity.setSpbm(taxClassCodeSpjc.getSpbm());
                            }
                        } else {
                            orderInvoiceItemEntity.setXmmc(invoiceIssueItemParam.getXmmc());
                        }
                        orderInvoiceItemEntity.setGgxh(invoiceIssueItemParam.getGgxh());
                        orderInvoiceItemEntity.setDw(invoiceIssueItemParam.getDw());
                        orderInvoiceItemEntity.setXmsl(invoiceIssueItemParam.getSpsl());
                        //保存发票行性质等内容
                        orderInvoiceItemEntity.setFphxz(StringUtils.isEmpty(invoiceIssueItemParam.getFphxz()) ? "0" : invoiceIssueItemParam.getFphxz());
                        orderInvoiceItemEntity.setYhzcbs(invoiceIssueItemParam.getYhzcbs());
                        orderInvoiceItemEntity.setLslbs(invoiceIssueItemParam.getLslbs());
                        orderInvoiceItemEntity.setZzstsgl(invoiceIssueItemParam.getZzstsgl());
                        orderInvoiceItemEntity.setHsbz(invoiceIssueInfoParam.getHsbz());
                        orderInvoiceItemEntity.setDj(invoiceIssueItemParam.getDj());
                        orderInvoiceItemEntity.setJe(invoiceIssueItemParam.getJe());
                        orderInvoiceItemEntity.setSl(invoiceIssueItemParam.getSl());

                        if (StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                            orderInvoiceItemEntity.setSe(invoiceIssueItemParam.getSe());
                        } else {
                            // 不含税
                            if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                String s = slTranst(orderInvoiceItemEntity.getSl());
                                BigDecimal sl = new BigDecimal(s);
                                BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                orderInvoiceItemEntity.setSe(se.toString());
                            } else {
                                // 含税金额
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                // 税率
                                String s = slTranst(orderInvoiceItemEntity.getSl());
                                BigDecimal bigDecimalSL = new BigDecimal(s);
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                // BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                                BigDecimal sev = je.subtract(divide);
                                orderInvoiceItemEntity.setSe(sev.toPlainString());
                            }
                        }
                        orderInvoiceItemEntity.setCreateTime(new Date());
                        // 折扣方式
                        orderInvoiceItemEntity.setByzd2(invoiceIssueItemParam.getZkfs());
                        // 折扣大小
                        orderInvoiceItemEntity.setByzd3(invoiceIssueItemParam.getZkdx());

                        // 按金额折扣
                        if ("01".equals(orderInvoiceItemEntity.getByzd2())) {
                            // 被折扣行
                            orderInvoiceItemEntity.setFphxz("2");
                            invoiceItemEntities.add(orderInvoiceItemEntity);
                            // 折扣行
                            OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                            invoiceItemEntity.setFphxz("1");
                            invoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                            invoiceItemEntity.setOrderInvoiceId(invoiceId);
                            invoiceItemEntity.setCreateTime(new Date());
                            // 折扣行 项目名称
                            if (!StringUtils.isEmpty(xmc)) {
                                invoiceItemEntity.setXmmc(xmc);
                            } else {
                                invoiceItemEntity.setXmmc(orderInvoiceItemEntity.getXmmc());
                            }
                            invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                            invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                            invoiceItemEntity.setSpid(orderInvoiceItemEntity.getSpid());
                            invoiceItemEntity.setJe("-" + orderInvoiceItemEntity.getByzd3());
                            // 计算不含税 税额
                            if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                                BigDecimal bigDecimalSl = new BigDecimal(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalJe = new BigDecimal(invoiceItemEntity.getJe());
                                BigDecimal se = bigDecimalJe.multiply(bigDecimalSl).setScale(2, BigDecimal.ROUND_HALF_UP);
                                invoiceItemEntity.setSe(se.toString());
                            } else {
                                //折扣含税
                                BigDecimal je = new BigDecimal(invoiceItemEntity.getJe());
                                // 税率
                                String s = slTranst(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalSL = new BigDecimal(s);
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                // BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                                BigDecimal sev = je.subtract(divide);
                                invoiceItemEntity.setSe(sev.toString());
                            }
                            invoiceItemEntities.add(invoiceItemEntity);

                            // 比例折扣
                        } else if ("02".equals(orderInvoiceItemEntity.getByzd2())) {
                            // 被折扣行
                            orderInvoiceItemEntity.setFphxz("2");
                            invoiceItemEntities.add(orderInvoiceItemEntity);
                            // 根据比例 计算金额
                            BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                            String bl = orderInvoiceItemEntity.getByzd3();
                            Double d = Double.valueOf(bl) / 100;
                            String xs = String.valueOf(d);

                            BigDecimal bigDecimal = new BigDecimal(xs);
                            // 折扣额
                            BigDecimal zke = je.multiply(bigDecimal).setScale(2, BigDecimal.ROUND_HALF_UP);
                            // 折扣行
                            OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                            invoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                            invoiceItemEntity.setOrderInvoiceId(invoiceId);
                            invoiceItemEntity.setFphxz("1");
                            invoiceItemEntity.setCreateTime(new Date());
                            // 折扣行 项目名称
                            if (!StringUtils.isEmpty(xmc)) {
                                invoiceItemEntity.setXmmc(xmc);
                            } else {
                                invoiceItemEntity.setXmmc(orderInvoiceItemEntity.getXmmc());
                            }
                            invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                            invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                            invoiceItemEntity.setSpid(orderInvoiceItemEntity.getSpid());
                            invoiceItemEntity.setJe("-" + zke.toString());
                            // 计算不含税 税额
                            if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                                BigDecimal bigDecimalSl = new BigDecimal(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalJe = new BigDecimal(invoiceItemEntity.getJe());
                                BigDecimal se = bigDecimalJe.multiply(bigDecimalSl).setScale(2, BigDecimal.ROUND_HALF_UP);
                                invoiceItemEntity.setSe(se.toString());
                            } else {
                                BigDecimal jej = new BigDecimal(invoiceItemEntity.getJe());
                                // 税率
                                String s = slTranst(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalSL = new BigDecimal(s);
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = jej.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                BigDecimal subtract = jej.subtract(divide);
                                invoiceItemEntity.setSe(subtract.toPlainString());
                            }
                            invoiceItemEntities.add(invoiceItemEntity);
                        } else {
                            // 无折扣行
                            invoiceItemEntities.add(orderInvoiceItemEntity);
                        }
                    }
                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : invoiceItemEntities) {
                        bje.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                        bse.add(new BigDecimal(orderInvoiceItemEntity.getSe()));

                        BigDecimal bigDecimalSl = new BigDecimal("0");
                        BigDecimal slv = new BigDecimal(orderInvoiceItemEntity.getSl());
                        if (bigDecimalSl.compareTo(slv) == 0) {
                            // 免税
                            orderInvoiceItemEntity.setSl("免税");

                        }
                        orderInvoiceItemDao.insert(orderInvoiceItemEntity);
                    }
                }
                if (orderInvoiceInfoEntity.getHsbz().equals("0")) {
                    // 不含税 计算合计金额
                    BigDecimal hjje = bje.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setHjbhsje(hjje.toString());
                    // 不含税 计算合计税额
                    BigDecimal hjse = bse.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setKpse(hjse.toString());
                    // 价税合计
                    BigDecimal jshj = hjje.add(hjse);
                    orderInvoiceInfoEntity.setJshj(jshj.toString());
                } else {
                    // 含税
                    BigDecimal hjje = bje.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 含税 税额
                    BigDecimal hjse = bse.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setKpse(hjse.toString());

                    // 计算不含税金额
                    BigDecimal subtract = hjje.subtract(hjse);
                    orderInvoiceInfoEntity.setHjbhsje(subtract.toString());
                    // 价税合计
                    orderInvoiceInfoEntity.setJshj(subtract.add(hjse).toString());
                }
                orderInvoiceInfoDao.insert(orderInvoiceInfoEntity);
                invoiceIssueSuccessRes.setDdh(orderInvoiceInfoEntity.getDdh());
                invoiceIssueSuccessRes.setSldh(orderInvoiceInfoEntity.getId());
                log.info("蓝字发票开具接口 invoiceIssue 发票主信息入库成功");
                res = InvoiceIssueRes.ok(invoiceIssueSuccessRes);
            } else {
                // 自动开票 直接调用接口
                OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
                orderInvoiceInfoEntity.setFpqqlsh(invoiceIssueInfoParam.getDdqqlsh());
                orderInvoiceInfoEntity.setFpqqlsh(invoiceIssueInfoParam.getYsdh());
                //保存电商平台信息
                orderInvoiceInfoEntity.setDsptlx(invoiceIssueInfoParam.getDsptlx());
                orderInvoiceInfoEntity.setDsptysddbh(invoiceIssueInfoParam.getDsptysddbh());
                //保存购买方证件及国籍信息
                orderInvoiceInfoEntity.setGmfzjlx(invoiceIssueInfoParam.getGmfzjlx());
                orderInvoiceInfoEntity.setGmfzjhm(invoiceIssueInfoParam.getGmfzjhm());
                orderInvoiceInfoEntity.setGmfgj(invoiceIssueInfoParam.getGmfgj());
                //结算方式代码
                orderInvoiceInfoEntity.setJsfsdm(invoiceIssueInfoParam.getJsfsdm());
                //开票人证件类型及号码
                orderInvoiceInfoEntity.setKprzjhm(invoiceIssueInfoParam.getKprzjhm());
                orderInvoiceInfoEntity.setKprzjlx(invoiceIssueInfoParam.getKprzjlx());
                //销售方/受票方自然人标志
                orderInvoiceInfoEntity.setSpfzrrbs(invoiceIssueInfoParam.getSpfzrrbs());
                orderInvoiceInfoEntity.setXsfzrrbs(invoiceIssueInfoParam.getXsfzrrbs());
                //征收方式
                orderInvoiceInfoEntity.setZsfs(invoiceIssueInfoParam.getZsfs());
                //发票类型代码直接取
                orderInvoiceInfoEntity.setFpzlDm(invoiceIssueInfoParam.getFplxdm());
                //系统来源
                orderInvoiceInfoEntity.setXtly(invoiceIssueInfoParam.getXtly());
                // 销方信息
                InvoiceConfigInfo invoiceConfigInfo = new InvoiceConfigInfo();
                invoiceConfigInfo.setNsrsbh(invoiceIssueInfoParam.getNsrsbh());
                InvoiceConfigInfo configInfo = invoiceConfigDao.checkHadConfigByNsrsbh(invoiceConfigInfo);
                if (!Objects.isNull(configInfo)) {
                    orderInvoiceInfoEntity.setXhfMc(configInfo.getQymc());
                    orderInvoiceInfoEntity.setXhfNsrsbh(configInfo.getNsrsbh());
                    orderInvoiceInfoEntity.setXhfZh(configInfo.getYhzh());
                    orderInvoiceInfoEntity.setXhfYh(configInfo.getKhyh());
                    orderInvoiceInfoEntity.setXhfDh(configInfo.getDh());
                    orderInvoiceInfoEntity.setXhfDz(configInfo.getDz());
                }
                orderInvoiceInfoEntity.setGhfNsrsbh(invoiceIssueInfoParam.getGmfsbh());
                orderInvoiceInfoEntity.setGhfMc(invoiceIssueInfoParam.getGmfmc());
                orderInvoiceInfoEntity.setGhfDz(invoiceIssueInfoParam.getGmfdz());
                orderInvoiceInfoEntity.setGhfDh(invoiceIssueInfoParam.getGmfdh());
                orderInvoiceInfoEntity.setGhfYh(invoiceIssueInfoParam.getGmfyh());
                orderInvoiceInfoEntity.setGhfZh(invoiceIssueInfoParam.getGmfzh());
                orderInvoiceInfoEntity.setGhfSj(invoiceIssueInfoParam.getGmjfsjh());
                orderInvoiceInfoEntity.setGhfYx(invoiceIssueInfoParam.getGmjfyx());
                orderInvoiceInfoEntity.setJbrzjhm(invoiceIssueInfoParam.getJbrzjhm());
                orderInvoiceInfoEntity.setJbrzjlx(invoiceIssueInfoParam.getJbrzjlx());
                orderInvoiceInfoEntity.setJbrlxdh(invoiceIssueInfoParam.getJbrlxdh());
                orderInvoiceInfoEntity.setGmfjbrxm(invoiceIssueInfoParam.getJbrxm());
                orderInvoiceInfoEntity.setKpr(invoiceIssueInfoParam.getKpr());
                orderInvoiceInfoEntity.setHsbz(invoiceIssueInfoParam.getHsbz());
                orderInvoiceInfoEntity.setJshj(invoiceIssueInfoParam.getJshj());
                orderInvoiceInfoEntity.setHjbhsje(invoiceIssueInfoParam.getHjje());
                orderInvoiceInfoEntity.setKpse(invoiceIssueInfoParam.getHjse());
                orderInvoiceInfoEntity.setBz(invoiceIssueInfoParam.getBz());
                orderInvoiceInfoEntity.setDdh(invoiceIssueInfoParam.getDdh());
                orderInvoiceInfoEntity.setIsDelete("0");
                orderInvoiceInfoEntity.setCreateTime(new Date());
                orderInvoiceInfoEntity.setDdscrq(new Date());
                // 手动开票
                orderInvoiceInfoEntity.setKpfs("0");
                orderInvoiceInfoEntity.setPushStatus("0");
                orderInvoiceInfoEntity.setEmailPushStatus("0");
                orderInvoiceInfoEntity.setShortMsgPushStatus("0");
                // 订单状态 0: 正常
                orderInvoiceInfoEntity.setDdzt("0");
                orderInvoiceInfoEntity.setKplx("0");
                // 订单来源 2 外部接口
                orderInvoiceInfoEntity.setDdly("2");
                orderInvoiceInfoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_0.getKey());
                orderInvoiceInfoEntity.setChBz("0");
                //特定业务
                orderInvoiceInfoEntity.setTdyw(invoiceIssueInfoParam.getTdywys());
                InvoiceIssueTdywParam tdywParam = invoiceIssueInfoParam.getInvoiceIssueTdywParam();
                if(tdywParam != null){
                    InvoiceTdywEntity tdywEntity = new InvoiceTdywEntity();
                    invoiceTdywService.copyTdywRelation(tdywParam,tdywEntity);
                    //设置特定业务
                    orderInvoiceInfoEntity.setInvoiceTdywEntity(tdywEntity);
                }
                //差额征税
                List<InvoiceIssueCezsParam> cezslist = invoiceIssueInfoParam.getCezslist();
                if(!CollectionUtils.isEmpty(cezslist)){
                    List<InvoiceCezsEntity> list = new ArrayList<>();
                    for(InvoiceIssueCezsParam cezsParam : cezslist){
                        InvoiceCezsEntity cezsEntity = new InvoiceCezsEntity();
                        BeanUtils.copyProperties(cezsParam,cezsEntity);
                        list.add(cezsEntity);
                    }
                    orderInvoiceInfoEntity.setCezslist(list);
                }
                //是否显示销方/购方，地址电话/银行账号
                orderInvoiceInfoEntity.setSfzsgfdzdh(invoiceIssueInfoParam.getSfzsgfdzdh());
                orderInvoiceInfoEntity.setSfzsxfdzdh(invoiceIssueInfoParam.getSfzsxfdzdh());
                orderInvoiceInfoEntity.setSfzsgfyhzh(invoiceIssueInfoParam.getSfzsgfyhzh());
                orderInvoiceInfoEntity.setSfzsxfyhzh(invoiceIssueInfoParam.getSfzsxfyhzh());
                //附加信息
                List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = new ArrayList<>();
                if (!CollectionUtils.isEmpty(invoiceIssueInfoParam.getDdfjxx())) {
                    for (InvoiceIssueAdditionParam invoiceIssueAdditionParam : invoiceIssueInfoParam.getDdfjxx()) {
                        InvoiceAdditionInfoEntity invoiceAdditionInfoEntity = new InvoiceAdditionInfoEntity();
                        invoiceAdditionInfoEntity.setFjxxmc(invoiceIssueAdditionParam.getFjxxmc());
                        invoiceAdditionInfoEntity.setFjxxz(invoiceIssueAdditionParam.getFjxxz());
                        invoiceAdditionInfoEntity.setSjnr(invoiceIssueAdditionParam.getSjnr());
                        invoiceAdditionInfoEntities.add(invoiceAdditionInfoEntity);
                    }
                }
                //设置附加信息
                orderInvoiceInfoEntity.setInfoEntityList(invoiceAdditionInfoEntities);

                // 明细信息
                List<BigDecimal> bje = new ArrayList<>();
                List<BigDecimal> bse = new ArrayList<>();
                List<OrderInvoiceItemEntity> invoiceItemEntities = new ArrayList<>();
                List<InvoiceIssueItemParam> ddmxxx = invoiceIssueInfoParam.getDdmxxx();
                if (!CollectionUtils.isEmpty(ddmxxx)) {
                    for (int i = 0; i < ddmxxx.size(); i++) {
                        InvoiceIssueItemParam invoiceIssueItemParam = ddmxxx.get(i);
                        OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                        orderInvoiceItemEntity.setXh(i + 1 + "");
                        orderInvoiceItemEntity.setSpid(invoiceIssueItemParam.getSpid());
                        orderInvoiceItemEntity.setSpbm(invoiceIssueItemParam.getSpbm());
                        // 项目名称 *简称*项目名称
                        String xmc = "";
                        TaxClassCodeEntity taxClassCodeSpjc = taxClassCodeDao.getTaxClassCodeSpjc(invoiceIssueItemParam.getSpbm());
                        if (Objects.nonNull(taxClassCodeSpjc)) {
                            xmc = "*" + taxClassCodeSpjc.getSpjc() + "*" + taxClassCodeSpjc.getSpmc();
                            orderInvoiceItemEntity.setXmmc(xmc);
                            // 智能匹配税收分类编码
                            if ("0".equals(orderInvoiceConfigEntity.getZnppssflbm())) {
                                orderInvoiceItemEntity.setSpbm(taxClassCodeSpjc.getSpbm());
                            }
                        } else {
                            orderInvoiceItemEntity.setXmmc(invoiceIssueItemParam.getXmmc());
                        }
                        orderInvoiceItemEntity.setGgxh(invoiceIssueItemParam.getGgxh());
                        orderInvoiceItemEntity.setDw(invoiceIssueItemParam.getDw());
                        orderInvoiceItemEntity.setXmsl(invoiceIssueItemParam.getSpsl());
                        //保存发票行性质等内容
                        orderInvoiceItemEntity.setFphxz(StringUtils.isEmpty(invoiceIssueItemParam.getFphxz()) ? "0" : invoiceIssueItemParam.getFphxz());
                        orderInvoiceItemEntity.setYhzcbs(invoiceIssueItemParam.getYhzcbs());
                        orderInvoiceItemEntity.setLslbs(invoiceIssueItemParam.getLslbs());
                        orderInvoiceItemEntity.setZzstsgl(invoiceIssueItemParam.getZzstsgl());
                        orderInvoiceItemEntity.setHsbz(invoiceIssueInfoParam.getHsbz());
                        orderInvoiceItemEntity.setDj(invoiceIssueItemParam.getDj());
                        orderInvoiceItemEntity.setJe(invoiceIssueItemParam.getJe());
                        orderInvoiceItemEntity.setSl(invoiceIssueItemParam.getSl());

                        if (StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                            orderInvoiceItemEntity.setSe(invoiceIssueItemParam.getSe());
                        } else {
                            // 不含税
                            if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                String s = slTranst(orderInvoiceItemEntity.getSl());
                                BigDecimal sl = new BigDecimal(s);
                                BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                orderInvoiceItemEntity.setSe(se.toString());
                            } else {
                                // 含税金额
                                BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                // 税率
                                String s = slTranst(orderInvoiceItemEntity.getSl());
                                BigDecimal bigDecimalSL = new BigDecimal(s);
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                BigDecimal se = je.subtract(divide);
                                orderInvoiceItemEntity.setSe(se.toString());
                                // 计算不含税金额
                                orderInvoiceItemEntity.setJe(divide.toString());
                                orderInvoiceItemEntity.setHsje(orderInvoiceItemEntity.getJe());
                                // 不含税单价
                                if (!StringUtils.isEmpty(orderInvoiceItemEntity.getDj())) {
                                    String bhsdj = new BigDecimal(orderInvoiceItemEntity.getDj()).divide(add, 2, BigDecimal.ROUND_HALF_UP).toString();
                                    orderInvoiceItemEntity.setDj(bhsdj);
                                } else {
                                    orderInvoiceItemEntity.setDj("");
                                }
                            }
                        }
                        orderInvoiceItemEntity.setCreateTime(new Date());
                        // 折扣方式
                        orderInvoiceItemEntity.setByzd2(invoiceIssueItemParam.getZkfs());
                        // 折扣大小
                        orderInvoiceItemEntity.setByzd3(invoiceIssueItemParam.getZkdx());

                        // 按金额折扣
                        if ("01".equals(orderInvoiceItemEntity.getByzd2())) {
                            // 被折扣行
                            orderInvoiceItemEntity.setFphxz("2");
                            orderInvoiceItemEntity.setHsbz("0");
                            invoiceItemEntities.add(orderInvoiceItemEntity);
                            // 折扣行
                            OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                            invoiceItemEntity.setFphxz("1");
                            invoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                            // 折扣行 项目名称
                            if (!StringUtils.isEmpty(xmc)) {
                                invoiceItemEntity.setXmmc(xmc);
                            } else {
                                invoiceItemEntity.setXmmc(orderInvoiceItemEntity.getXmmc());
                            }
                            invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                            invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                            invoiceItemEntity.setSpid(orderInvoiceItemEntity.getSpid());
                            invoiceItemEntity.setJe("-" + orderInvoiceItemEntity.getByzd3());
                            // 不含税折扣额
                            if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                                BigDecimal bigDecimalSl = new BigDecimal(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalJe = new BigDecimal(invoiceItemEntity.getJe());
                                BigDecimal se = bigDecimalJe.multiply(bigDecimalSl).setScale(2, BigDecimal.ROUND_HALF_UP);
                                invoiceItemEntity.setSe(se.toString());
                            } else {
                                //折扣含税
                                BigDecimal je = new BigDecimal(invoiceItemEntity.getJe());
                                // 税率
                                String s = slTranst(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalSL = new BigDecimal(s);
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                BigDecimal se = je.subtract(divide);
                                invoiceItemEntity.setSe(se.toString());
                                invoiceItemEntity.setJe(divide.toPlainString());
                            }
                            invoiceItemEntities.add(invoiceItemEntity);

                            // 比例折扣
                        } else if ("02".equals(orderInvoiceItemEntity.getByzd2())) {
                            // 被折扣行
                            orderInvoiceItemEntity.setFphxz("2");
                            orderInvoiceItemEntity.setHsbz("0");
                            invoiceItemEntities.add(orderInvoiceItemEntity);
                            // 根据比例 计算金额
                            BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                            String bl = orderInvoiceItemEntity.getByzd3();
                            Double d = Double.valueOf(bl) / 100;
                            String xs = String.valueOf(d);

                            BigDecimal bigDecimal = new BigDecimal(xs);
                            // 折扣额
                            BigDecimal zke = je.multiply(bigDecimal).setScale(2, BigDecimal.ROUND_HALF_UP);
                            // 折扣行
                            OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                            invoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                            invoiceItemEntity.setFphxz("1");
                            // 折扣行 项目名称
                            if (!StringUtils.isEmpty(xmc)) {
                                invoiceItemEntity.setXmmc(xmc);
                            } else {
                                invoiceItemEntity.setXmmc(orderInvoiceItemEntity.getXmmc());
                            }
                            invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                            invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                            invoiceItemEntity.setSpid(orderInvoiceItemEntity.getSpid());
                            // 不含税折扣金额
                            invoiceItemEntity.setJe("-" + zke.toString());
                            // 计算不含税 税额
                            if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                                BigDecimal bigDecimalSl = new BigDecimal(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalJe = new BigDecimal(invoiceItemEntity.getJe());
                                BigDecimal se = bigDecimalJe.multiply(bigDecimalSl).setScale(2, BigDecimal.ROUND_HALF_UP);
                                invoiceItemEntity.setSe(se.toString());
                            } else {
                                BigDecimal jej = new BigDecimal(invoiceItemEntity.getJe());
                                // 税率
                                String s = slTranst(invoiceItemEntity.getSl());
                                BigDecimal bigDecimalSL = new BigDecimal(s);
                                BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                BigDecimal divide = jej.divide(add, 2, RoundingMode.HALF_UP);
                                // 税额
                                BigDecimal se = jej.subtract(divide);
                                invoiceItemEntity.setSe(se.toString());
                                invoiceItemEntity.setJe(divide.toPlainString());
                            }
                            invoiceItemEntities.add(invoiceItemEntity);
                        } else {
                            // 无折扣行
                            orderInvoiceItemEntity.setHsbz("0");
                            invoiceItemEntities.add(orderInvoiceItemEntity);
                        }
                    }
                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : invoiceItemEntities) {
                        //上面代码已做价税合计  bje = 不含税金额
                        bje.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                        bse.add(new BigDecimal(orderInvoiceItemEntity.getSe()));

                        BigDecimal bigDecimalSl = new BigDecimal("0");
                        BigDecimal slv = new BigDecimal(orderInvoiceItemEntity.getSl());
                        if (bigDecimalSl.compareTo(slv) == 0) {
                            // 免税
                            orderInvoiceItemEntity.setSl("免税");
                        }
                    }
                }
                //设置明细
                orderInvoiceInfoEntity.setItemEntityList(invoiceItemEntities);
                if (orderInvoiceInfoEntity.getHsbz().equals("0")) {
                    // 不含税 计算合计金额
                    BigDecimal hjje = bje.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setHjbhsje(hjje.toString());
                    // 不含税 计算合计税额
                    BigDecimal hjse = bse.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setKpse(hjse.toString());
                    // 价税合计
                    BigDecimal jshj = hjje.add(hjse);
                    orderInvoiceInfoEntity.setJshj(jshj.toString());
                } else {
                    // 含税
                    BigDecimal hjje = bje.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 含税 税额
                    BigDecimal hjse = bse.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderInvoiceInfoEntity.setKpse(hjse.toString());
                    // 计算不含税金额
                    orderInvoiceInfoEntity.setHjbhsje(hjje.toPlainString());
                    // 价税合计
                    orderInvoiceInfoEntity.setJshj(hjje.add(hjse).toString());
                }
                // 开始调用开票 接口
                orderInvoiceInfoEntity.setHsbz("0");
                orderInvoiceInfoEntity.setNsrsbh(invoiceIssueInfoParam.getNsrsbh());
                orderInvoiceInfoEntity.setBaseNsrsbh(invoiceIssueInfoParam.getNsrsbh());
                R r = orderInvoiceInfoService.invoiceInputIssue(orderInvoiceInfoEntity);
                String code = (String) r.get("code");
                String msg = (String) r.get("msg");
                String data = (String) r.get("data");
                String sldh = (String) r.get("sldh");
                if (!"0000".equals(code)) {
                    res = InvoiceIssueRes.res(code,StringUtils.isEmpty(data) ? msg : data,orderInvoiceInfoEntity.getDdh());
                } else {
                    invoiceIssueSuccessRes.setDdh(orderInvoiceInfoEntity.getDdh());
                    invoiceIssueSuccessRes.setSldh(sldh);
                    res = InvoiceIssueRes.ok(invoiceIssueSuccessRes);
                }
            }
        } catch (Exception e) {
            log.error("对外蓝字发票开具接口 invoiceIssue 异常:{}", e);
            res = InvoiceIssueRes.res(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage(),invoiceIssueInfoParam.getDdh());
        }
        return res;
    }

    /**
     * 3.2 蓝字发票结果查询接口
     *
     * @return
     */
    @Override
    public InvoiceIssueRes queryInvoiceInfo(ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO) {
        log.info("{}，蓝字发票结果查询接口，入参：{}", LOGGER, JsonUtils.getInstance().toJsonString(apiHzkpqrxxsljgcxReqBO));
        if (StringUtils.isEmpty(apiHzkpqrxxsljgcxReqBO.getSldh())) {
            return InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_SLDH_ERROR_9496.getKey(), OrderInfoContentEnum.BULE_INVOICE_SLDH_ERROR_9496.getMessage());
        }
        InvoiceIssueRes invoiceIssueRes = null;
        try {
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectById(apiHzkpqrxxsljgcxReqBO.getSldh());
            if (Objects.nonNull(orderInvoiceInfoEntity)) {
                // 待开票
                if ("0".equals(orderInvoiceInfoEntity.getKpzt())) {
                    invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_WKJ_9497.getKey(), OrderInfoContentEnum.BULE_INVOICE_WKJ_9497.getMessage());
                } else if ("1".equals(orderInvoiceInfoEntity.getKpzt())) {
                    // 开具中
                    invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_KJZ_9498.getKey(), OrderInfoContentEnum.BULE_INVOICE_KJZ_9498.getMessage());
                } else if ("2".equals(orderInvoiceInfoEntity.getKpzt())) {
                    // 开具成功
                    QueryInvoiceInfoRes queryInvoiceInfoRes = new QueryInvoiceInfoRes();
                    String format = DateUtil.format(orderInvoiceInfoEntity.getKprq(), "yyyy-MM-dd HH:mm:ss");
                    queryInvoiceInfoRes.setKprq(format);
                    queryInvoiceInfoRes.setQdfphm(orderInvoiceInfoEntity.getQdfphm());
                    // PDF
                    queryInvoiceInfoRes.setPdfxzurl(orderInvoiceInfoEntity.getPdfUrl());
                    // OFD   xml
                    if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getPdfUrl()) && orderInvoiceInfoEntity.getPdfUrl().contains("PDF")) {
                        // 开始组装OFD文件
                        String ofdUrl = orderInvoiceInfoEntity.getPdfUrl().replace("PDF", "OFD");
                        queryInvoiceInfoRes.setOfdxzurl(ofdUrl);
                        // 开始组装XML文件
                        String xmlUrl = orderInvoiceInfoEntity.getPdfUrl().replace("PDF", "XML");
                        queryInvoiceInfoRes.setXmlxzurl(xmlUrl);
                    }

                    invoiceIssueRes = InvoiceIssueRes.res(OrderInfoContentEnum.API_RETURN_ERROR_0000.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_0000.getMessage(), queryInvoiceInfoRes);
                } else if ("3".equals(orderInvoiceInfoEntity.getKpzt())) {
                    // 开具失败
                    invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_KJSB_9500.getKey(), orderInvoiceInfoEntity.getByzd2());
                }
            } else {
                // 不存在
                invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_BCZ_9501.getKey(), OrderInfoContentEnum.BULE_INVOICE_BCZ_9501.getMessage());
            }
        } catch (Exception e) {
            log.error("蓝字发票结果查询接口 queryInvoiceInfo 异常:{}", e);
            invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
        }
        return invoiceIssueRes;
    }

    /**
     * 3.3 发票基础信息查询
     *
     * @return
     */
    @Override
    public InvoiceIssueRes getInvoiceBaseInfo(InvoiceBaseInfoParam invoiceBaseInfoParam) {
        log.info("{}，发票基础信息查询getInvoiceBaseInfo，入参：{}", LOGGER, JsonUtils.getInstance().toJsonString(invoiceBaseInfoParam));
        Map<String, String> invoiceIssueMap = this.InvoiceBaseInfoParam(invoiceBaseInfoParam);
        if (!CollectionUtils.isEmpty(invoiceIssueMap)) {
            String errorCode = invoiceIssueMap.get(OrderManagementConstant.ERRORCODE);
            String errorMsg = invoiceIssueMap.get(OrderManagementConstant.ERRORMESSAGE);
            return InvoiceIssueRes.error(errorCode, errorMsg);
        }
        InvoiceIssueRes invoiceIssueRes = null;
        try {
            InvoiceInfoReq invoiceInfoReq = new InvoiceInfoReq();
            invoiceInfoReq.setGjbq(invoiceBaseInfoParam.getGjbq());
            invoiceInfoReq.setNsrsbh(invoiceBaseInfoParam.getNsrsbh());
            invoiceInfoReq.setFpztDm(invoiceBaseInfoParam.getFpztdm());
            invoiceInfoReq.setFplyDm(invoiceBaseInfoParam.getFplydm());
            invoiceInfoReq.setFplxDm(invoiceBaseInfoParam.getFplxdm());
            invoiceInfoReq.setKprqq(invoiceBaseInfoParam.getKprqq());
            invoiceInfoReq.setKprqz(invoiceBaseInfoParam.getKprqz());
            invoiceInfoReq.setCurrent(invoiceBaseInfoParam.getCurrent());
            invoiceInfoReq.setSize(invoiceBaseInfoParam.getSize());

            String jsonString = JsonUtils.getInstance().toJsonString(invoiceInfoReq);
            log.info("发票基础信息查询invoiceInfoReq远程调用入参: {}", jsonString);
            long lo = System.currentTimeMillis();
            String res = HttpUtils.doPost(invoiceInfosAndItemsUrl, jsonString);
            log.info("发票基础信息查询invoiceInfoReq远程调用 耗时: {}", System.currentTimeMillis() - lo);
            log.info("发票基础信息查询invoiceInfoReq远程调用出参res: {}", res);
            R r = JsonUtils.getInstance().fromJson(res, R.class);
            log.info("发票基础信息查询远程调用出参-R: {}", r);
            if ("0000".equals(r.get("code").toString())) {
                String data = r.get("data").toString();
                log.info("发票基础信息查询远程调用出参data: {}", data);
                InvoiceInfoRes invoiceInfoRes = JsonUtils.getInstance().parseObject(data, InvoiceInfoRes.class);
                log.info("发票基础信息查询远程调用出参invoiceInfoRes: {}", invoiceInfoRes);
                InvoiceInfoQueryRes invoiceInfoQueryRes = new InvoiceInfoQueryRes();

                if (Objects.nonNull(invoiceInfoRes) && !CollectionUtils.isEmpty(invoiceInfoRes.getRecords())) {
                    List<InvoiceBaseInfoRes> records = invoiceInfoRes.getRecords();
                    List<InvoiceBaseInfoQueryRes> invoiceBaseInfoQueryResList = new ArrayList<>();
                    for (InvoiceBaseInfoRes invoiceBaseInfoRes : records) {
                        // 查询数据库 是否存在相同全电号码数据 不存在则入库
                        OrderInvoiceInfoEntity info = orderInvoiceInfoDao.seleInvoiceInfoByQdfphmIsDele(invoiceBaseInfoRes.getQdfphm());
                        if (Objects.isNull(info)) {
                            OrderInvoiceInfoEntity invoiceInfoEntity = new OrderInvoiceInfoEntity();
                            String id = DistributedKeyMaker.generateShotKey();
                            invoiceInfoEntity.setId(id);
                            invoiceInfoEntity.setPch(DistributedKeyMaker.generateShotKey());
                            invoiceInfoEntity.setFpqqlsh(DistributedKeyMaker.generateShotKey());
                            invoiceInfoEntity.setDdh(DistributedKeyMaker.generateShotKey());
                            invoiceInfoEntity.setGhfMc(invoiceBaseInfoRes.getGmfmc());
                            invoiceInfoEntity.setGhfNsrsbh(invoiceBaseInfoRes.getGmfnsrsbh());
                            invoiceInfoEntity.setGhfQylx("01");
                            invoiceInfoEntity.setKplx("0");
                            invoiceInfoEntity.setChBz("0");
                            // 1-销项发票； 2-进项发票
                            invoiceInfoEntity.setGjbq(invoiceBaseInfoParam.getGjbq());
                            invoiceInfoEntity.setXhfMc(invoiceBaseInfoRes.getXsfmc());
                            invoiceInfoEntity.setXhfNsrsbh(invoiceBaseInfoRes.getXsfnsrsbh());
                            invoiceInfoEntity.setSflzfp(invoiceBaseInfoRes.getSflzfp());
                            invoiceInfoEntity.setHjbhsje(StringUtils.isEmpty(invoiceBaseInfoRes.getHjje()) ? "0.00" : invoiceBaseInfoRes.getHjje());
                            invoiceInfoEntity.setKpse(StringUtils.isEmpty(invoiceBaseInfoRes.getHjse()) ? "0.00" : invoiceBaseInfoRes.getHjse());
                            BigDecimal hjje = new BigDecimal(invoiceInfoEntity.getHjbhsje());
                            BigDecimal hjse = new BigDecimal(invoiceInfoEntity.getKpse());
                            // 不含税考虑
                            invoiceInfoEntity.setJshj(hjje.add(hjse).toString());
                            invoiceInfoEntity.setKpzt("2");
                            Date date = transDate(invoiceBaseInfoRes.getKprq());
                            invoiceInfoEntity.setKprq(date);
                            invoiceInfoEntity.setDdly("4");
                            invoiceInfoEntity.setBz(invoiceBaseInfoRes.getBz());
                            invoiceInfoEntity.setDdzt("0");
                            invoiceInfoEntity.setHsbz("0");
                            invoiceInfoEntity.setQdfphm(invoiceBaseInfoRes.getQdfphm());
                            invoiceInfoEntity.setFpdm(invoiceBaseInfoRes.getFpdm());
                            invoiceInfoEntity.setFphm(invoiceBaseInfoRes.getFphm());
                            if ("81".equals(invoiceBaseInfoRes.getFplxDm())) {
                                invoiceInfoEntity.setFpzlDm("1");
                            } else if ("82".equals(invoiceBaseInfoRes.getFplxDm())) {
                                invoiceInfoEntity.setFpzlDm("0");
                            } else {
                                invoiceInfoEntity.setFpzlDm(invoiceBaseInfoRes.getFplxDm());
                            }
                            invoiceInfoEntity.setEwm(invoiceBaseInfoRes.getEwm());
                            invoiceInfoEntity.setKpr(invoiceBaseInfoRes.getKpr());
                            invoiceInfoEntity.setFply(invoiceBaseInfoRes.getFplyDm());
                            invoiceInfoEntity.setFpzt(invoiceBaseInfoRes.getFpztDm());
                            if ("01".equals(invoiceBaseInfoRes.getFpztDm())) {
                                invoiceInfoEntity.setChBz("0");
                            } else if ("02".equals(invoiceBaseInfoRes.getFpztDm())) {
                                invoiceInfoEntity.setZfBz("1");
                            } else if ("03".equals(invoiceBaseInfoRes.getFpztDm())) {
                                invoiceInfoEntity.setChBz("1");
                            } else if ("04".equals(invoiceBaseInfoRes.getFpztDm())) {
                                invoiceInfoEntity.setChBz("4");
                            }
                            invoiceInfoEntity.setTdyw(invoiceBaseInfoRes.getTdyslxDm());
                            invoiceInfoEntity.setKpfnsrsbh(invoiceBaseInfoRes.getKpfnsrsbh());
                            invoiceInfoEntity.setFppzdm(invoiceBaseInfoRes.getFppzDm());
                            invoiceInfoEntity.setCezslxdm(invoiceBaseInfoRes.getCezslxDm());
                            invoiceInfoEntity.setIsDelete("0");
                            invoiceInfoEntity.setCreateTime(new Date());
                            orderInvoiceInfoDao.insert(invoiceInfoEntity);
                        }


                        InvoiceBaseInfoQueryRes invoiceBaseInfoQueryRes = new InvoiceBaseInfoQueryRes();
                        BeanUtils.copyProperties(invoiceBaseInfoRes, invoiceBaseInfoQueryRes);
                        invoiceBaseInfoQueryRes.setFplxdm(invoiceBaseInfoRes.getFplxDm());
                        invoiceBaseInfoQueryRes.setFplydm(invoiceBaseInfoRes.getFplyDm());
                        invoiceBaseInfoQueryRes.setFppzdm(invoiceBaseInfoRes.getFppzDm());
                        invoiceBaseInfoQueryRes.setFpztdm(invoiceBaseInfoRes.getFpztDm());
                        invoiceBaseInfoQueryRes.setTdywlxdm(invoiceBaseInfoRes.getTdyslxDm());
                        invoiceBaseInfoQueryResList.add(invoiceBaseInfoQueryRes);
                    }
                    invoiceInfoQueryRes.setRecords(invoiceBaseInfoQueryResList);
                    invoiceInfoQueryRes.setTotal(invoiceInfoRes.getTotal());
                    invoiceInfoQueryRes.setCurrent(invoiceInfoRes.getCurrent());
                    invoiceInfoQueryRes.setSize(invoiceInfoRes.getSize());
                    invoiceIssueRes = InvoiceIssueRes.ok(invoiceInfoQueryRes);
                } else {
                    invoiceIssueRes = InvoiceIssueRes.ok(invoiceInfoQueryRes);
                }
            } else {
                String msg = r.get("msg").toString();
                invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), msg);
            }
        } catch (Exception e) {
            log.error("发票基础信息查询getInvoiceBaseInfo异常: {}", e);
            invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
        }
        return invoiceIssueRes;
    }

    private Map<String, String> InvoiceBaseInfoParam(InvoiceBaseInfoParam invoiceBaseInfoParam) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // 查询类型
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GJBQ_ERROR_9503, invoiceBaseInfoParam.getGjbq());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (!"1".equals(invoiceBaseInfoParam.getGjbq()) && !"2".equals(invoiceBaseInfoParam.getGjbq())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_GJBQ_1_ERROR_9504);
        }
        // 发票状态
        if (null == invoiceBaseInfoParam.getFpztdm() || invoiceBaseInfoParam.getFpztdm().length == 0) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_FPZTDM_ERROR_9505);
        }
        // 发票来源
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_FPLYDM_ERROR_9506, invoiceBaseInfoParam.getFplydm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 发票票种
        if (null == invoiceBaseInfoParam.getFplxdm() || invoiceBaseInfoParam.getFplxdm().length == 0) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_FPLXDM_ERROR_9507);
        }
        // 开票日期起
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_KPRQQ_ERROR_9508, invoiceBaseInfoParam.getKprqq());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 开票日期止
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_KPRQZ_ERROR_9509, invoiceBaseInfoParam.getKprqz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 销货方纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                invoiceBaseInfoParam.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        //第几页
        if (StringUtils.isEmpty(invoiceBaseInfoParam.getCurrent())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_CURRENT_ERROR_9510);
        }
        //每页几行
        if (StringUtils.isEmpty(invoiceBaseInfoParam.getSize())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SIZE_ERROR_9511);
        }
        return null;
    }

    /**
     * 3.4 单张发票信息查询
     *
     * @return
     */
    @Override
    public InvoiceIssueRes getSingleInvoiceInfo(SingleInvoiceInfoParam singleInvoiceInfoParam) {
        log.info("{}，单张发票信息查询，入参：{}", LOGGER, JsonUtils.getInstance().toJsonString(singleInvoiceInfoParam));
        Map<String, String> invoiceIssueMap = this.singleInvoiceInfoParam(singleInvoiceInfoParam);
        if (!CollectionUtils.isEmpty(invoiceIssueMap)) {
            String errorCode = invoiceIssueMap.get(OrderManagementConstant.ERRORCODE);
            String errorMsg = invoiceIssueMap.get(OrderManagementConstant.ERRORMESSAGE);
            return InvoiceIssueRes.error(errorCode, errorMsg);
        }
        InvoiceIssueRes invoiceIssueRes = null;
        try {
            String invoiceInfoReString = JsonUtils.getInstance().toJsonString(singleInvoiceInfoParam);
            log.info("getSingleInvoiceInfo-invoiceInfoReString 获取单张发票信息入参: {}", invoiceInfoReString);
            long lon = System.currentTimeMillis();
            String resString = HttpUtils.doPost(singelInvoiceInfosAndItemsUrl, invoiceInfoReString);
            log.info("redInvoiceConfirmChoose-invoiceInfoReString 获取单张发票信息 耗时: {}", System.currentTimeMillis() - lon);
            log.info("redInvoiceConfirmChoose-invoiceInfoReString 获取单张发票信息出参: {}", resString);
            R rest = JsonUtils.getInstance().fromJson(resString, R.class);
            if (Objects.nonNull(rest) && "0000".equals(rest.get("code"))) {
                OpenSingleInvoiceInfoRes openSingleInvoiceInfoRes = new OpenSingleInvoiceInfoRes();
                String data = (String) rest.get("data");
                SingleInvoiceInfoResult singleInvoiceInfoResult = JsonUtils.getInstance().fromJson(data, SingleInvoiceInfoResult.class);
                if (Objects.nonNull(singleInvoiceInfoResult)) {
                    // FPZTXX 组装
                    FPZTXX fpztxx = singleInvoiceInfoResult.getFpztxx();
                    FpztxxRes fpztxxRes = new FpztxxRes();
                    BeanUtils.copyProperties(fpztxx, fpztxxRes);
                    fpztxxRes.setFprzztdm(fpztxx.getFprzztDm());
                    fpztxxRes.setXfsytdm(fpztxx.getXfsytDm());
                    fpztxxRes.setZzsyttdm(fpztxx.getZzsytDm());
                    openSingleInvoiceInfoRes.setFpztxx(fpztxxRes);
                    // fpjcxx 组装
                    InvoiceBaseInfoRes fpjcxx = singleInvoiceInfoResult.getFpjcxx();
                    InvoiceBaseInfoQueryRes invoiceBaseInfoQueryRes = new InvoiceBaseInfoQueryRes();
                    BeanUtils.copyProperties(fpjcxx, invoiceBaseInfoQueryRes);
                    invoiceBaseInfoQueryRes.setTdywlxdm(fpjcxx.getTdyslxDm());
                    invoiceBaseInfoQueryRes.setFpztdm(fpjcxx.getFpztDm());
                    invoiceBaseInfoQueryRes.setFppzdm(fpjcxx.getFppzDm());
                    invoiceBaseInfoQueryRes.setFplydm(fpjcxx.getFplyDm());
                    invoiceBaseInfoQueryRes.setFplxdm(fpjcxx.getFplxDm());
                    invoiceBaseInfoQueryRes.setCezslxdm(fpjcxx.getCezslxDm());
                    openSingleInvoiceInfoRes.setFpjcxx(invoiceBaseInfoQueryRes);
                    // fpmxxx组装
                    List<SingleInvoiceItemRes> fpmxxxList = singleInvoiceInfoResult.getFpmxxx();
                    List<FpmxxxRes> fpmxxxRes = new ArrayList<>();
                    for (SingleInvoiceItemRes singleInvoiceItemRes : fpmxxxList) {
                        FpmxxxRes fpmxxx = new FpmxxxRes();
                        BeanUtils.copyProperties(singleInvoiceItemRes, fpmxxx);
                        fpmxxx.setHshyslwfwmc(singleInvoiceItemRes.getHwhyslwfwmc());
                        fpmxxx.setFphxzdm(singleInvoiceItemRes.getFphxzDm());
                        fpmxxx.setSsyhzclxdm(singleInvoiceItemRes.getSsyhzclxDm());
                        fpmxxxRes.add(fpmxxx);
                    }
                    openSingleInvoiceInfoRes.setFpmxxx(fpmxxxRes);
                    invoiceIssueRes = InvoiceIssueRes.ok(openSingleInvoiceInfoRes);
                }
            } else {
                String msg = rest.get("msg").toString();
                invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), msg);
            }
        } catch (Exception e) {
            log.error("getSingleInvoiceInfo单张发票信息查询 异常: {}", e);
            invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
        }
        return invoiceIssueRes;
    }

    private Map<String, String> singleInvoiceInfoParam(SingleInvoiceInfoParam singleInvoiceInfoParam) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // 全电号码
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_QDFPHM_ERROR_9601, singleInvoiceInfoParam.getQdfphm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 开票日期
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_KPRQ_ERROR_9602, singleInvoiceInfoParam.getKprq());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 销货方纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                singleInvoiceInfoParam.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        return null;
    }

    /**
     * 3.5 红字开票确认信息录入
     *
     * @param apiHzkpqrxxlrReqBO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvoiceIssueRes redInvoiceConfirmInfoEnter(ApiHzkpqrxxlrReqBO apiHzkpqrxxlrReqBO) {
        log.info("{}，3.5-红字开票确认信息录入，入参：{}", LOGGER, apiHzkpqrxxlrReqBO);

        String sldh = "";

        // 校验参数
        Map<String, String> checkMap = checkApiHzkpqrxxlrReqBO(apiHzkpqrxxlrReqBO);
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.5-红字开票确认信息录入，lzqdfphm：{}，校验失败：{}", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.5-红字开票确认信息录入，lzqdfphm：{}，校验成功", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm());

        // 两个分支 本系统蓝票信息未维护、本系统蓝票信息已维护
        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
        RedInvoiceConfirmEntity redInvoiceConfirmEntity = new RedInvoiceConfirmEntity();

        if (ObjectUtils.isEmpty(invoiceInfoEntity)) {
            // 业务逻辑 - 蓝票信息未维护 - 无需校验蓝票任何信息
            log.info("{}，3.5-红字开票确认信息录入(lzqdfphm：{}未维护)，业务开始", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm());
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9999.getKey(), "未获取到原蓝票信息，请确认信息无误后到发票归集菜单下载原蓝票后再进行红字确认单申请！");

        } else {
            log.info("{}，3.5-红字开票确认信息销方录入(lzqdfphm：{}已维护)，业务开始", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm());
            // 业务逻辑 - 蓝票信息已维护
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
            OrderInvoiceInfoEntity oldInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
            if (!ObjectUtils.isEmpty(oldInvoiceInfoEntity)) {
                orderInvoiceInfoEntity.setXhfMc(oldInvoiceInfoEntity.getXhfMc());
                orderInvoiceInfoEntity.setGhfMc(oldInvoiceInfoEntity.getGhfMc());
                orderInvoiceInfoEntity.setHjbhsje("-" + oldInvoiceInfoEntity.getHjbhsje());
                orderInvoiceInfoEntity.setKpse("-" + oldInvoiceInfoEntity.getKpse());
                orderInvoiceInfoEntity.setXhfNsrsbh(oldInvoiceInfoEntity.getXhfNsrsbh());
                orderInvoiceInfoEntity.setGhfNsrsbh(oldInvoiceInfoEntity.getGhfNsrsbh());
            }
            orderInvoiceInfoEntity.setBaseNsrsbh(apiHzkpqrxxlrReqBO.getNsrsbh());
            orderInvoiceInfoEntity.setGxfxz(apiHzkpqrxxlrReqBO.getGxfxz());
            if ("0".equals(apiHzkpqrxxlrReqBO.getGxfxz())) {
                orderInvoiceInfoEntity.setXhfNsrsbh(apiHzkpqrxxlrReqBO.getNsrsbh());
            } else {
                orderInvoiceInfoEntity.setGhfNsrsbh(apiHzkpqrxxlrReqBO.getNsrsbh());
            }
            orderInvoiceInfoEntity.setChyy(apiHzkpqrxxlrReqBO.getChyymc());
            orderInvoiceInfoEntity.setQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
            orderInvoiceInfoEntity.setKprq(DateUtil.parse(apiHzkpqrxxlrReqBO.getKprq(), "yyyy-MM-dd"));
            orderInvoiceInfoEntity.setFpdm(apiHzkpqrxxlrReqBO.getYsfpdm());
            orderInvoiceInfoEntity.setFphm(apiHzkpqrxxlrReqBO.getYsfphm());
            orderInvoiceInfoEntity.setXtly(apiHzkpqrxxlrReqBO.getXtly());
            if(CollectionUtils.isEmpty(apiHzkpqrxxlrReqBO.getHzkpqrxxmx())){
                //默认全部冲红,查询原蓝字发票明细
                List<OrderInvoiceItemEntity> oldInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(oldInvoiceInfoEntity.getId());
                // 全部冲红
                oldInvoiceItemEntities.forEach(oldItem -> {
                    if(!oldItem.getXmsl().contains("-")){
                        oldItem.setXmsl("-" + oldItem.getXmsl());
                    }
                    if(!oldItem.getJe().contains("-")){
                        oldItem.setJe("-" + oldItem.getJe());
                    }
                    if(!oldItem.getSe().contains("-")){
                        oldItem.setSe("-" + oldItem.getSe());
                    }
                });
                //将原蓝字发票明细set到orderInvoiceInfoEntity进行全部冲红
                orderInvoiceInfoEntity.setItemEntityList(oldInvoiceItemEntities);
            }else {
                List<OrderInvoiceItemEntity> itemEntityList = new ArrayList<>();
                //按传参数据进行部分冲红
                apiHzkpqrxxlrReqBO.getHzkpqrxxmx().forEach(hzqrxxmx->{
                    OrderInvoiceItemEntity itemEntity = new OrderInvoiceItemEntity();
                    itemEntity.setXmmc(hzqrxxmx.getXmmc());
                    itemEntity.setGgxh(hzqrxxmx.getGgxh());
                    itemEntity.setDj(hzqrxxmx.getFpspdj());
                    itemEntity.setDw(hzqrxxmx.getDw());
                    itemEntity.setXmsl(hzqrxxmx.getFpspsl());
                    itemEntity.setJe(hzqrxxmx.getJe());
                    itemEntity.setSl(hzqrxxmx.getSlv());
                    itemEntity.setSe(hzqrxxmx.getSe());
                    itemEntity.setSpbm(hzqrxxmx.getSphfwssflhbbm());
                    itemEntityList.add(itemEntity);
                });
                orderInvoiceInfoEntity.setItemEntityList(itemEntityList);
            }
            R r = null;
            try {
                r = redInvoiceConfirmService.redInvoiceConfirmSubmit(orderInvoiceInfoEntity);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            if (!"0000".equals(r.get("code").toString())) {
                log.error("{}，3.5-红字开票确认信息销方录入(lzqdfphm：{}已维护)，业务失败", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm());
                return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
            }
            redInvoiceConfirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoOneByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm(), "0");
            if (ObjectUtils.isEmpty(redInvoiceConfirmEntity) || StringUtils.isEmpty(redInvoiceConfirmEntity.getSldh())) {
                log.error("{}，3.5-红字开票确认信息销方录入(lzqdfphm：{}已维护)，业务失败", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm());
                return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
            }
            sldh = redInvoiceConfirmEntity.getSldh();
            log.info("{}，3.5-红字开票确认信息销方录入(lzqdfphm：{}已维护)，业务成功", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm());
        }
        // 返回数据
        ApiHzkpqrxxlrRspBO apiHzkpqrxxlrRspBO = new ApiHzkpqrxxlrRspBO();
        apiHzkpqrxxlrRspBO.setSldh(sldh);
        log.info("{}，3.5-红字开票确认信息录入(lzqdfphm：{})，出参：{}", LOGGER, apiHzkpqrxxlrReqBO.getLzqdfphm(), InvoiceIssueRes.ok(apiHzkpqrxxlrRspBO));
        return InvoiceIssueRes.ok(apiHzkpqrxxlrRspBO);

    }

    /**
     * 3.6 红字开票确认信息受理结果查询
     *
     * @param apiHzkpqrxxsljgcxReqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceIssueRes getRedInvoiceConfirmInfo(ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO) {
        log.info("{}，3.6-红字开票确认信息受理结果查询，入参：{}", LOGGER, apiHzkpqrxxsljgcxReqBO);
        // 校验参数
        Map<String, String> checkMap = checkApiHzkpqrxxsljgcxReqBO(apiHzkpqrxxsljgcxReqBO);
        //受理单号
        String sldh = apiHzkpqrxxsljgcxReqBO.getSldh();
        //纳税人识别号
        String nsrsbh = apiHzkpqrxxsljgcxReqBO.getNsrsbh();
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，校验失败：{}", LOGGER, sldh, checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，校验成功", LOGGER, sldh);
        ApiHzkpqrxxsljgcxRspBO apiHzkpqrxxsljgcxRspBO = new ApiHzkpqrxxsljgcxRspBO();
        try {
            RedConfirmReq redConfirmReq = new RedConfirmReq();
            redConfirmReq.setSldh(sldh);
            redConfirmReq.setNsrsbh(nsrsbh);
            //查询红字确认单信息
            List<RedInvoiceConfirmEntity> invoiceConfirmEntitys = redInvoiceConfirmDao.selectListBySldh(sldh);
            RedInvoiceConfirmEntity invoiceConfirmEntity = null;
            if(!CollectionUtils.isEmpty(invoiceConfirmEntitys)){
                invoiceConfirmEntity = invoiceConfirmEntitys.get(0);
            }
            OrderInvoiceInfoEntity invoiceInfoEntity = null;
            if (Objects.nonNull(invoiceConfirmEntity)) {
                invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(invoiceConfirmEntity.getDylpqdfphm());
                if (Objects.nonNull(invoiceInfoEntity)) {
                    redConfirmReq.setFplxdm(invoiceInfoEntity.getFpzlDm());
                    redConfirmReq.setTdyw(invoiceInfoEntity.getTdyw());
                }
            }
            long lon = System.currentTimeMillis();
            String invoiceInfoReString = JsonUtils.getInstance().toJsonString(redConfirmReq);
            log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，调用拉取红字确认单信息入参: {}", LOGGER, sldh, invoiceInfoReString);
            String resString = HttpUtils.doPost(redInvoiceConfirmSldhUrl, invoiceInfoReString);
            log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，调用拉取红字确认单信息出参: {}，耗时: {}", LOGGER, sldh, resString, System.currentTimeMillis() - lon);

            R rest = JsonUtils.getInstance().parseObject(resString, R.class);
            String msg = (String) rest.get("msg");
            String code = (String) rest.get("code");
            if (Objects.nonNull(rest) && "0000".equals(code)) {
                String datt = rest.get("data").toString();
                RedInvoiceConfirmSldhRes redInvoiceConfirmSldhRes = JsonUtils.getInstance().fromJson(datt, RedInvoiceConfirmSldhRes.class);
                log.info("{}，3.6-红字开票确认信息受理结果查询转换，sldh：{}，实体: {}", LOGGER, sldh, redInvoiceConfirmSldhRes);
                if (Objects.nonNull(redInvoiceConfirmSldhRes)) {
                    if (Objects.nonNull(invoiceConfirmEntity)) {
                        invoiceConfirmEntity.setSlzt("2");
                        invoiceConfirmEntity.setHztzdbh(redInvoiceConfirmSldhRes.getHzfpxxqrdbh());
//                            invoiceConfirmEntity.setKdsj(redInvoiceConfirmSldhRes.getKdsj());
//                            invoiceConfirmEntity.setJshj(redInvoiceConfirmSldhRes.getJshj());
//                            invoiceConfirmEntity.setXsfnsrsbh(redInvoiceConfirmSldhRes.getXsfnsrsbh());
//                            invoiceConfirmEntity.setXsfmc(redInvoiceConfirmSldhRes.getXsfmc());
                        invoiceConfirmEntity.setZt(redInvoiceConfirmSldhRes.getHzqrxxztDm());
//                            invoiceConfirmEntity.setGmfmc(redInvoiceConfirmSldhRes.getGmfmc());
//                            invoiceConfirmEntity.setGmfnsrsbh(redInvoiceConfirmSldhRes.getGmfnsrsbh());
                        invoiceConfirmEntity.setUpdateTime(new Date());

                        if ("0".equals(invoiceConfirmEntity.getGxsf()) && "0".equals(invoiceConfirmEntity.getLrfsf())) {
                            invoiceConfirmEntity.setDfnsrmc(redInvoiceConfirmSldhRes.getGmfmc());
                            invoiceConfirmEntity.setDfnsrsbh(redInvoiceConfirmSldhRes.getGmfnsrsbh());
                        } else if ("1".equals(invoiceConfirmEntity.getGxsf()) && "1".equals(invoiceConfirmEntity.getLrfsf())) {
                            invoiceConfirmEntity.setDfnsrmc(redInvoiceConfirmSldhRes.getXsfmc());
                            invoiceConfirmEntity.setDfnsrsbh(redInvoiceConfirmSldhRes.getXsfnsrsbh());
                        }

                        redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                    }
                    apiHzkpqrxxsljgcxRspBO.setHzqrxxztdm(redInvoiceConfirmSldhRes.getHzqrxxztDm());
                    apiHzkpqrxxsljgcxRspBO.setXsfmc(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getXsfmc()) ? invoiceInfoEntity.getXhfMc() : redInvoiceConfirmSldhRes.getXsfmc());
                    apiHzkpqrxxsljgcxRspBO.setHzfpxxqrdbh(redInvoiceConfirmSldhRes.getHzfpxxqrdbh());
                    apiHzkpqrxxsljgcxRspBO.setKdsj(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getKdsj()) ? DateUtil.format(invoiceConfirmEntity.getSqrq(), DatePattern.NORM_DATETIME_FORMAT) : redInvoiceConfirmSldhRes.getKdsj());
                    apiHzkpqrxxsljgcxRspBO.setXsfnsrsbh(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getXsfnsrsbh()) ? invoiceInfoEntity.getXhfNsrsbh() : redInvoiceConfirmSldhRes.getXsfnsrsbh());
                    apiHzkpqrxxsljgcxRspBO.setQdfphm(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getQdfphm()) ? invoiceConfirmEntity.getQdfphm() : redInvoiceConfirmSldhRes.getQdfphm());
                    apiHzkpqrxxsljgcxRspBO.setGmfmc(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getGmfmc()) ? invoiceInfoEntity.getGhfMc() : redInvoiceConfirmSldhRes.getGmfmc());
                    apiHzkpqrxxsljgcxRspBO.setJshj(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getJshj()) ? invoiceConfirmEntity.getJshj() : redInvoiceConfirmSldhRes.getJshj());
                    apiHzkpqrxxsljgcxRspBO.setGmfnsrsbh(StringUtils.isEmpty(redInvoiceConfirmSldhRes.getGmfnsrsbh()) ? invoiceInfoEntity.getGhfNsrsbh() : redInvoiceConfirmSldhRes.getGmfnsrsbh());
                    log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，出参：{}", LOGGER, sldh, apiHzkpqrxxsljgcxRspBO);
                    return InvoiceIssueRes.ok(apiHzkpqrxxsljgcxRspBO);
                }
            } else if (msg.contains("系统正在同步中")) {

                log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，申请失败", LOGGER, sldh);
                if(Objects.nonNull(invoiceConfirmEntity)){
                    invoiceConfirmEntity.setByzd2(code);
                    invoiceConfirmEntity.setByzd3(msg);
                    invoiceConfirmEntity.setZt("11");
                    invoiceConfirmEntity.setUpdateTime(new Date());
                    redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                }
                log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，系统正在同步中", LOGGER, sldh);
                return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), "系统正在同步中");

            } else if ("300111".equals(code)) {
                // 确认单 申请中状态
                log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，300111 确认单 置为申请中状态", LOGGER, sldh);
                if(Objects.nonNull(invoiceConfirmEntity)){
                    invoiceConfirmEntity.setByzd2(code);
                    invoiceConfirmEntity.setByzd3(msg);
                    invoiceConfirmEntity.setZt("10");
                    invoiceConfirmEntity.setSlzt("1");
                    invoiceConfirmEntity.setUpdateTime(new Date());
                    redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                }
                log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，确认单申请中", LOGGER, sldh);
                return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), "确认单申请中");
            } else {
                log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，处理失败 不可重试", LOGGER, sldh);
                if(Objects.nonNull(invoiceConfirmEntity)){
                    // 其他状态 都为 处理失败 不可重试
                    invoiceConfirmEntity.setByzd2(code);
                    invoiceConfirmEntity.setByzd3(msg);
                    // 12 处理失败(不可重试)
                    invoiceConfirmEntity.setZt("12");
                    // 3 受理失败
                    invoiceConfirmEntity.setSlzt("3");
                    invoiceConfirmEntity.setUpdateTime(new Date());
                    redInvoiceConfirmDao.updateById(invoiceConfirmEntity);
                }
                log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，处理失败", LOGGER, sldh);
                return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), "处理失败");
            }
        } catch (Exception e) {
            log.error("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，异常:{}", LOGGER, sldh, e);
        }
        // 返回逻辑
        log.info("{}，3.6-红字开票确认信息受理结果查询，sldh：{}，失败", LOGGER, sldh);
        return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());

    }

    /**
     * 3.7 红字发票确认信息处理
     *
     * @param apiHzfpqrxxclReqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceIssueRes getRedInvoiceConfirmInfoDeal(ApiHzfpqrxxclReqBO apiHzfpqrxxclReqBO) {
        log.info("{}，3.7-红字发票确认信息处理，入参：{}", LOGGER, apiHzfpqrxxclReqBO);
        // 校验参数
        Map<String, String> checkMap = checkApiHzfpqrxxclReqBO(apiHzfpqrxxclReqBO);
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.7-红字发票确认信息处理，确认单编号:{}，校验失败：{}", LOGGER, apiHzfpqrxxclReqBO.getHzfpxxqrdbh(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9519.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.7-红字发票确认信息处理，校验成功", LOGGER);

        // 业务逻辑
        RedConfirmHandle redConfirmHandle = new RedConfirmHandle();
        redConfirmHandle.setBaseNsrsbh(apiHzfpqrxxclReqBO.getXsfnsrsbh());
        redConfirmHandle.setXsfnsrsbh(apiHzfpqrxxclReqBO.getXsfnsrsbh());
        redConfirmHandle.setNsrsbh(apiHzfpqrxxclReqBO.getNsrsbh());
        redConfirmHandle.setHzqrdbh(apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
        redConfirmHandle.setCllx(apiHzfpqrxxclReqBO.getCllx());
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmDao.selectRedConfirmDaoListByHztzdbh(redConfirmHandle.getUuid());
        if(!CollectionUtils.isEmpty(redInvoiceConfirmEntities)){
            RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmEntities.get(0);
            redConfirmHandle.setKprq(DateUtil.formatDate(redInvoiceConfirmEntity.getSqrq()));
            if(org.apache.commons.lang3.StringUtils.equals(redInvoiceConfirmEntity.getBaseNsrsbh(),apiHzfpqrxxclReqBO.getNsrsbh())){
                redConfirmHandle.setSqly("0");
            }else {
                redConfirmHandle.setSqly("1");
            }
            //获取原蓝票信息
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
            if (Objects.nonNull(invoiceInfoEntity)) {
                redConfirmHandle.setFpzl(invoiceInfoEntity.getFpzlDm());
                redConfirmHandle.setTdyw(invoiceInfoEntity.getTdyw());
            }
        }
        long lo = System.currentTimeMillis();
        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmHandle);
        log.info("{}，3.7-红字发票确认信息处理，uuid：{}，调用红字发票确认信息处理入参: {}", LOGGER, redConfirmHandle.getHzqrdbh(), jsonString);
        String res = HttpUtils.doPost(hzqrxxUpdateUrl, jsonString);
        log.info("{}，3.7-红字发票确认信息处理：{}，结束调用红字发票确认信息处理 耗时: {}", LOGGER, redConfirmHandle.getUuid(), System.currentTimeMillis() - lo);
        log.info("{}，3.7-红字发票确认信息处理：{}，调用红字发票确认信息处理出参: {}", LOGGER, redConfirmHandle.getUuid(), res);
        R r = JsonUtils.getInstance().fromJson(res, R.class);
        if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
            String data = (String) r.get("data");
            HzqrxxUpdateRes hzqrxxUpdateRes = JsonUtils.getInstance().fromJson(data, HzqrxxUpdateRes.class);
            if (Objects.nonNull(hzqrxxUpdateRes)) {
                if ("Y".equals(hzqrxxUpdateRes.getCode())) {
                    // 购销方身份确认
                    if (redInvoiceConfirmEntities.size() > 0) {
                        for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : redInvoiceConfirmEntities) {
                            //处理类型【01 撤销、02 确认、03 拒绝】
                            if ("01".equals(redConfirmHandle.getCllx())) {
                                // 3已撤销
                                redInvoiceConfirmEntity.setKjzt("3");
                            } else if ("02".equals(redConfirmHandle.getCllx())) {
                                // 4已确认
                                //redInvoiceConfirmEntity.setKjzt("4");
                                redInvoiceConfirmEntity.setZt("04");
                            } else if ("03".equals(redConfirmHandle.getCllx())) {
                                // 5已拒绝
                                redInvoiceConfirmEntity.setKjzt("5");
                            }
                            redInvoiceConfirmEntity.setUpdateTime(new Date());
                            //redInvoiceConfirmEntity.setIsDelete("1");
                            redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);

                            // 撤销成功后  对应的蓝字发票继续回到【新增确认单】列表界面，这张票能继续发起红冲
                            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
                            if (Objects.nonNull(invoiceInfoEntity) && ("01".equals(redConfirmHandle.getCllx()) || "03".equals(redConfirmHandle.getCllx()))) {
                                invoiceInfoEntity.setChBz("0");
                                invoiceInfoEntity.setUpdateTime(new Date());
                                orderInvoiceInfoDao.updateById(invoiceInfoEntity);
                            }
                        }
                    }
                } else {
                    log.error("{}，3.7-红字发票确认信息处理，红字确认单编号:{}，接口处理失败", LOGGER, apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
                    if (!StringUtils.isEmpty(hzqrxxUpdateRes)) {
                        return InvoiceIssueRes.res(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage(), hzqrxxUpdateRes);
                    }
                    return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
                }
            } else {
                log.error("{}，3.7-红字发票确认信息处理，红字确认单编号:{}，接口返回失败", LOGGER, apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
                return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
            }
        } else {
            log.error("{}，3.7-红字发票确认信息处理，红字确认单编号:{}，接口返回失败", LOGGER, apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
        }

        // 返回逻辑
        ApiHzfpqrxxclRspBO apiHzfpqrxxclRspBO = new ApiHzfpqrxxclRspBO();
        apiHzfpqrxxclRspBO.setHzfpxxqrdbh(apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
        apiHzfpqrxxclRspBO.setCode("y");
        if ("01".equals(apiHzfpqrxxclReqBO.getCllx())) {
            apiHzfpqrxxclRspBO.setMessage("该红字确认单已撤销!");
        } else if ("02".equals(apiHzfpqrxxclReqBO.getCllx())) {
            apiHzfpqrxxclRspBO.setMessage("该红字确认单已确认!");
        } else {
            apiHzfpqrxxclRspBO.setMessage("该红字确认单已拒绝!");
        }
        log.info("{}，3.7-红字发票确认信息处理，红字确认单编号:{}，出参：{}", LOGGER, apiHzfpqrxxclReqBO.getHzfpxxqrdbh(), InvoiceIssueRes.ok(apiHzfpqrxxclRspBO));
        return InvoiceIssueRes.ok(apiHzfpqrxxclRspBO);
    }

    /**
     * 3.8 红字发票确认信息列表查询
     *
     * @param apiHzfpqrxxlbcxReqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceIssueRes getRedInvoiceConfirmInfoList(ApiHzfpqrxxlbcxReqBO apiHzfpqrxxlbcxReqBO) {
        log.info("{}，3.8-红字发票确认信息列表查询，入参：{}", LOGGER, apiHzfpqrxxlbcxReqBO);
        String nkk = apiHzfpqrxxlbcxReqBO.getNsrsbh() + "-" + apiHzfpqrxxlbcxReqBO.getKprqq() + "-" + apiHzfpqrxxlbcxReqBO.getKprqz();
        // 校验参数
        Map<String, String> checkMap = checkApiHzfpqrxxlbcxReqBO(apiHzfpqrxxlbcxReqBO);
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.8-红字发票确认信息列表查询，nsrsbh-kprqq-kprqz:{}，校验失败：{}", LOGGER, nkk, checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.8-红字发票确认信息列表查询，校验成功", LOGGER);

        // 业务逻辑
        RedConfirmListReq redConfirmListReq = new RedConfirmListReq();
        redConfirmListReq.setGxfxz(apiHzfpqrxxlbcxReqBO.getGxfxz());
        redConfirmListReq.setLrfsf(apiHzfpqrxxlbcxReqBO.getLrfsf());
        redConfirmListReq.setHzqrxxztDm(apiHzfpqrxxlbcxReqBO.getHzqrxxztdm());
        redConfirmListReq.setKprqq(apiHzfpqrxxlbcxReqBO.getKprqq());
        redConfirmListReq.setKprqz(apiHzfpqrxxlbcxReqBO.getKprqz());
        redConfirmListReq.setCurrent(apiHzfpqrxxlbcxReqBO.getCurrent());
        redConfirmListReq.setSize(apiHzfpqrxxlbcxReqBO.getSize());
        redConfirmListReq.setNsrsbh(apiHzfpqrxxlbcxReqBO.getNsrsbh());
        long lo = System.currentTimeMillis();
        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmListReq);
        log.info("{}，3.8-红字发票确认信息列表查询，nsrsbh-kprqq-kprqz:{}，调用我开具的接口-入参: {}", LOGGER, nkk, jsonString);
        String res = HttpUtils.doPost(queryHzqrxxUrl, jsonString);
        log.info("{}，3.8-红字发票确认信息列表查询，nsrsbh-kprqq-kprqz:{}，调用我开具的接口-出参: {}，耗时: {}", LOGGER, nkk, res, System.currentTimeMillis() - lo);
        // 模拟测试
        //String res = "{\"code\":\"0000\",\"msg\":\"成功\",\"data\":{\"total\":\"1\",\"current\":\"1\",\"pages\":\"1\",\"size\":\"1\",\"optimizeCountSql\":\"\",\"searchCount\":\"1\",\"records\":[{\"uuid\":\"123456789\",\"hzfpxxqrdbh\":\"987654321\",\" lrfsf\":\"1\",\"xsfnsrsbh\":\"1\",\" xsfmc\":\"1\",\"gmfnsrsbh\":\"1\",\"gmfmc\":\"1\",\" lzfphm\":\"22442000000000815333\",\"lzfpdm\":\"1\",\"lzkprq\":\"1\",\"lzhjje\":\"1\",\"lzhjse\":\"1\",\"lzfppzDm\":\"1\",\"zzsytDm\":\"1\",\"xfsytDm\":\"1\",\"fprzztDm\":\"1\",\"hzcxje\":\"1\",\"hzcxse\":\"1\",\"chyyDm\":\"1\",\"hzqrxxztDm\":\"1\",\"ykjhzfpbz\":\"1\",\"hzqrxxmxList\":[{\"lzfpdm\":\"2\",\"lzmxxh\":\"2\",\"xh\":\"2\",\"sphfwssflhbbm\":\"2\",\"hwhyslwfwmc\":\"2\",\"spfwjc\":\"2\",\"xmmc\":\"2\",\"ggxh\":\"2\",\"dw\":\"2\",\"fpspdj\":\"2\",\"fpspsl\":\"2\",\"je\":\"2\",\"sll\":\"2\",\"se\":\"2\"}]}]}}";

        R r = JsonUtils.getInstance().fromJson(res, R.class);
        CommonQueryHzqrxxRes queryHzqrxxRes = new CommonQueryHzqrxxRes();
        if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
            String data = r.get("data").toString();
            queryHzqrxxRes = JsonUtils.getInstance().fromJson(data, CommonQueryHzqrxxRes.class);
            if (Objects.nonNull(queryHzqrxxRes) && !CollectionUtils.isEmpty(queryHzqrxxRes.getRecords())) {
                for (QueryHzqrxxRes hzqrxxRes : queryHzqrxxRes.getRecords()) {
                    // 根据uuid 查询 是否含有重复数据
                    List<RedInvoiceConfirmEntity> confirmEntitys = redInvoiceConfirmDao.selectRedConfirmsDaoByQdfphm(hzqrxxRes.getLzfphm());
                    if (confirmEntitys.size() > 0) {
                        for (RedInvoiceConfirmEntity confirmEntity : confirmEntitys) {
                            confirmEntity.setUuid(hzqrxxRes.getUuid());
                            confirmEntity.setUpdateTime(new Date());
                            redInvoiceConfirmDao.updateById(confirmEntity);
                        }
                    }
                }
                updateInvoiceInfoByFphm(queryHzqrxxRes.getRecords());
            }

            // 返回逻辑
            ApiHzfpqrxxlbcxRspBO apiHzfpqrxxlbcxRspBO = fillHzfpqrxxlbcxRespParam(queryHzqrxxRes);
            log.info("{}，3.8-红字发票确认信息列表查询，nsrsbh-kprqq-kprqz:{}，业务成功", LOGGER, nkk);
            return InvoiceIssueRes.ok(apiHzfpqrxxlbcxRspBO);
        }
        // 返回逻辑
        log.info("{}，3.8-红字发票确认信息列表查询，nsrsbh-kprqq-kprqz:{}，业务失败", LOGGER, nkk);
        return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
    }

    /**
     * 3.9 红字发票确认明细信息查询
     *
     * @param apiHzfpqrmxxxcxReqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceIssueRes getRedInvoiceConfirmInfoDetail(ApiHzfpqrmxxxcxReqBO apiHzfpqrmxxxcxReqBO) {
        log.info("{}，3.9-红字发票确认明细信息查询，入参：{}", LOGGER, apiHzfpqrmxxxcxReqBO);
        // 校验参数
        Map<String, String> checkMap = checkApiHzfpqrmxxxcxReqBO(apiHzfpqrmxxxcxReqBO);
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.9-红字发票确认明细信息查询，红字确认单编号:{}，校验失败：{}", LOGGER, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.9-红字发票确认明细信息查询，红字确认单编号:{}，校验成功", LOGGER, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());

        // 业务逻辑
        RedConfirmHandle redConfirmHandle = new RedConfirmHandle();
        redConfirmHandle.setHzqrdbh(apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());
        redConfirmHandle.setXsfnsrsbh(apiHzfpqrmxxxcxReqBO.getNsrsbh());
        redConfirmHandle.setNsrsbh(apiHzfpqrmxxxcxReqBO.getNsrsbh());
        //红字确认单信息
        RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByHztzdbh(apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());
        // 蓝票信息
        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
        if (Objects.nonNull(invoiceInfoEntity)) {
            redConfirmHandle.setFpzl(invoiceInfoEntity.getFpzlDm());
            redConfirmHandle.setTdyw(invoiceInfoEntity.getTdyw());
        }
        long lo = System.currentTimeMillis();
        String jsonString = JsonUtils.getInstance().toJsonString(redConfirmHandle);
        log.info("{}，3.9-红字发票确认明细信息查询，红字确认单编号:{}，调用查询 确认单状态 入参: {}", LOGGER, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh(), jsonString);
        String res = HttpUtils.doPost(queryHzqrxxStatusUrl, jsonString);
        log.info("{}，3.9-红字发票确认明细信息查询，红字确认单编号:{}，调用查询 确认单状态 出参: {}，耗时: {}", LOGGER, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh(), res, System.currentTimeMillis() - lo);
        R r = JsonUtils.getInstance().fromJson(res, R.class);
        QueryHzqrxxRes queryHzqrxxRes = null;
        if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
            queryHzqrxxRes = JsonUtils.getInstance().parseObject(r.get("data").toString(), QueryHzqrxxRes.class);
            if (Objects.nonNull(queryHzqrxxRes)) {
                // 更新红字确认单信息
                redInvoiceConfirmEntity.setZt(queryHzqrxxRes.getHzqrxxztDm());
                redInvoiceConfirmEntity.setXfsytDm(queryHzqrxxRes.getXfsytDm());
                redInvoiceConfirmEntity.setLzfppzDm(queryHzqrxxRes.getLzfppzDm());
                redInvoiceConfirmEntity.setZzsytDm(queryHzqrxxRes.getZzsytDm());
                redInvoiceConfirmEntity.setUpdateTime(new Date());
                redInvoiceConfirmEntity.setFpje(queryHzqrxxRes.getHzcxje());
                redInvoiceConfirmEntity.setFpse(queryHzqrxxRes.getHzcxse());
                redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                //更新原蓝票信息
                if (Objects.nonNull(invoiceInfoEntity)) {
                    invoiceInfoEntity.setXfsytzt(queryHzqrxxRes.getXfsytDm());
                    invoiceInfoEntity.setRzzt(queryHzqrxxRes.getFprzztDm());
                    invoiceInfoEntity.setZzsytzt(queryHzqrxxRes.getZzsytDm());
                    invoiceInfoEntity.setUpdateTime(new Date());
                    orderInvoiceInfoDao.updateById(invoiceInfoEntity);
                }
            }
            // 返回数据
            ApiHzfpqrmxxxcxRspBO apiHzfpqrmxxxcxRspBO = fillHzfpqrxxmxcxRespParam(queryHzqrxxRes);
            log.info("{}，3.9-红字发票确认明细信息查询，红字确认单编号:{}，业务成功", LOGGER, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());
            return InvoiceIssueRes.ok(apiHzfpqrmxxxcxRspBO);
        }

        // 返回数据
        log.info("{}，3.9-红字发票确认明细信息查询，红字确认单编号:{}，业务失败", LOGGER, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());
        return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
    }

    /**
     * 3.10 红字发票开具受理
     *
     * @param apiHzfpkjslReqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceIssueRes redInvoiceIssue(ApiHzfpkjslReqBO apiHzfpkjslReqBO) {
        log.info("{}，3.10-红字发票开具受理，入参：{}", LOGGER, apiHzfpkjslReqBO);
        // 校验参数
        Map<String, String> checkMap = checkApiHzfpkjslReqBO(apiHzfpkjslReqBO);
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.10-红字发票开具受理，hzfpxxqrdbh:{}，校验失败：{}", LOGGER, apiHzfpkjslReqBO.getHzfpxxqrdbh(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.10-红字发票开具受理，hzfpxxqrdbh:{}，校验成功", apiHzfpkjslReqBO.getHzfpxxqrdbh(), LOGGER);

        //根据红字确认单号查询红字发票确认单
        RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByHztzdbh(apiHzfpkjslReqBO.getHzfpxxqrdbh());
        if (Objects.isNull(redInvoiceConfirmEntity)) {
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), "不存在红字确认单信息!");
        }
        if(!"01".equals(redInvoiceConfirmEntity.getZt()) && !"04".equals(redInvoiceConfirmEntity.getZt())){
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), "该红字确认单状态不为无需确认或已确认，不能开具红票!");
        }
        RedInvoiceConfirmSldh redInvoiceConfirmSldh = new RedInvoiceConfirmSldh();
        redInvoiceConfirmSldh.setRedConfirmId(redInvoiceConfirmEntity.getId());
        R r = redInvoiceConfirmService.redInvoiceIssue(redInvoiceConfirmSldh);

        if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
            String kpjgsldh = (String) r.get("kpjgsldh");
            // 返回逻辑
            ApiHzkpqrxxlrRspBO apiHzkpqrxxlrRspBO = new ApiHzkpqrxxlrRspBO();
            apiHzkpqrxxlrRspBO.setSldh(kpjgsldh);
            log.info("{}，3.10-红字发票开具受理，hzfpxxqrdbh:{}，出参：{}", LOGGER, apiHzfpkjslReqBO.getHzfpxxqrdbh(), InvoiceIssueRes.ok(apiHzkpqrxxlrRspBO));
            return InvoiceIssueRes.ok(apiHzkpqrxxlrRspBO);
        }
        log.info("{}，3.10-红字发票开具受理，hzfpxxqrdbh:{}，业务失败", LOGGER, apiHzfpkjslReqBO.getHzfpxxqrdbh());
        return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
    }

    /**
     * 3.11 红字发票开具受理结果查询
     *
     * @param apiHzfpkjsljgcxReqBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public InvoiceIssueRes getRedInvoiceResult(ApiHzfpkjsljgcxReqBO apiHzfpkjsljgcxReqBO) {
        log.info("{}，3.11-红字发票开具受理结果查询，入参：{}", LOGGER, apiHzfpkjsljgcxReqBO);
        //开票结果受理单号
        String kpjgsldh = apiHzfpkjsljgcxReqBO.getSldh();
        // 校验参数 (参数相同，使用同一个校验方法)
        ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO = new ApiHzkpqrxxsljgcxReqBO();
        apiHzkpqrxxsljgcxReqBO.setNsrsbh(apiHzfpkjsljgcxReqBO.getNsrsbh());
        apiHzkpqrxxsljgcxReqBO.setSldh(kpjgsldh);
        Map<String, String> checkMap = checkApiHzkpqrxxsljgcxReqBO(apiHzkpqrxxsljgcxReqBO);
        if (!ObjectUtils.isEmpty(checkMap)) {
            log.error("{}，3.11-红字发票开具受理结果查询，sldh:{}，校验失败：{}", LOGGER, kpjgsldh, checkMap.get(OrderManagementConstant.ERRORMESSAGE));
            return InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9515.getKey(), checkMap.get(OrderManagementConstant.ERRORMESSAGE));
        }
        log.info("{}，3.11-红字发票开具受理结果查询，sldh:{}，校验成功", LOGGER, kpjgsldh);

        // 业务逻辑
        ApiHzfpkjsljgcxRspBO apiHzfpkjsljgcxRspBO = new ApiHzfpkjsljgcxRspBO();
        RedInvoiceConfirmEntity invoiceConfirmEntity = redInvoiceConfirmDao.selectByKpjgsldh(kpjgsldh);
        // 组装返回
        apiHzfpkjsljgcxRspBO.setXsfmc(invoiceConfirmEntity.getXsfmc());
        apiHzfpkjsljgcxRspBO.setHzfpxxqrdbh(invoiceConfirmEntity.getHztzdbh());
        apiHzfpkjsljgcxRspBO.setHzqrxxztdm(invoiceConfirmEntity.getZt());
        apiHzfpkjsljgcxRspBO.setXsfnsrsbh(invoiceConfirmEntity.getXsfnsrsbh());
        apiHzfpkjsljgcxRspBO.setJshj(invoiceConfirmEntity.getJshj());
        apiHzfpkjsljgcxRspBO.setKprq("");
        apiHzfpkjsljgcxRspBO.setQdfphm("");
        apiHzfpkjsljgcxRspBO.setBzxx("");
        apiHzfpkjsljgcxRspBO.setPdfxzurl("");
        String errorCode = StringUtils.isEmpty(invoiceConfirmEntity.getKjztCode()) ? "9999" : invoiceConfirmEntity.getKjztCode();
        String errorMsg = StringUtils.isEmpty(invoiceConfirmEntity.getKjztMsg()) ? "红票未开具，请稍后重试" : "红票未开具，请稍后重试："+invoiceConfirmEntity.getKjztCode();
        if("Y".equals(invoiceConfirmEntity.getKjzt())){
            apiHzfpkjsljgcxRspBO.setQdfphm(invoiceConfirmEntity.getQdfphm());
            //红票已开具,查询红票信息
            List<OrderInvoiceInfoEntity> invoiceInfoByQdfphms = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(invoiceConfirmEntity.getQdfphm());
            if(!CollectionUtils.isEmpty(invoiceInfoByQdfphms)){
                OrderInvoiceInfoEntity hzfp = invoiceInfoByQdfphms.get(0);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                apiHzfpkjsljgcxRspBO.setKprq(dateFormat.format(hzfp.getKprq()));
                apiHzfpkjsljgcxRspBO.setBzxx(hzfp.getBz());
                apiHzfpkjsljgcxRspBO.setPdfxzurl(hzfp.getPdfUrl());
                log.info("{}，3.11-红字发票开具受理结果查询，sldh:{}，成功：{}", LOGGER, kpjgsldh, apiHzfpkjsljgcxRspBO);
                return InvoiceIssueRes.ok(apiHzfpkjsljgcxRspBO);
            }else {
                log.error("sldh:{}，红字发票开具受理结果查询，未查询到红票信息,异常编码{},异常信息{}", kpjgsldh, errorCode, errorMsg);
            }
        }else {
            log.error("sldh:{}，红字发票开具受理结果查询，红票还未开具,异常编码{},异常信息{}", kpjgsldh, errorCode, errorMsg);
        }
        //红票未开具
        return InvoiceIssueRes.res(errorCode, errorMsg, apiHzfpkjsljgcxRspBO);
    }

    /**
     * 3.5 红字开票确认信息录入参数校验
     *
     * @param apiHzkpqrxxlrReqBO
     * @return
     */
    public Map<String, String> checkApiHzkpqrxxlrReqBO(ApiHzkpqrxxlrReqBO apiHzkpqrxxlrReqBO) {

        Map<String, String> checkResultMap = new HashMap<>(10);
        // 购销方选择 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_GXFXZ_NULL_ERROR_9351, apiHzkpqrxxlrReqBO.getGxfxz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 蓝字全电发票号码 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_QDFPHM_NULL_ERROR_9352, apiHzkpqrxxlrReqBO.getLzqdfphm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 不允许重复调用 蓝票正在红冲  红字确认单为不是失败状态
        OrderInvoiceInfoEntity invoiceInfoEntity1 = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
        if (!ObjectUtils.isEmpty(invoiceInfoEntity1)) {
            List<RedInvoiceConfirmEntity> list = redInvoiceConfirmDao.selectRedConfirmIsNotFailByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
            if (!StringUtils.isEmpty(invoiceInfoEntity1.getChBz()) && "5".equals(invoiceInfoEntity1.getChBz()) && list.size() > 0) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_QDFPHM_NULL_ERROR_935201);
            }
        }

        // 冲红原因 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_CHYY_NULL_ERROR_935301, apiHzkpqrxxlrReqBO.getChyymc());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 冲红原因 内容校验
        if (!("开票有误".equals(apiHzkpqrxxlrReqBO.getChyymc()) || "销货退回".equals(apiHzkpqrxxlrReqBO.getChyymc())
                || "服务中止".equals(apiHzkpqrxxlrReqBO.getChyymc()) || "销售折让".equals(apiHzkpqrxxlrReqBO.getChyymc()))) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_CHYY_OPTION_ERROR_935303);
        }
        // 冲红原因 商品服务编码为以 1(货物)、2(劳务)开头的冲红原因不允许“服务中止”
        if ("服务中止".equals(apiHzkpqrxxlrReqBO.getChyymc())) {
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
            if (!ObjectUtils.isEmpty(invoiceInfoEntity)) {
                List<OrderInvoiceItemEntity> list = orderInvoiceItemDao.selectItemListById(invoiceInfoEntity.getId());
                if (list.size() > 0) {
                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : list) {
                        if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSpbm())
                                && ("1".equals(orderInvoiceItemEntity.getSpbm().substring(0, 1)) || "2".equals(orderInvoiceItemEntity.getSpbm().substring(0, 1)))) {
                            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_CHYY_OPTION_ERROR_935302);
                        }
                    }
                }
            }
        }

        // 开票日期 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_KPRQ_NULL_ERROR_935401, apiHzkpqrxxlrReqBO.getKprq());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_KPRQ_NULL_ERROR_935401);
        }
        // 开票日期 格式校验
        if (!ConfigureConstant.DATE_FORMAT_DATE.equals(StringUtil.checkDateFormat(apiHzkpqrxxlrReqBO.getKprq()))) {
            return CheckParamUtil.generateErrorMap("", "", OrderInfoContentEnum.RED_INVOICE_KPRQ_FORMAT_ERROR_935402);
        }
        // 开票日期 准确度校验
        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(apiHzkpqrxxlrReqBO.getLzqdfphm());
        if (!ObjectUtils.isEmpty(invoiceInfoEntity)) {
            if (!StringUtils.isEmpty(invoiceInfoEntity.getKprq()) && !(new SimpleDateFormat("yyyy-MM-dd").format(invoiceInfoEntity.getKprq())).equals(apiHzkpqrxxlrReqBO.getKprq())) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_CHYY_OPTION_ERROR_935303);
            }
        }
        // 纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                apiHzkpqrxxlrReqBO.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        return null;
    }

    /**
     * 3.6 红字开票确认信息受理结果查询参数校验
     *
     * @param apiHzkpqrxxsljgcxReqBO
     * @return
     */
    public Map<String, String> checkApiHzkpqrxxsljgcxReqBO(ApiHzkpqrxxsljgcxReqBO apiHzkpqrxxsljgcxReqBO) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // 受理单号 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_SLDH_NULL_ERROR_9356, apiHzkpqrxxsljgcxReqBO.getSldh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                apiHzkpqrxxsljgcxReqBO.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        return null;
    }

    /**
     * 3.7 红字开票确认信息受理结果查询参数校验
     *
     * @param apiHzfpqrxxclReqBO
     * @return
     */
    public Map<String, String> checkApiHzfpqrxxclReqBO(ApiHzfpqrxxclReqBO apiHzfpqrxxclReqBO) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // uuid 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_UUID_NULL_ERROR_9357, apiHzfpqrxxclReqBO.getHzfpxxqrdbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 销货方纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                apiHzfpqrxxclReqBO.getXsfnsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                apiHzfpqrxxclReqBO.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 处理类型 非空校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_CLLX_NULL_ERROR_9358, apiHzfpqrxxclReqBO.getCllx());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 处理类型 枚举校验
        if (!("01".equals(apiHzfpqrxxclReqBO.getCllx()) || "02".equals(apiHzfpqrxxclReqBO.getCllx()) || "03".equals(apiHzfpqrxxclReqBO.getCllx()))) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_CLLX_TYPE_ERROR_935801);
        }
        return null;
    }

    /**
     * 3.8 红字发票确认信息列表查询参数校验
     *
     * @param apiHzfpqrxxlbcxReqBO
     * @return
     */
    public Map<String, String> checkApiHzfpqrxxlbcxReqBO(ApiHzfpqrxxlbcxReqBO apiHzfpqrxxlbcxReqBO) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // 购销方选择 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_GXFXZ_NULL_ERROR_9351, apiHzfpqrxxlbcxReqBO.getGxfxz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 销货方纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                apiHzfpqrxxlbcxReqBO.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 确认单状态 非空校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_SLDH_NULL_ERROR_9359, apiHzfpqrxxlbcxReqBO.getHzqrxxztdm());
        if (!ConfigureConstant.DATE_FORMAT_DATE.equals(StringUtil.checkDateFormat(apiHzfpqrxxlbcxReqBO.getKprqq()))) {
            return checkResultMap;
        }
        // 开票日期起 非空校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_KPRQ_NULL_ERROR_935401, apiHzfpqrxxlbcxReqBO.getKprqq());
        if (!ConfigureConstant.DATE_FORMAT_DATE.equals(StringUtil.checkDateFormat(apiHzfpqrxxlbcxReqBO.getKprqq()))) {
            return checkResultMap;
        }
        // 开票日期起 格式校验
        if (!ConfigureConstant.DATE_FORMAT_DATE.equals(StringUtil.checkDateFormat(apiHzfpqrxxlbcxReqBO.getKprqq()))) {
            return CheckParamUtil.generateErrorMap("", "", OrderInfoContentEnum.RED_INVOICE_KPRQ_FORMAT_ERROR_935402);
        }
        // 开票日期止 非空校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_KPRQ_NULL_ERROR_935401, apiHzfpqrxxlbcxReqBO.getKprqz());
        if (!ConfigureConstant.DATE_FORMAT_DATE.equals(StringUtil.checkDateFormat(apiHzfpqrxxlbcxReqBO.getKprqz()))) {
            return checkResultMap;
        }
        // 开票日期止 格式校验
        if (!ConfigureConstant.DATE_FORMAT_DATE.equals(StringUtil.checkDateFormat(apiHzfpqrxxlbcxReqBO.getKprqz()))) {
            return CheckParamUtil.generateErrorMap("", "", OrderInfoContentEnum.RED_INVOICE_KPRQ_FORMAT_ERROR_935402);
        }
        // 第几页 非空校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_CURRENT_NULL_ERROR_9359, apiHzfpqrxxlbcxReqBO.getCurrent());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 第几页 非空校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_SIZE_NULL_ERROR_9359, apiHzfpqrxxlbcxReqBO.getCurrent());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 每页数据 枚举校验
        if (!("10".equals(apiHzfpqrxxlbcxReqBO.getSize()) || "20".equals(apiHzfpqrxxlbcxReqBO.getSize()) || "50".equals(apiHzfpqrxxlbcxReqBO.getSize()))) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_SIZE_OPTION_ERROR_935901);
        }
        return null;
    }

    /**
     * 3.9 红字发票确认明细信息查询校验
     *
     * @param apiHzfpqrmxxxcxReqBO
     * @return
     */
    public Map<String, String> checkApiHzfpqrmxxxcxReqBO(ApiHzfpqrmxxxcxReqBO apiHzfpqrmxxxcxReqBO) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // uuid 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_UUID_NULL_ERROR_9357, apiHzfpqrmxxxcxReqBO.getHzfpxxqrdbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 销货方纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                apiHzfpqrmxxxcxReqBO.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        return null;
    }


    /**
     * 3.10 红字发票开具受理入参校验
     *
     * @param apiHzfpkjslReqBO
     * @return
     */
    public Map<String, String> checkApiHzfpkjslReqBO(ApiHzfpkjslReqBO apiHzfpkjslReqBO) {
        Map<String, String> checkResultMap = new HashMap<>(10);

        // 红字发票确认单编号 空值校验
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_HZFPQRDBH_NULL_ERROR_9361, apiHzfpkjslReqBO.getHzfpxxqrdbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 不允许重复开具
        List<RedInvoiceConfirmEntity> list = redInvoiceConfirmDao.queryHzqrxxStatusListByHzqrdbh(apiHzfpkjslReqBO.getHzfpxxqrdbh());
        if (list.size() > 0) {
            for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : list) {
                if ("1".equals(redInvoiceConfirmEntity.getKjzt())) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_HPKJ_REQ_ERROR_936301);
                }
                if ("Y".equals(redInvoiceConfirmEntity.getKjzt())) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_HPKJ_REQ_ERROR_936302);
                }
                if ("3".equals(redInvoiceConfirmEntity.getKjzt()) || "4".equals(redInvoiceConfirmEntity.getKjzt()) || "5".equals(redInvoiceConfirmEntity.getKjzt())) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.RED_INVOICE_HPKJ_REQ_ERROR_936303);
                }
            }
        }
        return null;
    }


    /**
     * 3.8 红字发票确认信息列表查询返回数据填充
     */
    public ApiHzfpqrxxlbcxRspBO fillHzfpqrxxlbcxRespParam(CommonQueryHzqrxxRes queryHzqrxxRes) {
        ApiHzfpqrxxlbcxRspBO apiHzfpqrxxlbcxRspBO = new ApiHzfpqrxxlbcxRspBO();
        List<ApiHzfpqrmxxxcxRspBO> list = new ArrayList<>();
        if (queryHzqrxxRes.getRecords().size() > 0) {
            for (QueryHzqrxxRes hzqrxxRes : queryHzqrxxRes.getRecords()) {
                ApiHzfpqrmxxxcxRspBO apiHzfpqrmxxxcxRspBO = new ApiHzfpqrmxxxcxRspBO();
                apiHzfpqrmxxxcxRspBO.setTotal(queryHzqrxxRes.getTotal());
                apiHzfpqrmxxxcxRspBO.setSize(queryHzqrxxRes.getSize());
                apiHzfpqrmxxxcxRspBO.setCurrent(queryHzqrxxRes.getCurrent());
                apiHzfpqrmxxxcxRspBO.setUuid(hzqrxxRes.getUuid());
                apiHzfpqrmxxxcxRspBO.setHzfpxxqrdbh(hzqrxxRes.getHzfpxxqrdbh());
                apiHzfpqrmxxxcxRspBO.setLrfsf(hzqrxxRes.getLrfsf());
                apiHzfpqrmxxxcxRspBO.setXsfnsrsbh(hzqrxxRes.getXsfnsrsbh());
                apiHzfpqrmxxxcxRspBO.setXsfmc(hzqrxxRes.getXsfmc());
                apiHzfpqrmxxxcxRspBO.setGmfnsrsbh(hzqrxxRes.getGmfnsrsbh());
                apiHzfpqrmxxxcxRspBO.setGmfmc(hzqrxxRes.getGmfmc());
                apiHzfpqrmxxxcxRspBO.setLzqdhm(hzqrxxRes.getLzfphm());
                apiHzfpqrmxxxcxRspBO.setLzkprq(hzqrxxRes.getLzkprq());
                apiHzfpqrmxxxcxRspBO.setLzhjje(hzqrxxRes.getLzhjje());
                apiHzfpqrmxxxcxRspBO.setLzhjse(hzqrxxRes.getLzhjse());
                apiHzfpqrmxxxcxRspBO.setLzfppzdm(hzqrxxRes.getLzfppzDm());
                apiHzfpqrmxxxcxRspBO.setZzsytdm(hzqrxxRes.getZzsytDm());
                apiHzfpqrmxxxcxRspBO.setXfsytdm(hzqrxxRes.getXfsytDm());
                apiHzfpqrmxxxcxRspBO.setFprzztdm(hzqrxxRes.getFprzztDm());
                apiHzfpqrmxxxcxRspBO.setHzcxje(hzqrxxRes.getHzcxje());
                apiHzfpqrmxxxcxRspBO.setHzcxse(hzqrxxRes.getHzcxse());
                apiHzfpqrmxxxcxRspBO.setChyydm(hzqrxxRes.getChyyDm());
                apiHzfpqrmxxxcxRspBO.setHzqrxxztdm(hzqrxxRes.getHzqrxxztDm());
                apiHzfpqrmxxxcxRspBO.setYkjhzfpbz(hzqrxxRes.getYkjhzfpbz());
                List<ApiHzfpqrmxxxcxListRspBO> hzqrxxmxlist = new ArrayList<>();
                if (!ObjectUtils.isEmpty(hzqrxxRes.getHzqrxxmxList()) && hzqrxxRes.getHzqrxxmxList().size() > 0) {
                    for (HzqrxxmxListRes hzqrxxmxRes : hzqrxxRes.getHzqrxxmxList()) {
                        ApiHzfpqrmxxxcxListRspBO apiHzfpqrmxxxcxListRspBO = new ApiHzfpqrmxxxcxListRspBO();
                        apiHzfpqrmxxxcxListRspBO.setXh(hzqrxxmxRes.getXh());
                        apiHzfpqrmxxxcxListRspBO.setLzqdhm(hzqrxxmxRes.getLzfpdm());
                        apiHzfpqrmxxxcxListRspBO.setLzmxxh(hzqrxxmxRes.getLzmxxh());
                        apiHzfpqrmxxxcxListRspBO.setSphfwssflhbbm(hzqrxxmxRes.getSphfwssflhbbm());
                        apiHzfpqrmxxxcxListRspBO.setHwhyslwfwmc(hzqrxxmxRes.getHwhyslwfwmc());
                        apiHzfpqrmxxxcxListRspBO.setSpfwjc(hzqrxxmxRes.getSpfwjc());
                        apiHzfpqrmxxxcxListRspBO.setXmmc(hzqrxxmxRes.getXmmc());
                        apiHzfpqrmxxxcxListRspBO.setGgxh(hzqrxxmxRes.getGgxh());
                        apiHzfpqrmxxxcxListRspBO.setDw(hzqrxxmxRes.getDw());
                        apiHzfpqrmxxxcxListRspBO.setFpspdj(hzqrxxmxRes.getFpspdj());
                        apiHzfpqrmxxxcxListRspBO.setJe(hzqrxxmxRes.getJe());
                        apiHzfpqrmxxxcxListRspBO.setSll(hzqrxxmxRes.getSll());
                        apiHzfpqrmxxxcxListRspBO.setSe(hzqrxxmxRes.getSe());
                        hzqrxxmxlist.add(apiHzfpqrmxxxcxListRspBO);
                    }
                }
                apiHzfpqrmxxxcxRspBO.setHzqrxxmxlist(hzqrxxmxlist);
                list.add(apiHzfpqrmxxxcxRspBO);
            }
        }
        apiHzfpqrxxlbcxRspBO.setRecords(list);
        return apiHzfpqrxxlbcxRspBO;

    }

    /**
     * 3.9 红字发票确认明细信息查询
     */
    public ApiHzfpqrmxxxcxRspBO fillHzfpqrxxmxcxRespParam(QueryHzqrxxRes queryHzqrxxRes) {
        ApiHzfpqrmxxxcxRspBO apiHzfpqrxxlbcxRspBO = new ApiHzfpqrmxxxcxRspBO();
        if (!ObjectUtils.isEmpty(queryHzqrxxRes)) {
            apiHzfpqrxxlbcxRspBO.setUuid(queryHzqrxxRes.getUuid());
            apiHzfpqrxxlbcxRspBO.setHzfpxxqrdbh(queryHzqrxxRes.getHzfpxxqrdbh());
            apiHzfpqrxxlbcxRspBO.setLrfsf(queryHzqrxxRes.getLrfsf());
            apiHzfpqrxxlbcxRspBO.setXsfnsrsbh(queryHzqrxxRes.getXsfnsrsbh());
            apiHzfpqrxxlbcxRspBO.setXsfmc(queryHzqrxxRes.getXsfmc());
            apiHzfpqrxxlbcxRspBO.setGmfnsrsbh(queryHzqrxxRes.getGmfnsrsbh());
            apiHzfpqrxxlbcxRspBO.setGmfmc(queryHzqrxxRes.getGmfmc());
            apiHzfpqrxxlbcxRspBO.setLzqdhm(queryHzqrxxRes.getLzfphm());
            apiHzfpqrxxlbcxRspBO.setLzkprq(queryHzqrxxRes.getLzkprq());
            apiHzfpqrxxlbcxRspBO.setLzhjje(queryHzqrxxRes.getLzhjje());
            apiHzfpqrxxlbcxRspBO.setLzhjse(queryHzqrxxRes.getLzhjse());
            apiHzfpqrxxlbcxRspBO.setLzfppzdm(queryHzqrxxRes.getLzfppzDm());
            apiHzfpqrxxlbcxRspBO.setZzsytdm(queryHzqrxxRes.getZzsytDm());
            apiHzfpqrxxlbcxRspBO.setXfsytdm(queryHzqrxxRes.getXfsytDm());
            apiHzfpqrxxlbcxRspBO.setFprzztdm(queryHzqrxxRes.getFprzztDm());
            apiHzfpqrxxlbcxRspBO.setHzcxje(queryHzqrxxRes.getHzcxje());
            apiHzfpqrxxlbcxRspBO.setHzcxse(queryHzqrxxRes.getHzcxse());
            apiHzfpqrxxlbcxRspBO.setChyydm(queryHzqrxxRes.getChyyDm());
            apiHzfpqrxxlbcxRspBO.setHzqrxxztdm(queryHzqrxxRes.getHzqrxxztDm());
            apiHzfpqrxxlbcxRspBO.setYkjhzfpbz(queryHzqrxxRes.getYkjhzfpbz());
            List<ApiHzfpqrmxxxcxListRspBO> hzqrxxmxlist = new ArrayList<>();
            if (queryHzqrxxRes.getHzqrxxmxList().size() > 0) {
                for (HzqrxxmxListRes hzqrxxmxListRes : queryHzqrxxRes.getHzqrxxmxList()) {
                    ApiHzfpqrmxxxcxListRspBO apiHzfpqrmxxxcxListRspBO = new ApiHzfpqrmxxxcxListRspBO();
                    apiHzfpqrmxxxcxListRspBO.setXh(hzqrxxmxListRes.getXh());
                    apiHzfpqrmxxxcxListRspBO.setLzqdhm(hzqrxxmxListRes.getLzfpdm());
                    apiHzfpqrmxxxcxListRspBO.setLzmxxh(hzqrxxmxListRes.getLzmxxh());
                    apiHzfpqrmxxxcxListRspBO.setSphfwssflhbbm(hzqrxxmxListRes.getSphfwssflhbbm());
                    apiHzfpqrmxxxcxListRspBO.setHwhyslwfwmc(hzqrxxmxListRes.getHwhyslwfwmc());
                    apiHzfpqrmxxxcxListRspBO.setSpfwjc(hzqrxxmxListRes.getSpfwjc());
                    apiHzfpqrmxxxcxListRspBO.setXmmc(hzqrxxmxListRes.getXmmc());
                    apiHzfpqrmxxxcxListRspBO.setGgxh(hzqrxxmxListRes.getGgxh());
                    apiHzfpqrmxxxcxListRspBO.setDw(hzqrxxmxListRes.getDw());
                    apiHzfpqrmxxxcxListRspBO.setFpspdj(hzqrxxmxListRes.getFpspdj());
                    apiHzfpqrmxxxcxListRspBO.setJe(hzqrxxmxListRes.getJe());
                    apiHzfpqrmxxxcxListRspBO.setSll(hzqrxxmxListRes.getSll());
                    apiHzfpqrmxxxcxListRspBO.setSe(hzqrxxmxListRes.getSe());
                    hzqrxxmxlist.add(apiHzfpqrmxxxcxListRspBO);
                }
            }
            apiHzfpqrxxlbcxRspBO.setHzqrxxmxlist(hzqrxxmxlist);
        }
        return apiHzfpqrxxlbcxRspBO;
    }

    private String slTranst(String sl) {
        Float f = 0.00f;
        if (!StringUtils.isEmpty(sl)) {
            if (sl.contains("%")) {
                sl = sl.replace("%", "");
                f = Float.valueOf(sl) / 100;
            } else if ("免税".equals(sl) || "不征税".equals(sl)) {
                return sl;
            } else {
                return Float.valueOf(sl).toString();
            }
        }
        return f.toString();
    }

    private Date transDate(String kprqStr) {
        // String time = "2020-02-13 16:01:30";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(kprqStr);
        } catch (ParseException e) {
            log.error("日期转换失败");
        }
        return date;
    }

    public Map<String, String> checkInvoiceIssueInfoParam(InvoiceIssueInfoParam invoiceIssueInfoParam) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        //
        //OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectAllList();
        OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(invoiceIssueInfoParam.getNsrsbh());
        if (ObjectUtils.isEmpty(orderInvoiceConfigEntity)) {
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JFSJH_ERROR_9627.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JFSJH_ERROR_9627.getMessage());
            return checkResultMap;
        }

        if (Objects.nonNull(orderInvoiceConfigEntity)) {
            if ("0".equals(orderInvoiceConfigEntity.getGfjfsj()) && StringUtils.isEmpty(invoiceIssueInfoParam.getJfsjh())) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JFFYX_ERROR_9625.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JFFYX_ERROR_9625.getMessage());
                return checkResultMap;
            }
            if ("0".equals(orderInvoiceConfigEntity.getGfjhyx()) && StringUtils.isEmpty(invoiceIssueInfoParam.getJfyx())) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JFSJH_ERROR_9626.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JFSJH_ERROR_9626.getMessage());
                return checkResultMap;
            }
        }
        // 销货方纳税人识别号
        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_1_ERROR_945501,
                OrderInfoContentEnum.RED_INVOICE_NSRSBH_LENTH_2_ERROR_935502, OrderInfoContentEnum.RED_INVOICE_NSRSBH_FORM_ERROR_935503,
                invoiceIssueInfoParam.getNsrsbh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 订单请求流水号 长度和空值
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_DDQQLSH_ERROR_9451, invoiceIssueInfoParam.getDdqqlsh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.queryStatusByDdqqlshAndNsrsbh(invoiceIssueInfoParam.getDdqqlsh(), invoiceIssueInfoParam.getNsrsbh());
        if (Objects.nonNull(orderInvoiceInfoEntity)) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_FPQQLSHWY_ERROR_9473);
        }
        // 订单号
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_DDH_ERROR_9603, invoiceIssueInfoParam.getDdh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.queryStatusByDdh(invoiceIssueInfoParam.getDdh(), invoiceIssueInfoParam.getNsrsbh());
        if (Objects.nonNull(invoiceInfoEntity)) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_DDH_ERROR_9612);
        }
        // 发票类型代码
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_FPLXDM_ERROR_9452, invoiceIssueInfoParam.getFplxdm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 开票方式  0 自动开票  1 手动开票    默认为自动开票
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_KPFS_ERROR_9453, invoiceIssueInfoParam.getKpfs());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_KPFS_ERROR_9453);
        }
        // 购买方纳税人识别号  当发票类型为 专票时必填
        if (InvoiceTypeEnum.ORDER_INVOICE_TYPE_002.getKey().equals(invoiceIssueInfoParam.getFplxdm())) {
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.RED_INVOICE_GMFNSRSBH_LENTH_ERROR_9618, invoiceIssueInfoParam.getGmfsbh());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            if (!StringUtils.isEmpty(invoiceIssueInfoParam.getGmfsbh())) {
                // 空格
                if (invoiceIssueInfoParam.getGmfsbh().contains(ConfigureConstant.STRING_SPACE)) {
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107164.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107164.getMessage());
                    return checkResultMap;
                }
                // 纳税人识别号需要全部大写
                if (!ValidateUtil.isAcronym(invoiceIssueInfoParam.getGmfsbh())) {
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107163.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107163.getMessage());
                    return checkResultMap;
                }
            }
        }
        if (InvoiceTypeEnum.ORDER_INVOICE_TYPE_001.getKey().equals(invoiceIssueInfoParam.getFplxdm())) {
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFNSRSBH_ERROR_9455, invoiceIssueInfoParam.getGmfsbh());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            // 空格
            if (invoiceIssueInfoParam.getGmfsbh().contains(ConfigureConstant.STRING_SPACE)) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107164.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107164.getMessage());
                return checkResultMap;
            }
            // 纳税人识别号需要全部大写
            if (!ValidateUtil.isAcronym(invoiceIssueInfoParam.getGmfsbh())) {
                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107163.getKey());
                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107163.getMessage());
                return checkResultMap;
            }
        }
        // 购买方名称
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFMC_ERROR_9456, invoiceIssueInfoParam.getGmfmc());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 购买方地址
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFDZ_ERROR_9457, invoiceIssueInfoParam.getGmfdz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 购买方电话
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFDH_ERROR_9458, invoiceIssueInfoParam.getGmfdh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 购买方银行名称
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFYH_ERROR_9459, invoiceIssueInfoParam.getGmfyh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 购买方银行账号
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFZH_ERROR_9460, invoiceIssueInfoParam.getGmfzh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 购方手机号
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFSJH_ERROR_9604, invoiceIssueInfoParam.getGmjfsjh());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 购方邮箱
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GMFYX_ERROR_9605, invoiceIssueInfoParam.getGmjfyx());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 经办人姓名
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_JBRXM_ERROR_9461, invoiceIssueInfoParam.getJbrxm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 经办人证件类型
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_JBRZJLX_ERROR_9462, invoiceIssueInfoParam.getJbrzjlx());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 经办人证件号码
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_JBRZJHM_ERROR_9463, invoiceIssueInfoParam.getJbrzjhm());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 开票人
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_KPR_ERROR_9464, invoiceIssueInfoParam.getKpr());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 含税标志
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_HSBZ_ERROR_9465, invoiceIssueInfoParam.getHsbz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 合计金额
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_HJJE_ERROR_9467, invoiceIssueInfoParam.getHjje());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(invoiceIssueInfoParam.getHjje())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_HJJE_2_ERROR_9470);
        }
        // 合计税额
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_HJSE_ERROR_9468, invoiceIssueInfoParam.getHjse());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(invoiceIssueInfoParam.getHjse())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_HJSE_2_ERROR_9471);
        }
        // 价税合计
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_JSHJ_ERROR_9466, invoiceIssueInfoParam.getJshj());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        if (ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(invoiceIssueInfoParam.getJshj())) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_JSHJ_2_ERROR_9469);
        }
        // 不含税计算价税合计跟 传入的作比较
        if ("0".equals(invoiceIssueInfoParam.getHsbz())) {
            // 价税合计  =  合计金额+合计税额
            BigDecimal hjje = new BigDecimal(invoiceIssueInfoParam.getHjje());
            BigDecimal hjse = new BigDecimal(invoiceIssueInfoParam.getHjse());
            BigDecimal add = hjje.add(hjse);

            BigDecimal jshj = new BigDecimal(invoiceIssueInfoParam.getJshj());
            if (add.compareTo(jshj) != 0) {
                Map<String, String> map = new HashMap<>(5);
                map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JSHJ_CHECK_9613.getKey());
                map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JSHJ_CHECK_9613.getMessage());
                return map;
            }

        }
        // 含税
        if ("1".equals(invoiceIssueInfoParam.getHsbz())) {
            // 价税合计  =  合计金额+合计税额
            BigDecimal hjje = new BigDecimal(invoiceIssueInfoParam.getHjje());
            BigDecimal hjse = new BigDecimal(invoiceIssueInfoParam.getHjse());
            BigDecimal add = hjje.add(hjse);

            BigDecimal jshj = new BigDecimal(invoiceIssueInfoParam.getJshj());
            if (add.compareTo(jshj) != 0) {
                Map<String, String> map = new HashMap<>(5);
                map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JSHJ_CHECK_9613.getKey());
                map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JSHJ_CHECK_9613.getMessage());
                return map;
            }

        }
        // 备注
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_BZ_ERROR_9472, invoiceIssueInfoParam.getBz());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }

        /**
         * 发票附加信息(多条)
         */
        List<InvoiceIssueAdditionParam> ddfjxx = invoiceIssueInfoParam.getDdfjxx();
        if (!CollectionUtils.isEmpty(ddfjxx)) {
            for (InvoiceIssueAdditionParam invoiceIssueAdditionParam : ddfjxx) {
                // 数据类型
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_SJNR_ERROR_9476, invoiceIssueAdditionParam.getSjnr());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    return checkResultMap;
                }
                // 附加信息名称
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_FJXXMC_ERROR_9474, invoiceIssueAdditionParam.getFjxxmc());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    return checkResultMap;
                }
                if ("1".equals(invoiceIssueAdditionParam.getSjnr())) {
                    // 附加信息值
                    checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_FJXXZ_ERROR_9475, invoiceIssueAdditionParam.getFjxxz());
                    if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                        return checkResultMap;
                    }
                } else if ("2".equals(invoiceIssueAdditionParam.getSjnr())) {
                    // 数值型
                    Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
                    boolean matches = pattern.matcher(invoiceIssueAdditionParam.getFjxxz()).matches();
                    if (!matches) {
                        Map<String, String> map = new HashMap<>(5);
                        map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_SJLX_CHECK_9621.getKey());
                        map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_SJLX_CHECK_9621.getMessage());
                        return map;
                    }

                } else if ("3".equals(invoiceIssueAdditionParam.getSjnr())) {
                    //日期型
                    boolean validDate = isValidDate(invoiceIssueAdditionParam.getFjxxz());
                    if (!validDate) {
                        Map<String, String> map = new HashMap<>(5);
                        map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_SJLX_CHECK_9622.getKey());
                        map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_SJLX_CHECK_9622.getMessage());
                        return map;
                    }

                }
            }
        }
        /**
         * 项目信息(发票明细)(多条):商品明细不可传成品油和非成品油混合项目
         */
        List<InvoiceIssueItemParam> ddmxxx = invoiceIssueInfoParam.getDdmxxx();
        if (CollectionUtils.isEmpty(ddmxxx)) {
            return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_DDMXXXISNULL_ERROR_9477);
        }
        for (InvoiceIssueItemParam invoiceIssueItemParam : ddmxxx) {
            // 折扣方式
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_ZKFS_ERROR_9478, invoiceIssueItemParam.getZkfs());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            // 折扣大小
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_ZKDX_ERROR_9479, invoiceIssueItemParam.getZkdx());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            // 商品税收分类编码
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_SPBM_ERROR_9480, invoiceIssueItemParam.getSpbm());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getSpbm())) {
                TaxClassCodeEntity taxClassCodeSpjc = taxClassCodeDao.getTaxClassCodeSpjc(invoiceIssueItemParam.getSpbm());
                if (Objects.isNull(taxClassCodeSpjc)) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SPBM_1_ERROR_9492);
                }
            }
            // 项目名称
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_XMMC_ERROR_9481, invoiceIssueItemParam.getXmmc());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
//            if (!StringUtils.isEmpty(invoiceIssueItemParam.getXmmc())) {
//                TaxClassCodeEntity taxClassCodeSpjc = taxClassCodeDao.getTaxClassCodeXmmc(invoiceIssueItemParam.getXmmc());
//                if (Objects.isNull(taxClassCodeSpjc)) {
//                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SPMC_1_ERROR_9624);
//                }
//            }
            // 规格型号
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_GGXH_ERROR_9482, invoiceIssueItemParam.getGgxh());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            // 单位
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_DW_ERROR_9483, invoiceIssueItemParam.getDw());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            // 商品数量
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getSpsl())) {
                if (!invoiceIssueItemParam.getSpsl().matches(PatternConstant.PATTERN_XMSL)) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SPSL_ERROR_9484);
                }
            }
            // 单价
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getDj())) {
                if (!invoiceIssueItemParam.getDj().matches(PatternConstant.PATTERN_XMDJ)) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SPDJ_ERROR_9485);
                }
            }
            // 金额
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_JE_ERROR_9486, invoiceIssueItemParam.getJe());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            if (!invoiceIssueItemParam.getJe().matches(PatternConstant.PATTERN_XMJE)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_JE_2_ERROR_9487);
            }

            if (!StringUtils.isEmpty(invoiceIssueItemParam.getZkfs())) {
                if (!"01".equals(invoiceIssueItemParam.getZkfs()) && !"02".equals(invoiceIssueItemParam.getZkfs())) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_ZKFS_1_ERROR_9493);
                }
                if (!StringUtils.isEmpty(invoiceIssueItemParam.getZkdx()) && "01".equals(invoiceIssueItemParam.getZkfs())) {
                    // 折扣方式为 金额折扣
                    BigDecimal zkdx = new BigDecimal(invoiceIssueItemParam.getZkdx());
                    BigDecimal je = new BigDecimal(invoiceIssueItemParam.getJe());
                    // if (zkdx <= 0 || zkdx > je) {
                    if (zkdx.compareTo(BigDecimal.ZERO) <= 0 || zkdx.compareTo(je) > 0) {
                        return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_JE_1_ERROR_9494);
                    }
                }
                if (!StringUtils.isEmpty(invoiceIssueItemParam.getZkdx()) && "02".equals(invoiceIssueItemParam.getZkfs())) {
                    // 折扣方式为 比例折扣
                    BigDecimal zkdx = new BigDecimal(invoiceIssueItemParam.getZkdx());
                    BigDecimal bif = new BigDecimal("100");
                    if (zkdx.compareTo(BigDecimal.ZERO) <= 0 || zkdx.compareTo(bif) > 0) {
                        return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_BL_1_ERROR_9495);
                    }
                }
            }
            // 如果数量 单价不为空  重新计算金额
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getSpsl()) && !StringUtils.isEmpty(invoiceIssueItemParam.getDj())) {
                BigDecimal spsl = new BigDecimal(invoiceIssueItemParam.getSpsl());
                BigDecimal dj = new BigDecimal(invoiceIssueItemParam.getDj());
                BigDecimal je = spsl.multiply(dj).setScale(2, BigDecimal.ROUND_HALF_UP);

                BigDecimal spje = new BigDecimal(invoiceIssueItemParam.getJe());
                if (je.compareTo(spje) != 0) {
                    Map<String, String> map = new HashMap<>(5);
                    map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JE_CHECK_9614.getKey());
                    map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JE_CHECK_9614.getMessage());
                    return map;
                }
            }
            // 如果 数量为空 单价不为空 金额不为空
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getJe()) && !StringUtils.isEmpty(invoiceIssueItemParam.getDj())) {
                BigDecimal je = new BigDecimal(invoiceIssueItemParam.getJe());
                BigDecimal dj = new BigDecimal(invoiceIssueItemParam.getDj());

                BigDecimal spsl = je.divide(dj, 6, RoundingMode.HALF_UP);
                BigDecimal slsp = new BigDecimal(invoiceIssueItemParam.getSpsl());
                if (spsl.compareTo(slsp) != 0) {
                    Map<String, String> map = new HashMap<>(5);
                    map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_SPSL_CHECK_9615.getKey());
                    map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_SPSL_CHECK_9615.getMessage());
                    return map;
                }

            }
            // 如果 单价为空 数量不为空 金额不为空
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getJe()) && !StringUtils.isEmpty(invoiceIssueItemParam.getSpsl())) {
                BigDecimal je = new BigDecimal(invoiceIssueItemParam.getJe());
                BigDecimal spsl = new BigDecimal(invoiceIssueItemParam.getSpsl());

                BigDecimal dj = je.divide(spsl, 6, RoundingMode.HALF_UP);
                BigDecimal spdj = new BigDecimal(invoiceIssueItemParam.getDj());
                if (dj.compareTo(spdj) != 0) {
                    Map<String, String> map = new HashMap<>(5);
                    map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_SPDJ_CHECK_9616.getKey());
                    map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_SPDJ_CHECK_9616.getMessage());
                    return map;
                }

            }

            // 税率
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_SL_ERROR_9488, invoiceIssueItemParam.getSl());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            if (!invoiceIssueItemParam.getSl().matches(PatternConstant.PATTERN_SL)) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SL_2_ERROR_9502);
            }

            // 税额
            checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_SE_ERROR_9489, invoiceIssueItemParam.getSe());
            if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                return checkResultMap;
            }
            if (invoiceIssueInfoParam.getHsbz().equals("0") && StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SE_3_ERROR_9491);
            }
            if (!StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                if (ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(invoiceIssueItemParam.getSe())) {
                    return CheckParamUtil.generateErrorMap(OrderInfoContentEnum.BULE_INVOICE_SE_2_ERROR_9490);
                }
            }
            // 不含税------计算税额
            if ("0".equals(invoiceIssueInfoParam.getHsbz())) {
                // 计算税额 = 金额 * 税率
                BigDecimal je = new BigDecimal(invoiceIssueItemParam.getJe());
                BigDecimal sl = new BigDecimal(invoiceIssueItemParam.getSl());
                BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_HALF_UP);

                BigDecimal xmse = new BigDecimal(invoiceIssueItemParam.getSe());
                if (xmse.compareTo(se) != 0) {
                    Map<String, String> map = new HashMap<>(5);
                    map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_XMSE_CHECK_9617.getKey());
                    map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_XMSE_CHECK_9617.getMessage());
                    return map;
                }

            } else {
                if (!StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                    // 含税
                    BigDecimal je = new BigDecimal(invoiceIssueItemParam.getJe());
                    // 税率
                    String s = slTranst(invoiceIssueItemParam.getSl());
                    BigDecimal bigDecimalSL = new BigDecimal(s);
                    BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                    // 不含税金额
                    BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                    // 税额
                    BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal xmse = new BigDecimal(invoiceIssueItemParam.getSe());
                    if (xmse.compareTo(se) != 0) {
                        Map<String, String> map = new HashMap<>(5);
                        map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_XMSE_CHECK_9617.getKey());
                        map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_XMSE_CHECK_9617.getMessage());
                        return map;
                    }
                }
            }
        }
        // 跟传入的价税合计做比对
        List<BigDecimal> bje = new ArrayList<>();
        List<BigDecimal> bse = new ArrayList<>();
        OrderInvoiceInfoEntity entity = new OrderInvoiceInfoEntity();
        if (invoiceIssueInfoParam.getHsbz().equals("0")) {
            // 不含税 比较价税合计和 合计金额 和税额、
            if (!CollectionUtils.isEmpty(invoiceIssueInfoParam.getDdmxxx())) {
                List<InvoiceIssueItemParam> mxxx = invoiceIssueInfoParam.getDdmxxx();
                List<OrderInvoiceItemEntity> invoiceItemEntities = new ArrayList<>();
                for (int i = 0; i < mxxx.size(); i++) {
                    InvoiceIssueItemParam invoiceIssueItemParam = mxxx.get(i);
                    OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                    orderInvoiceItemEntity.setXmsl(invoiceIssueItemParam.getSpsl());
                    orderInvoiceItemEntity.setFphxz("0");
                    orderInvoiceItemEntity.setDj(invoiceIssueItemParam.getDj());
                    orderInvoiceItemEntity.setJe(invoiceIssueItemParam.getJe());
                    orderInvoiceItemEntity.setSl(invoiceIssueItemParam.getSl());

                    if (!StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                        orderInvoiceItemEntity.setSe(invoiceIssueItemParam.getSe());
                    } else {
                        // 不含税
                        if ("0".equals(invoiceIssueInfoParam.getHsbz())) {
                            BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                            String s = slTranst(orderInvoiceItemEntity.getSl());
                            BigDecimal sl = new BigDecimal(s);
                            BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                            orderInvoiceItemEntity.setSe(se.toString());
                        }
                    }
                    // 折扣方式
                    orderInvoiceItemEntity.setByzd2(invoiceIssueItemParam.getZkfs());
                    // 折扣大小
                    orderInvoiceItemEntity.setByzd3(invoiceIssueItemParam.getZkdx());

                    // 按金额折扣
                    if ("01".equals(orderInvoiceItemEntity.getByzd2())) {
                        // 被折扣行
                        orderInvoiceItemEntity.setFphxz("2");
                        invoiceItemEntities.add(orderInvoiceItemEntity);
                        // 折扣行
                        OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                        invoiceItemEntity.setFphxz("1");
                        invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                        invoiceItemEntity.setJe("-" + orderInvoiceItemEntity.getByzd3());
                        // 计算不含税 税额
                        if ("0".equals(invoiceIssueInfoParam.getHsbz())) {
                            BigDecimal bigDecimalSl = new BigDecimal(invoiceItemEntity.getSl());
                            BigDecimal bigDecimalJe = new BigDecimal(invoiceItemEntity.getJe());
                            BigDecimal se = bigDecimalJe.multiply(bigDecimalSl).setScale(2, BigDecimal.ROUND_HALF_UP);
                            invoiceItemEntity.setSe(se.toString());
                        }
                        invoiceItemEntities.add(invoiceItemEntity);

                        // 比例折扣
                    } else if ("02".equals(orderInvoiceItemEntity.getByzd2())) {
                        // 被折扣行
                        orderInvoiceItemEntity.setFphxz("2");
                        invoiceItemEntities.add(orderInvoiceItemEntity);
                        // 根据比例 计算金额
                        BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                        String bl = orderInvoiceItemEntity.getByzd3();
                        Double d = Double.valueOf(bl) / 100;
                        String xs = String.valueOf(d);

                        BigDecimal bigDecimal = new BigDecimal(xs);
                        // 折扣额
                        BigDecimal zke = je.multiply(bigDecimal).setScale(2, BigDecimal.ROUND_HALF_UP);
                        // 折扣行
                        OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                        invoiceItemEntity.setFphxz("1");
                        invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                        invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                        invoiceItemEntity.setJe("-" + zke.toString());
                        // 计算不含税 税额
                        if ("0".equals(invoiceIssueInfoParam.getHsbz())) {
                            BigDecimal bigDecimalSl = new BigDecimal(invoiceItemEntity.getSl());
                            BigDecimal bigDecimalJe = new BigDecimal(invoiceItemEntity.getJe());
                            BigDecimal se = bigDecimalJe.multiply(bigDecimalSl).setScale(2, BigDecimal.ROUND_HALF_UP);
                            invoiceItemEntity.setSe(se.toString());
                        }
                        invoiceItemEntities.add(invoiceItemEntity);
                    } else {
                        // 无折扣行
                        invoiceItemEntities.add(orderInvoiceItemEntity);
                    }
                }
                for (OrderInvoiceItemEntity orderInvoiceItemEntity : invoiceItemEntities) {
                    bje.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                    bse.add(new BigDecimal(orderInvoiceItemEntity.getSe()));
                }
            }
            if (invoiceIssueInfoParam.getHsbz().equals("0")) {
                // 不含税 计算合计金额
                BigDecimal hjje = bje.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                entity.setHjbhsje(hjje.toString());
                // 不含税 计算合计税额
                BigDecimal hjse = bse.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                entity.setKpse(hjse.toString());
                // 价税合计
                BigDecimal jshj = hjje.add(hjse);
                entity.setJshj(jshj.toString());
            }

        } else {
            // 明细信息入库
            if (!CollectionUtils.isEmpty(invoiceIssueInfoParam.getDdmxxx())) {
                List<InvoiceIssueItemParam> mxxx = invoiceIssueInfoParam.getDdmxxx();
                List<OrderInvoiceItemEntity> invoiceItemEntities = new ArrayList<>();
                for (int i = 0; i < mxxx.size(); i++) {
                    InvoiceIssueItemParam invoiceIssueItemParam = mxxx.get(i);
                    OrderInvoiceItemEntity orderInvoiceItemEntity = new OrderInvoiceItemEntity();
                    orderInvoiceItemEntity.setXmsl(invoiceIssueItemParam.getSpsl());
                    orderInvoiceItemEntity.setFphxz("0");
                    orderInvoiceItemEntity.setDj(invoiceIssueItemParam.getDj());
                    orderInvoiceItemEntity.setJe(invoiceIssueItemParam.getJe());
                    orderInvoiceItemEntity.setSl(invoiceIssueItemParam.getSl());

                    if (!StringUtils.isEmpty(invoiceIssueItemParam.getSe())) {
                        orderInvoiceItemEntity.setSe(invoiceIssueItemParam.getSe());
                    } else {

                        // 含税金额
                        BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                        // 税率
                        String s = slTranst(orderInvoiceItemEntity.getSl());
                        BigDecimal bigDecimalSL = new BigDecimal(s);
                        BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                        BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                        // 税额
                        BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                        orderInvoiceItemEntity.setSe(se.toString());
                    }
                    orderInvoiceItemEntity.setCreateTime(new Date());
                    // 折扣方式
                    orderInvoiceItemEntity.setByzd2(invoiceIssueItemParam.getZkfs());
                    // 折扣大小
                    orderInvoiceItemEntity.setByzd3(invoiceIssueItemParam.getZkdx());

                    // 按金额折扣
                    if ("01".equals(orderInvoiceItemEntity.getByzd2())) {
                        // 被折扣行
                        orderInvoiceItemEntity.setFphxz("2");
                        invoiceItemEntities.add(orderInvoiceItemEntity);
                        // 折扣行
                        OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                        invoiceItemEntity.setFphxz("1");
                        invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                        invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                        invoiceItemEntity.setJe("-" + orderInvoiceItemEntity.getByzd3());

                        //折扣含税
                        BigDecimal je = new BigDecimal(invoiceItemEntity.getJe());
                        // 税率
                        String s = slTranst(invoiceItemEntity.getSl());
                        BigDecimal bigDecimalSL = new BigDecimal(s);
                        BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                        BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                        // 税额
                        BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                        invoiceItemEntity.setSe(se.toString());
                        invoiceItemEntities.add(invoiceItemEntity);

                        // 比例折扣
                    } else if ("02".equals(orderInvoiceItemEntity.getByzd2())) {
                        // 被折扣行
                        orderInvoiceItemEntity.setFphxz("2");
                        invoiceItemEntities.add(orderInvoiceItemEntity);
                        // 根据比例 计算金额
                        BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                        String bl = orderInvoiceItemEntity.getByzd3();
                        Double d = Double.valueOf(bl) / 100;
                        String xs = String.valueOf(d);

                        BigDecimal bigDecimal = new BigDecimal(xs);
                        // 折扣额
                        BigDecimal zke = je.multiply(bigDecimal).setScale(2, BigDecimal.ROUND_HALF_UP);
                        // 折扣行
                        OrderInvoiceItemEntity invoiceItemEntity = new OrderInvoiceItemEntity();
                        invoiceItemEntity.setFphxz("1");
                        invoiceItemEntity.setSl(orderInvoiceItemEntity.getSl());
                        invoiceItemEntity.setSpbm(orderInvoiceItemEntity.getSpbm());
                        invoiceItemEntity.setJe("-" + zke.toString());
                        BigDecimal jej = new BigDecimal(invoiceItemEntity.getJe());
                        // 税率
                        String s = slTranst(invoiceItemEntity.getSl());
                        BigDecimal bigDecimalSL = new BigDecimal(s);
                        BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                        BigDecimal divide = jej.divide(add, 2, RoundingMode.HALF_UP);
                        // 税额
                        BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                        invoiceItemEntity.setSe(se.toString());

                        invoiceItemEntities.add(invoiceItemEntity);
                    } else {
                        // 无折扣行
                        invoiceItemEntities.add(orderInvoiceItemEntity);
                    }
                }
                for (OrderInvoiceItemEntity orderInvoiceItemEntity : invoiceItemEntities) {
                    bje.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                    bse.add(new BigDecimal(orderInvoiceItemEntity.getSe()));
                }
            }

            // 含税
            BigDecimal hjjee = bje.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 含税 税额
            BigDecimal hjsee = bse.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
            entity.setKpse(hjsee.toString());
            // 计算不含税金额
            BigDecimal subtract = hjjee.subtract(hjsee);
            entity.setHjbhsje(subtract.toString());
            // 价税合计
            entity.setJshj(subtract.add(hjsee).toString());

        }

        // 合计金额
        BigDecimal hjje = new BigDecimal(entity.getHjbhsje());
        BigDecimal hjjeWb = new BigDecimal(invoiceIssueInfoParam.getHjje());
        BigDecimal subtract = hjje.subtract(hjjeWb);
        BigDecimal abs = subtract.abs();
        BigDecimal one = new BigDecimal("1");

        if (abs.compareTo(one) > 0) {
            Map<String, String> map = new HashMap<>(5);
            map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_HJJE_CHECK_9618.getKey());
            map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_HJJE_CHECK_9618.getMessage());
            return map;
        }
        // 合计税额
        BigDecimal hjse = new BigDecimal(entity.getKpse());
        BigDecimal hjseWb = new BigDecimal(invoiceIssueInfoParam.getHjse());
        BigDecimal see = hjse.subtract(hjseWb);
        BigDecimal decimal = see.abs();
        if (decimal.compareTo(one) > 0) {
            Map<String, String> map = new HashMap<>(5);
            map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_HJSE_CHECK_9619.getKey());
            map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_HJSE_CHECK_9619.getMessage());
            return map;
        }

        BigDecimal jshj = new BigDecimal(entity.getJshj());
        BigDecimal jshjWb = new BigDecimal(invoiceIssueInfoParam.getJshj());
        if (jshj.compareTo(jshjWb) != 0) {
            Map<String, String> map = new HashMap<>(5);
            map.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.BULE_INVOICE_JSHJ_CHECK_9620.getKey());
            map.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.BULE_INVOICE_JSHJ_CHECK_9620.getMessage());
            return map;
        }
        return null;
    }

    public static boolean isValidDate(String dateStr) {
        boolean judgeresult = true;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            format.setLenient(false);
            Date date = format.parse(dateStr);
        } catch (Exception e) {
            judgeresult = false;
        }
        if (judgeresult) {
            String yearStr = dateStr.split("-")[0];
            if (yearStr.startsWith("0") || yearStr.length() != 4) {
                judgeresult = false;
            }
        }
        return judgeresult;
    }

    private void updateInvoiceInfoByFphm(List<QueryHzqrxxRes> records) {
        //对返回报文进行分组
        Map<String, List<QueryHzqrxxRes>> listMap = records.stream().collect(Collectors.groupingBy(QueryHzqrxxRes::getLzfphm));
        for (String s : listMap.keySet()) {
            List<String> qrdDmList = listMap.get(s).stream().map(QueryHzqrxxRes::getHzqrxxztDm).distinct().collect(Collectors.toList());
            List<String> sfkpList = listMap.get(s).stream().map(QueryHzqrxxRes::getYkjhzfpbz).distinct().collect(Collectors.toList());
            //有01-04 并且是未开票   ch_bz更新为 5  冲红中
            log.info("发票号码:{},对应的确认单状态:{} 确认单对应的开票状态{}", s, qrdDmList.toString(), sfkpList.toString());
            if (qrdDmList.toString().contains("01") || qrdDmList.toString().contains("02") || qrdDmList.toString().contains("03") || qrdDmList.toString().contains("04")) {
                if (sfkpList.toString().contains("N")) {
                    List<OrderInvoiceInfoEntity> invoiceInfoByQdfphm = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(s);
                    if (invoiceInfoByQdfphm.size() > 0) {
                        //更新蓝字发票 ch_bz 为5   冲红中
                        invoiceInfoByQdfphm.forEach(x -> {
                            log.info("蓝票发票号码{}需要更新冲红标志为5 冲红中", s);
                            x.setChBz("5");
                            orderInvoiceInfoDao.updateById(x);
                        });
                    }
                }
            } else {
                //ch_bz更新为 0
                List<OrderInvoiceInfoEntity> invoiceInfoByQdfphm = orderInvoiceInfoDao.getInvoiceInfoByQdfphm(s);
                if (invoiceInfoByQdfphm.size() > 0) {
                    //更新蓝字发票 ch_bz 为0
                    invoiceInfoByQdfphm.forEach(x -> {
                        log.info("蓝票发票号码{}需要更新冲红标志为0 正常状态", s);
                        x.setChBz("0");
                        orderInvoiceInfoDao.updateById(x);
                    });
                }
            }
        }
    }

    // 不足两位小数补0
    private String formatDecimal(BigDecimal bc) {
        // 不足两位小数补0
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(bc);
    }

    private String getDdh() {
        StringBuilder dd = new StringBuilder("DD");
        String date = System.currentTimeMillis() + "";
        int i = (int) ((Math.random() * 9 + 1) * 1000);
        StringBuilder append = dd.append(date).append(i);
        return append.toString();
    }
}
