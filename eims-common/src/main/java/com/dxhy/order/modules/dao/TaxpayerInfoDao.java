package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.TaxpayerInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
* <AUTHOR>
* @description 针对表【taxpayer_info(销方企业信息表)】的数据库操作Mapper
* @createDate 2022-06-29 17:13:17
*/
@Mapper
public interface TaxpayerInfoDao extends BaseMapper<TaxpayerInfo> {

    TaxpayerInfo selectZsxedByNsrsbh(@Param("nsrsbh") String nsrsbh);

    TaxpayerInfo selectKeyBySecretId(@Param("secretId") String secretId);
}
