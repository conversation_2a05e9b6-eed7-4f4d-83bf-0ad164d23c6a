package com.dxhy.order.modules.pojo.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 场景模板表列表VO
 * <AUTHOR>
 * @Date 2022/6/27 12:04
 * @Version 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("场景模板表列表VO")
public class SceneTemplateListVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 场景模板主键
     */
    @ApiModelProperty("场景模板主键")
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 名字
     */
    @ApiModelProperty("名字")
    private String name;

    /**
     * 附加信息名称
     */
    @ApiModelProperty("附加信息名称")
    private List<String> fjxxmc;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 清理null
     * @param
     * @return void
     * <AUTHOR>
     **/
    public void clearNull(){
        if(StringUtils.isEmpty(this.id)){
            this.id = StringUtils.EMPTY;
        }
        if(StringUtils.isEmpty(this.name)){
            this.name = StringUtils.EMPTY;
        }
        if(StringUtils.isEmpty(this.createBy)){
            this.createBy = StringUtils.EMPTY;
        }
        if(StringUtils.isEmpty(this.updateBy)){
            this.updateBy = StringUtils.EMPTY;
        }
    }

}
