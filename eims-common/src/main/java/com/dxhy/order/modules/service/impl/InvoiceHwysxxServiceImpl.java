package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceHwysxxDao;
import com.dxhy.order.modules.entity.InvoiceHwysxxEntity;
import com.dxhy.order.modules.service.InvoiceHwysxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 货物运输信息的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceHwysxxService")
@Slf4j
@RefreshScope
public class InvoiceHwysxxServiceImpl extends ServiceImpl<InvoiceHwysxxDao, InvoiceHwysxxEntity>
        implements InvoiceHwysxxService {

    @Override
    public void saveBatch(List<InvoiceHwysxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceHwysxxEntity> hwysxxWrapper = Wrappers.lambdaUpdate();
        hwysxxWrapper.set(InvoiceHwysxxEntity::getIsDelete, "1");
        hwysxxWrapper.eq(InvoiceHwysxxEntity::getOrderInvoiceInfoId, invoiceId);
        hwysxxWrapper.eq(InvoiceHwysxxEntity::getInvoiceTdywId, tdywId);
        this.update(hwysxxWrapper);
    }
}




