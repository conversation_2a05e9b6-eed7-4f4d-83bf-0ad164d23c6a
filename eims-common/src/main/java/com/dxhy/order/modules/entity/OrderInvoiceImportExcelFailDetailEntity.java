package com.dxhy.order.modules.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class OrderInvoiceImportExcelFailDetailEntity {
    // 订单号
    @ExcelProperty(value = "订单号*",index = 0)
    private String ddh;
    // 发票类型
    @ExcelProperty(value = "发票类型*",index = 1)
    private String fplx;
    // 特殊票种
    @ExcelProperty(value = "特殊票种*",index = 2)
    private String tspz;
    // 抬头类型
    @ExcelProperty(value = "抬头类型*",index = 3)
    private String ttlx;
    // 购方编码
    @ExcelProperty(value = "购方编码",index = 4)
    private String ghfId;
    // 购方名称
    @ExcelProperty(value = "购方名称",index = 5)
    private String ghfMc;
    // 购方税号
    @ExcelProperty(value = "购方税号",index = 6)
    private String ghfNsrsbh;
    // 购方地址
    @ExcelProperty(value = "购方地址",index = 7)
    private String ghfDz;
    // 购方电话
    @ExcelProperty(value = "购方电话",index = 8)
    private String ghfDh;
    // 开户银行
    @ExcelProperty(value = "开户银行",index = 9)
    private String ghfYh;
    // 银行账号
    @ExcelProperty(value = "银行账号",index = 10)
    private String ghfZh;
    // 邮箱
    @ExcelProperty(value = "邮箱",index = 11)
    private String ghfYx;
    // 商品名称
    @ExcelProperty(value = "商品名称*",index = 12)
    private String xmmc;
    // 规格型号
    @ExcelProperty(value = "规格型号",index = 13)
    private String ggxh;
    // 单位
    @ExcelProperty(value = "单位",index = 14)
    private String dw;
    // 数量
    @ExcelProperty(value = "数量",index = 15)
    private String xmsl;
    // 单价
    @ExcelProperty(value = "单价",index = 16)
    private String dj;
    // 金额
    @ExcelProperty(value = "金额*",index = 17)
    private String je;
    // 含税标志
    @ExcelProperty(value = "含税标志*",index = 18)
    private String hsbz;
    // 税率
    @ExcelProperty(value = "税率",index = 19)
    private String sl;
    // 税额
    @ExcelProperty(value = "税额",index = 20)
    private String se;
    // 编码版本号
    @ExcelProperty(value = "编码版本号",index = 21)
    private String bmbbh;
    // 税收分类编码
    @ExcelProperty(value = "税收分类编码",index = 22)
    private String ssflbm;
    // 是否享受税收优惠政策
    @ExcelProperty(value = "是否享受税收优惠政策*",index = 23)
    private String sfxsssyhzc;
    // 享受税收优惠政策内容
    @ExcelProperty(value = "享受税收优惠政策内容",index = 24)
    private String xsyhzenr;
    // 企业自编码
    @ExcelProperty(value = "企业自编码",index = 25)
    private String zxbm;
    // 备注
    @ExcelProperty(value = "备注",index = 26)
    private String bz;
    // 业务类型
    @ExcelProperty(value = "业务类型",index = 27)
    private String businessType;
    // 机动车企业类型
    @ExcelProperty(value = "机动车企业类型",index = 28)
    private String jdcqylx;

}
