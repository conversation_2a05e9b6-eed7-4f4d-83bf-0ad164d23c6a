package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 短信发送 外层报文
 */
@Data
public class SendMsgReq implements Serializable {

    /** 请求上下 **/
    private SendMsgReqContext sessionContext;
    /** 平台编号 **/
    private String platformNo;
    /** 平台密钥 **/
    private String platformKey;
    /** 短信接收方 **/
    private String phone;
    /** 调用端ip地址 **/
    private String ipAddress;
    /** 短信内容 **/
    private String content;
    /** 短信类型 (1：验证码 2：通知  5:语音验证码) **/
    private String msgType;
    /** 渠道 **/
    private String channel;

    /** 短信群发接收方 **/
    private List<String> phones;
}
