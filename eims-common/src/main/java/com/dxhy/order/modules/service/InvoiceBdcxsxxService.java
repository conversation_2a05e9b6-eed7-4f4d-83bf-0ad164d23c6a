package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceBdcxsxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 不动产销售信息的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceBdcxsxxService extends IService<InvoiceBdcxsxxEntity> {
    void saveBatch(List<InvoiceBdcxsxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
