package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.order.config.ConfigurerInfo;
import com.dxhy.order.constant.*;
import com.dxhy.order.model.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.*;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.service.*;
import com.dxhy.order.permit.tenant.DynamicDataSource;
import com.dxhy.order.permit.tenant.service.TenantRdsService;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class OpenOderInvoiceIssueServiceImpl implements OpenOderInvoiceIssueService {

    private static final String LOGGER_MSG = "(加解密接口)";

    @Autowired
    TaxpayerInfoDao taxpayerInfoDao;
    @Autowired
    OrderInvoiceItemDao orderInvoiceItemDao;
    @Autowired
    InvoiceAdditionInfoDao invoiceAdditionInfoDao;
    @Autowired
    OrderInvoiceInfoDao orderInvoiceInfoDao;
    @Autowired
    private ItemInfoDao itemInfoDao;

    @Resource
    private DbTenantDao dbTenantDao;

    @Autowired
    private RedInvoiceConfirmDao redInvoiceConfirmDao;
    @Autowired
    private NsrsbhTenantRelationService nsrsbhTenantRelationService;
    @Autowired
    private TenantRdsService tenantRdsService;
    @Autowired
    OrderInvoiceInfoService orderInvoiceInfoService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    InvoiceIssueService invoiceIssueService;
    @Autowired
    private OpenApiService openApiService;
    @Resource
    private OrderInvoiceConfigDao orderInvoiceConfigDao;
    @Resource
    private OrderInvoiceConfigService orderInvoiceConfigService;
    @Autowired
    private OrderStatusOptRecordDao orderStatusOptRecordDao;
    @Autowired
    private InvoiceBackpushConfigService invoiceBackpushConfigService;
    /**
     * 自己开票服务路径
     */
    @Value("${order.encrypt.invoiceIssueUrl}")
    private String invoiceIssueUrl;
    /**
     * 查询开票结果路径
     */
    @Value("${order.encrypt.invoiceQueryUrl}")
    private String invoiceQueryUrl;
    /**
     * 域名
     */
    @Value("${order.encrypt.domain}")
    private String domain;
    /**
     * 路径
     */
    @Value("${order.encrypt.path}")
    private String path;
    /**
     * 开票方法
     */
    @Value("${order.encrypt.allocateInvoicesMthod}")
    private String method;
    /**
     * 查询开票结果方法
     */
    @Value("${order.encrypt.orderInfoAndInvoiceInfoMthod}")
    private String orderInfoAndInvoiceInfoMthod;

    @Value("${order.encrypt.invoiceGenerateRedConfResult}")
    private String invoiceGenerateRedConfResult;

    @Value("${order.dx.appkey}")
    private String appkey;
    @Value("${order.dx.appSecret}")
    private String appSecret;

    @Override
    public String allocateInvoices(String secretId, String timestamp, String nonce, String signature, String encryptCode, String zipCode, String content) {
        try {
            // 根据secretId查询 税号和 secretKey
            TaxpayerInfo taxpayerInfo = taxpayerInfoDao.selectKeyBySecretId(secretId);
            String secretKey = Objects.isNull(taxpayerInfo) ? "" : taxpayerInfo.getSecretKey();
            // 参数校验
            if (StringUtils.isEmpty(secretId) || StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(nonce) || StringUtils.isEmpty(signature)
                    || StringUtils.isEmpty(encryptCode) || StringUtils.isEmpty(zipCode) || StringUtils.isEmpty(content)) {
                OrderInvoicesIssueRes orderInvoicesIssueRess = new OrderInvoicesIssueRes();
                orderInvoicesIssueRess.setZTXX("传入参数不合法");
                orderInvoicesIssueRess.setZTDM("0003");
                String encrypt = allocateInvoiceEncrypt(orderInvoicesIssueRess, secretId, secretKey);
                log.info("allocateInvoices 传入参数不合法:{}", encrypt);
                return encrypt;
            }
            // 验签
            String srcStr = String.format("POST%s%s%s?Nonce=%s&SecretId=%s&Timestamp=%s&content=%s&encryptCode=%s&zipCode=%s", domain, path, method, nonce, secretId, timestamp, content, encryptCode, zipCode);
            log.info("拼接签名原文字符串: {}", srcStr);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(keySpec);
            byte[] signBytes = mac.doFinal(srcStr.getBytes("UTF-8"));
            String signStr = Base64.encodeBase64URLSafeString(signBytes);
            log.info("发票开具重新计算后签名值为: {}", signStr);
            if (!Objects.equals(signature, signStr)) {
                OrderInvoicesIssueRes orderInvoicesIssueRes = new OrderInvoicesIssueRes();
                orderInvoicesIssueRes.setZTXX("鉴权失败");
                orderInvoicesIssueRes.setZTDM("0001");
                String jsonString = allocateInvoiceEncrypt(orderInvoicesIssueRes, secretId, secretKey);
                log.info("allocateInvoices 鉴权失败:{}", jsonString);
                return jsonString;
            }
            // 是否解压 和 解密
            String encry = commonDecrypt(zipCode, encryptCode, content, secretKey);
            log.info("allocateInvoices 解压缩，解密后返回json: {}", encry);
            // 开始组装开票请求报文
            OrderInvoicesIssueRes orderInvoicesIssueRes = new OrderInvoicesIssueRes();
            OpenOrderInvoiceInfoEntity openOrderInvoiceInfoEntity = JsonUtils.getInstance().fromJson(encry, OpenOrderInvoiceInfoEntity.class);
            log.info("反序列化openOrderInvoiceInfoEntity对象: {}", JsonUtils.getInstance().toJsonString(openOrderInvoiceInfoEntity));
            List<Map<String, String>> msgs = null;
            if (Objects.nonNull(openOrderInvoiceInfoEntity)) {
                List<OrderInvoicesIssueResGxx> orderInvoicesIssueResGxxes = new ArrayList<>();
                // 批次号
                OrderBatchInfo orderBatchInfo = openOrderInvoiceInfoEntity.getOrderBatchInfo();
                // 发票信息
                List<orderHeadInfo> orderHeadInfos = openOrderInvoiceInfoEntity.getOrderHeadInfos();
                String ztdm = "";
                String ztxx = "";
                // 校验金额 明细
                msgs = checkJe(orderHeadInfos, orderBatchInfo);
                // 校验通过 开始调用 开票接口
                if (CollectionUtils.isEmpty(msgs)) {
                    for (orderHeadInfo orderHeadInfo : orderHeadInfos) {
                        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderHeadInfo.getOrderInvoiceInfoEntity();
                        orderInvoiceInfoEntity.setPch(Objects.isNull(orderBatchInfo) ? "" : orderBatchInfo.getDDQQPCH());
                        if (StringUtils.isEmpty(orderInvoiceInfoEntity.getKpfs())) {
                            // 默认为自动开票
                            orderInvoiceInfoEntity.setKpfs("0");
                        }
                        // 不含税 计算税额
                        if ("0".equals(orderInvoiceInfoEntity.getHsbz())) {
                            List<BigDecimal> bigDecimalJe = new ArrayList<>();
                            List<BigDecimal> bigDecimalSe = new ArrayList<>();
                            for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceInfoEntity.getItemEntityList()) {
                                // 金额为空，计算金额
                                if (StringUtils.isEmpty(orderInvoiceItemEntity.getJe())) {
                                    BigDecimal dj = new BigDecimal(orderInvoiceItemEntity.getDj());
                                    BigDecimal xmsl = new BigDecimal(orderInvoiceItemEntity.getXmsl());
                                    BigDecimal je = dj.multiply(xmsl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                    String s = formatDecimal(je);
                                    orderInvoiceItemEntity.setJe(s);
                                }
                                // 税额为空 ，计算税额
                                if (StringUtils.isEmpty(orderInvoiceItemEntity.getSe())) {
                                    BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                    String sll = orderInvoiceItemEntity.getSl();
                                    Float aFloat = slTranst(sll);
                                    BigDecimal sl = new BigDecimal(aFloat.toString());
                                    BigDecimal se = je.multiply(sl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                    String s = formatDecimal(se);
                                    orderInvoiceItemEntity.setSe(s);
                                }
                                bigDecimalJe.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                                bigDecimalSe.add(new BigDecimal(orderInvoiceItemEntity.getSe()));
                            }
                            // 不含税 计算合计金额
                            BigDecimal hjje = bigDecimalJe.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                            orderInvoiceInfoEntity.setHjbhsje(hjje.toString());
                            // 不含税 计算合计税额
                            BigDecimal hjse = bigDecimalSe.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                            orderInvoiceInfoEntity.setKpse(hjse.toString());
                            // 价税合计
                            BigDecimal jshj = hjje.add(hjse);
                            orderInvoiceInfoEntity.setJshj(jshj.toString());
                        } else {
                            // 含税 计算税额
                            List<BigDecimal> bigDecimalHsJe = new ArrayList<>();
                            List<BigDecimal> bigDecimalHsSe = new ArrayList<>();
                            for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceInfoEntity.getItemEntityList()) {
                                // 金额为空，计算含税金额
                                if (StringUtils.isEmpty(orderInvoiceItemEntity.getJe())) {
                                    BigDecimal dj = new BigDecimal(orderInvoiceItemEntity.getDj());
                                    BigDecimal xmsl = new BigDecimal(orderInvoiceItemEntity.getXmsl());
                                    BigDecimal je = dj.multiply(xmsl).setScale(2, BigDecimal.ROUND_DOWN);//截取结果两位
                                    String s = formatDecimal(je);
                                    orderInvoiceItemEntity.setJe(s);
                                }
                                // 税额为空 ，计算含税金额的 税额
                                if (StringUtils.isEmpty(orderInvoiceItemEntity.getSe())) {
                                    // 含税金额
                                    BigDecimal je = new BigDecimal(orderInvoiceItemEntity.getJe());
                                    // 税率
                                    String sl = orderInvoiceItemEntity.getSl();
                                    Float aFloat = slTranst(sl);
                                    BigDecimal bigDecimalSL = new BigDecimal(aFloat.toString());
                                    BigDecimal add = new BigDecimal("1").add(bigDecimalSL);
                                    BigDecimal divide = je.divide(add, 2, RoundingMode.HALF_UP);
                                    // 税额
                                    BigDecimal se = divide.multiply(bigDecimalSL).setScale(2, BigDecimal.ROUND_HALF_UP);
                                    orderInvoiceItemEntity.setSe(se.toString());
                                }
                                bigDecimalHsJe.add(new BigDecimal(orderInvoiceItemEntity.getJe()));
                                bigDecimalHsSe.add(new BigDecimal(orderInvoiceItemEntity.getSe()));
                            }
                            // 含税 计算合计金额
                            BigDecimal hjje = bigDecimalHsJe.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                            orderInvoiceInfoEntity.setHjbhsje(hjje.toString());
                            // 含税 计算合计税额
                            BigDecimal hjse = bigDecimalHsSe.stream().map(a -> ObjectUtils.isEmpty(a) ? new BigDecimal(0) : a).reduce(BigDecimal.ZERO, BigDecimal::add);
                            orderInvoiceInfoEntity.setKpse(hjse.toString());
                            // 价税合计
                            orderInvoiceInfoEntity.setJshj(hjje.toString());
                        }
                        // 开票方式 0 自动开票  1 手动开票 2 扫码开票
                        if (InvoicePatternEnum.INVOICE_PATTERN_ENUM_1.getKey().equals(orderInvoiceInfoEntity.getKpfs())) {
                            // 发票主信息入库
                            String invoiceId = DistributedKeyMaker.generateShotKey();
                            orderInvoiceInfoEntity.setId(invoiceId);
                            orderInvoiceInfoEntity.setIsDelete("0");
                            orderInvoiceInfoEntity.setCreateTime(new Date());
                            orderInvoiceInfoEntity.setUpdateTime(new Date());
                            orderInvoiceInfoEntity.setDdscrq(new Date());
                            // 手动开票
                            orderInvoiceInfoEntity.setKpfs("1");
                            orderInvoiceInfoEntity.setPushStatus("0");
                            orderInvoiceInfoEntity.setEmailPushStatus("0");
                            orderInvoiceInfoEntity.setShortMsgPushStatus("0");
                            // 订单状态 0: 正常
                            orderInvoiceInfoEntity.setDdzt("0");
                            // 订单来源 2 外部接口
                            orderInvoiceInfoEntity.setDdly("2");
                            orderInvoiceInfoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_0.getKey());
                            orderInvoiceInfoEntity.setChBz("0");
                            orderInvoiceInfoDao.insert(orderInvoiceInfoEntity);
                            // 明细信息
                            List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
                            if (!CollectionUtils.isEmpty(itemEntityList)) {
                                for (OrderInvoiceItemEntity orderInvoiceItemEntity : itemEntityList) {
                                    orderInvoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                                    orderInvoiceItemEntity.setOrderInvoiceId(invoiceId);
                                    orderInvoiceItemEntity.setCreateTime(new Date());
                                    orderInvoiceItemEntity.setUpdateTime(new Date());
                                    orderInvoiceItemEntity.setIsDelete("0");
                                    orderInvoiceItemEntity.setHsbz(orderInvoiceInfoEntity.getHsbz());
                                    String sl = orderInvoiceItemEntity.getSl();
                                    Float aFloat = slTranst(sl);
                                    orderInvoiceItemEntity.setSl(aFloat.toString());
                                    orderInvoiceItemDao.insert(orderInvoiceItemEntity);
                                }
                            }
                            // 附加信息
                            List<InvoiceAdditionInfoEntity> infoEntityList = orderInvoiceInfoEntity.getInfoEntityList();
                            if (!CollectionUtils.isEmpty(infoEntityList)) {
                                for (InvoiceAdditionInfoEntity invoiceAdditionInfoEntity : infoEntityList) {
                                    invoiceAdditionInfoEntity.setId(DistributedKeyMaker.generateShotKey());
                                    invoiceAdditionInfoEntity.setOrderInvoiceInfoId(invoiceId);
                                    invoiceAdditionInfoDao.insert(invoiceAdditionInfoEntity);
                                }
                            }
                            // 手动开票组装返回报文
                            OrderInvoicesIssueResGxx orderInvoicesIssueResGxx = new OrderInvoicesIssueResGxx();
                            orderInvoicesIssueResGxx.setDDH(orderInvoiceInfoEntity.getDdh());
                            orderInvoicesIssueResGxx.setDDLX(orderInvoiceInfoEntity.getDdzt());
                            orderInvoicesIssueResGxx.setDDQQLSH(orderInvoiceInfoEntity.getFpqqlsh());
                            orderInvoicesIssueResGxxes.add(orderInvoicesIssueResGxx);
                            ztdm = "0000";
                            ztxx = "成功";
                        } else if (InvoicePatternEnum.INVOICE_PATTERN_ENUM_0.getKey().equals(orderInvoiceInfoEntity.getKpfs())) {
                            //拼接项目简称
                            List<OrderInvoiceItemEntity> invoiceItemEntities = orderInvoiceInfoEntity.getItemEntityList();
                            if (!CollectionUtils.isEmpty(invoiceItemEntities)) {
                                for (OrderInvoiceItemEntity orderInvoiceItemEntity : invoiceItemEntities) {
                                    ItemInfoEntity itemInfoEntity1 = new ItemInfoEntity();
                                    itemInfoEntity1.setSphssflbm(orderInvoiceItemEntity.getSpbm());
                                    itemInfoEntity1.setBaseNsrsbh(orderInvoiceInfoEntity.getBaseNsrsbh());
                                    itemInfoEntity1.setXmmc(orderInvoiceItemEntity.getXmmc());
                                    ItemInfoEntity itemInfoEntity = itemInfoDao.selectItemInfoByName(itemInfoEntity1);
                                    orderInvoiceItemEntity.setXmmc(Objects.isNull(itemInfoEntity) ? orderInvoiceItemEntity.getXmmc() : "*" + itemInfoEntity.getSphssfljc() + "*" + orderInvoiceItemEntity.getXmmc());
                                }
                            }
                            // 自动开票
                            long l = System.currentTimeMillis();
                            String jsonString = JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity);
                            log.info("调用开票接口入参: {}", jsonString);
                            log.info("开始调用开票接口");
                            String res = HttpUtils.doPost(invoiceIssueUrl, jsonString);
                            log.info("结束调用开票接口 耗时: {}", System.currentTimeMillis() - l);
                            log.info("调用开票接口出参: {}", jsonString);
                            R r = JsonUtils.getInstance().fromJson(res, R.class);
                            // 返回成功入库
                            if (Objects.nonNull(r) && "0000".equals((String) r.get("code"))) {
                                ztdm = (String) r.get("code");
                                ztxx = (String) r.get("msg");
                                String invoiceId = DistributedKeyMaker.generateShotKey();
                                orderInvoiceInfoEntity.setId(invoiceId);
                                // 订单状态 (0: 正常
                                orderInvoiceInfoEntity.setDdzt("0");
                                // "开票方式 0 自动开票
                                orderInvoiceInfoEntity.setKpfs("0");
                                //开票成功 保存数据到本地 设置订单来源和 开票状态
                                orderInvoiceInfoEntity.setDdly(OrderSourceEnum.ORDER_SOURCE_ENUM_2.getKey());
                                // 根据返回状态 设置开票状态
                                orderInvoiceInfoEntity.setByzd1(ztdm);
                                orderInvoiceInfoEntity.setByzd2(ztxx);
                                orderInvoiceInfoEntity.setIsDelete("0");
                                orderInvoiceInfoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_1.getKey());
                                orderInvoiceInfoEntity.setCreateTime(new Date());
                                orderInvoiceInfoEntity.setUpdateTime(new Date());
                                orderInvoiceInfoEntity.setChBz("0");
                                orderInvoiceInfoDao.insert(orderInvoiceInfoEntity);
                                // 明细信息
                                List<OrderInvoiceItemEntity> itemEntityList = orderInvoiceInfoEntity.getItemEntityList();
                                if (!CollectionUtils.isEmpty(itemEntityList)) {
                                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : itemEntityList) {
                                        orderInvoiceItemEntity.setId(DistributedKeyMaker.generateShotKey());
                                        orderInvoiceItemEntity.setOrderInvoiceId(invoiceId);
                                        orderInvoiceItemEntity.setCreateTime(new Date());
                                        orderInvoiceItemEntity.setUpdateTime(new Date());
                                        orderInvoiceItemEntity.setIsDelete("0");
                                        Float aFloat = slTranst(orderInvoiceItemEntity.getSl());
                                        orderInvoiceItemEntity.setSl(aFloat.toString());
                                        orderInvoiceItemDao.insert(orderInvoiceItemEntity);
                                    }
                                }
                                // 附加信息
                                List<InvoiceAdditionInfoEntity> infoEntityList = orderInvoiceInfoEntity.getInfoEntityList();
                                if (!CollectionUtils.isEmpty(infoEntityList)) {
                                    for (InvoiceAdditionInfoEntity invoiceAdditionInfoEntity : infoEntityList) {
                                        invoiceAdditionInfoEntity.setId(DistributedKeyMaker.generateShotKey());
                                        invoiceAdditionInfoEntity.setOrderInvoiceInfoId(invoiceId);
                                        invoiceAdditionInfoDao.insert(invoiceAdditionInfoEntity);
                                    }
                                }
                            }
                            // 自动开票组装返回报文
                            OrderInvoicesIssueResGxx orderInvoicesIssueResGxx = new OrderInvoicesIssueResGxx();
                            orderInvoicesIssueResGxx.setDDH(orderInvoiceInfoEntity.getDdh());
                            orderInvoicesIssueResGxx.setDDLX(orderInvoiceInfoEntity.getDdzt());
                            orderInvoicesIssueResGxx.setDDQQLSH(orderInvoiceInfoEntity.getFpqqlsh());
                            orderInvoicesIssueResGxxes.add(orderInvoicesIssueResGxx);
                        } else if (InvoicePatternEnum.INVOICE_PATTERN_ENUM_2.getKey().equals(orderInvoiceInfoEntity.getKpfs())) {
                            // 扫码开票
                            // todo 扫码开票逻辑

                            // 扫码开票组装返回报文
                            OrderInvoicesIssueResGxx orderInvoicesIssueResGxx = new OrderInvoicesIssueResGxx();
                            orderInvoicesIssueResGxx.setDDH(orderInvoiceInfoEntity.getDdh());
                            orderInvoicesIssueResGxx.setDDLX(orderInvoiceInfoEntity.getDdzt());
                            orderInvoicesIssueResGxx.setDDQQLSH(orderInvoiceInfoEntity.getFpqqlsh());
                            orderInvoicesIssueResGxxes.add(orderInvoicesIssueResGxx);
                        } else {
                            log.info("开票方式不支持");
                        }

                    }
                    orderInvoicesIssueRes.setDDQQPCH(orderBatchInfo.getDDQQPCH());
                    orderInvoicesIssueRes.setZTDM(ztdm);
                    orderInvoicesIssueRes.setZTXX(ztxx);
                    orderInvoicesIssueRes.setDDJGXX(orderInvoicesIssueResGxxes);
                } else {
                    // 校验不通过 返回 状态码和不通过信息
                    orderInvoicesIssueRes.setZTXX(JsonUtils.getInstance().toJsonString(msgs));
                    orderInvoicesIssueRes.setZTDM("0004");
                    orderInvoicesIssueRes.setDDQQPCH(orderBatchInfo.getDDQQPCH());
                }
            }
            // 开票结果报文 开始加密返回数据
            String jsonString = allocateInvoiceEncrypt(orderInvoicesIssueRes, secretId, secretKey);
            return jsonString;
        } catch (Exception e) {
            log.error("allocateInvoices 对外开票函数异常: {}", e);
        }
        return null;
    }

    private List<Map<String, String>> checkJe(List<orderHeadInfo> orderHeadInfos, OrderBatchInfo orderBatchInfo) {
        List<Map<String, String>> mgs = new ArrayList<>();
        Map<String, String> checkResultMap = null;
        if (CollectionUtils.isEmpty(orderHeadInfos)) {
            checkResultMap = new HashMap<>(10);
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_001.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_001.getMessage());
            mgs.add(checkResultMap);
            return mgs;
        }
        if (Objects.isNull(orderBatchInfo)) {
            checkResultMap = new HashMap<>(10);
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_002.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_002.getMessage());
            mgs.add(checkResultMap);
            return mgs;
        }
        /**
         * 批次号
         */
        if (StringUtils.isEmpty(orderBatchInfo.getDDQQPCH())) {
            checkResultMap = new HashMap<>(10);
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_027.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_027.getMessage());
            mgs.add(checkResultMap);
            return mgs;
        }
        for (orderHeadInfo orderHeadInfo : orderHeadInfos) {
            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderHeadInfo.getOrderInvoiceInfoEntity();
            if (Objects.nonNull(orderInvoiceInfoEntity)) {
                /**
                 * 订单号
                 */
                if (StringUtils.isEmpty(orderInvoiceInfoEntity.getDdh())) {
                    checkResultMap = new HashMap<>(10);
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_025.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_025.getMessage());
                    mgs.add(checkResultMap);
                    return mgs;
                }
                /**
                 * 发票请求流水号
                 */
                if (StringUtils.isEmpty(orderInvoiceInfoEntity.getFpqqlsh())) {
                    checkResultMap = new HashMap<>(10);
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_026.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_026.getMessage());
                    mgs.add(checkResultMap);
                    return mgs;
                } else {
                    // 校验发票请求流水号 是否已经存在
                    OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInvoiceInfoEntity.getFpqqlsh());
                    if (Objects.nonNull(invoiceInfoEntity)) {
                        checkResultMap = new HashMap<>(10);
                        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_0029.getKey());
                        checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_0029.getMessage());
                        mgs.add(checkResultMap);
                        return mgs;
                    }
                }

                /**
                 * 订单主体-开票类型
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107020, orderInvoiceInfoEntity.getKplx());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 订单主体-发票种类代码
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107019, orderInvoiceInfoEntity.getFpzlDm());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 含税标志
                 */
                if (StringUtils.isEmpty(orderInvoiceInfoEntity.getHsbz())) {
                    checkResultMap = new HashMap<>(10);
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_022.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_022.getMessage());
                    mgs.add(checkResultMap);
                    return mgs;
                }
                /**
                 * 订单主体-销货方税号
                 */
                if (StringUtils.isEmpty(orderInvoiceInfoEntity.getXhfNsrsbh())) {
                    checkResultMap = new HashMap<>(10);
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_005.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_005.getMessage());
                    mgs.add(checkResultMap);
                    return mgs;
                } else {
                    List<Map<String, String>> maps = checkNsrshb(orderInvoiceInfoEntity);
                    mgs.addAll(maps);
                    if (!CollectionUtils.isEmpty(maps)) {
                        return mgs;
                    }
                }
                /**
                 * 订单主体-销售方纳税人名称
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107024, orderInvoiceInfoEntity.getXhfMc());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 订单主体-销售方地址
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107282, orderInvoiceInfoEntity.getXhfDz());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 订单主体-销售方电话
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107283, orderInvoiceInfoEntity.getXhfDh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 订单主体-销售方银行
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107284, orderInvoiceInfoEntity.getXhfYh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 订单主体-销售方帐号
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107285, orderInvoiceInfoEntity.getXhfZh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                // 购买方税号
                if (StringUtils.isEmpty(orderInvoiceInfoEntity.getGhfNsrsbh())) {
                    checkResultMap = new HashMap<>(10);
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_009.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_009.getMessage());
                    mgs.add(checkResultMap);
                    return mgs;
                } else {
                    List<Map<String, String>> maps = checkNsrshb(orderInvoiceInfoEntity);
                    mgs.addAll(maps);
                    if (!CollectionUtils.isEmpty(maps)) {
                        return mgs;
                    }
                }
                /**
                 * 新增购买方ID,ID非必填,如果填写ID需要保证购方税号,名称,地址,电话,银行,帐号等非必填.
                 */
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107286, orderInvoiceInfoEntity.getGhfId());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                *//**
                 * 订单主体-购买方纳税人名称
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107288, orderInvoiceInfoEntity.getGhfMc());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                *//**
                 * 订单主体-购买方地址
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107289, orderInvoiceInfoEntity.getGhfDz());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                *//**
                 * 订单主体-购买方电话
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107290, orderInvoiceInfoEntity.getGhfDh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                *//**
                 * 订单主体-购买方银行
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107291, orderInvoiceInfoEntity.getGhfYh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                *//**
                 * 订单主体-购买方帐号
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107292, orderInvoiceInfoEntity.getGhfZh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 如果开票方式为自动开票校验数据必填
                 * 开票方式0为自动开票
                 */
                if (OrderInfoEnum.ORDER_REQUEST_TYPE_0.getKey().equals(orderInvoiceInfoEntity.getKpfs())) {
                    /**
                     * 订单主体-购买方企业类型
                     */
                    /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107035, orderInvoiceInfoEntity.getGhfQylx());
                    if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                        mgs.add(checkResultMap);
                        return mgs;
                    }
                    *//**
                     * 订单主体-购买方名称
                     *//*
                    checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107032, orderInvoiceInfoEntity.getGhfMc());
                    if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                        mgs.add(checkResultMap);
                        return mgs;
                    }*/
                }
                /**
                 * 订单主体-企业类型合法性
                 */
                /*if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getGhfQylx())) {
                    //订单主体-企业类型为01(企业),02(机关事业单位)时需要保证购方税号非空并且正确
                    if (OrderInfoEnum.GHF_QYLX_01.getKey().equals(orderInvoiceInfoEntity.getGhfQylx())) {
                        checkResultMap = CheckParamUtil.checkNsrsbhParam(OrderInfoContentEnum.CHECK_ISS7PRI_107027, OrderInfoContentEnum.CHECK_ISS7PRI_107025, OrderInfoContentEnum.CHECK_ISS7PRI_107023, orderInvoiceInfoEntity.getGhfNsrsbh());
                        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                            mgs.add(checkResultMap);
                            return mgs;
                        }
                    }
                }
                *//**
                 * 订单主体-购买方省份
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107037, orderInvoiceInfoEntity.getGhfSf());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                *//**
                 * 订单主体-购买方手机
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107040, orderInvoiceInfoEntity.getGhfSj());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }

                *//**
                 * 订单主体-购买方邮箱
                 *//*
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107042, orderInvoiceInfoEntity.getGhfYx());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                // 价税合计
                if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getJshj())) {
                    if (ConfigureConstant.STRING_0.equals(orderInvoiceInfoEntity.getJshj()) || ConfigureConstant.STRING_000.equals(orderInvoiceInfoEntity.getJshj()) || ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceInfoEntity.getJshj())) {
                        checkResultMap = new HashMap<>(10);
                        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_012.getKey());
                        checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_012.getMessage());
                        mgs.add(checkResultMap);
                        return mgs;
                    }
                }
                // 合计金额
                if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getHjbhsje())) {
                    // 合计金额为不为0时,需要保证金额为小数点后两位
                    if (ConfigureConstant.DOUBLE_PENNY_ZERO != new BigDecimal(orderInvoiceInfoEntity.getHjbhsje()).doubleValue() && ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceInfoEntity.getHjbhsje())) {
                        checkResultMap = new HashMap<>(10);
                        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_014.getKey());
                        checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_014.getMessage());
                        mgs.add(checkResultMap);
                        return mgs;
                    }
                }
                // 合计税额
                if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getKpse())) {
                    if (ConfigureConstant.DOUBLE_PENNY_ZERO != new BigDecimal(orderInvoiceInfoEntity.getKpse()).doubleValue() && ConfigureConstant.INT_2 != ValidateUtil.checkNumberic(orderInvoiceInfoEntity.getKpse())) {
                        checkResultMap = new HashMap<>(10);
                        checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_016.getKey());
                        checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_016.getMessage());
                        mgs.add(checkResultMap);
                        return mgs;
                    }
                }
                // 订单号
                /*checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107067, orderInvoiceInfoEntity.getDdh());
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }
                // 订单日期
                checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.CHECK_ISS7PRI_107068, orderInvoiceInfoEntity.getDdscrq() == null ? "" : DateUtil.format(orderInvoiceInfoEntity.getDdscrq(), ConfigureConstant.DATE_FORMAT_DATE_Y_M_DH_M_S));
                if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
                    mgs.add(checkResultMap);
                    return mgs;
                }*/
                /**
                 * 订单明细信息正确性与合法性
                 */
                if (CollectionUtils.isEmpty(orderInvoiceInfoEntity.getItemEntityList())) {
                    checkResultMap = new HashMap<>(10);
                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_004.getKey());
                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_004.getMessage());
                    mgs.add(checkResultMap);
                    return mgs;
                } else {
                    for (OrderInvoiceItemEntity orderInvoiceItemEntity : orderInvoiceInfoEntity.getItemEntityList()) {
                        /**
                         * 订单明细信息-发票行性质
                         */
                        if (StringUtils.isEmpty(orderInvoiceItemEntity.getFphxz())) {
                            checkResultMap = new HashMap<>(10);
                            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_023.getKey());
                            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_023.getMessage());
                            mgs.add(checkResultMap);
                            return mgs;
                        }
                        // 如果为折扣行
                        if ("1".equals(orderInvoiceItemEntity.getFphxz())) {
                            /**
                             * 订单明细信息-项目名称
                             */
                            if (StringUtils.isEmpty(orderInvoiceItemEntity.getXmmc())) {
                                checkResultMap = new HashMap<>(10);
                                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_024.getKey());
                                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_024.getMessage());
                                mgs.add(checkResultMap);
                                return mgs;
                            }
                            /**
                             * 校验金额
                             */
                            /*if (!StringUtils.isEmpty(orderInvoiceItemEntity.getJe())) {
                                if (!orderInvoiceItemEntity.getJe().matches(PatternConstant.PATTERN_XMJE)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107160.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107160.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }*/
                            //校验税额
                            /*if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSe())) {
                                if (!orderInvoiceItemEntity.getSe().matches(PatternConstant.PATTERN_XMJE)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107161.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107161.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }
                            //校验税率
                            if (StringUtils.isEmpty(orderInvoiceItemEntity.getSl())) {
                                checkResultMap = new HashMap<>(10);
                                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107146.getKey());
                                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107146.getMessage());
                                mgs.add(checkResultMap);
                                return mgs;
                            }*/
                        } else {
                            //校验单价
                            if (!StringUtils.isEmpty(orderInvoiceItemEntity.getDj())) {
                                if (!orderInvoiceItemEntity.getDj().matches(PatternConstant.PATTERN_XMDJ)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_017.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_017.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }
                            // 校验数量
                            /*if (!StringUtils.isEmpty(orderInvoiceItemEntity.getXmsl())) {
                                if (!orderInvoiceItemEntity.getXmsl().matches(PatternConstant.PATTERN_XMSL)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107153.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107153.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }
                            //校验金额
                            if (!StringUtils.isEmpty(orderInvoiceItemEntity.getJe())) {
                                if (!orderInvoiceItemEntity.getJe().matches(PatternConstant.PATTERN_XMJE)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107160.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107160.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }
                            //校验税额
                            if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSe())) {
                                if (!orderInvoiceItemEntity.getSe().matches(PatternConstant.PATTERN_XMJE)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107161.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107161.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }
                            //校验税率
                            if (StringUtils.isEmpty(orderInvoiceItemEntity.getSl())) {
                                checkResultMap = new HashMap<>(10);
                                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107146.getKey());
                                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107146.getMessage());
                                mgs.add(checkResultMap);
                                return mgs;
                            }
                            if (!StringUtils.isEmpty(orderInvoiceItemEntity.getSl())) {
                                if (!orderInvoiceItemEntity.getSl().matches(PatternConstant.PATTERN_SL)) {
                                    checkResultMap = new HashMap<>(10);
                                    checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderInfoContentEnum.CHECK_ISS7PRI_107168.getKey());
                                    checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderInfoContentEnum.CHECK_ISS7PRI_107168.getMessage());
                                    mgs.add(checkResultMap);
                                    return mgs;
                                }
                            }*/
                            /**
                             * 订单明细信息-项目名称
                             */
                            if (StringUtils.isEmpty(orderInvoiceItemEntity.getXmmc())) {
                                checkResultMap = new HashMap<>(10);
                                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_019.getKey());
                                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_019.getMessage());
                                mgs.add(checkResultMap);
                                return mgs;
                            }
                            /**
                             * 订单明细信息-规格型号
                             */
                            if (StringUtils.isEmpty(orderInvoiceItemEntity.getGgxh())) {
                                checkResultMap = new HashMap<>(10);
                                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_020.getKey());
                                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_020.getMessage());
                                mgs.add(checkResultMap);
                                return mgs;
                            }
                            /**
                             * 订单明细信息-单位
                             */
                            if (StringUtils.isEmpty(orderInvoiceItemEntity.getDw())) {
                                checkResultMap = new HashMap<>(10);
                                checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_021.getKey());
                                checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_021.getMessage());
                                mgs.add(checkResultMap);
                                return mgs;
                            }
                        }
                    }
                }
            } else {
                checkResultMap = new HashMap<>(10);
                checkResultMap.put(OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_003.getKey(), OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_003.getMessage());
                mgs.add(checkResultMap);
            }
        }
        return mgs;
    }

    private List<Map<String, String>> checkNsrshb(OrderInvoiceInfoEntity orderInvoiceInfoEntity) {
        List<Map<String, String>> mgs = new ArrayList<>();
        Map<String, String> checkResultMap = null;
        // 是否包含空格
        if (orderInvoiceInfoEntity.getXhfNsrsbh().contains(ConfigureConstant.STRING_SPACE)) {
            checkResultMap = new HashMap<>(10);
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_0028.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_0028.getMessage());
            mgs.add(checkResultMap);
            return mgs;
        }
        // 判断税号长度合法性问题,长度必须14,15,16,17,18,20位
        if (ConfigureConstant.INT_6 > ValidateUtil.getStrBytesLength(orderInvoiceInfoEntity.getXhfNsrsbh())
                && ConfigureConstant.INT_20 < ValidateUtil.getStrBytesLength(orderInvoiceInfoEntity.getXhfNsrsbh())) {
            checkResultMap = new HashMap<>(10);
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_006.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_006.getMessage());
            mgs.add(checkResultMap);
            return mgs;
        }
        // 纳税人识别号需要全部大写
        if (!ValidateUtil.isAcronym(orderInvoiceInfoEntity.getXhfNsrsbh())) {
            checkResultMap = new HashMap<>(10);
            checkResultMap.put(OrderManagementConstant.ERRORCODE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_007.getKey());
            checkResultMap.put(OrderManagementConstant.ERRORMESSAGE, OrderReturnMsgEnum.ORDER_RETURN_MSG_ENUM_007.getMessage());
            mgs.add(checkResultMap);
            return mgs;
        }
        return mgs;
    }

    @Override
    public String getOrderInfoAndInvoiceInfo(String secretId, String timestamp, String nonce, String signature, String encryptCode, String zipCode, String content) {
        try {
            TaxpayerInfo taxpayerInfo = taxpayerInfoDao.selectKeyBySecretId(secretId);
            String secretKey = Objects.isNull(taxpayerInfo) ? "" : taxpayerInfo.getSecretKey();
            if (StringUtils.isEmpty(secretId) || StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(nonce) || StringUtils.isEmpty(signature)
                    || StringUtils.isEmpty(encryptCode) || StringUtils.isEmpty(zipCode) || StringUtils.isEmpty(content)) {
                OrderInfoAndInvoiceInfoRes orderInfoAndInvoiceInfoRes = new OrderInfoAndInvoiceInfoRes();
                orderInfoAndInvoiceInfoRes.setZTXX("传入参数不合法");
                orderInfoAndInvoiceInfoRes.setZTDM("0003");
                String orderInfoAndInvoiceInfoEncrypt = getOrderInfoAndInvoiceInfoEncrypt(orderInfoAndInvoiceInfoRes, secretId, secretKey);
                log.info("getOrderInfoAndInvoiceInfo 传入参数不合法: {}", orderInfoAndInvoiceInfoEncrypt);
                return orderInfoAndInvoiceInfoEncrypt;
            }
            // 验签
            String srcStr = String.format("POST%s%s%s?Nonce=%s&SecretId=%s&Timestamp=%s&content=%s&encryptCode=%s&zipCode=%s", domain, path, orderInfoAndInvoiceInfoMthod, nonce, secretId, timestamp, content, encryptCode, zipCode);
            log.info("getOrderInfoAndInvoiceInfo 拼接签名原文字符串: {}", srcStr);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(keySpec);
            byte[] signBytes = mac.doFinal(srcStr.getBytes("UTF-8"));
            String signStr = Base64.encodeBase64URLSafeString(signBytes);
            log.info("getOrderInfoAndInvoiceInfo 发票查询重新计算后签名值为: {}", signStr);
            if (!Objects.equals(signature, signStr)) {
                OrderInfoAndInvoiceInfoRes orderInfoAndInvoiceInfoRes = new OrderInfoAndInvoiceInfoRes();
                orderInfoAndInvoiceInfoRes.setZTXX("鉴权失败");
                orderInfoAndInvoiceInfoRes.setZTDM("0001");
                String orderInfoAndInvoiceInfoEncrypt = getOrderInfoAndInvoiceInfoEncrypt(orderInfoAndInvoiceInfoRes, secretId, secretKey);
                log.info("getOrderInfoAndInvoiceInfo 鉴权失败:{}", orderInfoAndInvoiceInfoEncrypt);
                return orderInfoAndInvoiceInfoEncrypt;
            }
            // 是否解压 和 解密
            String encry = commonDecrypt(zipCode, encryptCode, content, secretKey);
            log.info("getOrderInfoAndInvoiceInfo 解压缩，解密后返回json: {}", encry);
            // 开始组装查询开票结果请求报文
            OrderInfoAndInvoiceInfoQuery orderInfoAndInvoiceInfoQuery = JsonUtils.getInstance().fromJson(encry, OrderInfoAndInvoiceInfoQuery.class);
            OrderInfoAndInvoiceInfoRes orderInfoAndInvoiceInfoRes = new OrderInfoAndInvoiceInfoRes();
            List<String> msgList = new ArrayList<>();
            if (Objects.nonNull(orderInfoAndInvoiceInfoQuery)) {
                if (StringUtils.isEmpty(orderInfoAndInvoiceInfoQuery.getNSRSBH())) {
                    msgList.add("纳税人识别号不能为空");
                }
                if (StringUtils.isEmpty(orderInfoAndInvoiceInfoQuery.getDDQQLSH()) && StringUtils.isEmpty(orderInfoAndInvoiceInfoQuery.getDDH())) {
                    msgList.add("订单请求流水号和订单号不能同时为空");
                }
            } else {
                orderInfoAndInvoiceInfoRes.setZTDM("0006");
                orderInfoAndInvoiceInfoRes.setZTXX("底层返回错误");
                orderInfoAndInvoiceInfoRes.setDDFPZXX(null);
                String orderInfoAndInvoiceInfoEncrypt = getOrderInfoAndInvoiceInfoEncrypt(orderInfoAndInvoiceInfoRes, secretId, secretKey);
                log.info("getOrderInfoAndInvoiceInfo 底层返回为null");
                return orderInfoAndInvoiceInfoEncrypt;
            }
            if (!CollectionUtils.isEmpty(msgList)) {
                String msg = JsonUtils.getInstance().toJsonString(msgList);
                orderInfoAndInvoiceInfoRes.setZTDM("0005");
                orderInfoAndInvoiceInfoRes.setZTXX(msg);
                orderInfoAndInvoiceInfoRes.setDDFPZXX(null);
                String orderInfoAndInvoiceInfoEncrypt = getOrderInfoAndInvoiceInfoEncrypt(orderInfoAndInvoiceInfoRes, secretId, secretKey);
                log.info("getOrderInfoAndInvoiceInfo 返回查询校验信息:{}", msg);
                return orderInfoAndInvoiceInfoEncrypt;
            }
            OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInfoAndInvoiceInfoQuery.getDDQQLSH());
            if (Objects.nonNull(orderInvoiceInfo)) {
                // 明细信息
                List<OrderInvoiceItemEntity> orderInvoiceItemEntities = orderInvoiceItemDao.selectItemListById(orderInvoiceInfo.getId());
                orderInvoiceInfo.setItemEntityList(orderInvoiceItemEntities);
                // 附加信息
                List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities = invoiceAdditionInfoDao.selectAdditionListById(orderInvoiceInfo.getId());
                orderInvoiceInfo.setInfoEntityList(invoiceAdditionInfoEntities);
                // 组装结构
                List<OrderInfoAndInvoiceInfoZxx> orderInfoAndInvoiceInfoZxxes = new ArrayList<>();
                orderInfoAndInvoiceInfoRes.setZTDM(orderInvoiceInfo.getByzd1());
                orderInfoAndInvoiceInfoRes.setZTXX(orderInvoiceInfo.getByzd2());
                OrderInfoAndInvoiceInfoZxx orderInfoAndInvoiceInfoZxx = new OrderInfoAndInvoiceInfoZxx();
                orderInfoAndInvoiceInfoZxx.setDDFPXX(orderInvoiceInfo);
                orderInfoAndInvoiceInfoZxxes.add(orderInfoAndInvoiceInfoZxx);
                orderInfoAndInvoiceInfoRes.setDDFPZXX(orderInfoAndInvoiceInfoZxxes);
                log.info("getOrderInfoAndInvoiceInfo 正常处理");
            } else {
                orderInfoAndInvoiceInfoRes.setZTDM("0004");
                orderInfoAndInvoiceInfoRes.setZTXX("发票信息为空");
                orderInfoAndInvoiceInfoRes.setDDFPZXX(null);
                log.info("getOrderInfoAndInvoiceInfo 发票信息为空");
            }
            String orderInfoAndInvoiceInfoEncrypt = getOrderInfoAndInvoiceInfoEncrypt(orderInfoAndInvoiceInfoRes, secretId, secretKey);
            return orderInfoAndInvoiceInfoEncrypt;
        } catch (Exception e) {
            log.error("getOrderInfoAndInvoiceInfo 对外查询开票结果函数异常: {}", e);
        }
        return null;
    }

    @Override
    public void getTaskInvoiceInfo() {
        // todo 按时间缩小查询范围
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntities = nsrsbhTenantRelationService.listAll();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntities)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntities) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                if(org.apache.commons.lang3.StringUtils.isNotBlank(nsrsbhTenantRelationEntity.getDbUrl())){
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getDbUrl());
                }
            }
            for (String dbUrl : tenantCodeList) {
                Map map = new HashMap();
                map.put("db_url", dbUrl);
                List<DbTenantEntity> list =  dbTenantDao.selectByMap(map);
                String tenantCode = list.get(0).getTenantCode();
                log.info("开始切换到数据源:{}", tenantCode);
                String changeTenantResult = tenantRdsService.switchRds(tenantCode);
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("蓝字发票开具结果查询 切换业务数据源失败tenantCode: {}", tenantCode);
                    continue;
                }
                taskForInvoiceInfo(tenantCode);
            }
        } else {
            log.info("蓝字发票开具结果查询 销方税号租户关联表数据为空");
        }
    }

    private void taskForInvoiceInfo(String tenantCode) {
        log.info("蓝字发票开具 定时任务查询开票结果Start");
        List<OrderInvoiceInfoEntity> list = orderInvoiceInfoDao.selectTaskInvoiceInfo();
        // log.info("getTaskInvoiceInfo 定时任务查询开票结果个数: {}",list.size());
        if (!CollectionUtils.isEmpty(list)) {
            for (OrderInvoiceInfoEntity orderInvoiceInfoEntity1 : list) {
                try {
                    // 组装开票查询请求参数
                    OrderInfoAndInvoiceInfoQuery orderInfoAndInvoiceInfoQuery = new OrderInfoAndInvoiceInfoQuery();
                    orderInfoAndInvoiceInfoQuery.setNSRSBH(orderInvoiceInfoEntity1.getXhfNsrsbh());
                    orderInfoAndInvoiceInfoQuery.setDDH(orderInvoiceInfoEntity1.getDdh());
                    orderInfoAndInvoiceInfoQuery.setDDQQLSH(orderInvoiceInfoEntity1.getFpqqlsh());
                    orderInfoAndInvoiceInfoQuery.setFpzl(orderInvoiceInfoEntity1.getFpzlDm());
                    // 版式文件 0 需要 1不需要
                    orderInfoAndInvoiceInfoQuery.setBSWJ("0");
                    orderInfoAndInvoiceInfoQuery.setTdyw(orderInvoiceInfoEntity1.getTdyw());
                    String jsonString = JsonUtils.getInstance().toJsonString(orderInfoAndInvoiceInfoQuery);
                    // 发起http调用 发票查询接口
                    log.info("getTaskInvoiceInfo 发票查询请求报文: {}", jsonString);
                    long l = System.currentTimeMillis();
                    String res = HttpUtils.doPost(invoiceQueryUrl, jsonString);
                    log.info("getTaskInvoiceInfo 结束调用开票查询接口 耗时: {}", System.currentTimeMillis() - l);
                    log.info("getTaskInvoiceInfo 发票查询返回报文:{}", res);
                    R r = JsonUtils.getInstance().fromJson(res, R.class);
                    log.info("getTaskInvoiceInfo R返回报文:{}", r);
                    if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
                        log.info("getTaskInvoiceInfo 0000正确返回");
                        JSONObject data = (JSONObject) r.get("data");
                        String status = (String) r.get("code");
                        String message = (String) r.get("msg");
                        if (!StringUtils.isEmpty(data)) {
                            OrderInvoiceInfoEntity orderInvoiceInfoEntity = JsonUtils.getInstance().fromJson(data.toJSONString(), OrderInvoiceInfoEntity.class);
                            if (Objects.nonNull(orderInvoiceInfoEntity)) {
                                // 开票结果返回成功 根据ddqqlsh 修改本地发票状态为成功
                                OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInvoiceInfoEntity1.getFpqqlsh());
                                orderInvoiceInfo.setByzd1(status);
                                orderInvoiceInfo.setByzd2(message);
                                orderInvoiceInfo.setPdfUrl(orderInvoiceInfoEntity.getPdfUrl());
                                orderInvoiceInfo.setOfdUrl(orderInvoiceInfoEntity.getOfdUrl());
                                orderInvoiceInfo.setKprq(orderInvoiceInfoEntity.getKprq());
                                orderInvoiceInfo.setQdfphm(orderInvoiceInfoEntity.getQdfphm());
                                orderInvoiceInfo.setFpdm(orderInvoiceInfoEntity.getFpdm());
                                orderInvoiceInfo.setFphm(orderInvoiceInfoEntity.getFphm());
                                orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_2.getKey());
                                orderInvoiceInfo.setUpdateTime(new Date());
                                orderInvoiceInfo.setPushStatus("0");
                                orderInvoiceInfo.setEmailPushStatus("0");
                                orderInvoiceInfo.setShortMsgPushStatus("0");
                                orderInvoiceInfoDao.updateById(orderInvoiceInfo);

                                if (!StringUtils.isEmpty(orderInvoiceInfo.getDjcjsz()) && "1".equals(orderInvoiceInfo.getDjcjsz())) {
                                    // 更新同一应收单下 另一个或者多个订单的状态
                                    if (!StringUtils.isEmpty(orderInvoiceInfoEntity1.getYsdh())) {
                                        //应收单情况下，发起开票请求，其他订单状态会置为未勾选状态
                                        // 强制统一应收单下订单状态
                                        List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(orderInvoiceInfoEntity1.getYsdh(), orderInvoiceInfoEntity1.getXhfNsrsbh(),null);
                                        if (!CollectionUtils.isEmpty(orderInvoiceInfoEntities)) {
                                            for (OrderInvoiceInfoEntity infoEntity : orderInvoiceInfoEntities) {
                                                if (!orderInvoiceInfoEntity1.getId().equals(infoEntity.getId())) {
                                                    infoEntity.setKpzt("2");
                                                    infoEntity.setPdfUrl(orderInvoiceInfoEntity.getPdfUrl());
                                                    infoEntity.setOfdUrl(orderInvoiceInfoEntity.getOfdUrl());
                                                    infoEntity.setKprq(orderInvoiceInfoEntity.getKprq());
                                                    infoEntity.setQdfphm(orderInvoiceInfoEntity.getQdfphm());
                                                    infoEntity.setUpdateTime(new Date());
                                                    orderInvoiceInfoDao.updateById(infoEntity);
                                                }
                                            }
                                        }
                                    }
                                }

                                // 查看配置是否 自动交付
                                //OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectAllList();
                                OrderInvoiceConfigEntity orderInvoiceConfigEntity = orderInvoiceConfigDao.selectByNsrsbh(orderInvoiceInfoEntity1.getXhfNsrsbh());

                                if (Objects.nonNull(orderInvoiceConfigEntity)) {
                                    // 自动交付
                                    if ("0".equals(orderInvoiceConfigEntity.getKpzdjf())) {
                                        log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动推送邮件或者手机 Start");
                                        // 自动发送
                                        if (!StringUtils.isEmpty(orderInvoiceInfo.getGhfYx())) {
                                            // 自动发送邮箱
                                            log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动发送邮件账号: {},全电号码: {}", orderInvoiceInfo.getGhfYx(), orderInvoiceInfo.getQdfphm());
                                            Map<String, Object> sendEmailMap = openApiService.sendEmail(orderInvoiceInfo.getGhfYx(), orderInvoiceInfo.getQdfphm());
                                            if ("0000".equals(String.valueOf(sendEmailMap.get(OrderManagementConstant.CODE)))) {
                                                orderInvoiceInfo.setEmailPushStatus("2");
                                            }else {
                                                orderInvoiceInfo.setEmailPushStatus("3");
                                            }
                                        }
                                        if (!StringUtils.isEmpty(orderInvoiceInfo.getGhfSj())) {
                                            // 自动发送手机
                                            log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动发送手机号码: {},全电号码: {}", orderInvoiceInfo.getGhfSj(), orderInvoiceInfo.getQdfphm());
                                            Map<String, Object> sendMessageMap = openApiService.sendShortMessage(orderInvoiceInfo.getGhfSj(), orderInvoiceInfo.getQdfphm());
                                            if ("0000".equals(String.valueOf(sendMessageMap.get(OrderManagementConstant.CODE)))) {
                                                orderInvoiceInfo.setShortMsgPushStatus("2");
                                            }else {
                                                orderInvoiceInfo.setShortMsgPushStatus("3");
                                            }
                                        }
                                        log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动推送邮件或者手机 End");
                                    }
                                    // 自动回推
                                    if (!StringUtils.isEmpty(orderInvoiceInfo.getXtly())) {
                                        InvoiceBackpushConfigEntity invoiceBackpushConfigEntity = invoiceBackpushConfigService.getInvoiceBackpushConfigByOrderConfigIdAndXtly(orderInvoiceConfigEntity.getId(), orderInvoiceInfo.getXtly());
                                        if (Objects.nonNull(invoiceBackpushConfigEntity)) {
                                            log.info("getTaskInvoiceInfo 定时任务查询开票结果-自动推送接口地址: {},全电号码: {}",invoiceBackpushConfigEntity.getBackpushUrl(),orderInvoiceInfo.getQdfphm());
                                            Map<String, Object> sendBackpushMap = openApiService.sendPushData(invoiceBackpushConfigEntity.getBackpushUrl(), orderInvoiceInfo, tenantCode);
                                            if ("0000".equals(String.valueOf(sendBackpushMap.get(OrderManagementConstant.CODE)))) {
                                                orderInvoiceInfo.setPushStatus("1");
                                            }else {
                                                orderInvoiceInfo.setPushStatus("2");
                                            }
                                        }
                                    }
                                    //更新一下推送状态
                                    orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                                }
                                // 如果为红票
                                if ("1".equals(orderInvoiceInfo.getKplx())) {
                                    // 红字信息表的开具状态更新为 已开具
                                    RedInvoiceConfirmEntity redInvoiceConfirmEntity = redInvoiceConfirmDao.selectRedConfirmDaoByHztzdbh(orderInvoiceInfo.getHzxxbbh());
                                    if (Objects.nonNull(redInvoiceConfirmEntity)) {
                                        redInvoiceConfirmEntity.setKjzt("1");
                                        redInvoiceConfirmEntity.setKprq(orderInvoiceInfo.getKprq());
                                        redInvoiceConfirmEntity.setQdfphm(orderInvoiceInfo.getQdfphm());
                                        redInvoiceConfirmEntity.setUpdateTime(new Date());
                                        redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                                    }
                                    // 红票开具成功,更改蓝票红冲状态为 红冲成功
                                    OrderInvoiceInfoEntity infoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(orderInvoiceInfo.getYqdfphm());
                                    if (Objects.nonNull(infoEntity)) {
                                        // 红冲成功
                                        infoEntity.setChBz("1");
                                        infoEntity.setUpdateTime(new Date());
                                        orderInvoiceInfoDao.updateById(infoEntity);
                                    }
                                }
                            } else {
                                log.info("getTaskInvoiceInfo 863正确返回 反序列化data失败");
                            }
                        } else {
                            log.info("getTaskInvoiceInfo 863正确返回 data为空");
                            OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInvoiceInfoEntity1.getFpqqlsh());
                            orderInvoiceInfo.setByzd1(status);
                            orderInvoiceInfo.setByzd2(message);
                            orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                            orderInvoiceInfo.setUpdateTime(new Date());
                            orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                            // 应收单情况下开票失败将开票时置为未勾选状态的订单统一为开票失败状态
                            if (!StringUtils.isEmpty(orderInvoiceInfo.getDjcjsz()) && "1".equals(orderInvoiceInfo.getDjcjsz())) {
                                // 更新同一应收单下 另一个或者多个订单的状态
                                if (!StringUtils.isEmpty(orderInvoiceInfoEntity1.getYsdh())) {
                                    //应收单情况下，发起开票请求，其他订单状态会置为未勾选状态
                                    // 强制统一应收单下订单状态
                                    List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(orderInvoiceInfoEntity1.getYsdh(), orderInvoiceInfoEntity1.getXhfNsrsbh(),null);
                                    if (!CollectionUtils.isEmpty(orderInvoiceInfoEntities)) {
                                        for (OrderInvoiceInfoEntity infoEntity : orderInvoiceInfoEntities) {
                                            if (!orderInvoiceInfoEntity1.getId().equals(infoEntity.getId())) {
                                                infoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                                                infoEntity.setByzd1(status);
                                                infoEntity.setByzd2(message);
                                                infoEntity.setUpdateTime(new Date());
                                                orderInvoiceInfoDao.updateById(infoEntity);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else if (Objects.nonNull(r) && !"0000".equals(r.get("code"))) {
                        log.info("getTaskInvoiceInfo 非863返回");
                        String status = (String) r.get("code");
                        String message = (String) r.get("msg");
                        if (status.equals("300111")) {
                            // 开具中
                            OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInvoiceInfoEntity1.getFpqqlsh());
                            orderInvoiceInfo.setByzd1(status);
                            orderInvoiceInfo.setByzd2(message);
                            orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_1.getKey());
                            orderInvoiceInfo.setUpdateTime(new Date());
                            orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                        } else {
                            // 开票结果返回失败 根据ddqqlsh 更细本地发票状态为失败
                            OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInvoiceInfoEntity1.getFpqqlsh());
                            orderInvoiceInfo.setByzd1(status);
                            orderInvoiceInfo.setByzd2(message);
                            orderInvoiceInfo.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                            orderInvoiceInfo.setUpdateTime(new Date());
                            orderInvoiceInfoDao.updateById(orderInvoiceInfo);
                            // 应收单情况下开票失败将开票时置为未勾选状态的订单统一为开票失败状态
                            if (!StringUtils.isEmpty(orderInvoiceInfo.getDjcjsz()) && "1".equals(orderInvoiceInfo.getDjcjsz())) {
                                // 更新同一应收单下 另一个或者多个订单的状态
                                if (!StringUtils.isEmpty(orderInvoiceInfoEntity1.getYsdh())) {
                                    //应收单情况下，发起开票请求，其他订单状态会置为未勾选状态
                                    // 强制统一应收单下订单状态
                                    List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities = orderInvoiceInfoDao.selectDdhListByYsdhAndKpzt(orderInvoiceInfoEntity1.getYsdh(), orderInvoiceInfoEntity1.getXhfNsrsbh(),null);
                                    if (!CollectionUtils.isEmpty(orderInvoiceInfoEntities)) {
                                        for (OrderInvoiceInfoEntity infoEntity : orderInvoiceInfoEntities) {
                                            if (!orderInvoiceInfoEntity1.getId().equals(infoEntity.getId())) {
                                                infoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                                                infoEntity.setByzd1(status);
                                                infoEntity.setByzd2(message);
                                                infoEntity.setUpdateTime(new Date());
                                                orderInvoiceInfoDao.updateById(infoEntity);
                                            }
                                        }
                                    }
                                }
                            }
                            log.error("getTaskInvoiceInfo 非863返回 返回data为空");

                        }
                    } else {
                        log.info("定时任务getTaskInvoiceInfo函数执行查询返回为空");
                    }
                } catch (Exception e) {
                    OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.queryStatusByDdqqlsh(orderInvoiceInfoEntity1.getFpqqlsh());
                    if (Objects.nonNull(orderInvoiceInfoEntity)) {
                        orderInvoiceInfoEntity.setByzd1("9999");
                        orderInvoiceInfoEntity.setByzd2("开票异常");
                        orderInvoiceInfoEntity.setKpzt(InvoiceStatusEnum.INVOICE_STATUS_3.getKey());
                        orderInvoiceInfoEntity.setUpdateTime(new Date());
                        orderInvoiceInfoDao.updateById(orderInvoiceInfoEntity);
                    }
                    log.error("定时任务getTaskInvoiceInfo函数 出现异常: {}", e);
                }
            }
        }
        log.info("getTaskInvoiceInfo 定时任务查询开票结果End");
    }

    @Override
    public void taxGenerateRedTableResult() {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntities = nsrsbhTenantRelationService.listAll();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntities)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntities) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                if(org.apache.commons.lang3.StringUtils.isNotBlank(nsrsbhTenantRelationEntity.getDbUrl())){
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getDbUrl());
                }
            }
            for (String dbUrl : tenantCodeList) {
                Map map = new HashMap();
                map.put("db_url", dbUrl);
                List<DbTenantEntity> list =  dbTenantDao.selectByMap(map);
                log.info("开始切换到数据源:{}",list.get(0).getTenantCode());
                String changeTenantResult = tenantRdsService.switchRds(list.get(0).getTenantCode());
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("taxGenerateRedTableResult 切换业务数据源失败tenantCode: {}", list.get(0).getTenantCode());
                    continue;
                }
                taskForRedTableResult();
            }
        } else {
            log.info("taxGenerateRedTableResult 销方税号租户关联表数据为空");
        }
    }

    @Override
    public BlueInvoicesIssueRes blueInvoiceIssue(List<OrderInvoiceInfoEntity> orderInvoiceInfoEntities) {
        DynamicDataSource.setDataSourceDefault();
        List<NsrsbhTenantRelationEntity> nsrsbhTenantRelationEntities = nsrsbhTenantRelationService.listAll();
        // 查询tenant_code
        if (!CollectionUtils.isEmpty(nsrsbhTenantRelationEntities)) {
            Set<String> tenantCodeList = new HashSet<>();
            for (NsrsbhTenantRelationEntity nsrsbhTenantRelationEntity : nsrsbhTenantRelationEntities) {
                // 获取数据源
                // 每次循环获取数据源 再把下面的code封装为方法 循环调用
                if(org.apache.commons.lang3.StringUtils.isNotBlank(nsrsbhTenantRelationEntity.getDbUrl())){
                    tenantCodeList.add(nsrsbhTenantRelationEntity.getDbUrl());
                }
            }
            for (String dbUrl : tenantCodeList) {
                Map map = new HashMap();
                map.put("db_url", dbUrl);
                List<DbTenantEntity> list =  dbTenantDao.selectByMap(map);
                log.info("开始切换到数据源:{}",list.get(0).getTenantCode());
                String changeTenantResult = tenantRdsService.switchRds(list.get(0).getTenantCode());
                if (!StringUtils.isEmpty(changeTenantResult)) {
                    log.info("getTaskInvoiceInfo 切换业务数据源失败tenantCode: {}", list.get(0).getTenantCode());
                    continue;
                }
                BlueInvoicesIssueRes blueInvoicesIssueRes = orderInvoiceInfoService.blueOrderInvoiceIssue(orderInvoiceInfoEntities);
                return blueInvoicesIssueRes;
            }
        } else {
            log.info("getTaskInvoiceInfo 销方税号租户关联表数据为空");
        }
        return new BlueInvoicesIssueRes();
    }

    @Override
    public InvoiceIssueRes getToken(AccessTokeReq accessTokeReq) {
        AccessTokenRes accessTokenRes = new AccessTokenRes();
        accessTokenRes.setTokenType("bearer");
        accessTokenRes.setExpiresIn(RandomUtil.randomInt(35000, 42000) + "");
        accessTokenRes.setScope("read write");
        String uuid = UUID.randomUUID().toString();
        accessTokenRes.setAccessToken(uuid);

        return InvoiceIssueRes.ok(accessTokenRes);
    }

    @Override
    public InvoiceIssueRes queryInvoiceInfo(DdqqlshParam ddqqlshParam) {
        log.info("发票查询接口-queryInvoiceInfo 入参: {}", JsonUtils.getInstance().toJsonString(ddqqlshParam));

        InvoiceIssueRes invoiceIssueRes = null;
        if (StringUtils.isEmpty(ddqqlshParam.getDdqqlsh())) {
            // 校验
            invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_DDQQLSH_ERROR_9611.getKey(), OrderInfoContentEnum.BULE_INVOICE_DDQQLSH_ERROR_9611.getMessage());
        } else {
            try {
                OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.queryStatusByDdqqlsh(ddqqlshParam.getDdqqlsh());
                if (Objects.nonNull(orderInvoiceInfoEntity)) {
                    /*FpxxListRes fpxxListRes = new FpxxListRes();
                    List<FpxxRes> fpxx = new ArrayList<>();*/

                    /*if (StringUtils.isEmpty(orderInvoiceInfoEntity.getYddh())) {
                        //代表原始订单，没有做过合并拆分
                        FpxxRes fpxxRes = new FpxxRes();
                        fpxxRes.setDdqqlsh(ddqqlshParam.getDdqqlsh());
                        fpxxRes.setDdlx("0");
                        fpxxRes.setDdh(orderInvoiceInfoEntity.getDdh());
                        KzxxRes kzxxRes = new KzxxRes();
                        kzxxRes.setDdh(orderInvoiceInfoEntity.getDdh());
                        kzxxRes.setDdqqlsh(ddqqlshParam.getDdqqlsh());
                        if ("0".equals(orderInvoiceInfoEntity.getKpzt()) || "1".equals(orderInvoiceInfoEntity.getKpzt())) {
                            fpxxRes.setKpzt("2");
                        } else if ("2".equals(orderInvoiceInfoEntity.getKpzt())) {

                            // 开票日期
                            String format = DateUtil.format(orderInvoiceInfoEntity.getKprq(), "yyyy-MM-dd HH:mm:ss");
                            fpxxRes.setKprq(format);
                            fpxxRes.setQdfphm(orderInvoiceInfoEntity.getQdfphm());
                            fpxxRes.setFplx(orderInvoiceInfoEntity.getKplx());
                            fpxxRes.setDylpqdfphm("");
                            // PDF
                            fpxxRes.setPdfxzurl(orderInvoiceInfoEntity.getPdfUrl());
                            // OFD   xml
                            if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getPdfUrl()) && orderInvoiceInfoEntity.getPdfUrl().contains("PDF")) {
                                // 开始组装OFD文件
                                String ofdUrl = orderInvoiceInfoEntity.getPdfUrl().replace("PDF", "OFD");
                                fpxxRes.setOfdxzurl(ofdUrl);
                                // 开始组装XML文件
                                String xmlUrl = orderInvoiceInfoEntity.getPdfUrl().replace("PDF", "XML");
                                fpxxRes.setXmlxzurl(xmlUrl);
                            }
                            fpxx.add(fpxxRes);

                            // 验证红票
                            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByYqdfphm(orderInvoiceInfoEntity.getQdfphm());
                            if (Objects.nonNull(invoiceInfoEntity)) {
                                // 该蓝票已经被红冲 -----生成红票
                                FpxxRes hpxx = new FpxxRes();
                                hpxx.setQdfphm(invoiceInfoEntity.getQdfphm());
                                hpxx.setDylpqdfphm(orderInvoiceInfoEntity.getQdfphm());
                                hpxx.setFplx(invoiceInfoEntity.getKplx());
                                // 格式化开票日期
                                String hpFormat = DateUtil.format(invoiceInfoEntity.getKprq(), "yyyy-MM-dd HH:mm:ss");
                                hpxx.setKprq(hpFormat);

                                // 红票PDF
                                hpxx.setPdfxzurl(invoiceInfoEntity.getPdfUrl());
                                // 红票 OFD -------  红票 xml
                                if (!StringUtils.isEmpty(invoiceInfoEntity.getPdfUrl()) && invoiceInfoEntity.getPdfUrl().contains("PDF")) {
                                    // 开始组装OFD文件
                                    String ofdUrl = invoiceInfoEntity.getPdfUrl().replace("PDF", "OFD");
                                    hpxx.setOfdxzurl(ofdUrl);
                                    // 开始组装XML文件
                                    String xmlUrl = invoiceInfoEntity.getPdfUrl().replace("PDF", "XML");
                                    hpxx.setXmlxzurl(xmlUrl);
                                }
                                fpxx.add(hpxx);
                            }
                            fpxxListRes.setFpxx(fpxx);
                            invoiceIssueRes = InvoiceIssueRes.ok(fpxxListRes);
                        } else if ("3".equals(orderInvoiceInfoEntity.getKpzt())) {
                            // 开具失败
                            invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_KJSB_9500.getKey(), orderInvoiceInfoEntity.getByzd2());
                        }
                    } else {
                        QueryWrapper<OrderStatusOptRecordEntity> query = new QueryWrapper<>();
                        query.eq("is_delete", "0");
                        query.eq("orderInvoiceId", orderInvoiceInfoEntity.getId());
                        List<OrderStatusOptRecordEntity> orderStatusOptRecordEntities = orderStatusOptRecordDao.selectList(query);
                        if (orderStatusOptRecordEntities.size() > 0) {
                            //orderStatusOptRecordDao

                        } else {

                        }

                    }*/
                    // 待开票
                    if ("0".equals(orderInvoiceInfoEntity.getKpzt())) {
                        // 未开具
                        invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_WKJ_9497.getKey(), OrderInfoContentEnum.BULE_INVOICE_WKJ_9497.getMessage());
                    } else if ("1".equals(orderInvoiceInfoEntity.getKpzt())) {
                        // 开具中
                        invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_KJZ_9498.getKey(), OrderInfoContentEnum.BULE_INVOICE_KJZ_9498.getMessage());
                    } else if ("2".equals(orderInvoiceInfoEntity.getKpzt())) {
                        // 先判断 是否过合并

                        // 开具成功
                        FpxxListRes fpxxListRes = new FpxxListRes();
                        List<FpxxRes> fpxx = new ArrayList<>();

                        FpxxRes fpxxRes = new FpxxRes();
                        // 开票日期
                        String format = DateUtil.format(orderInvoiceInfoEntity.getKprq(), "yyyy-MM-dd HH:mm:ss");
                        fpxxRes.setKprq(format);
                        fpxxRes.setQdfphm(orderInvoiceInfoEntity.getQdfphm());
                        fpxxRes.setFplx(orderInvoiceInfoEntity.getKplx());
                        fpxxRes.setDylpqdfphm("");
                        // PDF
                        fpxxRes.setPdfxzurl(orderInvoiceInfoEntity.getPdfUrl());
                        // OFD   xml
                        if (!StringUtils.isEmpty(orderInvoiceInfoEntity.getPdfUrl()) && orderInvoiceInfoEntity.getPdfUrl().contains("PDF")) {
                            // 开始组装OFD文件
                            String ofdUrl = orderInvoiceInfoEntity.getPdfUrl().replace("PDF", "OFD");
                            fpxxRes.setOfdxzurl(ofdUrl);
                            // 开始组装XML文件
                            String xmlUrl = orderInvoiceInfoEntity.getPdfUrl().replace("PDF", "XML");
                            fpxxRes.setXmlxzurl(xmlUrl);
                        }
                        fpxx.add(fpxxRes);

                        // 验证红票
                        OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByYqdfphm(orderInvoiceInfoEntity.getQdfphm());
                        if (Objects.nonNull(invoiceInfoEntity)) {
                            // 该蓝票已经被红冲 -----生成红票
                            FpxxRes hpxx = new FpxxRes();
                            hpxx.setQdfphm(invoiceInfoEntity.getQdfphm());
                            hpxx.setDylpqdfphm(orderInvoiceInfoEntity.getQdfphm());
                            hpxx.setFplx(invoiceInfoEntity.getKplx());
                            // 格式化开票日期
                            String hpFormat = DateUtil.format(invoiceInfoEntity.getKprq(), "yyyy-MM-dd HH:mm:ss");
                            hpxx.setKprq(hpFormat);

                            // 红票PDF
                            hpxx.setPdfxzurl(invoiceInfoEntity.getPdfUrl());
                            // 红票 OFD -------  红票 xml
                            if (!StringUtils.isEmpty(invoiceInfoEntity.getPdfUrl()) && invoiceInfoEntity.getPdfUrl().contains("PDF")) {
                                // 开始组装OFD文件
                                String ofdUrl = invoiceInfoEntity.getPdfUrl().replace("PDF", "OFD");
                                hpxx.setOfdxzurl(ofdUrl);
                                // 开始组装XML文件
                                String xmlUrl = invoiceInfoEntity.getPdfUrl().replace("PDF", "XML");
                                hpxx.setXmlxzurl(xmlUrl);
                            }
                            fpxx.add(hpxx);
                        }
                        fpxxListRes.setFpxx(fpxx);
                        invoiceIssueRes = InvoiceIssueRes.ok(fpxxListRes);
                    } else if ("3".equals(orderInvoiceInfoEntity.getKpzt())) {
                        // 开具失败
                        invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_KJSB_9500.getKey(), orderInvoiceInfoEntity.getByzd2());
                    }
                } else {
                    // 不存在
                    invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.BULE_INVOICE_BCZ_9501.getKey(), OrderInfoContentEnum.BULE_INVOICE_BCZ_9501.getMessage());
                }
            } catch (Exception e) {
                log.error("蓝字发票结果查询接口 queryInvoiceInfo 异常:{}", e);
                invoiceIssueRes = InvoiceIssueRes.error(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
            }
        }

        return invoiceIssueRes;
    }

    @Override
    public InvoiceIssueRes invoiceIssue(InvoiceIssueInfoParam invoiceIssueInfoParam) {
        log.info("ERP 推送订单接口 入参: {}", JsonUtils.getInstance().toJsonString(invoiceIssueInfoParam));

        InvoiceIssueRes invoiceIssueRes = null;
        try {
            if (ObjectUtils.isEmpty(invoiceIssueInfoParam)) {
                String errorCode = OrderInfoContentEnum.BULE_INVOICE_DATA_ERROR_9610.getKey();
                String errorMsg = OrderInfoContentEnum.BULE_INVOICE_DATA_ERROR_9610.getMessage();
                invoiceIssueRes = InvoiceIssueRes.res(errorCode, errorMsg);
            } else {
                log.info("ERP 推送订单接口-invoiceIssueInfoParam :{}", invoiceIssueInfoParam);
                invoiceIssueInfoParam.setKpfs("1");
                InvoiceIssueRes res = invoiceIssueService.invoiceIssue(Collections.singletonList(invoiceIssueInfoParam));
                log.info("ERP 推送订单接口-invoiceIssueRes 返回: {}", JsonUtils.getInstance().toJsonString(res));
                // 组装data
                String ztdm = (String) res.get("ztdm");
                String ztxx = (String) res.get("ztxx");
                invoiceIssueRes = InvoiceIssueRes.res(ztdm, ztxx);
            }
        } catch (Exception e) {
            log.error("推送订单接口-invoiceIssue异常: {}", e);
            invoiceIssueRes = InvoiceIssueRes.res(OrderInfoContentEnum.API_RETURN_ERROR_9998.getKey(), OrderInfoContentEnum.API_RETURN_ERROR_9998.getMessage());
        }

        return invoiceIssueRes;
    }

    private Map<String, String> checkCommonDataParam(CommonDataParam commonDataParam) {
        Map<String, String> checkResultMap = new HashMap<>(10);
        // requestId
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_REQUESTID_ERROR_9606, commonDataParam.getRequestId());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // entId
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_ENTID_ERROR_9607, commonDataParam.getEntId());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 压缩标识compressType
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_YASUO_ERROR_9608, commonDataParam.getCompressType());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // 加密标识encryptType
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_JIAMI_ERROR_9609, commonDataParam.getEncryptType());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        // data
        checkResultMap = CheckParamUtil.checkParam(OrderInfoContentEnum.BULE_INVOICE_DATA_ERROR_9610, commonDataParam.getData());
        if (!OrderInfoContentEnum.SUCCESS.getKey().equals(checkResultMap.get(OrderManagementConstant.ERRORCODE))) {
            return checkResultMap;
        }
        return null;
    }


    private void taskForRedTableResult() {
        log.info("taxGenerateRedTableResult 定时任务获取红字信息表编号及状态Start");
        List<RedInvoiceConfirmEntity> redInvoiceConfirmEntities = redInvoiceConfirmDao.taxGenerateRedTableResult();
        if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
            // 状态为 未开具 和 红字通知单编码为空
            redInvoiceConfirmEntities = redInvoiceConfirmEntities.stream().filter(a -> StringUtils.isEmpty(a.getHztzdbh())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(redInvoiceConfirmEntities)) {
                for (RedInvoiceConfirmEntity redInvoiceConfirmEntity : redInvoiceConfirmEntities) {
                    try {
                        TaxGenerateRedConfResultEntity taxGenerateRedConfResultEntity = new TaxGenerateRedConfResultEntity();
                        // 蓝票
                        OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
                        log.info("taxGenerateRedTableResult orderInvoiceInfoEntity蓝票信息: {}", JsonUtils.getInstance().toJsonString(orderInvoiceInfoEntity));
                        if (Objects.nonNull(orderInvoiceInfoEntity)) {
                            taxGenerateRedConfResultEntity.setLsh(orderInvoiceInfoEntity.getFpqqlsh());
                            taxGenerateRedConfResultEntity.setNsrsbh(orderInvoiceInfoEntity.getXhfNsrsbh());
                        }
                        long lo = System.currentTimeMillis();
                        String jsonString = JsonUtils.getInstance().toJsonString(taxGenerateRedConfResultEntity);
                        log.info("taxGenerateRedTableResult 定时任务获取红字信息表编号及状态入参: {}", jsonString);
                        String res = HttpUtils.doPost(invoiceGenerateRedConfResult, jsonString);
                        log.info("taxGenerateRedTableResult 定时任务获取红字信息表编号及状态接口耗时: {}", System.currentTimeMillis() - lo);
                        log.info("taxGenerateRedTableResult 定时任务获取红字信息表编号及状态接口出参: {}", res);
                        R r = JsonUtils.getInstance().fromJson(res, R.class);
                        if (Objects.nonNull(r) && "0000".equals(r.get("code"))) {
                            JSONObject data = (JSONObject) r.get("data");
                            if (!StringUtils.isEmpty(data)) {
                                RedInvoiceConfirmEntity invoiceConfirmEntity = JsonUtils.getInstance().fromJson(data.toJSONString(), RedInvoiceConfirmEntity.class);
                                if (Objects.nonNull(invoiceConfirmEntity)) {
                                    // 红字信息表 更新红字通知单编号
                                    redInvoiceConfirmEntity.setHztzdbh(invoiceConfirmEntity.getHzqrdbh());
                                    redInvoiceConfirmEntity.setZt(invoiceConfirmEntity.getZt());
                                    redInvoiceConfirmDao.updateById(redInvoiceConfirmEntity);
                                    // 发票主表 更新红字通知单编号
                                    // 红票信息
                                    OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByYqdfphm(redInvoiceConfirmEntity.getDylpqdfphm());
                                    if (Objects.nonNull(invoiceInfoEntity)) {
                                        invoiceInfoEntity.setHzxxbbh(invoiceConfirmEntity.getHzqrdbh());
                                        invoiceInfoEntity.setUpdateTime(new Date());
                                        orderInvoiceInfoDao.updateById(invoiceInfoEntity);
                                    }
                                } else {
                                    log.info("taxGenerateRedTableResult 返序列化data为空");
                                }
                            } else {
                                log.info("taxGenerateRedTableResult data返回为空");
                            }
                        } else {
                            log.info("taxGenerateRedTableResult 底层接口未正确返回");
                        }
                    } catch (Exception e) {
                        log.error("taxGenerateRedTableResult 出现异常: {}", e);
                    }
                }
            }
        }
        log.info("taxGenerateRedTableResult 定时任务获取红字信息表编号及状态End");
    }

    /**
     * 参数解压缩 解密
     *
     * @return
     */
    public String commonDecrypt(String zipCode, String encryptCode, String content, String secretKey) {

        String json = content;
        byte[] de = null;
        try {
            json = Base64Encoding.decodeToString(content);
            de = Base64Encoding.decode(content);
        } catch (Exception e) {
            log.error("base64解密出现异常:{}", e);
        }
        if (ConfigurerInfo.ZIPCODE_1.equals(zipCode)) {
            // 解压缩
            try {
                de = GzipUtils.decompress(de);
                json = new String(de, StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("解压缩出现异常:{}", e);
            }
        }
        // 解密
        if (ConfigurerInfo.ENCRYPTCODE_1.equals(encryptCode)) {
            try {
                // 截取秘钥
                String password = secretKey.substring(0, ConfigurerInfo.PASSWORD_SIZE);

                // 解密
                json = new String(TripleDesUtil.decryptMode(password, de), StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("3DES解密出现异常:{}", e);
            }
        }
        return json;
    }

    /**
     * 参数压缩 加密
     */
    public String commonEncrypt(String zipCode, String encryptCode, String content, String secretKey) {
        String json = content;
        byte[] de = null;
        // 加密
        if (ConfigurerInfo.ENCRYPTCODE_1.equals(encryptCode)) {
            try {
                // 截取秘钥
                String password = secretKey.substring(0, ConfigurerInfo.PASSWORD_SIZE);
                // 加密
                de = TripleDesUtil.encryptMode(password, json.getBytes());
            } catch (Exception e) {
                log.error("{}3DES加密出现异常:{}", LOGGER_MSG, e);
            }
        }
        if (ConfigurerInfo.ZIPCODE_1.equals(zipCode)) {
            // 压缩
            try {
                if (de != null) {
                    de = GzipUtils.compress(de);
                } else {
                    de = GzipUtils.compress(json.getBytes());
                }
            } catch (Exception e) {
                log.error("{}GZIP压缩出现异常:{}", LOGGER_MSG, e);
            }
        }
        try {
            if (de != null) {
                json = Base64Encoding.encodeToString(de);
            } else {
                json = Base64Encoding.encodeToString(content.getBytes());
            }
        } catch (Exception e) {
            log.error("{}base64压缩出现异常:{}", LOGGER_MSG, e);
        }
        return json;
    }

    /**
     * 封装并对公共参数排序
     *
     * @param gzip
     * @param encode
     * @param content
     * @param secretId
     * @param secretKey
     * @param signUrl
     * @return
     */
    private static Map<String, String> getSignature(String gzip, String encode, String content, String secretId, String secretKey, String signUrl) {
        Calendar c = Calendar.getInstance();
        long timeInMillis = c.getTimeInMillis();
        int nonce = HmacSha1Util.getRandNum(1, 999999);
        HashMap<String, String> reqMap = new HashMap<>(10);
        try {
            TreeMap<String, String> sortMap = new TreeMap<>();
            sortMap.put(ConfigurerInfo.NONCE, String.valueOf(nonce));
            sortMap.put(ConfigurerInfo.SECRETID, secretId);
            sortMap.put(ConfigurerInfo.TIMESTAMP, String.valueOf(timeInMillis));
            sortMap.put(ConfigurerInfo.CONTENT, content);
            sortMap.put(ConfigurerInfo.ENCRYPTCODE, encode);
            sortMap.put(ConfigurerInfo.ZIPCODE, gzip);
            String localSign = HmacSha1Util.genSign(signUrl, sortMap, secretKey);
            log.debug("{}生成的签名值为:{}", LOGGER_MSG, localSign);
            sortMap.put(ConfigurerInfo.SIGNATURE, localSign);
            reqMap = new HashMap<>(sortMap);
        } catch (Exception e) {
            log.error("{}生成签名异常:{}", LOGGER_MSG, e);
        }

        return reqMap;
    }

    /**
     * 拼接url
     *
     * @param reqUrl
     * @return
     */
    public static String getAuthUrl(String reqUrl) {
        if (reqUrl.split(ConfigureConstant.STRING_COLON).length > ConfigureConstant.INT_2) {
            String one = reqUrl.split(ConfigureConstant.STRING_COLON)[1];
            String two = reqUrl.split(ConfigureConstant.STRING_COLON)[2];
            //支持带端口数据的返回
            if (two.indexOf(ConfigureConstant.STRING_SLASH_LINE) > 0) {
                reqUrl = one.replaceAll("//", "") + ConfigureConstant.STRING_COLON + two;
            } else {
                reqUrl = one.replaceAll("//", "") + two.substring(two.indexOf(ConfigureConstant.STRING_SLASH_LINE));
            }
        } else if (reqUrl.split(ConfigureConstant.STRING_COLON).length == ConfigureConstant.INT_2) {
            String one = reqUrl.split(ConfigureConstant.STRING_COLON)[1];
            reqUrl = one.contains("//") ? one.replaceAll("//", "") : reqUrl;
        }
        return "POST" + reqUrl + "?";
    }

    private String allocateInvoiceEncrypt(OrderInvoicesIssueRes orderInvoicesIssueRess, String secretId, String secretKey) {
        String json = JsonUtils.getInstance().toJsonString(orderInvoicesIssueRess);
        log.info("allocateInvoiceEncrypt json: {}", json);
        String encrypt = commonEncrypt("0", "0", json, secretKey);
        log.info("allocateInvoiceEncrypt encrypt: {}", encrypt);
        Map<String, String> signatureRes = getSignature("0", "0", encrypt, secretId, secretKey, getAuthUrl(domain + path + method));
        String jsonString = JsonUtils.getInstance().toJsonString(signatureRes);
        log.info("allocateInvoiceEncrypt signatureRes: {}", jsonString);
        OrderInvoiceResponseDataRes orderInvoiceResponseDataRes = new OrderInvoiceResponseDataRes();
        ResponseStatusRes responseStatusRes = new ResponseStatusRes();
        responseStatusRes.setCode("0000");
        responseStatusRes.setMessage("success");
        orderInvoiceResponseDataRes.setResponseStatusRes(responseStatusRes);
        ResponseDataRes responseDataRes = new ResponseDataRes();
        responseDataRes.setEncryptCode("0");
        responseDataRes.setZipCode("0");
        responseDataRes.setContent(encrypt);
        orderInvoiceResponseDataRes.setResponseDataRes(responseDataRes);
        String orderInvoiceResponseRes = JsonUtils.getInstance().toJsonString(orderInvoiceResponseDataRes);
        return orderInvoiceResponseRes;
    }

    private String getOrderInfoAndInvoiceInfoEncrypt(OrderInfoAndInvoiceInfoRes orderInvoicesIssueRess, String secretId, String secretKey) {
        String json = JsonUtils.getInstance().toJsonString(orderInvoicesIssueRess);
        log.info("getOrderInfoAndInvoiceInfoEncrypt json: {}", json);
        String encrypt = commonEncrypt("0", "0", json, secretKey);
        log.info("getOrderInfoAndInvoiceInfoEncrypt encrypt: {}", encrypt);
        Map<String, String> signatureRes = getSignature("0", "0", encrypt, secretId, secretKey, getAuthUrl(domain + path + method));
        String jsonString = JsonUtils.getInstance().toJsonString(signatureRes);
        log.info("getOrderInfoAndInvoiceInfoEncrypt signatureRes: {}", jsonString);
        OrderInvoiceResponseDataRes orderInvoiceResponseDataRes = new OrderInvoiceResponseDataRes();
        ResponseStatusRes responseStatusRes = new ResponseStatusRes();
        responseStatusRes.setCode("0000");
        responseStatusRes.setMessage("success");
        orderInvoiceResponseDataRes.setResponseStatusRes(responseStatusRes);
        ResponseDataRes responseDataRes = new ResponseDataRes();
        responseDataRes.setEncryptCode("0");
        responseDataRes.setZipCode("0");
        responseDataRes.setContent(encrypt);
        orderInvoiceResponseDataRes.setResponseDataRes(responseDataRes);
        String orderInvoiceResponseRes = JsonUtils.getInstance().toJsonString(orderInvoiceResponseDataRes);
        return orderInvoiceResponseRes;
    }

    private Float slTranst(String sl) {
        Float f = 0.00f;
        if (!StringUtils.isEmpty(sl)) {
            if (sl.contains("%")) {
                sl = sl.replace("%", "");
                f = Float.valueOf(sl) / 100;
            } else {
                return Float.valueOf(sl);
            }
        }
        return f;
    }

    // 不足两位小数补0
    private String formatDecimal(BigDecimal bc) {
        // 不足两位小数补0
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        return decimalFormat.format(bc);
    }

}
