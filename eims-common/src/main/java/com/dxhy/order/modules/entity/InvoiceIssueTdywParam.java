package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InvoiceIssueTdywParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * @link OrderInfoEnum.ORDER_QD_TDYS_00 相关枚举
     */
    private String tdys;
    /**
     * 不动产租赁信息
     */
    private List<InvoiceIssueBdczlxxParam> bdczlxx;
    /**
     * 不动产销售信息
     */
    private List<InvoiceIssueBdcxsxxParam> bdcxsxx;
    /**
     * 建筑服务信息
     */
    private InvoiceIssueJzfwxxParam jzfwxx;
    /**
     * 旅客运输服务信息
     */
    private List<InvoiceIssueLkysfwxxParam> lkysfwxx;
    /**
     * 货物运输信息
     */
    private List<InvoiceIssueHwysxxParam> hwysxx;
    /**
     * 机动车信息
     */
    private InvoiceIssueJdcxxParam jdcxx;
    /**
     * 二手车信息
     */
    private InvoiceIssueEscxxParam escxx;

}
