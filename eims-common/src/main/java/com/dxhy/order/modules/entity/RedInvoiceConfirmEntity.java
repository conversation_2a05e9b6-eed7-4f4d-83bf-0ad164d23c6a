package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 红字发票确认信息表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-22 11:39:24
 */
@TableName("red_invoice_confirm")
@Data
@Getter
@Setter
public class RedInvoiceConfirmEntity extends BasePage implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 红字信息表主键
	 */
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 购销身份 0销售方 1 购买方
	 */
	private String gxsf;
	/**
	 * 对方纳税人识别号
	 */
	private String dfnsrsbh;
	/**
	 * 对方纳税人名称
	 */
	private String dfnsrmc;
	/**
	 * base的纳税人识别号
	 */
	private String nsrsbh;
	/**
	 * 对应蓝票全电发票号码代码
	 */
	private String dylpqdfphm;
	/**
	 * 红字通知单编号
	 */
	private String hztzdbh;
	/**
	 * 发票金额
	 */
	private String fpje;
	/**
	 * 发票税额
	 */
	private String fpse;
	/**
	 * 冲红原因 0 开票有误 1 销售退回 2 销售折让
	 */
	private String chyy;
	/**
	 * 确认单状态：01无需确认 02销方录入待购方确认 03购方录入待销方确认 04购销双方已确认 05作废（销方录入购方否认）
	 * 06作废 （购方录入销方否认） 07作废（超72小时未确认） 08作废（发起方撤销） 09作废（确认后撤销）10 -申请中  11 申请失败(可重试) 12处理失败(不可重试)
	 */
	private String zt;
	/**
	 * 开具状态 N 未开具 1开具中 Y 已开具 3已撤销 4已确认 5已拒绝 6开具失败
	 */
	private String kjzt;
	/**
	 * 受理状态 0 未受理 1受理中 2受理成功 3受理失败
	 */
	private String slzt;
	/**
	 * 确认单标识
	 */
	private String qrdbs;
	/**
	 * 录入方身份 0销售方 1购买方
	 */
	private String lrfsf;
	/**
	 * 全电发票号码
	 */
	private String qdfphm;
	/**
	 * 受理单号
	 */
	private String sldh;
	/**
	 * 开票结果受理单号
	 */
	private String kpjgsldh;
	/**
	 * 开单时间
	 */
	private String kdsj;
	/**
	 * 价税合计
	 */
	private String jshj;
	/**
	 * 销售方纳税人识别号
	 */
	private String xsfnsrsbh;
	/**
	 * 销售方名称
	 */
	private String xsfmc;
	/**
	 * 购买方纳税人识别号
	 */
	private String gmfnsrsbh;
	/**
	 * 购买方名称
	 */
	private String gmfmc;
	/**
	 * 逻辑删除
	 */
	private String isDelete;
	/**
	 * 申请日期
	 */
	private Date sqrq;

	/**
	 * 开票日期
	 */
	private Date kprq;

	private String kprqStr;

	private String lzkprq;
	private String lzhjje;
	private String lzhjse;
	private String lzfppzDm;
	private String zzsytDm;
	private String xfsytDm;
	private String fprzztDm;
	private String ykjhzfpbz;
	private String uuid;
	private String lzfpdm;
	private String lzfphm;
	// 0-我发出的  1-我收到的
	private String fpfqr;
	private String ztIng;

	/**
	 * 开票日期起
	 */
	@TableField(exist = false)
	private String kprqStartTime;

	/**
	 * 开票日期止
	 */
	@TableField(exist = false)
	private String kprqEndTime;

	@TableField(exist = false)
	private String hzqrdbh;

	@TableField(exist = false)
	private String baseNsrsbh;

	@TableField(exist = false)
	private List<OrderInvoiceItemEntity> invoiceItemEntities;

	@TableField(exist = false)
	private List<InvoiceAdditionInfoEntity> invoiceAdditionInfoEntities;

	/**
	 * 创建日期
	 */
	private Date createTime;

	/**
	 * 更新日期
	 */
	private Date updateTime;
	/**
	 * 备用字段1 标识 已被占用
	 */
	private String byzd1;
	/**
	 * 备用字段2 已被占用
	 */
	private String byzd2;
	/**
	 * 备用字段3 已被占用
	 */
	private String byzd3;
	/**
	 * 备用字段4
	 */
	private String byzd4;
	/**
	 * 备用字段5
	 */
	private String byzd5;
	/**
	 * 开具状态code
	 */
	private String kjztCode;
	/**
	 * 开具状态msg
	 */
	private String kjztMsg;
	/**
	 * 系统来源
	 */
	private String xtly;




}
