package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-建筑服务信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceIssueJzfwxxParam implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 土地增值税项目编号
	 */
	private String tdzzsxmbh;

	/**
	 * 跨区域涉税事项报验管理编号
	 */
	private String kqysssxbgglbm;

	/**
	 * 建筑服务发生地（地区）
	 */
	private String jzfwfsd;

	/**
	 * 建筑服务详细地址
	 */
	private String jzfwxxdz;

	/**
	 * 建筑服务名称
	 */
	private String jzfwmc;

	/**
	 * 跨地(市)标志
	 */
	private String kdsbz;
}
