package com.dxhy.order.modules.service;

import com.dxhy.order.exception.OrderReceiveException;
import com.dxhy.order.exception.OrderSplitException;
import com.dxhy.order.model.OrderSplitConfig;
import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;
import com.dxhy.order.pojo.SplitInvoiceInfo;
import com.dxhy.order.utils.R;

import java.util.List;

public interface OrderSplitService {
    /**
     * 按明细拆分并保存
     * @param splitInvoiceInfo
     * @return
     */
    R splitByInvoiceItem(SplitInvoiceInfo splitInvoiceInfo);
    /*
     * 订单拆分后还原
     */
    R splitBack(String orderInvoiceId);
    /**
     * 订单拆分
     *
     * @param orderId 原订单id
     * @param shList  税号集合
     * @param config  页面传参的本次配置
     * @return
     * @throws OrderSplitException
     */
    List<OrderInvoiceInfoEntity> splitOrder(String orderId, List<String> shList, OrderSplitConfig config) throws OrderSplitException;
    /**
     * 保存拆分数据
     *
     * @param orderInvoiceInfoEntityList
     * @throws OrderReceiveException
     */
    void saveOrderSplitInfo(List<OrderInvoiceInfoEntity> orderInvoiceInfoEntityList);

    /**
     * 拆分预校验
     * @param orderIds
     * @param shList
     * @return
     */
    R checkPreSpilt(List<String> orderIds, List<String> shList);
}
