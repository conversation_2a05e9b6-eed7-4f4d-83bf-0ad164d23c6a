package com.dxhy.order.modules.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dxhy.order.utils.BasePage;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票附加要素表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_addition_info")
@Data
public class InvoiceAdditionInfoEntity extends BasePage implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(type = IdType.INPUT)
	@JsonProperty("ID")
	private String id;
	/**
	 * 
	 */
	@ApiModelProperty("发票主表主键")
	@JsonProperty("ORDERINVOICEINFOID")
	private String orderInvoiceInfoId;
	/**
	 * 
	 */
	@ApiModelProperty("附加信息名称")
	@JSONField(name = "FJXXMC")
	@JsonProperty("FJXXMC")
	private String fjxxmc;
	/**
	 * 
	 */
	@ApiModelProperty("附加信息值")
	@JSONField(name = "FJXXZ")
	@JsonProperty("FJXXZ")
	private String fjxxz;

	/**
	 *数据类型 1 文本型 2 数值型 3 日期型
	 */
	@ApiModelProperty("数据类型")
	@JSONField(name = "SJNR")
	@JsonProperty("SJNR")
	private String sjnr;

	/**
	 *附加信息表主键
	 */
	@ApiModelProperty("附加信息表主键")
	@JSONField(name = "ADDITIONELEMENTID")
	@JsonProperty("ADDITIONELEMENTID")
	private String additionElementId;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty("逻辑删除")
	private String isDelete;
}
