package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.modules.pojo.bo.CustomerUpdateExcelBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * excel新增 客户信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("excel新增 客户信息DTO")
public class CustomerInfoExcelSaveDTO extends BaseNsrsbhDTO{

    @ApiModelProperty(name = "客户信息数组", required = true)
    private List<CustomerUpdateExcelBO> dataList;

}
