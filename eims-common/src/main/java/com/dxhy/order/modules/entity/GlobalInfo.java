package com.dxhy.order.modules.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName ：GlobalInfo
 * @Description ：调用大象接口的外层报文参数
 * @date ：2022年6月10日
 */

@Setter
@Getter
public class GlobalInfo implements Serializable {

    private String zipCode;

    private String encryptCode;

    private String dataExchangeId;

    private String entCode;

    private String content;

    // 返回内容
    private GlobalInfoStateInfo returnStateInfo;

}
