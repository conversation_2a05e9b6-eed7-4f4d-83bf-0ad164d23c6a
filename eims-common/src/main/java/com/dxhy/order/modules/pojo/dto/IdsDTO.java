package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * ID数组 DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("ID数组 DTO")
public class IdsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID数组
     * @param
     * @return
     * <AUTHOR>
     **/
    @ApiModelProperty(name = "ID", required = true)
    private List<String> ids;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty(name = "所属纳税人识别号", required = true)
    private String baseNsrsbh;

}
