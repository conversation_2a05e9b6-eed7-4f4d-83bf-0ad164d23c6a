package com.dxhy.order.modules.pojo.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dxhy.order.modules.entity.SceneTemplateEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 场景模板表VO
 * <AUTHOR>
 * @Date 2022/6/27 12:04
 * @Version 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("场景模板表VO")
public class SceneTemplateVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 场景模板主键
     */
    @ApiModelProperty("场景模板主键")
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 名字
     */
    @ApiModelProperty("名字")
    private String name;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * uuid 第三方慧企返回uuid
     */
    @ApiModelProperty("uuid")
    private String uuid;

    public SceneTemplateVO(SceneTemplateEntity entityData){
        if(entityData != null){
            this.id = entityData.getId();
            this.name = entityData.getName();
            if(entityData.getCreateTime() != null){
                this.createTime = entityData.getCreateTime();
            }
            this.createBy = entityData.getCreateBy();
            if(entityData.getUpdateTime() != null){
                this.updateTime = entityData.getUpdateTime();
            }
            this.updateBy = entityData.getUpdateBy();
        }
    }
}
