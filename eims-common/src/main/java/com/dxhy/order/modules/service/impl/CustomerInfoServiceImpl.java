package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.config.OrderConfig;
import com.dxhy.order.constant.ExcelReadContext;
import com.dxhy.order.constant.ExportCustomerInfoEnum;
import com.dxhy.order.constant.ExportExcelCustomerInfoEnum;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.modules.dao.CustomerGroupDao;
import com.dxhy.order.modules.dao.CustomerInfoDao;
import com.dxhy.order.modules.entity.CustomerGroupEntity;
import com.dxhy.order.modules.entity.CustomerInfoEntity;
import com.dxhy.order.modules.pojo.bo.CustomerUpdateExcelBO;
import com.dxhy.order.modules.pojo.bo.GroupTreeBO;
import com.dxhy.order.modules.pojo.dto.*;
import com.dxhy.order.modules.pojo.vo.CustomerUpdateExcelVO;
import com.dxhy.order.modules.pojo.vo.ExportCustomerInfoVO;
import com.dxhy.order.modules.service.CustomerInfoService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 客户信息表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/27 12:13
 * @Version 1.0
 **/
@Service("customerInfoService")
@Slf4j
public class CustomerInfoServiceImpl extends ServiceImpl<CustomerInfoDao, CustomerInfoEntity> implements CustomerInfoService {

    @Autowired
    private CustomerInfoDao customerInfoDao;
    @Autowired
    private CustomerGroupDao customerGroupDao;
    @Resource
    private OrderConfig orderConfig;
    @Resource
    private SsoUtil ssoUtil;
    @Value("${order.invoice.rootName.customer}")
    private String customerRootName;

//    @Value("${user-base/dept/queryAllTaxListByTax}")
//    private String queryAllTaxListByTaxUrl;

    @Override
    public R getGroupTree(String nsrsbh) {
        // 1 判断nsrsbh是否为空
        if(StringUtils.isEmpty(nsrsbh)){
            return R.error("请选择公司主体");
        }
        // 2 根节点不保存到数据库 查询时需要重新生成 为空或没有数据的时候只返回根节点对象
        GroupTreeBO baseGroupTreeBO = new GroupTreeBO(customerRootName);
        List<CustomerGroupEntity> customerGroupEntityList = customerGroupDao.listByNsrsbh(nsrsbh);
        if(CollectionUtils.isEmpty(customerGroupEntityList)){
            return R.ok().put("data", baseGroupTreeBO);
        }
        // 3 组装树形结构
        Map<String, GroupTreeBO> treeMap = new HashMap<>();
        treeMap.put(baseGroupTreeBO.getId(), baseGroupTreeBO);
        List<GroupTreeBO> groupTreeList = new ArrayList<>();
        for (CustomerGroupEntity customerGroupEntity : customerGroupEntityList) {
            GroupTreeBO tempGroupTreeBO = new GroupTreeBO(customerGroupEntity.getId(), customerGroupEntity.getParentId(), customerGroupEntity.getKhflmc(), new ArrayList<>());
            groupTreeList.add(tempGroupTreeBO);
            treeMap.put(customerGroupEntity.getId(), tempGroupTreeBO);
        }
        for (GroupTreeBO groupTreeBO : groupTreeList) {
            GroupTreeBO tempBO = treeMap.get(groupTreeBO.getParentId());
            if(tempBO != null){
                tempBO.getChildren().add(groupTreeBO);
            }
        }
        return R.ok().put("data", treeMap.get(baseGroupTreeBO.getId()));
    }

    @Override
    public R saveCustomerGroup(CustomerGroupSaveDTO customerGroupSaveDTO) {
        String checkStr = this.checkCustomerGroupSaveDTO(customerGroupSaveDTO);
        if(StringUtils.isNotEmpty(checkStr)){
            return R.error(checkStr);
        }
        if(StringUtils.isNotEmpty(customerGroupSaveDTO.getId())){
            // 修改
            CustomerGroupEntity updateData = customerGroupDao.selectById(customerGroupSaveDTO.getId());
            if(updateData == null){
                return R.error("数据不存在");
            }
            updateData.setParentId(customerGroupSaveDTO.getParentId());
            updateData.setKhflmc(customerGroupSaveDTO.getKhflmc());
            updateData.setUpdateTime(new Date());
            updateData.setUpdateBy(ssoUtil.getUserName());
            customerGroupDao.updateById(updateData);
            return R.ok();
        }else{
            // 新增
            CustomerGroupEntity saveData = new CustomerGroupEntity();
            saveData.setId(DistributedKeyMaker.generateShotKey());
            saveData.setParentId(customerGroupSaveDTO.getParentId());
            saveData.setBaseNsrsbh(customerGroupSaveDTO.getBaseNsrsbh());
            saveData.setKhflmc(customerGroupSaveDTO.getKhflmc());
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(ssoUtil.getUserName());
            customerGroupDao.insert(saveData);
            return R.ok().put("data", saveData.getId());
        }
    }

    private String checkCustomerGroupSaveDTO(CustomerGroupSaveDTO customerGroupSaveDTO){
        if(customerGroupSaveDTO == null){
            return "入参为空";
        }
        if(StringUtils.isNotEmpty(customerGroupSaveDTO.getId()) && "0".equals(customerGroupSaveDTO.getId())){
            return "根节点不允许修改";
        }
        if(StringUtils.isEmpty(customerGroupSaveDTO.getParentId())){
            return "上级客户分类为空";
        }
        if(StringUtils.isEmpty(customerGroupSaveDTO.getKhflmc())){
            return "客户分类名称为空";
        }
        if(StringUtils.isEmpty(customerGroupSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isNotEmpty(customerGroupSaveDTO.getId()) && customerGroupSaveDTO.getId().equals(customerGroupSaveDTO.getParentId())){
            return "不能选择分类自身为移动目标";
        }
        List<CustomerGroupEntity> customerGroupEntityList = customerGroupDao.selectListByCustomerGroupSaveDTO(customerGroupSaveDTO);
        if(CollectionUtils.isEmpty(customerGroupEntityList)){
            return "";
        }
        for (CustomerGroupEntity customerGroupEntity : customerGroupEntityList) {
            if(!customerGroupEntity.getParentId().equals(customerGroupSaveDTO.getParentId())){
                continue;
            }
            if(StringUtils.isEmpty(customerGroupSaveDTO.getId())){
                return "客户分类名称重复";
            }
            if(!customerGroupSaveDTO.getId().equals(customerGroupEntity.getId())){
                return "客户分类名称重复.";
            }
        }
        return "";
    }

    @Override
    public R deleteCustomerGroup(String id) {
        if(StringUtils.isEmpty(id)){
            return R.error("请选择要删除的数据");
        }
        CustomerGroupEntity baseData = customerGroupDao.selectById(id);
        if(baseData == null){
            return R.error("请选择要删除的数据.");
        }
        // 1 递归清理客户分类数据
        List<String> customerGroupInList = customerGroupDao.listIdByParentId(id);
        if(CollectionUtils.isNotEmpty(customerGroupInList)){
            deleteCustomerGroup(customerGroupInList);
        }
        customerGroupDao.deleteById(id);
        // 2 清理客户信息数据
        customerInfoDao.clearDataByNsrsbh(baseData.getBaseNsrsbh());
        return R.ok();
    }

    /**
     * 递归清理客户分类数据
     * @param idList
     * @return void
     * <AUTHOR>
     **/
    private void deleteCustomerGroup(List<String> idList){
        for (String id : idList) {
            List<String> customerGroupIdList = customerGroupDao.listIdByParentId(id);
            if(CollectionUtils.isNotEmpty(customerGroupIdList)){
                deleteCustomerGroup(customerGroupIdList);
            }
            customerGroupDao.deleteById(id);
        }
    }

    @Override
    public R queryPage(CustomerInfoListDTO customerInfoListDTO) {
        Page page = new Page(customerInfoListDTO.getCurrPage(), customerInfoListDTO.getPageSize());
        if(StringUtils.isEmpty(customerInfoListDTO.getId())){
            R.error("请选择客户分类");
        }
        if(StringUtils.isNotEmpty(customerInfoListDTO.getTimeStart())){
            customerInfoListDTO.setTimeStart(customerInfoListDTO.getTimeStart() + "000000");
        }
        if(StringUtils.isNotEmpty(customerInfoListDTO.getTimeEnd())){
            customerInfoListDTO.setTimeStart(customerInfoListDTO.getTimeEnd() + "235959");
        }
        List<CustomerInfoEntity> list = customerInfoDao.selectList(page, customerInfoListDTO);
        page.setRecords(list);
        PageUtils pageUtil = new PageUtils(page);
        return R.ok().put("data", pageUtil);
    }

    @Override
    public R listWithoutId(CustomerInfoListWithoutIdDTO customerInfoListWithoutIdDTO) {
        // 开关判断，开启查所有税号
        if(customerInfoListWithoutIdDTO.getCheck().equals("ON")) {
            // 查询税号所属集团的所有企业税号
            long lo = System.currentTimeMillis();
            // 拼接调用参数，为{taxNo: "baseNsrsbh"}
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("taxNo", customerInfoListWithoutIdDTO.getBaseNsrsbh());
            log.info("listWithoutId 调用集团所属企业税号查询接口入参: {}", jsonObject);
            // 获取token
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String auth = request.getHeader("Authorization");
            // 请求头加入token
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", auth);
            log.info("listWithoutId 开始调用集团所属企业税号查询接口");
            String queryAllTaxListByTaxResponseBodyStr = "";
            String url = orderConfig.getQueryAllTaxListByTaxNo()+ customerInfoListWithoutIdDTO.getBaseNsrsbh();
//            String url = "http://172.16.60.111:8501/user-base/dept/queryAllTaxListByTax?taxNo=" + customerInfoListWithoutIdDTO.getBaseNsrsbh();
            try {
                ResponseEntity<String> queryAllTaxListByTaxResponseEntity = restTemplate.exchange(
                        url,
                        HttpMethod.POST,
                        new HttpEntity<>(jsonObject, httpHeaders),
                        String.class);
                queryAllTaxListByTaxResponseBodyStr = queryAllTaxListByTaxResponseEntity.getBody();
                log.info("listWithoutId 结束调用集团所属企业税号查询接口 耗时: {}", System.currentTimeMillis() - lo);
                log.info("listWithoutId 调用集团所属企业税号查询接口出参: {}", queryAllTaxListByTaxResponseBodyStr);
                // 将返回的Json转换成查询用的税号list
                JSONObject queryAllTaxListByTaxResponseBody = JSONObject.parseObject(queryAllTaxListByTaxResponseBodyStr);
                String data = queryAllTaxListByTaxResponseBody.getString("data");
                List<String> res = JsonUtils.getInstance().fromJson(data, List.class);
                // 如果获取列表为空，则把当前企业税号加入列表
                if(CollectionUtils.isEmpty(res)){
                    res.add(customerInfoListWithoutIdDTO.getBaseNsrsbh());
                }
                // 设置查询用的税号列表
                customerInfoListWithoutIdDTO.setBaseNsrsbhList(res);
            } catch (Exception e) {
                log.error("queryAllTaxListByTax error: {}", queryAllTaxListByTaxResponseBodyStr, e);
            }
        }
        if(StringUtils.isEmpty(customerInfoListWithoutIdDTO.getIntoType())){
            R.error("请选择数据来源");
        }
        Page page = new Page(customerInfoListWithoutIdDTO.getCurrPage(), customerInfoListWithoutIdDTO.getPageSize());
        List<CustomerInfoEntity> list = customerInfoDao.listWithoutId(page, customerInfoListWithoutIdDTO);
        page.setRecords(list);
        PageUtils pageUtil = new PageUtils(page);
        return R.ok().put("data", pageUtil);
    }

    @Override
    public R listByNameWithoutPage(CustomerInfoListByNameWithoutPageDTO customerInfoListByNameWithoutPageDTO) {
        if(StringUtils.isEmpty(customerInfoListByNameWithoutPageDTO.getBaseNsrsbh())){
            R.error("请选择公司主体");
        }
        List<CustomerInfoEntity> list = customerInfoDao.listByNameWithoutPage(customerInfoListByNameWithoutPageDTO);
        return R.ok().put("data", list);
    }

    @Override
    public R saveData(CustomerInfoSaveDTO customerInfoSaveDTO) {
        String checkStr = this.checkCustomerInfoSaveDTO(customerInfoSaveDTO);
        if(StringUtils.isNotEmpty(checkStr)){
            return R.error(checkStr);
        }
        if(StringUtils.isNotEmpty(customerInfoSaveDTO.getId())){
            // 修改
            CustomerInfoEntity updateData = customerInfoDao.selectById(customerInfoSaveDTO.getId());
            if(updateData == null){
                return R.error("数据不存在");
            }
            updateData.setGsmc(customerInfoSaveDTO.getGsmc());
            updateData.setNsrsbh(customerInfoSaveDTO.getNsrsbh());
            updateData.setGsdz(customerInfoSaveDTO.getGsdz());
            updateData.setGsdh(customerInfoSaveDTO.getGsdh());
            updateData.setKhyh(customerInfoSaveDTO.getKhyh());
            updateData.setYhzh(customerInfoSaveDTO.getYhzh());
            updateData.setLxr(customerInfoSaveDTO.getLxr());
            updateData.setSjhm(customerInfoSaveDTO.getSjhm());
            updateData.setEmail(customerInfoSaveDTO.getEmail());
            updateData.setScode(customerInfoSaveDTO.getScode());
            updateData.setUpdateTime(new Date());
            updateData.setUpdateBy(ssoUtil.getUserName());
            customerInfoDao.updateById(updateData);
            return R.ok();
        }else{
            // 新增
            CustomerInfoEntity saveData = new CustomerInfoEntity();
            saveData.setId(DistributedKeyMaker.generateShotKey());
            saveData.setParentId(customerInfoSaveDTO.getParentId());
            saveData.setBaseNsrsbh(customerInfoSaveDTO.getBaseNsrsbh());
            saveData.setGsmc(customerInfoSaveDTO.getGsmc());
            saveData.setNsrsbh(customerInfoSaveDTO.getNsrsbh());
            saveData.setGsdz(customerInfoSaveDTO.getGsdz());
            saveData.setGsdh(customerInfoSaveDTO.getGsdh());
            saveData.setKhyh(customerInfoSaveDTO.getKhyh());
            saveData.setYhzh(customerInfoSaveDTO.getYhzh());
            saveData.setLxr(customerInfoSaveDTO.getLxr());
            saveData.setSjhm(customerInfoSaveDTO.getSjhm());
            saveData.setEmail(customerInfoSaveDTO.getEmail());
            saveData.setScode(customerInfoSaveDTO.getScode());
            saveData.setIntoType("1");
            saveData.setIsDelete("0");
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(ssoUtil.getUserName());
            customerInfoDao.insert(saveData);
            return R.ok().put("data", saveData.getId());
        }
    }

    private String checkCustomerInfoSaveDTO(CustomerInfoSaveDTO customerInfoSaveDTO){
        if(customerInfoSaveDTO == null){
            return "入参为空";
        }
        if(StringUtils.isEmpty(customerInfoSaveDTO.getParentId())){
            return "上级客户分类为空";
        }
        if(StringUtils.isEmpty(customerInfoSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isEmpty(customerInfoSaveDTO.getGsmc())){
            return "公司名称为空";
        }
        List<CustomerInfoEntity> customerInfoEntityList = customerInfoDao.selectListByCustomerInfoSaveDTO(customerInfoSaveDTO);
        if(CollectionUtils.isNotEmpty(customerInfoEntityList)){
            if(customerInfoEntityList.size() > 1){
                return "公司名称、纳税人识别号重复";
            }
            if(StringUtils.isEmpty(customerInfoSaveDTO.getId())){
                return "公司名称、纳税人识别号重复.";
            }
            if(!customerInfoSaveDTO.getId().equals(customerInfoEntityList.get(0).getId())){
                return "公司名称、纳税人识别号重复..";
            }
        }
        return "";
    }

    @Override
    public R deleteData(String id) {
        if(StringUtils.isEmpty(id)){
            return R.error("请选择要删除的数据");
        }
        CustomerInfoEntity customerInfoEntity = customerInfoDao.selectById(id);
        if(customerInfoEntity == null){
            return R.error("数据不存在");
        }
        customerInfoEntity.setIsDelete("1");
        customerInfoEntity.setUpdateTime(new Date());
        customerInfoDao.updateById(customerInfoEntity);
        return R.ok();
    }

    @Override
    public void exportData(List<String> idList, HttpServletResponse response) {
        List<CustomerInfoEntity> customerInfoEntityList = customerInfoDao.selectBatchIds(idList);
        if(CollectionUtils.isEmpty(customerInfoEntityList)){
            return ;
        }
        String fileName = "客户信息";
        log.debug("开始导出{}excel", fileName);
        OutputStream out = null;
        String filePrefix = DateUtil.format(new Date(), OrderInfoContentEnum.DATE_FORMAT_DATE_YMDHMS);
        try {
            response.setContentType("octets/stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + filePrefix + ".xlsx");
            out = response.getOutputStream();
            List<ExportCustomerInfoVO> exportCustomerInfoVOList = this.genExportCustomerInfoVOList(idList, customerInfoEntityList);
            exportCustomerInfoList(out, exportCustomerInfoVOList, fileName);
        } catch (Exception e) {
            log.error("(导出{}excel)，出现异常", fileName, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

            } catch (Exception e) {
                log.error("(导出{}excel)，流关闭异常", fileName, e);
            }
        }
    }

    /**
     * 将 List<CustomerInfoEntity> 转为 List<ExportCustomerInfoVO>
     **/
    private List<ExportCustomerInfoVO> genExportCustomerInfoVOList(List<String> idList, List<CustomerInfoEntity> customerInfoEntityList){
        List<ExportCustomerInfoVO> exportCustomerInfoVOList = new LinkedList<>();
        List<Map<String, String>> idAndParentNameMapList = customerInfoDao.listParentNameByIdList(idList);
        Map<String, String> idAneParentNameMap = new HashMap<>();
        idAndParentNameMapList.forEach(tempData -> idAneParentNameMap.put(tempData.get("id"), tempData.get("khflmc")));
        for (CustomerInfoEntity customerInfoEntity : customerInfoEntityList) {
            ExportCustomerInfoVO tempExportCustomerInfoVO = new ExportCustomerInfoVO();
            if("0".equals(customerInfoEntity.getParentId())){
                tempExportCustomerInfoVO.setKhflmc(customerRootName);
            }else{
                tempExportCustomerInfoVO.setKhflmc(idAneParentNameMap.get(customerInfoEntity.getId()));
            }
            tempExportCustomerInfoVO.setGsmc(customerInfoEntity.getGsmc());
            tempExportCustomerInfoVO.setNsrsbh(customerInfoEntity.getNsrsbh());
            tempExportCustomerInfoVO.setScode(customerInfoEntity.getScode());
            tempExportCustomerInfoVO.setGsdz(customerInfoEntity.getGsdz());
            tempExportCustomerInfoVO.setGsdh(customerInfoEntity.getGsdh());
            tempExportCustomerInfoVO.setKhyh(customerInfoEntity.getKhyh());
            tempExportCustomerInfoVO.setYhzh(customerInfoEntity.getYhzh());
            tempExportCustomerInfoVO.setEmail(customerInfoEntity.getEmail());
            tempExportCustomerInfoVO.setSfmrdz("");
            tempExportCustomerInfoVO.clearNull();
            exportCustomerInfoVOList.add(tempExportCustomerInfoVO);
        }
        return exportCustomerInfoVOList;
    }

    /**
     * 导出excel
     **/
    private void exportCustomerInfoList(OutputStream out, List<ExportCustomerInfoVO> list, String fileName) {
        Long startTime = System.currentTimeMillis();
        // 创建一个工作簿
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(-1);
        try {
            //设置单个sheet最大容量,是个约值,暂定为2000
            int limitSheetSize = list.size() + 10;
            int columnSize = 10;
            Map<String, String> headToProperty = new HashMap<>(columnSize);
            Map<String, String> headerToColumnMap = new HashMap<>(columnSize);
            for (ExportCustomerInfoEnum exportCustomerInfoEnum : ExportCustomerInfoEnum.values()) {
                headToProperty.put(exportCustomerInfoEnum.getValue(), exportCustomerInfoEnum.getCellName());
                headerToColumnMap.put(exportCustomerInfoEnum.getValue(), exportCustomerInfoEnum.getKey());
            }
            ExcelReadContext context = new ExcelReadContext(headToProperty, true, headerToColumnMap);
            context.setFilePrefix(".xlsx");
            context.setSheetIndex(0);
            context.setHeadRow(0);
            context.setSheetLimit(limitSheetSize);
            ExcelExportUtils handle = new ExcelExportUtils(context);
            sxssfWorkbook = handle.exportExcel(sxssfWorkbook, list);
            sxssfWorkbook.write(out);
            Long endTime = System.currentTimeMillis();
            log.debug("(导出{}excel)结束,耗时:{}", fileName, endTime - startTime);
        } catch (Exception e) {
            log.error("(导出{}excel)异常", fileName, e);
        } finally {
            try {
                sxssfWorkbook.close();
            } catch (IOException e) {
                log.error("(导出{}excel)输出流关闭异常:{}", fileName, e);
            }
        }
    }

    @Override
    public List selectGhfmcList(String nsrsbh) {
        List<String> list = customerInfoDao.selectGhfmcList(nsrsbh);
        return list;
    }
    @Override
    public List selectGhfmcListByGhfsbh(String baseNsrsbh, String nsrsbh) {
        List<String> list = customerInfoDao.selectGhfmcListByGhfsbh(baseNsrsbh, nsrsbh);
        return list;
    }

    @Override
    public R uploadCustomerExcel(MultipartFile file) {
        try{
            if(file == null){
                return R.error("请上传文件");
            }
            // 1 获取数据
            Set<CustomerUpdateExcelBO> dataSet = getDataFromExcel(file);
            // 2 校验数据
            List<CustomerUpdateExcelBO> rightList = new ArrayList<>();
            List<CustomerUpdateExcelBO> errorList = new ArrayList<>();
            this.dealCheckExcelCustomerDataResult(dataSet, rightList, errorList);
            // 3 响应数据
            return R.ok().put("data", new CustomerUpdateExcelVO(rightList, String.valueOf(rightList.size()), errorList, String.valueOf(errorList.size())));
        }catch (Exception e){
            return R.error();
        }
    }

    /**
     * 从excel中获取CustomerUpdateExcelBO的Set集合
     * @param file
     * @return java.util.Set<com.dxhy.order.modules.pojo.bo.CustomerUpdateExcelBO>
     * <AUTHOR>
     **/
    private Set<CustomerUpdateExcelBO> getDataFromExcel(MultipartFile file) throws Exception{
        Set<CustomerUpdateExcelBO> dataSet = new HashSet<>();
        EasyExcel.read(file.getInputStream(), CustomerUpdateExcelBO.class, new AnalysisEventListener<CustomerUpdateExcelBO>() {
            @Override
            public void invoke(CustomerUpdateExcelBO o, AnalysisContext analysisContext) {
                dataSet.add(o);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
        }).doReadAll();
        dataSet.forEach(CustomerUpdateExcelBO::clearNull);
        return dataSet;
    }

    /**
     * 处理Excel传入的参数
     * @param dataSet
     * @param rightList
     * @param errorList
     * @return void
     * <AUTHOR>
     **/
    private void dealCheckExcelCustomerDataResult(Set<CustomerUpdateExcelBO> dataSet, List<CustomerUpdateExcelBO> rightList, List<CustomerUpdateExcelBO> errorList){
        if(CollectionUtils.isEmpty(dataSet)){
            return;
        }
        for (CustomerUpdateExcelBO customerUpdateExcelBO : dataSet) {
            String checkResult = checkExcelCustomerData(customerUpdateExcelBO);
            if(StringUtils.isEmpty(checkResult)){
                rightList.add(customerUpdateExcelBO);
            }else{
                customerUpdateExcelBO.setErrorMsg(checkResult);
                errorList.add(customerUpdateExcelBO);
            }
        }
    }

    /**
     * 校验Excel传入的参数
     * @param customerUpdateExcelBO
     * @return String
     * <AUTHOR>
     **/
    private String checkExcelCustomerData(CustomerUpdateExcelBO customerUpdateExcelBO){
        if(StringUtils.isEmpty(customerUpdateExcelBO.getKhflmc())){
            return "客户分类名称不能为空";
        }
        if(StringUtils.isEmpty(customerUpdateExcelBO.getKhmc())){
            return "客户名称不能为空";
        }
        return "";
    }

    @Override
    @Transactional
    public R saveDataFromExcel(CustomerInfoExcelSaveDTO customerInfoExcelSaveDTO) {
        String baseNsrsbh = customerInfoExcelSaveDTO.getBaseNsrsbh();
        Date dateNow = new Date();
        String userNameNow = ssoUtil.getUserName();
        List<CustomerUpdateExcelBO> dataList = customerInfoExcelSaveDTO.getDataList();
        // 1 查询当前已存在的二级分类
        List<CustomerGroupEntity> levelTwoCustomerGroupEntity = customerGroupDao.listLevelTwoDataByNsrsbh(baseNsrsbh);
        Map<String, CustomerGroupEntity> customerGroupMap = new HashMap<>();
        levelTwoCustomerGroupEntity.forEach(e -> customerGroupMap.put(e.getKhflmc(), e));
        CustomerGroupEntity rootCustomerGroupEntity = new CustomerGroupEntity();
        rootCustomerGroupEntity.setId("0");
        rootCustomerGroupEntity.setParentId("-1");
        rootCustomerGroupEntity.setBaseNsrsbh(customerInfoExcelSaveDTO.getBaseNsrsbh());
        rootCustomerGroupEntity.setKhflmc(customerRootName);
        customerGroupMap.put(customerRootName, rootCustomerGroupEntity);
        // 2.1 格式化分类数据
        List<CustomerGroupEntity> saveGroupDataList = new ArrayList<>();
        String groupParentId = "0";
        if(CollectionUtils.isNotEmpty(dataList)){
            for (CustomerUpdateExcelBO tempData : dataList) {
                if(customerGroupMap.get(tempData.getKhflmc()) != null){
                    continue;
                }
                CustomerGroupEntity saveData = new CustomerGroupEntity();
                saveData.setId(DistributedKeyMaker.generateShotKey());
                saveData.setParentId(groupParentId);
                saveData.setBaseNsrsbh(baseNsrsbh);
                saveData.setKhflmc(tempData.getKhflmc());
                saveData.setCreateTime(dateNow);
                saveData.setCreateBy(userNameNow);
                saveGroupDataList.add(saveData);
                customerGroupMap.put(tempData.getKhflmc(), saveData);
            }
        }
        // 2.2 保存分类数据
        if(CollectionUtils.isNotEmpty(saveGroupDataList)){
            customerGroupDao.insertList(saveGroupDataList);
        }
        // 3.1 格式化业务数据
        List<CustomerInfoEntity> saveCustomerDataList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dataList)){
            for (CustomerUpdateExcelBO customerUpdateExcelBO : dataList) {
                CustomerInfoEntity saveData = new CustomerInfoEntity();
                saveData.setId(DistributedKeyMaker.generateShotKey());
                saveData.setParentId(customerGroupMap.get(customerUpdateExcelBO.getKhflmc()).getId());
                saveData.setBaseNsrsbh(baseNsrsbh);
                saveData.setGsmc(customerUpdateExcelBO.getKhmc());
                saveData.setNsrsbh(customerUpdateExcelBO.getNsrsbh());
                saveData.setGsdz(customerUpdateExcelBO.getDz());
                saveData.setGsdh(customerUpdateExcelBO.getDh());
                saveData.setKhyh(customerUpdateExcelBO.getKhhmc());
                saveData.setYhzh(customerUpdateExcelBO.getYhzh());
                saveData.setLxr("");
                saveData.setSjhm("");
                saveData.setEmail(customerUpdateExcelBO.getLxyx());
                saveData.setScode(customerUpdateExcelBO.getJm());
                saveData.setIntoType("1");
                saveData.setIsDelete("0");
                saveData.setCreateTime(dateNow);
                saveData.setCreateBy(userNameNow);
                saveCustomerDataList.add(saveData);
            }
        }
        // 3.2 保存业务数据
        if(CollectionUtils.isNotEmpty(saveCustomerDataList)){
            customerInfoDao.insertList(saveCustomerDataList);
        }
        return R.ok();
    }

    @Override
    public void genExcelForErrorData(CustomerInfoExcelSaveDTO customerInfoExcelSaveDTO, HttpServletResponse response) {
        List<CustomerUpdateExcelBO> customerInfoList = customerInfoExcelSaveDTO.getDataList();
        if(CollectionUtils.isEmpty(customerInfoList)){
            return ;
        }
        String fileName = "客户信息";
        log.debug("开始导出{}excel", fileName);
        OutputStream out = null;
        String filePrefix = DateUtil.format(new Date(), OrderInfoContentEnum.DATE_FORMAT_DATE_YMDHMS);
        try {
            response.setContentType("octets/stream");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + filePrefix + ".xlsx");
            out = response.getOutputStream();
            exportExcelCustomerInfoList(out, customerInfoList, fileName);
        } catch (Exception e) {
            log.error("(导出{}excel)，出现异常", fileName, e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

            } catch (Exception e) {
                log.error("(导出{}excel)，流关闭异常", fileName, e);
            }
        }
    }

    /**
     * 导出excel
     **/
    private void exportExcelCustomerInfoList(OutputStream out, List<CustomerUpdateExcelBO> list, String fileName) {
        Long startTime = System.currentTimeMillis();
        // 创建一个工作簿
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(-1);
        try {
            //设置单个sheet最大容量,是个约值,暂定为2000
            int limitSheetSize = list.size() + 10;
            int columnSize = 10;
            Map<String, String> headToProperty = new HashMap<>(columnSize);
            Map<String, String> headerToColumnMap = new HashMap<>(columnSize);
            for (ExportExcelCustomerInfoEnum exportExcelCustomerInfoEnum : ExportExcelCustomerInfoEnum.values()) {
                headToProperty.put(exportExcelCustomerInfoEnum.getValue(), exportExcelCustomerInfoEnum.getCellName());
                headerToColumnMap.put(exportExcelCustomerInfoEnum.getValue(), exportExcelCustomerInfoEnum.getKey());
            }
            ExcelReadContext context = new ExcelReadContext(headToProperty, true, headerToColumnMap);
            context.setFilePrefix(".xlsx");
            context.setSheetIndex(0);
            context.setHeadRow(0);
            context.setSheetLimit(limitSheetSize);
            ExcelExportUtils handle = new ExcelExportUtils(context);
            sxssfWorkbook = handle.exportExcel(sxssfWorkbook, list);
            sxssfWorkbook.write(out);
            Long endTime = System.currentTimeMillis();
            log.debug("(导出{}excel)结束,耗时:{}", fileName, endTime - startTime);
        } catch (Exception e) {
            log.error("(导出{}excel)异常", fileName, e);
        } finally {
            try {
                sxssfWorkbook.close();
            } catch (IOException e) {
                log.error("(导出{}excel)输出流关闭异常:{}", fileName, e);
            }
        }
    }

}
