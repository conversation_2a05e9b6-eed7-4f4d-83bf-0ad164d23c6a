package com.dxhy.order.modules.service;

import com.dxhy.order.modules.entity.OrderInvoiceInfoEntity;

import java.util.Map;

/**
 * OpenApiService
 * <AUTHOR>
 * @Date 2022/6/28 18:28
 **/
public interface OpenApiService {

    /**
     * 发送邮箱 - 发票推送
     * @param qdfphm
     * @return map
     */
    Map<String, Object> sendEmail(String email, String qdfphm);

    /**
     * 发送短信 - 发票推送
     * @param qdfphm
     * @return map
     */
    Map<String, Object> sendShortMessage(String phone, String qdfphm);

    /**
     * 发送短信、邮箱 - 预警
     * @param phones
     * @param emails
     * @param content
     * @return map
     */
    void sendMessageAndEmail(String phones, String emails, String content);

    /**
     * 推送数据到指定URL - 发票推送
     * @param pushUrl 推送地址
     * @param orderInvoiceInfo 发票信息
     * @param tenantCode 租户编码
     * @return map
     */
    Map<String, Object> sendPushData(String pushUrl, OrderInvoiceInfoEntity orderInvoiceInfo,String tenantCode);

    /**
     * 获取密钥key
     * @param secretId 密钥id
     * @param tenantCode 租户编码
     * @return
     */
    Map getSecret(String secretId,String tenantCode);
    /**
     * 校验租户是否有该产品的权限
     */
    boolean validTenantProduct(String secretId, String productId);
}
