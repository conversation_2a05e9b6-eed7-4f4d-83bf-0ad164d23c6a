package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 电子税局登录相关
 */
@Data
public class ApiLoginReqBO implements Serializable {

    private static final long serialVersionUID = -866035990190162929L;

    /**
     * 电子税局用户
     */
    private String dzswjyh;
    /**
     * 纳税人识别号
     */
    private String nsrsbh;
    /**
     * 电子税局密码
     */
    private String dzswjmm;
    /**
     * 登录方式 0 rpa端手工输入短信 1手机卡托管模式 2，调用方式输入短信
     */
    private String dlfs;
    /**
     * 身份类型  01 法定代表人 02 财务负责人 03 办税员 04 涉税服务人员 05 管理员
     *          06 出口退税人员 07 领票人 08 社保经办人 09 开票员 10 销售人员
     *          99 其他人员 31 行政办事员
     */
    private String sflx;
    /**
     * 验证码
     */
    private String yzm;
    /**
     * 认证标识
     */
    private String rzid;
}
