package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 新增&修改 商品信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("新增&修改 商品信息DTO")
public class ItemInfoSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 开票项目主键（此参数为空时表示新增 不为空时表示修改）
     */
    @ApiModelProperty("开票项目主键（此参数为空时表示新增 不为空时表示修改）")
    private String id;

    /**
     * 项目分类主键
     */
    @ApiModelProperty(name = "项目分类主键", required = true)
    private String parentId;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 项目名称
     */
    @ApiModelProperty(name = "项目名称", required = true)
    private String xmmc;

    /**
     * 商品和服务税收分类编码
     */
    @ApiModelProperty(name = "商品和服务税收分类编码", required = true)
    private String sphssflbm;

    /**
     * 商品和服务税收分类简称
     */
    @ApiModelProperty(name = "商品和服务税收分类简称", required = true)
    private String sphssfljc;

    /**
     * 优惠政策标识 0 否 1 是
     */
    @ApiModelProperty("优惠政策标识 0 否 1 是")
    private String yhzcbs;

    /**
     * 优惠政策类型
     */
    @ApiModelProperty("优惠政策类型")
    private String yhzclx;

    /**
     * 税率
     */
    @ApiModelProperty(name = "税率", required = true)
    private String sl;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String jm;

    /**
     * 含税标识 0 否 1 是
     */
    @ApiModelProperty("含税标识 0 否 1 是")
    private String hsbs;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private String dj;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String dw;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String ggxh;
}
