package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceAdditionInfoDao;
import com.dxhy.order.modules.entity.InvoiceAdditionInfoEntity;
import com.dxhy.order.modules.service.InvoiceAdditionInfoService;
import com.dxhy.order.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("invoiceAdditionInfoService")
public class InvoiceAdditionInfoServiceImpl extends ServiceImpl<InvoiceAdditionInfoDao, InvoiceAdditionInfoEntity> implements InvoiceAdditionInfoService {

    @Autowired
    InvoiceAdditionInfoDao invoiceAdditionInfoDao;

    @Override
    public PageUtils queryPage(InvoiceAdditionInfoEntity invoiceAdditionInfoEntity) {
        Page page = new Page(invoiceAdditionInfoEntity.getCurrPage(),invoiceAdditionInfoEntity.getPageSize());
        List<InvoiceAdditionInfoEntity> list = invoiceAdditionInfoDao.selectList(page,invoiceAdditionInfoEntity);
        page.setRecords(list);
        return new PageUtils(page);
    }

}
