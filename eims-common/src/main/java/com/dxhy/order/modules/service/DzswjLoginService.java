package com.dxhy.order.modules.service;

import com.dxhy.order.modules.entity.ApiLoginReqBO;
import com.dxhy.order.modules.entity.InvoiceIssueRes;

public interface DzswjLoginService {


    InvoiceIssueRes dzswjLogin(ApiLoginReqBO apiLoginReqBO);

    InvoiceIssueRes setSms(ApiLoginReqBO apiLoginReqBO);

    InvoiceIssueRes getConfirmQrcode(ApiLoginReqBO apiLoginReqBO);

    InvoiceIssueRes getConfirmStatus(ApiLoginReqBO apiLoginReqBO);

    InvoiceIssueRes getSessionInfo(ApiLoginReqBO apiLoginReqBO);
}
