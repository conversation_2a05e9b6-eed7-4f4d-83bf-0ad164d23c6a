package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceConfigInfo;
import com.dxhy.order.utils.R;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【invoice_config】的数据库操作Service
* @createDate 2022-08-26 12:14:23
*/
public interface InvoiceConfigService extends IService<InvoiceConfigInfo> {

    /**
     * 查詢
     */
    R queryList(InvoiceConfigInfo invoiceConfigInfo);

    /**
     * 查詢用户
     */
    R queryPersons(InvoiceConfigInfo invoiceConfigInfo);

    /**
     * 新增
     */
    R insertInvoiceConfig(InvoiceConfigInfo invoiceConfigInfo);

    /**
     *更新
     */
    R updateInvoiceConfig(InvoiceConfigInfo invoiceConfigInfo);

    /**
     * 删除
     */
    R delInvoiceConfig(InvoiceConfigInfo invoiceConfigInfo);

    /**
     * 批量删除
     */
    R delMoreInvoiceConfig(Map map);

    /**
     * 默认账户勾选校验
     */
    R checkHadConfig(InvoiceConfigInfo invoiceConfigInfo);
}
