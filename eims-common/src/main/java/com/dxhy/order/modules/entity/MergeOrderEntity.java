package com.dxhy.order.modules.entity;

import lombok.Data;

import java.util.List;

@Data
public class MergeOrderEntity {

    /**
     * 正数 与 正数 合并时使用
     */
    private String zzSame;
    /**
     * 负数 与 正数 合并时使用
     */
    private String zfSame;
    /**
     * item集合信息
     */
    private OrderInvoiceItemEntity orderInvoiceItemEntity;
    /**
     * item集合信息 - 折扣行
     */
    private List<OrderInvoiceItemEntity> orderInvoiceItemEntityList;
}
