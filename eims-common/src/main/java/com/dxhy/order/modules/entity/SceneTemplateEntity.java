package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 场景模板表
 * <AUTHOR>
 * @Date 2022/6/27 12:04
 * @Version 1.0
 **/
@TableName("scene_template")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("场景模板表")
public class SceneTemplateEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 场景模板主键
     */
    @ApiModelProperty("场景模板主键")
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String baseNsrsbh;
    /**
     * 纳税人名称
     */
    @ApiModelProperty("名字")
    private String name;
    /**
     * 逻辑删除 0 正常 1 已删除
     */
    @ApiModelProperty("逻辑删除 0 正常 1 已删除")
    private String isDelete;
    /**
     * 备用字段1
     */
    @ApiModelProperty("备用字段1")
    private String byzd1;
    /**
     * 备用字段2
     */
    @ApiModelProperty("备用字段2")
    private String byzd2;
    /**
     * 备用字段3
     */
    @ApiModelProperty("备用字段3")
    private String byzd3;
    /**
     * 备用字段4
     */
    @ApiModelProperty("备用字段4")
    private String byzd4;
    /**
     * 备用字段5
     */
    @ApiModelProperty("备用字段5")
    private String byzd5;
    /**
     * 备用字段6
     */
    @ApiModelProperty("备用字段6")
    private String byzd6;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
    /**
     * uuid 第三方慧企返回uuid
     */
    @ApiModelProperty("uuid")
    private String uuid;
    /**
     * 场景模板名称
     */
    @ApiModelProperty("cjmbmc")
    private String cjmbmc;
    /**
     * 有效标志
     */
    @ApiModelProperty("yxbz")
    private String yxbz;


}
