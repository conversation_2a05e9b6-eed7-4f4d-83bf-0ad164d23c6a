package com.dxhy.order.modules.pojo.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dxhy.order.modules.entity.AdditionElementEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 附加信息表VO
 * <AUTHOR>
 * @Date 2022/6/28 16:34
 * @Version 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
@ApiModel("附加信息表VO")
public class AdditionElementVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 附加信息主键
	 */
	@TableId(type = IdType.INPUT)
	@ApiModelProperty("附加信息主键")
	private String id;

	/**
	 * 附加信息名称
	 */
	@ApiModelProperty("附加信息名称")
	private String fjxxmc;

	/**
	 * 数据类型 1 文本型 2 数值型 3 日期型
	 */
	@ApiModelProperty("数据类型 1 文本型 2 数值型 3 日期型")
	private String sjlx;

	/**
	 * 输入方式 1 手工录入 2 Excel导入
	 */
	@ApiModelProperty("输入方式 1 手工录入 2 Excel导入")
	private String srfs;

	/**
	 * 引用状态 0 未引用 1 已引用
	 */
	@ApiModelProperty("引用状态 0 未引用 1 已引用")
	private String yyzt;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date createTime;

	/**
	 * 创建人
	 */
	@ApiModelProperty("创建人")
	private String createBy;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@ApiModelProperty("更新人")
	private String updateBy;

	/**
	 * uuid 第三方接口慧企 返回的UUID
	 */
	@ApiModelProperty("慧企uuid")
	private String uuid;

	public AdditionElementVO(AdditionElementEntity entityData){
		if(entityData != null){
			this.id = entityData.getId();
			this.fjxxmc = entityData.getFjxxmc();
			this.sjlx = entityData.getSjlx();
			this.srfs = entityData.getSrfs();
			this.yyzt = entityData.getYyzt();
			this.uuid = entityData.getUuid();
			if(entityData.getCreateTime() != null){
				this.createTime = entityData.getCreateTime();
			}
			this.createBy = entityData.getCreateBy();
			if(entityData.getUpdateTime() != null){
				this.updateTime = entityData.getUpdateTime();
			}
			this.updateBy = entityData.getUpdateBy();
		}
	}

}
