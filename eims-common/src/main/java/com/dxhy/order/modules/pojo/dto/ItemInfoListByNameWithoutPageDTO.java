package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 根据名称查询列表 - 不分页 项目信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("根据名称查询列表 - 不分页 项目信息DTO")
public class ItemInfoListByNameWithoutPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty(name = "纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 项目名称
     */
    @ApiModelProperty(name = "项目名称", required = true)
    private String xmmc;
}
