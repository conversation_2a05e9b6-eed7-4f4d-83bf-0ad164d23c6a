package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-二手车信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceIssueEscxxParam implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 开票方类型
	 */
	private String kpflx;
	/**
	 * 车牌照号
	 */
	private String cpzh;

	/**
	 * 登记证号
	 */
	private String djzh;

	/**
	 * 车辆类型
	 */
	private String cllx;

	/**
	 * 车架号/车辆识别代码
	 */
	private String clsbdm;

	/**
	 * 厂牌型号
	 */
	private String cpxh;

	/**
	 * 转入地车辆管理所名称
	 */
	private String zrdclglsmc;

	/**
	 * 经营、拍卖单位名称
	 */
	private String jypmdw_mc;

	/**
	 * 经营、拍卖单位税号
	 */
	private String jypmdw_sh;

	/**
	 * 经营、拍卖单位地址
	 */
	private String jypmdw_dz;

	/**
	 * 经营、拍卖单位电话
	 */
	private String jypmdw_dh;

	/**
	 * 经营、拍卖单位开户银行
	 */
	private String jypmdw_khyh;

	/**
	 * 经营、拍卖单位银行账号
	 */
	private String jypmdw_yhzh;

	/**
	 * 二手车市场名称
	 */
	private String sc_mc;

	/**
	 * 二手车市场税号
	 */
	private String sc_sh;

	/**
	 * 二手车市场地址
	 */
	private String sc_dz;

	/**
	 * 二手车市场电话
	 */
	private String sc_dh;

	/**
	 * 二手车市场开户银行
	 */
	private String sc_khyh;

	/**
	 * 二手车市场银行账号
	 */
	private String sc_yhzh;

	/**
	 * 二手车销货方代码
	 */
	private String esc_xhfdm;

	/**
	 * 二手车销货方名称
	 */
	private String esc_xhfmc;

	/**
	 * 二手车销货方地址
	 */
	private String esc_xhfdz;

	/**
	 * 二手车销货方电话
	 */
	private String esc_xhfdh;

	/**
	 * 二手车销货方自然人标识
	 */
	private String esc_xhf_zrrbs;
}
