package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dxhy.order.modules.entity.TaxClassCodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 税收分类编码表 mapper
 * <AUTHOR>
 * @Date 2022/6/27 12:17
 * @Version 1.0
 **/
@Mapper
public interface TaxClassCodeDao extends BaseMapper<TaxClassCodeEntity> {

    /**
     * 通过名称搜索列表
     * @param spmc
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    List<TaxClassCodeEntity> listByName(@Param("spmc") String spmc);

    /**
     * 通过ID搜索下级
     * @param pid
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    List<TaxClassCodeEntity> listById(@Param("pid") Long pid);

    TaxClassCodeEntity getTaxClassCodeSpjc(@Param("spbm") String spbm);

    TaxClassCodeEntity getTaxClassCodeXmmc(@Param("spmc") String spmc);
}
