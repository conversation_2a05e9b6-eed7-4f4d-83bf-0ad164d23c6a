package com.dxhy.order.modules.pojo.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开票记录 - 计算各开票状态的订单总量
 * @date 2022/12/10 15:45
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("开票记录 - 计算各开票状态的订单总量")
@JsonInclude(JsonInclude.Include.ALWAYS)
public class InvoiceRecordSumNumberDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总数量
     */
    @ApiModelProperty(name = "alls")
    private String alls;

    /**
     * 开票成功
     */
    @ApiModelProperty(name = "kpcg")
    private String kpcg;

    /**
     * 开票中
     */
    @ApiModelProperty(name = "kpz")
    private String kpz;

    /**
     * 开票失败
     */
    @ApiModelProperty(name = "kpsb")
    private String kpsb;
}
