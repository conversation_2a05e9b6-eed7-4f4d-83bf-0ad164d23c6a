package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class ApiHzfpqrmxxxcxListRspBO implements Serializable {

    private static final long serialVersionUID = 6787684669917937556L;

    /**
     * 序号
     */
    private String xh;
    /**
     * 蓝字全电号码
     */
    private String lzqdhm;
    /**
     * 蓝字明细序号
     */
    private String lzmxxh;
    /**
     * 商品和服务税收分类合并编码
     */
    private String sphfwssflhbbm;
    /**
     * 项目名称全称
     */
    private String hwhyslwfwmc;
    /**
     * 商品服务简称
     */
    private String spfwjc;
    /**
     * 项目名称
     */
    private String xmmc;
    /**
     * 规格型号
     */
    private String ggxh;
    /**
     * 单位
     */
    private String dw;
    /**
     * 发票商品单价
     */
    private String fpspdj;
    /**
     * 金额
     */
    private String je;
    /**
     * 税率
     */
    private String sll;
    /**
     * 税额
     */
    private String se;

    // 2.8 红字发票确认信息列表查询字段

    /**
     * 发票商品数量
     */
    private String fpspsl;
}
