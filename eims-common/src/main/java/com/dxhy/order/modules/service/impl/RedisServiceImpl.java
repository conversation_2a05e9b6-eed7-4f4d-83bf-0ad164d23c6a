package com.dxhy.order.modules.service.impl;

import com.dxhy.order.modules.service.RedisService;
import com.dxhy.order.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * redis业务实现类
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
@Service
@Slf4j
public class RedisServiceImpl implements RedisService {
    
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    
    private static final String LOGGER_MSG = "(redis 服务实现类)";
    
    @Override
    public Boolean expire(final String key, final int seconds) {
        return redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
    }
    
    @Override
    public boolean set(final String key, final String value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("{},异常，异常信息：{}", LOGGER_MSG, e);
            return false;
        }
    }
    
    @Override
    public boolean set(final String key, final Object value, final int seconds) {
        String objectJson = JsonUtils.getInstance().toJsonString(value);
        return set(key, objectJson, seconds);
    }
    
    @Override
    public boolean setNx(final String key, final String value) {
    
        try {
            return redisTemplate.opsForValue().setIfAbsent(key, value);
        } catch (Exception e) {
            log.error("{},异常，异常信息：{}", LOGGER_MSG, e);
            return false;
        }
    }
    
    @Override
    public boolean set(String key, String value, int seconds) {
        try {
            redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
            if (seconds > 0) {
                expire(key, seconds);
            }
            return true;
        } catch (Exception e) {
            log.error("{},异常，异常信息：{}", LOGGER_MSG, e);
            return false;
        }
    }
    
    
    @Override
    public boolean del(final String key) {
        try {
            redisTemplate.delete(key);
            return true;
        } catch (Exception e) {
            log.error("{},异常，异常信息：{}", LOGGER_MSG, e);
            return false;
        }
    }
    
    @Override
    public <T> T get(final String key, Class<T> clazz) {
        String value = get(key);
        return StringUtils.isBlank(value) ? null : JsonUtils.getInstance().parseObject(value, clazz);
    }
    
    @Override
    public long append(String key, String value) {
        try {
            Integer append = redisTemplate.opsForValue().append(key, value);
            if (append != null) {
                return append;
            }
        
        } catch (Exception e) {
            log.error("{},异常，异常信息：{}", LOGGER_MSG, e.getMessage(), e);
        }
        return 0L;
    }
    
    @Override
    public String get(final String key) {
        if (key == null) {
            return null;
        }
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 放数据
     *
     * @param key
     * @param value
     * @return
     */
    @Override
    public Long lPush(String key, String... value) {
        Long aLong = redisTemplate.opsForList().leftPushAll(key, value);
        if (aLong == null) {
            return 0L;
        }
        return aLong;
    }
    
    /**
     * 取数据
     *
     * @param key
     * @return
     */
    @Override
    public String rPop(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }
    
    
    /**
     * 删除List中某一个数据
     *
     * @param key
     * @return
     */
    @Override
    public Long listRemove(String key, String value) {
        return redisTemplate.opsForList().remove(key, 1, value);
    }
    
    @Override
    public Long rPush(String key, String... value) {
        Long aLong = redisTemplate.opsForList().rightPushAll(key, value);
        if (aLong == null) {
            return 0L;
        }
        return aLong;
    }
    
    @Override
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }
    
    @Override
    public List<String> lRange(String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }
    
    @Override
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }
}
