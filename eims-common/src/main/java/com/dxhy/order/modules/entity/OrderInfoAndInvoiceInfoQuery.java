package com.dxhy.order.modules.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class OrderInfoAndInvoiceInfoQuery {
    @J<PERSON>NField(name = "NSRSBH")
    private String NSRSBH;
    @J<PERSON><PERSON>ield(name = "<PERSON>QQLSH")
    private String DDQQLSH;
    @J<PERSON><PERSON>ield(name = "DDH")
    private String DDH;
    @J<PERSON>NField(name = "BSWJ")
    private String BSWJ;
    @JSONField(name = "DDRQQ")
    private String DDRQQ;
    @JSONField(name = "DDRQZ")
    private String DDRQZ;
    /**
     * 发票种类
     */
    private String fpzl;
    /**
     * 特定业务要素
     */
    private String tdyw;

}
