package com.dxhy.order.modules.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 开票配置表
 *
 * <AUTHOR>
 * @email
 * @date 2022-12-06 20:00:18
 */
@TableName("order_invoice_config")
@Data
public class OrderInvoiceConfigEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private String id;
    /**
     * 纳税人识别号
     */
    private String nsrsbh;
    /**
     * 编码
     */
    private String code;
    /**
     * 纳税人类型
     */
    private String nsrlx;
    /**
     * 企业可选税率
     */
    private String qykxsl;
    /**
     * 购方交付手机
     */
    private String gfjfsj;
    /**
     * 购方交付邮箱
     */
    private String gfjhyx;
    /**
     * 开票自动交付
     */
    private String kpzdjf;
    /**
     * 金额阻断开票
     */
    private String jezdkp;
    /**
     * 客户信息自动保存
     */
    private String khxxzdbc;
    /**
     * 红字发票自动开具
     */
    private String hzfpzdkj;
    /**
     * 智能匹配税收分类编码
     */
    private String znppssflbm;
    /**
     * 单据层级设置 0 订单  1应收单+订单
     */
    private String djcjsz;
    /**
     * 商品明细行设置
     */
    private String spmxhsz;
    /**
     * 不合并同类明细：0勾选，1未勾选
     */
    private String hbfsBhbtlmx;
    /**
     * 合并同类明细(正数）：1商品税率；2单价；3商品名称；4规格型号；5计量单位
     */
    private String hbfsHbtlmx;
    /**
     * 负数行合并，0 与正数商品名称、税率、规格型号、计量单位、单价一致合并，数量、金额直接冲抵,1与正数商品名称、税率、规格型号、计量单位一致时合并，冲抵金额时反算数量，单价以被冲抵正数商品单价为准,2 与正数商品名称、税率、规格型号、计量单位一致时合并，冲抵金额时反算单价，数量以被冲抵正数商品数量为准
     */
    private String hbfsFshhb;
    /**
     * 其他规则_商品行排序
     */
    private String qtgzSphpx;
    /**
     * 其他规则_发票备注行规则,0-取第一行备注；1-叠加明细行备注
     */
    private String qtgzFpbzhgz;
    /**
     * 其他规则_发票备注行规则_符号,1-分号'；'；2-逗号'，'；3-换行符',
     */
    private String qtgzFpbzhgzFh;
    /**
     * 备用字段1
     */
    private String byzd1;
    /**
     * 备用字段2
     */
    private String byzd2;
    /**
     * 备用字段3
     */
    private String byzd3;
    /**
     * 备用字段4
     */
    private String byzd4;
    /**
     * 备用字段5
     */
    private String byzd5;
    /**
     * 备用字段6
     */
    private String byzd6;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private String baseNsrsbh;

    /**
     * 发票回推配置列表
     */
    @TableField(exist = false)
    private List<InvoiceBackpushConfigEntity> backpushConfigList;

}
