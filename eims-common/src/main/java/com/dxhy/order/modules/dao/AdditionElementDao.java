package com.dxhy.order.modules.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dxhy.order.modules.entity.AdditionElementEntity;
import com.dxhy.order.modules.pojo.dto.AdditionElementListDTO;
import com.dxhy.order.modules.pojo.vo.AdditionElementVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 附加信息表 mapper
 * <AUTHOR>
 * @Date 2022/6/28 16:26
 * @Version 1.0
 **/
@Mapper
public interface AdditionElementDao extends BaseMapper<AdditionElementEntity> {

    /**
     * 查询附加信息列表
     * @param additionElementListDTO
     * @param page
     * @return java.util.List<com.dxhy.order.modules.entity.AdditionElementEntity>
     * <AUTHOR>
     **/
    List<AdditionElementEntity> selectList(Page page, @Param("additionElementListDTO") AdditionElementListDTO additionElementListDTO);

    /**
     * 根据税号查询全部附加信息
     * @param baseNsrsbh
     * @return java.util.List<com.dxhy.order.modules.entity.AdditionElementEntity>
     * <AUTHOR>
     **/
    List<AdditionElementEntity> selectListByNsrsbh(@Param("baseNsrsbh") String baseNsrsbh);

    /**
     * 根据税号和附加信息名称查询列表
     * @param baseNsrsbh
     * @param fjxxmc
     * @return java.util.List<com.dxhy.order.modules.entity.AdditionElementEntity>
     * <AUTHOR>
     **/
    List<AdditionElementEntity> selectListByNsrsbhAndName(@Param("baseNsrsbh") String baseNsrsbh, @Param("fjxxmc") String fjxxmc);

    /**
     * 根据场景模板ID查询附加信息
     * @param
     * @return java.util.List<com.dxhy.order.modules.pojo.vo.AdditionElementVO>
     * <AUTHOR>
     **/
    List<AdditionElementVO> listAdditionElementBySceneTemplateId(@Param("id") String id);

    /**
     * 根据场景模板ID查询附加信息名称
     * @param id
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     **/
    List<String> listNameBySceneTemplateId(@Param("id") String id);

    /**
     * 根据ID查询数据
     * @param id
     * @return com.dxhy.order.modules.entity.AdditionElementEntity
     * <AUTHOR>
     **/
    AdditionElementEntity queryDataById(@Param("id") String id);

    /**
     * 根据UUID查询数据
     * @param uuid
     * @return com.dxhy.order.modules.entity.AdditionElementEntity
     * <AUTHOR>
     **/
    AdditionElementEntity queryDataByUUID(@Param("uuid") String uuid);

    /**
     * 根据ID查询已引用数据的数量
     * @param idList
     * @return java.lang.Integer
     * <AUTHOR>
     **/
    Integer countYyztTureDataByIdList(@Param("idList") List<String> idList);

    /**
     * 根据ID删除数据（逻辑删除）
     * @param idList
     * @return void
     * <AUTHOR>
     **/
    void deleteByIdList(@Param("idList") List<String> idList);

    /**
     * 更新引用状态 0
     * @param
     * @return void
     * <AUTHOR>
     **/
    void refreshYyzt_0();

    /**
     * 更新引用状态 1
     * @param
     * @return void
     * <AUTHOR>
     **/
    void refreshYyzt_1();

}
