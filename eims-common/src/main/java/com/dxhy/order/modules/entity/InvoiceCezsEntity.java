package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 差额征税
 * 
 * <AUTHOR> @email 
 * @date 2024-12-30 19:47:06
 */
@TableName("invoice_cezs")
@Data
public class InvoiceCezsEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 差额征税主键
	 */
	@ApiModelProperty(value = "差额征税主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	private String xh;
	/**
	 * 凭证类型
	 * 01 数电发票
	 * 02 增值税专用发票
	 * 03 增值税普通发票
	 * 04 营业税发票
	 * 05 财政票据
	 * 06 法院裁决书
	 * 07 契税完税凭证
	 * 08 其他发票类
	 * 09 其他扣除凭证
	 */
	@ApiModelProperty("凭证类型")
	private String pzlx;
	/**
	 * 发票代码
	 * 凭证类型是“增值税专用发票”或“增值
	 * 税普通发票”或“营业税发票”时，必填
	 */
	@ApiModelProperty("发票代码")
	private String fpdm;
	/**
	 * 发票号码
	 * 凭证类型是“数电发票”时，必填
	 */
	@ApiModelProperty("发票号码")
	@JsonProperty("qdfpHm")
	private String fphm;
	/**
	 * 纸质发票号码
	 * 凭证类型是“增值税专用发票”或“增值
	 * 税普通发票”或“营业税发票”时，必填
	 */
	@ApiModelProperty("纸质发票号码")
	private String zzfphm;
	/**
	 * 凭证号码
	 */
	@ApiModelProperty("凭证号码")
	private String pzhm;
	/**
	 * 开具日期 yyyy-MM-dd HH:mm:ss 凭证类型是“数电发票”或“增值税专用发票 或“增值税普通发票”或“营业税发票”时， 必填
	 */
	@ApiModelProperty("开具日期")
	private String kjrq;
	/**
	 * 合计金额
	 */
	@ApiModelProperty("合计金额")
	private String hjje;
	/**
	 * 扣除额
	 */
	@ApiModelProperty("扣除额")
	private String kce;
	/**
	 * 备注 凭证类型是“其他发票类”或“其他扣除凭证 时，必填
	 */
	@ApiModelProperty("备注")
	private String bz;
	/**
	 * 录入方式 0:手工录入 1:勾选录入 2:模板录入
	 */
	@ApiModelProperty("录入方式")
	private String lrfs;
	/**
	 * 本次扣除金额，必须小于等于凭证合计金额
	 */
	@ApiModelProperty("本次扣除金额")
	private String bckcje;
	/**
	 * 凭证合计金额
	 */
	@ApiModelProperty("凭证合计金额")
	private String pzhjje;
	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
