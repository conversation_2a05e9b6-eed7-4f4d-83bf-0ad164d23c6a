package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.TaxClassCodeEntity;
import com.dxhy.order.utils.R;

/**
 * 税收分类编码表 service
 * <AUTHOR>
 * @Date 2022/6/27 12:10
 * @Version 1.0
 **/
public interface TaxClassCodeService extends IService<TaxClassCodeEntity> {

    /**
     * 通过名称搜索列表
     * @param
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listByName(String spmc);

    /**
     * 通过ID搜索下级
     * @param
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    R listById(String pid);

}

