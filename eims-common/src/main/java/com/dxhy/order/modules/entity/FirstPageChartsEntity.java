package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 全电概览 - 图标外层实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-24 11:09:24
 */
@Data
public class FirstPageChartsEntity implements Serializable {

    /**
     * 可用授信额度（元）
     */
    //@ApiModelProperty(value = "可用授信额度",dataType = "string", name ="kysxed")
    private String kysxed;

    /**
     * 总授信额度（元）
     */
    private String zsxed;

    /**
     * 可用授信额度（元）  用于计算
     */
    //@ApiModelProperty(value = "可用授信额度",dataType = "string", name ="kysxed")
    private String kysxedForCnt;

    /**
     * 总授信额度（元） 用于计算
     */
    private String zsxedForCnt;


    /**
     * 本月已开金额 面积图
     */
    private Map<String, Object> monthYkje;

    /**
     * 本月红冲金额 面积图
     */
    private Map<String, Object> monthHcje;

    /**
     * 本月已开张数 面积图
     */
    private Map<String, Object> monthYkzs;

    /**
     * 本月红冲张数 面积图
     */
    private Map<String, Object> monthHczs;

    /**
     * 累计数据 - 累计开具金额（元）
     */
    private String ljkjje;

    /**
     * 累计数据 - 累计开具税额（元）
     */
    private String ljkjse;

    /**
     * 累计数据 - 累计开具数量（元）
     */
    private String ljkjsl;

    /**
     * 累计数据 -  图
     */
    private Map<String, Object> ljsj;

    /**
     * 累计开具金额 折线图
     */
    private List<Map<String, Object>> yearLjkjje;

    /**
     * 累计开具税额 折线图
     */
    private List<Map<String, Object>> yearLjkjse;

    /**
     * 累计开具数量 折线图
     */
    private List<Map<String, Object>> yearLjkjsl;
}
