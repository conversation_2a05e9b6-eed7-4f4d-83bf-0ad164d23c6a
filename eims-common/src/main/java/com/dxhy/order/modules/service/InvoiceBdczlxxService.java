package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceBdczlxxEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 不动产租赁信息的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceBdczlxxService extends IService<InvoiceBdczlxxEntity> {
    void saveBatch(List<InvoiceBdczlxxEntity> list);
    void deleteByInvoiceId(String invoiceId,String tdywId);
}
