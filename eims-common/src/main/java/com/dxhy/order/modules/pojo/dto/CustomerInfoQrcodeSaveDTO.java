package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 新增 客户扫码信息DTO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("新增 客户扫码信息DTO")
public class CustomerInfoQrcodeSaveDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty(name = "所属纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 公司名称
     */
    @ApiModelProperty(name = "公司名称", required = true)
    private String gsmc;

    /**
     * 纳税人识别号
     */
    @ApiModelProperty("纳税人识别号")
    private String nsrsbh;

    /**
     * 公司地址
     */
    @ApiModelProperty("公司地址")
    private String gsdz;

    /**
     * 公司电话
     */
    @ApiModelProperty("公司电话")
    private String gsdh;

    /**
     * 开户银行
     */
    @ApiModelProperty("开户银行")
    private String khyh;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String yhzh;

    /**
     * 联系邮箱
     */
    @ApiModelProperty("联系邮箱")
    private String lxyx;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String lxdh;

}
