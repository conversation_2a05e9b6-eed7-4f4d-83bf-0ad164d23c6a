package com.dxhy.order.modules.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-机动车信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@Data
public class InvoiceIssueJdcxxParam implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 车辆类型
	 */
	private String cllx;

	/**
	 * 厂牌型号
	 */
	private String cpxh;

	/**
	 * 产地
	 */
	private String clcd;

	/**
	 * 合格证号
	 */
	private String hgzh;

	/**
	 * 进口证明书号
	 */
	private String jkzmsh;

	/**
	 * 商检单号
	 */
	private String sjdh;

	/**
	 * 发动机号码
	 */
	private String fdjhm;

	/**
	 * 车辆识别单号
	 */
	private String clsbdh;

	/**
	 * 完税凭证号码
	 */
	private String wspzhm;

	/**
	 * 吨位
	 */
	private String dw;

	/**
	 * 限乘人数
	 */
	private String xcrs;

	/**
	 * 车辆识别代号uuid
	 */
	private String clsbdhuuid;

	/**
	 * 生产企业名称
	 */
	private String scqymc;
}
