package com.dxhy.order.modules.service.impl;

import cn.hutool.core.date.DateUtil;
import com.dxhy.order.constant.ExcelReadContext;
import com.dxhy.order.constant.ExportInvoiceRecordEnum;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderInfoEnum;
import com.dxhy.order.modules.dao.OrderInvoiceInfoDao;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.bo.FileDownLoad;
import com.dxhy.order.modules.service.FileOperateService;
import com.dxhy.order.modules.service.OrderInvoiceInfoService;
import com.dxhy.order.modules.service.TaxpayerInfoService;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service("fileOperateService")
public class FileOperateServiceImpl implements FileOperateService {

    @Autowired
    private OrderInvoiceInfoService orderInvoiceInfoService;

    @Autowired
    private TaxpayerInfoService taxpayerInfoService;

    @Autowired
    private OrderInvoiceInfoDao orderInvoiceInfoDao;

    @Override
    public void downLoadOFD(String qdfphm, HttpServletRequest request, HttpServletResponse response) {
        log.info("(PDF下载)，发票号码：{}", qdfphm);
        if (StringUtils.isEmpty(qdfphm)) {
            log.info("(PDF下载)，全电发票号码为空");
            return;
        }

        try {

//            使用mongo存储pdf文件流方案：
//            // 查询MongodbId
//            OrderInvoiceInfoEntity orderInvoiceInfoEntity = orderInvoiceInfoDao.selectMongodbIdByFphm(fphm);
//
//            // 根据MongodbId从mongo获取返回的base64加密的字节流
//            String base64Str = mongodbService.find(orderInvoiceInfoEntity.getMongodbId();
//
//            BASE64Decoder decoder = new BASE64Decoder();
//            byte[] ofd_byte = decoder.decodeBuffer(base64Str);
//            log.info("模板下载ofd_byte，{}",ofd_byte);
//
//            // 将字节流直接返回，需要修改 1 文件名称 2 文件类型 ，并更新到枚举类中
//            response.setContentType("application/pdf");
//            response.setHeader("Content-Disposition", "attachment;filename=test.pdf");
//            response.setHeader("fileName", "test.pdf");
//            response.getOutputStream().write(ofd_byte);
//


//            本地开发阶段测试方案：
//            String devUrl = "/home/<USER>/eims-order/file/invoice/test.pdf";
//            FileInputStream fileInputStream = new FileInputStream(devUrl);
//            //FileInputStream fileInputStream = new FileInputStream("D:\\java\\Java-Idea\\eims-service\\eims-order\\src\\main\\resources\\download\\test.pdf");
//            BufferedInputStream bufferInputStream = new BufferedInputStream(fileInputStream);
//            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//            BufferedOutputStream bout = new BufferedOutputStream(byteArrayOutputStream);
//            byte[] buffer = new byte[1024];
//            int len = bufferInputStream.read(buffer);
//            while (len != -1) {
//                bout.write(buffer, 0, len);
//                len = bufferInputStream.read(buffer);
//            }
//            bout.flush();
//            byte[] bytes = byteArrayOutputStream.toByteArray();
//            log.info("bytes：{}", bytes);
//            bufferInputStream.close();


            // 20220721 当前方案: 存储ofd链接到数据库，根据全电发票号码查询ofd_url并请求获取文件字节流，返回前端
            OrderInvoiceInfoEntity invoiceInfoEntity = orderInvoiceInfoDao.seleInvoiceInfoByQdfphm(qdfphm);
            if (ObjectUtils.isEmpty(invoiceInfoEntity) || StringUtils.isEmpty(invoiceInfoEntity.getOfdUrl())) {
                log.info("(PDF下载)，全电发票号码：{}，未查询到有效ofd_url", qdfphm);
                return;
            }

            String url = invoiceInfoEntity.getPdfUrl();
            log.info("(PDF下载)，Https请求，参数：url:{}", url);
            byte[] ofd_byte = HttpsUtils.doGet(url);
            log.info("(PDF下载)，Https请求，返回：byte:{}", ofd_byte);

            if (ObjectUtils.isEmpty(ofd_byte)) {
                log.info("(PDF下载)，全电发票号码：{}，返回ofd字节流为空", qdfphm);
                return;
            }

            //通知类型
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment;filename=" + qdfphm + ".pdf");
            response.setHeader("fileName", qdfphm + ".pdf");
            response.getOutputStream().write(ofd_byte);
            log.info("(PDF下载)，全电发票号码: {}，下载成功", qdfphm);

        } catch (Exception e) {
            log.info("(PDF下载)，全电发票号码: {}，下载pdf文件失败：{}", qdfphm, e);
            return;
        }
    }

    @Override
    public void exportExcel(InvoiceRecordQueryList invoiceRecordQueryList, HttpServletRequest request, HttpServletResponse response) {

        //Page page = new Page(null, invoiceRecordQueryList.getPageSize());
        List<InvoiceRecordInfoEntity> list = orderInvoiceInfoDao.selectInvoiceRecordList(null, invoiceRecordQueryList);
        log.info("(excel导出),查询结果为:{}", JsonUtils.getInstance().toJsonString(list));
        for (InvoiceRecordInfoEntity invoiceRecordInfoEntity : list) {
            invoiceRecordInfoEntity.setKplx(OrderInfoEnum.ORDER_BILLING_INVOICE_TYPE_0.getKey().equals(invoiceRecordInfoEntity.getKplx()) ? OrderInfoEnum.ORDER_BILLING_INVOICE_TYPE_0.getValue() : OrderInfoEnum.ORDER_BILLING_INVOICE_TYPE_1.getValue());
            invoiceRecordInfoEntity.setFpzldm(FpzldmUtils.FpzldmToMc(invoiceRecordInfoEntity.getFpzldm()));
            invoiceRecordInfoEntity.setGhfmc(StringUtils.isEmpty(invoiceRecordInfoEntity.getGhfmc()) ? "" : invoiceRecordInfoEntity.getGhfmc());
            invoiceRecordInfoEntity.setQdfphm(StringUtils.isEmpty(invoiceRecordInfoEntity.getQdfphm()) ? "" : invoiceRecordInfoEntity.getQdfphm());
            invoiceRecordInfoEntity.setKprq(StringUtils.isEmpty(invoiceRecordInfoEntity.getKprq()) ? "" : invoiceRecordInfoEntity.getKprq());
            invoiceRecordInfoEntity.setKpje(StringUtils.isEmpty(invoiceRecordInfoEntity.getKpje()) ? "" : invoiceRecordInfoEntity.getKpje());
            invoiceRecordInfoEntity.setKpse(StringUtils.isEmpty(invoiceRecordInfoEntity.getKpse()) ? "" : invoiceRecordInfoEntity.getKpse());
        }
        log.info("(excel导出)，查询结果list转换:{}", JsonUtils.getInstance().toJsonString(list));

        // 创建临时文件
        OutputStream out = null;
        String filePrefix = DateUtil.format(new Date(), OrderInfoContentEnum.DATE_FORMAT_DATE_YMDHMS);

        try {
            String fileName = OrderInfoContentEnum.EXPORT_EXCEL_INVOICERECORD.getMessage();
            log.info("(excel导出)，excel文件名称为:{}", fileName);

            response.setContentType("octets/stream");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1") + filePrefix + ".xlsx");
            out = response.getOutputStream();
            // 调用生成excel方法
            log.info("(导出excel)，开始填充");
            // 创建excel填充数据
            exportInvoiceDetailExcel(out, list);
        } catch (Exception e) {
            log.error("(导出excel)，出现异常,异常信息为:{}", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

            } catch (IOException e) {
                log.error("流关闭异常");
            }
        }
    }


    /**
     * excel导出
     */
    public void exportInvoiceDetailExcel(OutputStream out, List<InvoiceRecordInfoEntity> list) {

        Long startTime = System.currentTimeMillis();

        // 创建一个工作簿
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(1000);

        try {

            //设置单个sheet最大容量,是个约值,暂定为50W
            int limitSheetSize = 500000;
            Map<String, String> headToProperty = new HashMap<>(10);
            Map<String, String> headerToColumnMap = new HashMap<>(10);

            for (ExportInvoiceRecordEnum exportInvoiceRecordEnum : ExportInvoiceRecordEnum.values()) {
                headToProperty.put(exportInvoiceRecordEnum.getValue(), exportInvoiceRecordEnum.getCellName());
                headerToColumnMap.put(exportInvoiceRecordEnum.getValue(), exportInvoiceRecordEnum.getKey());
            }
            ExcelReadContext context = new ExcelReadContext(headToProperty, true, headerToColumnMap);
            context.setFilePrefix(".xlsx");
            context.setSheetIndex(0);
            context.setHeadRow(0);
            context.setSheetLimit(limitSheetSize);

            ExcelExportUtils handle = new ExcelExportUtils(context);

            sxssfWorkbook = handle.exportExcel(sxssfWorkbook, list);

            sxssfWorkbook.write(out);
            Long endTime = System.currentTimeMillis();
            log.info("(导出excel)，结束,耗时:{}", endTime - startTime);
            log.info("(导出excel)，调用生成excel方法结束");

        } catch (Exception e) {
            log.error("导出excel)，异常:{}", e);
        } finally {
            try {
                sxssfWorkbook.close();
            } catch (IOException e) {
                log.error("(导出excel)，输出流关闭异常:{}", e);
            }
        }
    }


    /**
     * 数据统计 - 导出Excel
     *
     * @param dataStatisticQueryList
     * @return
     */
    @Override
    public void exportStaisticList(DataStatisticQueryList dataStatisticQueryList, HttpServletRequest request, HttpServletResponse response) {
        Map map = new HashMap();
        //Map map = orderInvoiceInfoService.queryXMStaisticList(dataStatisticQueryList);
        log.info("(导出excel)，税号：{}，list(yhzxxs):{}", dataStatisticQueryList.getBaseNsrsbh(), JsonUtils.getInstance().toJsonString(map));
        List list = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(map.get("yhzxxs")), ArrayList.class);
        log.info("(导出excel)，税号：{}，list(yhzxxs):{}", dataStatisticQueryList.getBaseNsrsbh(), JsonUtils.getInstance().toJsonString(list));
        DataStatisticsEntity dataStatisticsEntity = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(map.get("hjjes")), DataStatisticsEntity.class);
        log.info("(导出excel)，税号：{}，map1(hjjes):{}", dataStatisticQueryList.getBaseNsrsbh(), JsonUtils.getInstance().toJsonString(dataStatisticsEntity));

        OutputStream out = null;
        InputStream resource = null;
        try {

            // 创建Excel的工作书册 Workbook,对应到一个excel文档
            XSSFWorkbook wb = new XSSFWorkbook();
            wb.createSheet(FpzldmUtils.FpzldmToMc(dataStatisticQueryList.getFpzldm()) + "发票资料统计");
            XSSFSheet sheet = wb.getSheetAt(0);

            // 初始化40*40的表格
            for (int i = 0; i < 40; i++) {
                XSSFRow row = sheet.createRow(i);
                for (int k = 0; k < 40; k++) {
                    row.createCell(k);
                }
            }

            // 初始化表头信息
            // 制表日期
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, 0);
            String createDate = "制表日期： " + new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(Calendar.getInstance().getTime());
            // 所属期间
            String begin = "";
            String end = "";
            Date tmp = new Date();
            switch (dataStatisticQueryList.getTjfs()) {
                case "1": // 月度
                    tmp = DateUtil.parse(dataStatisticQueryList.getTime() + "-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
                    begin = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.beginOfMonth(tmp));
                    end = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.endOfMonth(tmp));
                    log.info("数据统计, 查询月度的日期区间：{}到{}", begin, end);
                    break;
                case "2": // 季度
                    switch (dataStatisticQueryList.getJd()) {
                        case "1":  // 1 - 3 月
                            tmp = DateUtil.parse(dataStatisticQueryList.getTime() + "-01-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
                            begin = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.beginOfQuarter(tmp));
                            end = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.endOfQuarter(tmp));
                            log.info("数据统计,查询第一季度的日期区间：{}到{}", begin, end);
                            break;
                        case "2": // 4 - 6 月
                            tmp = DateUtil.parse(dataStatisticQueryList.getTime() + "-04-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
                            begin = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.beginOfQuarter(tmp));
                            end = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.endOfQuarter(tmp));
                            log.info("数据统计,查询第二季度的日期区间：{}到{}", begin, end);
                            break;
                        case "3": // 7 - 9 月
                            tmp = DateUtil.parse(dataStatisticQueryList.getTime() + "-07-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
                            begin = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.beginOfQuarter(tmp));
                            end = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.endOfQuarter(tmp));
                            log.info("数据统计,查询第三季度的日期区间：{}到{}", begin, end);
                            break;
                        case "4": // 10 - 12 月
                            tmp = DateUtil.parse(dataStatisticQueryList.getTime() + "-10-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
                            begin = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.beginOfQuarter(tmp));
                            end = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.endOfQuarter(tmp));
                            log.info("数据统计,查询第四季度的日期区间：{}到{}", begin, end);
                            break;
                    }
                    break;
                case "3": // 年度
                    tmp = DateUtil.parse(dataStatisticQueryList.getTime() + "-01-01", OrderInfoContentEnum.DATE_FORMAT_DATE);
                    begin = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.beginOfYear(tmp));
                    end = new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_CHINESE).format(DateUtil.endOfYear(tmp));
                    log.info("数据统计,查询年度的日期区间：{}到{}", begin, end);
                    break;
            }
            String belongDate = "所属期间： " + begin + "-" + end;

            // 企业名称
            TaxpayerInfo taxpayerInfo = taxpayerInfoService.selectZsxedByNsrsbh(dataStatisticQueryList.getBaseNsrsbh());
            String xhfMc = "企业名称： " + taxpayerInfo.getXhfMc();
            // 纳税人识别号
            String nsrsbh = "纳税人识别号： " + dataStatisticQueryList.getBaseNsrsbh();
            // 金额单位
            String jedw = "金额单位： 元";
            // 表格标题
            String headTitle = FpzldmUtils.FpzldmToMc(dataStatisticQueryList.getFpzldm()) + "发票资料统计";

            // 头部标题 合并单元格
            CellRangeAddress cra = new CellRangeAddress(0, 0, 0, list.size() + 2);
            sheet.addMergedRegion(cra);
            CellRangeAddress cra1 = new CellRangeAddress(1, 1, 0, list.size() + 2);
            sheet.addMergedRegion(cra1);
            CellRangeAddress cra2 = new CellRangeAddress(2, 2, 0, list.size() + 2);
            sheet.addMergedRegion(cra2);
            CellRangeAddress cra3 = new CellRangeAddress(3, 3, 0, list.size() + 2);
            sheet.addMergedRegion(cra3);
            CellRangeAddress cra4 = new CellRangeAddress(4, 4, 0, list.size() + 2);
            sheet.addMergedRegion(cra4);
            CellRangeAddress cra5 = new CellRangeAddress(5, 5, 0, list.size() + 2);
            sheet.addMergedRegion(cra5);

            // 使用RegionUtil类为合并后的单元格添加边框
            // 下边框
            RegionUtil.setBorderBottom(BorderStyle.THIN, cra, sheet);
            // 左边框
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra, sheet);
            // 右边框
            RegionUtil.setBorderRight(BorderStyle.THIN, cra, sheet);
            // 上边框
            RegionUtil.setBorderTop(BorderStyle.THIN, cra, sheet);

            RegionUtil.setBorderBottom(BorderStyle.THIN, cra1, sheet);
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra1, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN, cra1, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN, cra1, sheet);

            RegionUtil.setBorderBottom(BorderStyle.THIN, cra2, sheet);
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra2, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN, cra2, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN, cra2, sheet);

            RegionUtil.setBorderBottom(BorderStyle.THIN, cra3, sheet);
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra3, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN, cra3, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN, cra3, sheet);

            RegionUtil.setBorderBottom(BorderStyle.THIN, cra4, sheet);
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra4, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN, cra4, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN, cra4, sheet);

            RegionUtil.setBorderBottom(BorderStyle.THIN, cra5, sheet);
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra5, sheet);
            RegionUtil.setBorderRight(BorderStyle.THIN, cra5, sheet);
            RegionUtil.setBorderTop(BorderStyle.THIN, cra5, sheet);


            //设置大标题样式字体
            XSSFCellStyle headBorder = createCellStyle(wb, "2");

            //设置制表信息样式字体
            XSSFCellStyle smallHeadBorder = createCellStyle(wb, "1");

            //设置列表头和行表头样式字体
            XSSFCellStyle textBorder = createCellStyle(wb, "2");

            //设置数据列样式字体
            XSSFCellStyle textBorderProjecrt = createCellStyle(wb, "3");

            //创建头部大小标题
            creatCellValue(sheet, 0, 0, headTitle, headBorder);
            creatCellValue(sheet, 1, 0, createDate, smallHeadBorder);
            creatCellValue(sheet, 2, 0, belongDate, smallHeadBorder);
            creatCellValue(sheet, 3, 0, nsrsbh, smallHeadBorder);
            creatCellValue(sheet, 4, 0, xhfMc, smallHeadBorder);
            creatCellValue(sheet, 5, 0, jedw, smallHeadBorder);

            // 创建第一列数据 序号列
            creatCellValue(sheet, 6, 0, "序号", textBorder);
            creatCellValue(sheet, 7, 0, "1", textBorder);
            creatCellValue(sheet, 8, 0, "2", textBorder);
            creatCellValue(sheet, 9, 0, "3", textBorder);
            creatCellValue(sheet, 10, 0, "4", textBorder);
            creatCellValue(sheet, 11, 0, "5", textBorder);
            creatCellValue(sheet, 12, 0, "6", textBorder);
            log.info("(导出excel)，税号：{}，序号列添加完毕", dataStatisticQueryList.getBaseNsrsbh());

            // 创建第二列数据 项目名称列
            creatCellValue(sheet, 6, 1, "项目名称", textBorder);
            creatCellValue(sheet, 7, 1, "销项正数金额", textBorder);
            creatCellValue(sheet, 8, 1, "销项正数税额", textBorder);
            creatCellValue(sheet, 9, 1, "销项负数金额", textBorder);
            creatCellValue(sheet, 10, 1, "销项负数税额", textBorder);
            creatCellValue(sheet, 11, 1, "实际销项金额", textBorder);
            creatCellValue(sheet, 12, 1, "实际销项税额", textBorder);
            log.info("(导出excel)，税号：{}，项目名称列添加完毕", dataStatisticQueryList.getBaseNsrsbh());

            // 创建第三列数据 合计列
            creatCellValue(sheet, 6, 2, "合计", textBorder);
            creatCellValue(sheet, 7, 2, dataStatisticsEntity.getZsje(), textBorderProjecrt);
            creatCellValue(sheet, 8, 2, dataStatisticsEntity.getZsse(), textBorderProjecrt);
            creatCellValue(sheet, 9, 2, dataStatisticsEntity.getFsje(), textBorderProjecrt);
            creatCellValue(sheet, 10, 2, dataStatisticsEntity.getFsse(), textBorderProjecrt);
            creatCellValue(sheet, 11, 2, dataStatisticsEntity.getSjje(), textBorderProjecrt);
            creatCellValue(sheet, 12, 2, dataStatisticsEntity.getSjse(), textBorderProjecrt);
            log.info("(导出excel)，税号：{}，合计列添加完毕", dataStatisticQueryList.getBaseNsrsbh());

            // 创建列数据
            log.info("(导出excel)，税号：{}，list(大小):{}", dataStatisticQueryList.getBaseNsrsbh(), list.size());
            for (int j = 0; j < list.size(); j++) {
                Map map2 = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(list.get(j)), HashMap.class);
                // log.info("(导出excel)，list(税率数据):{}", JsonUtils.getInstance().toJsonString(map2));
                List data = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(map2.get("mxxxs")), ArrayList.class);
                // log.info("(导出excel)，list(mxxxs):{}", JsonUtils.getInstance().toJsonString(data));
                creatCellValue(sheet, 6, j + 3, map2.get("sl").toString(), textBorder);
                creatCellValue(sheet, 7, j + 3, JsonUtils.getInstance().parseObject(data.get(0).toString(), HashMap.class).get("hjjese").toString(), textBorderProjecrt);
                creatCellValue(sheet, 8, j + 3, JsonUtils.getInstance().parseObject(data.get(1).toString(), HashMap.class).get("hjjese").toString(), textBorderProjecrt);
                creatCellValue(sheet, 9, j + 3, JsonUtils.getInstance().parseObject(data.get(2).toString(), HashMap.class).get("hjjese").toString(), textBorderProjecrt);
                creatCellValue(sheet, 10, j + 3, JsonUtils.getInstance().parseObject(data.get(3).toString(), HashMap.class).get("hjjese").toString(), textBorderProjecrt);
                creatCellValue(sheet, 11, j + 3, JsonUtils.getInstance().parseObject(data.get(4).toString(), HashMap.class).get("hjjese").toString(), textBorderProjecrt);
                creatCellValue(sheet, 12, j + 3, JsonUtils.getInstance().parseObject(data.get(5).toString(), HashMap.class).get("hjjese").toString(), textBorderProjecrt);
            }
            log.info("(导出excel)，税号：{}，税率列添加完毕", dataStatisticQueryList.getBaseNsrsbh());

            //将第3到15列表格宽度自适应
            for (int i = 0; i < OrderInfoContentEnum.INT_15; i++) {
                sheet.autoSizeColumn(i);
            }

            response.setContentType("octets/stream");
            String xlsName = FpzldmUtils.FpzldmToMc(dataStatisticQueryList.getFpzldm()) + "发票资料表";
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + new String(xlsName.getBytes("gb2312"), "ISO8859-1") + ".xlsx");
            out = response.getOutputStream();
            wb.write(out);
        } catch (Exception e) {
            log.error("{}发票资料表导出异常,税号：{}，异常信息为:{}", FpzldmUtils.FpzldmToMc(dataStatisticQueryList.getFpzldm()), dataStatisticQueryList.getBaseNsrsbh(), e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("{}发票资料表导出异常，税号：{}，异常信息为:{}", FpzldmUtils.FpzldmToMc(dataStatisticQueryList.getFpzldm()), dataStatisticQueryList.getBaseNsrsbh(), e);
                }
            }
            if (resource != null) {
                try {
                    resource.close();
                } catch (IOException e) {
                    log.error("{}发票资料表导出异常，税号：{}，异常信息为:{}", FpzldmUtils.FpzldmToMc(dataStatisticQueryList.getFpzldm()), dataStatisticQueryList.getBaseNsrsbh(), e);
                }
            }
        }
    }


    /**
     * 开票记录 - 批量导出zip
     *
     * @param request
     * @param response
     */
    @Override
    public void exportZip(HttpServletRequest request, HttpServletResponse response, InvoiceRecordZip invoiceRecordZip) {

        String nsrsbh = invoiceRecordZip.getBaseNsrsbh();
        log.info("(导出压缩包)，nsrsbh:{}，invoiceRecordZip：{}", nsrsbh, JsonUtils.getInstance().toJsonString(invoiceRecordZip));

        long lo = System.currentTimeMillis();

        String fileType = invoiceRecordZip.getFileType();
        List<String> colunm = invoiceRecordZip.getColunm();
        List<String> data = invoiceRecordZip.getList();

        List<OrderInvoiceInfoEntity> list = new ArrayList<>();
        if (data.size() > 0) {
            log.info("(导出压缩包)，nsrsbh:{}，批量导出勾选内容", nsrsbh);
            for (String id : data) {
                OrderInvoiceInfoEntity orderInvoiceInfoEntity = new OrderInvoiceInfoEntity();
                orderInvoiceInfoEntity.setId(id);
                OrderInvoiceInfoEntity orderInvoiceInfo = orderInvoiceInfoDao.selectById(orderInvoiceInfoEntity);
                list.add(orderInvoiceInfo);
            }
            log.info("(导出压缩包)，nsrsbh:{}，勾选内容共{}条", nsrsbh, data.size());
        } else {
            log.info("(导出压缩包)，nsrsbh:{}，批量导出全部内容", nsrsbh);
            invoiceRecordZip.getOrderInvoiceInfoEntity().setBaseNsrsbh(invoiceRecordZip.getBaseNsrsbh());
            list = orderInvoiceInfoDao.getOrderInvoiceInfoAllByKPZT(invoiceRecordZip.getOrderInvoiceInfoEntity());
            if (ObjectUtils.isEmpty(list)) {
                log.info("(导出压缩包)，nsrsbh:{}，全部内容为空", nsrsbh);
                return;
            }
            log.info("(导出压缩包)，nsrsbh:{}，全部内容共{}条", nsrsbh, list.size());

        }

        List<FileDownLoad> fileDownLoadList = new ArrayList<>();

        /******************************生成EXCEL**********************************/
        log.info("(导出压缩包)，nsrsbh:{}，生成excel开始", nsrsbh);
        // 接收前台数据，整理需要导出excel的列表
        List<String> colunmList = new ArrayList<>();
        for (String s : colunm) {
            colunmList.add(s);
        }

        // 将前台返回的list放入map中
        //Map colunmMap = new HashMap();
        List<Map<String, String>> finalList = new ArrayList<>();

        for (OrderInvoiceInfoEntity entity : list) {
            log.info("(导出压缩包)，nsrsbh:{}，ele：{}", nsrsbh, entity);
            Map entityMap = new HashMap();
            entityMap.put("订单号", entity.getDdh());
            entityMap.put("应收单号", entity.getYsdh());
            entityMap.put("生成订单时间", ObjectUtils.isEmpty(entity.getDdscrq()) ? "" : new SimpleDateFormat("yyyy-MM-dd").format(entity.getDdscrq()));
            entityMap.put("全电发票号码", entity.getQdfphm());
            entityMap.put("开票日期", ObjectUtils.isEmpty(entity.getKprq()) ? "" : new SimpleDateFormat("yyyy-MM-dd").format(entity.getKprq()));
            entityMap.put("购方名称", entity.getGhfMc());
            entityMap.put("购方税号", entity.getGhfNsrsbh());
            entityMap.put("不含税金额", entity.getHjbhsje());
            entityMap.put("税额", entity.getKpse());

            entityMap.put("价税合计", entity.getJshj());
            entityMap.put("开票状态", entity.getKpzt());
            entityMap.put("发票类型", entity.getFpzlDm());
            entityMap.put("订单来源", entity.getDdly());
            String jfsjyx = "";
            if (!StringUtils.isEmpty(entity.getGhfSj())) {
                jfsjyx = entity.getGhfSj();
                if (!StringUtils.isEmpty(entity.getGhfYx())) {
                    jfsjyx = jfsjyx + "," + entity.getGhfYx();
                }
            } else {
                if (!StringUtils.isEmpty(entity.getGhfYx())) {
                    jfsjyx = entity.getGhfYx();
                }
            }
            entityMap.put("交付手机/邮箱", jfsjyx);
            entityMap.put("冲红状态", entity.getChBz());
            String dyhp = "";
            if (!StringUtils.isEmpty(entity.getQdfphm())) {
                OrderInvoiceInfoEntity orderInvoiceInfo1 = orderInvoiceInfoDao.seleInvoiceInfoByYqdfphm(entity.getQdfphm());
                if (!ObjectUtils.isEmpty(orderInvoiceInfo1) && !StringUtils.isEmpty(orderInvoiceInfo1.getQdfphm())) {
                    dyhp = orderInvoiceInfo1.getQdfphm();
                }
            }
            entityMap.put("对应红票", dyhp);
            entityMap.put("错误原因", entity.getSbyy());
            entityMap.put("备注", entity.getBz());
            entityMap.put("是否标记为手工开票完成", "5".equals(entity.getKpzt()) ? "是" : "否");
            finalList.add(entityMap);
        }

        // 去除不需要导出的列
        for (Map map : finalList) {
            for (String key : colunmList) {
                if (ObjectUtils.isEmpty(map.get(key))) {
                    //if (!map.containsKey(key)) {
                    map.remove(key);
                }
            }
        }

        log.info("(导出压缩包)，nsrsbh:{}，map:{}", nsrsbh, finalList);

        // 根据选中的列，生成Excel
        // OutputStream out = null;
        try {

            // 创建Excel的工作书册 Workbook,对应到一个excel文档
            XSSFWorkbook wb = new XSSFWorkbook();
            wb.createSheet("发票记录表");
            XSSFSheet sheet = wb.getSheetAt(0);

            // 初始化40*40的表格
            for (int i = 0; i < 1000; i++) {
                XSSFRow row = sheet.createRow(i);
                for (int k = 0; k < 40; k++) {
                    row.createCell(k);
                }
            }

            //设置列表头和行表头样式字体
            XSSFCellStyle textBorder = createCellStyle(wb, "2");
            //设置数据列样式字体
            //XSSFCellStyle textBorderProjecrt = createCellStyle(wb, "3");

            // 创建标题名称列
            for (int i = 0; i < colunmList.size(); i++) {
                creatCellValue(sheet, 0, i, colunmList.get(i), textBorder);
            }
            log.info("(导出压缩包)，nsrsbh:{}，标题列添加完毕", nsrsbh);

            // 创建列数据
            for (int j = 0; j < finalList.size(); j++) {
                for (int k = 0; k < colunmList.size(); k++) {
                    String enumColunm = colunmList.get(k);
                    String enumCode = "";
                    String enumValue = finalList.get(j).get(colunmList.get(k));
                    // 开票状态 (0:待开;1:开票中;2:开票成功;3:开票失败;4:无需开票;5:手工标记开票成功)
                    if ("开票状态".equals(enumColunm)) {
                        enumCode = finalList.get(j).get(colunmList.get(k));
                        if ("0".equals(enumCode)) {
                            enumValue = "待开";
                        } else if ("1".equals(enumCode)) {
                            enumValue = "开票中";
                        } else if ("2".equals(enumCode)) {
                            enumValue = "开票成功";
                        } else if ("3".equals(enumCode)) {
                            enumValue = "开票失败";
                        } else if ("4".equals(enumCode)) {
                            enumValue = "无需开票";
                        } else if ("5".equals(enumCode)) {
                            enumValue = "手工标记开票成功";
                        } else {
                            enumValue = "";
                        }
                    } else if ("订单来源".equals(enumColunm)) {
                        // 订单来源 (0：扫码开票；1：批量导入，2：接口，3：手动填开)
                        enumCode = finalList.get(j).get(colunmList.get(k));
                        if ("0".equals(enumCode)) {
                            enumValue = "扫码开票";
                        } else if ("1".equals(enumCode)) {
                            enumValue = "批量导入";
                        } else if ("2".equals(enumCode)) {
                            enumValue = "接口";
                        } else if ("3".equals(enumCode)) {
                            enumValue = "手动填开";
                        } else if ("4".equals(enumCode)) {
                            enumValue = "慧企";
                        } else {
                            enumValue = "";
                        }
                    } else if ("冲红状态".equals(enumColunm)) {
                        // 冲红状态 (0:正常;1:全部冲红成功;2:全部冲红中;3:全部冲红失败;4:部分冲红成功;5:部分冲红中;6:部分冲红失败;(特殊说明:部分冲红只记录当前最后一次操作的记录))',
                        enumCode = finalList.get(j).get(colunmList.get(k));
                        if ("1".equals(enumCode)) {
                            enumValue = "已冲红";
                        } else {
                            enumValue = "未冲红";
                        }
                    } else if ("发票类型".equals(enumColunm)) {
                        // 0:全电电普,1:全电电专'
                        enumCode = finalList.get(j).get(colunmList.get(k));
                        if ("0".equals(enumCode)) {
                            enumValue = "电子发票（普通发票）";
                        } else if ("1".equals(enumCode)) {
                            enumValue = "全电发票(增值税专用发票)";
                        } else {
                            enumValue = "";
                        }
                    }
                    creatCellValue(sheet, j + 1, k, enumValue, textBorder);
                }
            }
            log.info("(导出压缩包)，nsrsbh:{}，列数据添加完毕", nsrsbh);

            // 将第0到20列表格宽度自适应
            for (int i = 0; i < OrderInfoContentEnum.INT_20; i++) {
                sheet.autoSizeColumn(i);
            }

            FileDownLoad fileDownLoad = new FileDownLoad();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            byte[] bytes = bos.toByteArray();
            fileDownLoad.setFileContent(Base64Encoding.encodeToString(bytes));
            fileDownLoad.setFileName("发票记录.xlsx");
            fileDownLoad.setFileSuffix(".xlsx");
            fileDownLoadList.add(fileDownLoad);

            log.info("(导出压缩包)，nsrsbh:{}，生成excel完成耗时：{}", nsrsbh, System.currentTimeMillis() - lo);

            /*********************************获取板式文件********************************/
            log.info("(导出压缩包)，nsrsbh:{}，打包发票文件开始", nsrsbh);
            int i = 1;
            long lo1 = System.currentTimeMillis();
            // 根据选中的文件类型，获取文件  pdf ofd xml
            for (OrderInvoiceInfoEntity entityForUrl : list) {
                //OrderInvoiceInfoEntity entityForUrl = orderInvoiceInfoDao.selectById(orderInvoiceInfoEntity);
                //log.info("(导出压缩包)，下载链接：{}", entityForUrl);
                log.info("(导出压缩包)，nsrsbh:{}，下载链接，第{}条数据，发票ID：{}，发票号码：{}", nsrsbh, i, entityForUrl.getId(), entityForUrl.getQdfphm());
                try {
                    if (fileType.contains("PDF")) {
                        if (!StringUtils.isEmpty(entityForUrl.getPdfUrl())) {
                            String pdfUrl = entityForUrl.getPdfUrl();
                            byte[] pdf_byte = null;
                            if (pdfUrl.contains("http")) {
                                log.info("(导出压缩包)，nsrsbh:{}，PDF下载，Http请求，参数：url:{}", nsrsbh, pdfUrl);
                                pdf_byte = HttpUtils.doGet(pdfUrl);
                            } else {
                                log.info("(导出压缩包)，nsrsbh:{}，PDF下载，Https请求，参数：url:{}", nsrsbh, pdfUrl);
                                pdf_byte = HttpsUtils.doGet(pdfUrl);
                            }
                            //log.info("(PDF下载)，Https请求，返回：byte:{}", pdf_byte);
                            FileDownLoad fileDownLoadPDF = new FileDownLoad();
                            fileDownLoadPDF.setFileContent(Base64Encoding.encodeToString(pdf_byte));
                            fileDownLoadPDF.setFileName(entityForUrl.getQdfphm() + ".pdf");
                            fileDownLoadPDF.setFileSuffix(".pdf");
                            fileDownLoadList.add(fileDownLoadPDF);
                        }
                    }

                    if (fileType.contains("OFD")) {
                        if (!StringUtils.isEmpty(entityForUrl.getPdfUrl())) {
                            String ofdUrl = entityForUrl.getPdfUrl().replace("PDF", "OFD");
                            byte[] ofd_byte = null;
                            if (ofdUrl.contains("http")) {
                                log.info("(导出压缩包)，nsrsbh:{}，OFD下载，Http请求，参数：url:{}", nsrsbh, ofdUrl);
                                ofd_byte = HttpUtils.doGet(ofdUrl);
                            } else {
                                log.info("(导出压缩包)，nsrsbh:{}，OFD下载，Https请求，参数：url:{}", nsrsbh, ofdUrl);
                                ofd_byte = HttpsUtils.doGet(ofdUrl);
                            }
                            //log.info("(OFD下载)，Https请求，返回：byte:{}", ofdUrl);
                            FileDownLoad fileDownLoadOFD = new FileDownLoad();
                            fileDownLoadOFD.setFileContent(Base64Encoding.encodeToString(ofd_byte));
                            fileDownLoadOFD.setFileName(entityForUrl.getQdfphm() + ".ofd");
                            fileDownLoadOFD.setFileSuffix(".ofd");
                            fileDownLoadList.add(fileDownLoadOFD);
                        }
                    }

                    if (fileType.contains("XML")) {
                        if (!StringUtils.isEmpty(entityForUrl.getPdfUrl())) {
                            String xmlUrl = entityForUrl.getPdfUrl().replace("PDF", "XML");
                            byte[] xml_byte = null;
                            if (xmlUrl.contains("http")) {
                                log.info("(导出压缩包)，nsrsbh:{}，XML下载，Http请求，参数：url:{}", nsrsbh, xmlUrl);
                                xml_byte = HttpUtils.doGet(xmlUrl);
                            } else {
                                log.info("(导出压缩包)，nsrsbh:{}，XML下载，Https请求，参数：url:{}", nsrsbh, xmlUrl);
                                xml_byte = HttpsUtils.doGet(xmlUrl);
                            }
                            //log.info("(XML下载)，Https请求，返回：byte:{}", xml_byte);
                            FileDownLoad fileDownLoadXML = new FileDownLoad();
                            fileDownLoadXML.setFileContent(Base64Encoding.encodeToString(xml_byte));
                            fileDownLoadXML.setFileName(entityForUrl.getQdfphm() + ".zip");
                            fileDownLoadXML.setFileSuffix(".zip");
                            fileDownLoadList.add(fileDownLoadXML);
                        }
                    }
                } catch (Exception e) {
                    log.error("(导出压缩包)，nsrsbh:{}，下载文件异常信息：{}，异常内容：{}", nsrsbh, entityForUrl, e);
                    i++;
                    continue;
                }
                i++;
            }

            log.info("(导出压缩包)，nsrsbh:{}，打包发票文件结束耗时：{}", nsrsbh, System.currentTimeMillis() - lo1);

            /**********************************压缩文件*******************************/
            log.info("(导出压缩包)，nsrsbh:{}，打包ZIP开始", nsrsbh);
            // 文件都压缩到一起
            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());
            for (FileDownLoad file : fileDownLoadList) {

                zipOut.putNextEntry(new ZipEntry(file.getFileName()));

                InputStream stream = new ByteArrayInputStream(Base64Encoding.decode(file.getFileContent()));

                int temp;
                while ((temp = stream.read()) != -1) {
                    zipOut.write(temp);
                }
                stream.close();
            }

            zipOut.closeEntry();
            zipOut.close();

            log.info("(导出压缩包)，nsrsbh:{}，打包zip结束总耗时：{}", nsrsbh, System.currentTimeMillis() - lo);

            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;filename=发票记录.zip");
            response.setHeader("fileName", "发票记录" + ".zip");
        } catch (Exception e) {
            log.error("(导出压缩包)，nsrsbh:{}，异常，异常信息为:{}", nsrsbh, e);
            return;
        }

    }


    /**
     * @Description: 创建表格样式字体
     */
    private XSSFCellStyle createCellStyle(XSSFWorkbook wb, String styleType) {
        //设置表头字体 标题
        XSSFCellStyle cellStyle = wb.createCellStyle();
        //下边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        //左边框
        cellStyle.setBorderLeft(BorderStyle.THIN);
        //上边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        //右边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        //垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        XSSFFont cellFont = wb.createFont();
        cellFont.setFontName("宋体");
        //设置字体大小
        cellFont.setFontHeightInPoints((short) 12);

        switch (styleType) {
            case "1":
                // 水平居左
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                break;
            case "2":
                // 水平居中
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                break;
            case "3":
                //水平居右
                cellStyle.setAlignment(HorizontalAlignment.RIGHT);
                break;
            default:
                break;
        }
        //选择需要用到的字体格式
        cellStyle.setFont(cellFont);

        return cellStyle;
    }


    /**
     * <p>
     * 功能实现描述
     * </p>
     *
     * @param sheet     这个指的是这个excel表的第几页对象
     * @param rowIndex  表示哪一行的index ,第一行为 index : 0,.....
     * @param cellIndex 表示这一行哪一个单元格index,第一个单元格index : 0,...
     * @param value     void 表示给具体的单元格设置值
     */
    private static void creatCellValue(XSSFSheet sheet, int rowIndex, int cellIndex, String value, XSSFCellStyle
            cellStyle) {
        // log.info("(导出excel)，creatCellValue入参，sheet：{}，rowIndex：{}，cellIndex：{}，value：{}，cellStyle：{}：", sheet, rowIndex, cellIndex, value, cellStyle);

        XSSFRow row = sheet.getRow(rowIndex);

        //log.info("单元格处理，rowindex:{}, cellindex:{},value:{}", rowIndex, cellIndex, value);

        XSSFCell createCell = row.createCell(cellIndex);
//        XSSFCell createCell;
//        if (ObjectUtils.isEmpty(row.getCell(cellIndex))) {
//            createCell = row.createCell(cellIndex);
//        } else {
//            createCell
//        }
//
//        if (ObjectUtils.isEmpty(createCell)) {
//            createCell = row.createCell(cellIndex);
//        }

        if (null == value || StringUtils.isEmpty(value)) {
            createCell.setCellValue("");
        } else {
            // 单元格赋值
            createCell.setCellValue(value);
        }
//        log.info("(导出excel)，value：{}，已插入", value);
        // 设置样式
        createCell.setCellStyle(cellStyle);
    }
}
