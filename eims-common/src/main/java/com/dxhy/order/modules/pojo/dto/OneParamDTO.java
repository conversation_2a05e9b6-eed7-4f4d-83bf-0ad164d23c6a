package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 单个参数入参DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("单个参数入参DTO")
public class OneParamDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("单个参数入参")
    private String str;
}
