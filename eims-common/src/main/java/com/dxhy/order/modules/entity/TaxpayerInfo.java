package com.dxhy.order.modules.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 销方企业信息表
 * @TableName taxpayer_info
 */
@Data
@Getter
@Setter
public class TaxpayerInfo implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 销方名称
     */
    private String xhfMc;

    /**
     * 销方税号
     */
    private String xhfNsrsbh;

    /**
     * 销方地址
     */
    private String xhfDz;

    /**
     * 销方电话
     */
    private String xhfDh;

    /**
     * 销方银行
     */
    private String xhfYh;

    /**
     * 销方账号
     */
    private String xhfZh;

    /**
     * 销方所属地区编码
     */
    private String sfbm;

    /**
     * 总授信额度
     */
    private String zsxed;

    /**
     * secretId
     */
    private String secretId;
    /**
     * secretKey
     */
    private String secretKey;

    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 销方名称
     */
    public String getXhfMc() {
        return xhfMc;
    }

    /**
     * 销方名称
     */
    public void setXhfMc(String xhfMc) {
        this.xhfMc = xhfMc;
    }

    /**
     * 销方税号
     */
    public String getXhfNsrsbh() {
        return xhfNsrsbh;
    }

    /**
     * 销方税号
     */
    public void setXhfNsrsbh(String xhfNsrsbh) {
        this.xhfNsrsbh = xhfNsrsbh;
    }

    /**
     * 销方地址
     */
    public String getXhfDz() {
        return xhfDz;
    }

    /**
     * 销方地址
     */
    public void setXhfDz(String xhfDz) {
        this.xhfDz = xhfDz;
    }

    /**
     * 销方电话
     */
    public String getXhfDh() {
        return xhfDh;
    }

    /**
     * 销方电话
     */
    public void setXhfDh(String xhfDh) {
        this.xhfDh = xhfDh;
    }

    /**
     * 销方银行
     */
    public String getXhfYh() {
        return xhfYh;
    }

    /**
     * 销方银行
     */
    public void setXhfYh(String xhfYh) {
        this.xhfYh = xhfYh;
    }

    /**
     * 销方账号
     */
    public String getXhfZh() {
        return xhfZh;
    }

    /**
     * 销方账号
     */
    public void setXhfZh(String xhfZh) {
        this.xhfZh = xhfZh;
    }

    /**
     * 销方所属地区编码
     */
    public String getSfbm() {
        return sfbm;
    }

    /**
     * 销方所属地区编码
     */
    public void setSfbm(String sfbm) {
        this.sfbm = sfbm;
    }

    /**
     * 总授信额度
     */
    public String getZsxed() {
        return zsxed;
    }

    /**
     * 总授信额度
     */
    public void setZsxed(String zsxed) {
        this.zsxed = zsxed;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TaxpayerInfo other = (TaxpayerInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getXhfMc() == null ? other.getXhfMc() == null : this.getXhfMc().equals(other.getXhfMc()))
            && (this.getXhfNsrsbh() == null ? other.getXhfNsrsbh() == null : this.getXhfNsrsbh().equals(other.getXhfNsrsbh()))
            && (this.getXhfDz() == null ? other.getXhfDz() == null : this.getXhfDz().equals(other.getXhfDz()))
            && (this.getXhfDh() == null ? other.getXhfDh() == null : this.getXhfDh().equals(other.getXhfDh()))
            && (this.getXhfYh() == null ? other.getXhfYh() == null : this.getXhfYh().equals(other.getXhfYh()))
            && (this.getXhfZh() == null ? other.getXhfZh() == null : this.getXhfZh().equals(other.getXhfZh()))
            && (this.getSfbm() == null ? other.getSfbm() == null : this.getSfbm().equals(other.getSfbm()))
            && (this.getZsxed() == null ? other.getZsxed() == null : this.getZsxed().equals(other.getZsxed()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getXhfMc() == null) ? 0 : getXhfMc().hashCode());
        result = prime * result + ((getXhfNsrsbh() == null) ? 0 : getXhfNsrsbh().hashCode());
        result = prime * result + ((getXhfDz() == null) ? 0 : getXhfDz().hashCode());
        result = prime * result + ((getXhfDh() == null) ? 0 : getXhfDh().hashCode());
        result = prime * result + ((getXhfYh() == null) ? 0 : getXhfYh().hashCode());
        result = prime * result + ((getXhfZh() == null) ? 0 : getXhfZh().hashCode());
        result = prime * result + ((getSfbm() == null) ? 0 : getSfbm().hashCode());
        result = prime * result + ((getZsxed() == null) ? 0 : getZsxed().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", xhfMc=").append(xhfMc);
        sb.append(", xhfNsrsbh=").append(xhfNsrsbh);
        sb.append(", xhfDz=").append(xhfDz);
        sb.append(", xhfDh=").append(xhfDh);
        sb.append(", xhfYh=").append(xhfYh);
        sb.append(", xhfZh=").append(xhfZh);
        sb.append(", sfbm=").append(sfbm);
        sb.append(", zsxed=").append(zsxed);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}