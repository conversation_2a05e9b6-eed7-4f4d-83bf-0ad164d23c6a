package com.dxhy.order.modules.service;

import java.util.List;
import java.util.Set;

/**
 * redis缓存服务
 *
 * <AUTHOR>
 */
public interface RedisService {
    
    /**
     * 给一个key值设置过期时间
     *
     * @param key
     * @param seconds
     * @return
     */
    Boolean expire(String key, int seconds);
    
    /**
     * 删除缓存中得对象，根据key
     *
     * @param key
     * @return
     */
    boolean del(String key);
    
    /**
     * 根据key 获取内容
     *
     * @param key
     * @return
     */
    String get(String key);
    
    /**
     * 根据key 获取对象
     *
     * @param key
     * @param clazz
     * @return
     */
    <T> T get(String key, Class<T> clazz);
    
    /**
     * 如果 key 已经存在并且是一个字符串， APPEND 命令将 指定value 追加到改 key 原来的值（value）的末尾。
     *
     * @param key
     * @param value
     * @return
     */
    long append(String key, String value);
    
    /**
     * setNX
     *
     * @param key
     * @param value
     * @return
     */
    boolean setNx(String key, String value);
    
    /**
     * 向缓存中设置字符串内容
     *
     * @param key
     * @param value
     * @return
     */
    boolean set(String key, String value);
    
    /**
     * 保存缓存
     *
     * @param key
     * @param value
     * @param seconds 有效时间/秒
     * @return
     */
    boolean set(String key, String value, int seconds);
    
    /**
     * 保存缓存
     *
     * @param key
     * @param value   对象，转为json字符串后存入redis
     * @param seconds 有效时间，单位为秒
     * @return
     */
    boolean set(String key, Object value, int seconds);
    
    /**
     * 将一个或多个值插入到列表头部
     *
     * @param key
     * @param value
     * @return
     */
    Long lPush(String key, String... value);
    
    /**
     * 移除并获取列表最后一个元素
     *
     * @param key
     * @return
     */
    String rPop(String key);
    
    /**
     * 移除列表某一个元素
     *
     * @param key
     * @param value
     * @return
     */
    Long listRemove(String key, String value);
    
    /**
     * 在列表中添加一个或多个值
     *
     * @param key
     * @param value
     * @return
     */
    Long rPush(String key, String... value);
    
    /**
     * 模糊查询key
     *
     * @param pattern
     * @return
     */
    Set<String> keys(String pattern);
    
    /**
     * 模糊查询key
     *
     * @param key
     * @return
     */
    List<String> lRange(String key);
    
    /**
     * 获取失效时间
     *
     * @param key
     * @return
     */
    Long getExpire(String key);
}
