package com.dxhy.order.modules.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.util.StringUtils;

import java.io.Serializable;

/**
 * 客户信息导出VO
 * @author: zhangjinjing
 * @Date: 2022/6/27 14:37
 * @Version 1.0
 */
@TableName("customer_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("客户信息导出VO")
public class ExportCustomerInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户分类名称
     */
    @ApiModelProperty("客户分类名称")
    private String khflmc;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String gsmc;

    /**
     * 统一社会信用代码/纳税人识别号
     */
    @ApiModelProperty("统一社会信用代码/纳税人识别号")
    private String nsrsbh;

    /**
     * 简码
     */
    @ApiModelProperty("简码")
    private String scode;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String gsdz;

    /**
     * 电话
     */
    @ApiModelProperty("电话")
    private String gsdh;

    /**
     * 开户行名称
     */
    @ApiModelProperty("开户行名称")
    private String khyh;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String yhzh;

    /**
     * 联系邮箱
     */
    @ApiModelProperty("联系邮箱")
    private String email;

    /**
     * 是否默认地址
     */
    @ApiModelProperty("是否默认地址")
    private String sfmrdz;

    public void clearNull(){
        if(StringUtils.isEmpty(khflmc)){
            khflmc = " ";
        }
        if(StringUtils.isEmpty(gsmc)){
            gsmc = " ";
        }
        if(StringUtils.isEmpty(nsrsbh)){
            nsrsbh = " ";
        }
        if(StringUtils.isEmpty(scode)){
            scode = " ";
        }
        if(StringUtils.isEmpty(gsdz)){
            gsdz = " ";
        }
        if(StringUtils.isEmpty(gsdh)){
            gsdh = " ";
        }
        if(StringUtils.isEmpty(khyh)){
            khyh = " ";
        }
        if(StringUtils.isEmpty(yhzh)){
            yhzh = " ";
        }
        if(StringUtils.isEmpty(email)){
            email = " ";
        }
        if(StringUtils.isEmpty(sfmrdz)){
            sfmrdz = " ";
        }
    }

}
