package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-建筑服务信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_jzfwxx")
@Data
public class InvoiceJzfwxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 建筑服务主键
	 */
	@ApiModelProperty(value = "建筑服务主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("土地增值税项目编号")
	private String tdzzsxmbh;

	@ApiModelProperty("跨区域涉税事项报验管理编号")
	private String kqysssxbgglbm;

	@ApiModelProperty("建筑服务发生地（地区）")
	private String jzfwfsd;

	@ApiModelProperty("建筑服务详细地址")
	private String jzfwxxdz;

	@ApiModelProperty("建筑服务名称")
	private String jzfwmc;

	@ApiModelProperty("跨地(市)标志")
	private String kdsbz;
	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
