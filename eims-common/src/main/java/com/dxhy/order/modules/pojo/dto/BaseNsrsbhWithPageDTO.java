package com.dxhy.order.modules.pojo.dto;

import com.dxhy.order.utils.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 当前销方税号分页查询DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("当前销方税号分页查询DTO")
public class BaseNsrsbhWithPageDTO extends BasePage implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "当前销方税号", required = true)
    private String baseNsrsbh;
}
