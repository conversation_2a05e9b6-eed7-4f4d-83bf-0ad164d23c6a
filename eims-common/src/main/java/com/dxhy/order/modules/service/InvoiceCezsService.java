package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.InvoiceCezsEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 差额征税的数据库操作Service
* @createDate 2024-12-30 12:14:23
*/
public interface InvoiceCezsService extends IService<InvoiceCezsEntity> {
    void saveBatch(List<InvoiceCezsEntity> list,String invoiceId);
    void deleteByInvoiceId(String invoiceId);
}
