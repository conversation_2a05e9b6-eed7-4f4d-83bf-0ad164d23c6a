package com.dxhy.order.modules.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.constant.OrderInfoContentEnum;
import com.dxhy.order.constant.OrderManagementConstant;
import com.dxhy.order.modules.dao.AdditionElementDao;
import com.dxhy.order.modules.dao.SceneTemplateDao;
import com.dxhy.order.modules.dao.TemplateAdditionRelationDao;
import com.dxhy.order.modules.entity.*;
import com.dxhy.order.modules.pojo.dto.IdsDTO;
import com.dxhy.order.modules.pojo.dto.SceneTemplateListDTO;
import com.dxhy.order.modules.pojo.dto.SceneTemplateSaveDTO;
import com.dxhy.order.modules.pojo.vo.SceneTemplateListVO;
import com.dxhy.order.modules.pojo.vo.SceneTemplateVO;
import com.dxhy.order.modules.service.AdditionElementService;
import com.dxhy.order.modules.service.SceneTemplateService;
import com.dxhy.order.permit.sso.SsoUtil;
import com.dxhy.order.pojo.HuiqiSceneTemplateDTO;
import com.dxhy.order.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 场景模板表 serviceImpl
 * <AUTHOR>
 * @Date 2022/6/27 12:13
 * @Version 1.0
 **/
@Service("sceneTemplateService")
@Slf4j
@RefreshScope
public class SceneTemplateServiceImpl extends ServiceImpl<SceneTemplateDao, SceneTemplateEntity> implements SceneTemplateService {

    private static final String LOGGER = "(场景模板)";

    @Value("${order.encrypt.cjmbcxUrl}")
    private String cjmbcxUrl;
    @Value("${order.encrypt.cjmbxzUrl}")
    private String cjmbxzUrl;
    @Value("${order.encrypt.cjmbxgUrl}")
    private String cjmbxgUrl;
    @Value("${order.encrypt.cjmbscUrl}")
    private String cjmbscUrl;

    @Autowired
    private SceneTemplateDao sceneTemplateDao;
    @Autowired
    private AdditionElementDao additionElementDao;
    @Autowired
    private TemplateAdditionRelationDao templateAdditionRelationDao;
    @Autowired
    private AdditionElementService additionElementService;
    @Resource
    private SsoUtil ssoUtil;

    @Override
    public R queryPage(SceneTemplateListDTO sceneTemplateListDTO) {
        if(sceneTemplateListDTO == null || StringUtils.isEmpty(sceneTemplateListDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        Page page = new Page(sceneTemplateListDTO.getCurrPage(), sceneTemplateListDTO.getPageSize());
        List<SceneTemplateListVO> list = sceneTemplateDao.selectList(page, sceneTemplateListDTO);
        for (SceneTemplateListVO sceneTemplateListVO : list) {
            sceneTemplateListVO.clearNull();
            List<String> fjxxmcList = additionElementDao.listNameBySceneTemplateId(sceneTemplateListVO.getId());
            if(CollectionUtils.isNotEmpty(fjxxmcList)){
                sceneTemplateListVO.setFjxxmc(fjxxmcList);
            }else{
                sceneTemplateListVO.setFjxxmc(new ArrayList<>());
            }
        }
        page.setRecords(list);
        PageUtils pageUtils = new PageUtils(page);
        return R.ok().put("data", pageUtils);
    }

    @Override
    public R listWithoutPage(SceneTemplateListDTO sceneTemplateListDTO) {
        if(sceneTemplateListDTO == null || StringUtils.isEmpty(sceneTemplateListDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        List<SceneTemplateEntity> list = sceneTemplateDao.listWithoutPage(sceneTemplateListDTO);
        return R.ok().put("data", list);
    }

    @Override
    public R queryDataById(String id) {
        if(StringUtils.isEmpty(id)){
            return R.error("数据不存在");
        }
        SceneTemplateEntity sceneTemplateEntity = sceneTemplateDao.queryDataById(id);
        SceneTemplateVO voData = new SceneTemplateVO(sceneTemplateEntity);
        return R.ok().put("data", voData);
    }

    @Override
    public R saveData(SceneTemplateSaveDTO sceneTemplateSaveDTO) {
        String checkStr = this.checkSceneTemplateSaveDTO(sceneTemplateSaveDTO);
        if(StringUtils.isNotEmpty(checkStr)){
            return R.error(checkStr);
        }
        if(StringUtils.isNotEmpty(sceneTemplateSaveDTO.getId())){
            // 修改
            // 调用慧企接口后再更新
            HuiqiSceneTemplateDTO huiqiSceneTemplateDTO = new HuiqiSceneTemplateDTO();
            huiqiSceneTemplateDTO.setBaseNsrsbh(sceneTemplateSaveDTO.getBaseNsrsbh());
            huiqiSceneTemplateDTO.setCjmbmc(sceneTemplateSaveDTO.getName());
            List<String> list = new ArrayList<>();
            if (sceneTemplateSaveDTO.getAdditionElementIdList().size() > 0) {
                for (String id : sceneTemplateSaveDTO.getAdditionElementIdList()) {
                    AdditionElementEntity additionElementEntity = additionElementDao.queryDataById(id);
                    list.add(additionElementEntity.getUuid());
                }
            }
            huiqiSceneTemplateDTO.setJcxxFpfjysxxList(list);
            SceneTemplateEntity sceneTemplateEntity = sceneTemplateDao.queryDataById(sceneTemplateSaveDTO.getId());
            sceneTemplateEntity.setName(sceneTemplateSaveDTO.getName());
            sceneTemplateEntity.setUpdateTime(new Date());
            sceneTemplateEntity.setUpdateBy(ssoUtil.getUserName());
            // 修改数据
            sceneTemplateDao.updateById(sceneTemplateEntity);
            // 完善关联表数据
            List<String> sceneTemplateIdList = new ArrayList<>();
            sceneTemplateIdList.add(sceneTemplateSaveDTO.getId());
            templateAdditionRelationDao.deleteBySceneTemplateIdList(sceneTemplateIdList);
            if(CollectionUtils.isNotEmpty(sceneTemplateSaveDTO.getAdditionElementIdList())){
                sceneTemplateSaveDTO.getAdditionElementIdList().forEach(tempAdditionElementId -> {
                    TemplateAdditionRelationEntity tempData = new TemplateAdditionRelationEntity(DistributedKeyMaker.generateShotKey(), sceneTemplateSaveDTO.getId(), tempAdditionElementId);
                    templateAdditionRelationDao.insert(tempData);
                });
            }
            // 更新附加信息表的引用状态
            additionElementService.refreshYyzt();
            log.info("{}，场景模板修改成功", LOGGER);
            return R.ok();
//            long begin = System.currentTimeMillis();
//            String reqString = JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO);
//            log.info("{}，调用场景模板修改接口入参: {}", LOGGER, reqString);
//            String respString = HttpUtils.doPost(cjmbxgUrl, reqString);
//            log.info("{}，结束调用附加要素新增接口， 耗时: {}", System.currentTimeMillis() - begin);
//            log.info("{}，调用场景模板修改接口出参: {}", LOGGER, respString);
//            R res = JsonUtils.getInstance().parseObject(respString, R.class);
//            if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
//                SceneTemplateEntity updateData = sceneTemplateDao.queryDataById(sceneTemplateSaveDTO.getId());
//                if(updateData == null){
//                    return R.error("数据不存在");
//                }
//                updateData.setName(sceneTemplateSaveDTO.getName());
//                updateData.setUpdateTime(new Date());
//                updateData.setUpdateBy(ssoUtil.getUserName());
//                // 修改数据
//                sceneTemplateDao.updateById(updateData);
//                // 完善关联表数据
//                List<String> sceneTemplateIdList = new ArrayList<>();
//                sceneTemplateIdList.add(sceneTemplateSaveDTO.getId());
//                templateAdditionRelationDao.deleteBySceneTemplateIdList(sceneTemplateIdList);
//                if(CollectionUtils.isNotEmpty(sceneTemplateSaveDTO.getAdditionElementIdList())){
//                    sceneTemplateSaveDTO.getAdditionElementIdList().forEach(tempAdditionElementId -> {
//                        TemplateAdditionRelationEntity tempData = new TemplateAdditionRelationEntity(DistributedKeyMaker.generateShotKey(), sceneTemplateSaveDTO.getId(), tempAdditionElementId);
//                        templateAdditionRelationDao.insert(tempData);
//                    });
//                }
//                // 更新附加信息表的引用状态
//                additionElementService.refreshYyzt();
//                log.info("{}，场景模板修改成功", LOGGER);
//                return R.ok();
//            }

        }else{
            // 新增
            // 调用慧企接口后再入库
//            HuiqiSceneTemplateDTO huiqiSceneTemplateDTO = new HuiqiSceneTemplateDTO();
//            huiqiSceneTemplateDTO.setBaseNsrsbh(sceneTemplateSaveDTO.getBaseNsrsbh());
//            huiqiSceneTemplateDTO.setCjmbmc(sceneTemplateSaveDTO.getName());
//            List<String> list = new ArrayList<>();
//            if (sceneTemplateSaveDTO.getAdditionElementIdList().size() > 0) {
//                for (String id : sceneTemplateSaveDTO.getAdditionElementIdList()) {
//                    AdditionElementEntity additionElementEntity = additionElementDao.queryDataById(id);
//                    list.add(additionElementEntity.getUuid());
//                }
//            }
//            huiqiSceneTemplateDTO.setJcxxFpfjysxxList(list);

            SceneTemplateEntity saveData = new SceneTemplateEntity();
            saveData.setUuid(DistributedKeyMaker.generateShotKey());
            saveData.setId(DistributedKeyMaker.generateShotKey());
            saveData.setBaseNsrsbh(sceneTemplateSaveDTO.getBaseNsrsbh());
            saveData.setName(sceneTemplateSaveDTO.getName());
            saveData.setIsDelete("0");
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(ssoUtil.getUserName());
            // 新增数据
            sceneTemplateDao.insert(saveData);
            // 完善关联表数据
            if (CollectionUtils.isNotEmpty(sceneTemplateSaveDTO.getAdditionElementIdList())) {
                sceneTemplateSaveDTO.getAdditionElementIdList().forEach(tempAdditionElementId -> {
                    TemplateAdditionRelationEntity tempData = new TemplateAdditionRelationEntity(DistributedKeyMaker.generateShotKey(), saveData.getId(), tempAdditionElementId);
                    templateAdditionRelationDao.insert(tempData);
                });
            }
            // 更新附加信息表的引用状态
            additionElementService.refreshYyzt();
            log.info("{}，场景模板新增入库成功", LOGGER);
            return R.ok().put("data", saveData.getId());















//            long begin = System.currentTimeMillis();
//            String reqString = JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO);
//            log.info("{}，调用场景模板新增接口入参: {}", LOGGER, reqString);
//            String respString = HttpUtils.doPost(cjmbxzUrl, reqString);
//            log.info("{}，结束调用附加要素新增接口， 耗时: {}", System.currentTimeMillis() - begin);
//            log.info("{}，调用场景模板新增接口出参: {}", LOGGER, respString);
//            R res = JsonUtils.getInstance().parseObject(respString, R.class);
//            if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
//                SceneTemplateEntity saveData = new SceneTemplateEntity();
//                Map map = JsonUtils.getInstance().parseObject(res.get("data").toString(), HashMap.class);
//                saveData.setUuid(map.get("uuid").toString());
//                saveData.setId(DistributedKeyMaker.generateShotKey());
//                saveData.setBaseNsrsbh(sceneTemplateSaveDTO.getBaseNsrsbh());
//                saveData.setName(sceneTemplateSaveDTO.getName());
//                saveData.setIsDelete("0");
//                saveData.setCreateTime(new Date());
//                saveData.setCreateBy(ssoUtil.getUserName());
//                // 新增数据
//                sceneTemplateDao.insert(saveData);
//                // 完善关联表数据
//                if (CollectionUtils.isNotEmpty(sceneTemplateSaveDTO.getAdditionElementIdList())) {
//                    sceneTemplateSaveDTO.getAdditionElementIdList().forEach(tempAdditionElementId -> {
//                        TemplateAdditionRelationEntity tempData = new TemplateAdditionRelationEntity(DistributedKeyMaker.generateShotKey(), saveData.getId(), tempAdditionElementId);
//                        templateAdditionRelationDao.insert(tempData);
//                    });
//                }
//                // 更新附加信息表的引用状态
//                additionElementService.refreshYyzt();
//                log.info("{}，场景模板新增入库成功", LOGGER);
//                return R.ok().put("data", saveData.getId());
//            }

        }
    }

    private String checkSceneTemplateSaveDTO(SceneTemplateSaveDTO sceneTemplateSaveDTO){
        if(sceneTemplateSaveDTO == null){
            return "入参为空";
        }
        if(StringUtils.isEmpty(sceneTemplateSaveDTO.getBaseNsrsbh())){
            return "请选择公司主体";
        }
        if(StringUtils.isEmpty(sceneTemplateSaveDTO.getName())){
            return "场景模板名称为空";
        }
        List<SceneTemplateEntity> sceneTemplateEntityList = sceneTemplateDao.selectListByNsrsbhAndName(sceneTemplateSaveDTO.getBaseNsrsbh(), sceneTemplateSaveDTO.getName());
        if(CollectionUtils.isNotEmpty(sceneTemplateEntityList)){
            if(sceneTemplateEntityList.size() > 1){
                return "场景模板名称重复";
            }
            if(StringUtils.isEmpty(sceneTemplateSaveDTO.getId())){
                return "场景模板名称重复.";
            }
            if(!sceneTemplateSaveDTO.getId().equals(sceneTemplateEntityList.get(0).getId())){
                return "场景模板名称重复..";
            }
        }
        return "";
    }

    @Override
    public R deleteData(IdsDTO idsDTO) {
        if(idsDTO == null || CollectionUtils.isEmpty(idsDTO.getIds())){
            return R.error("请选择要操作的数据");
        }
        if(StringUtils.isEmpty(idsDTO.getBaseNsrsbh())){
            return R.error("请选择公司主体");
        }
        // 1 清理关联表
        templateAdditionRelationDao.deleteBySceneTemplateIdList(idsDTO.getIds());
        // 2 逻辑删除
        sceneTemplateDao.deleteByIdList(idsDTO.getIds());
        // 3 更新引用状态
        additionElementService.refreshYyzt();
        log.info("{}，场景模板删除成功", LOGGER);
        return R.ok();




//        HuiqiSceneTemplateDTO huiqiSceneTemplateDTO = new HuiqiSceneTemplateDTO();
//        List<String> list = new ArrayList();
//        for (String id : idsDTO.getIds()) {
//            SceneTemplateEntity sceneTemplateEntity = sceneTemplateDao.queryDataById(id);
//            list.add(sceneTemplateEntity.getUuid());
//        }
//        huiqiSceneTemplateDTO.setBaseNsrsbh(idsDTO.getBaseNsrsbh());
//        huiqiSceneTemplateDTO.setUuidList(list);
//        long begin = System.currentTimeMillis();
//        String reqString = JsonUtils.getInstance().toJsonString(huiqiSceneTemplateDTO);
//        log.info("{}，调用场景模板删除接口入参: {}", LOGGER, reqString);
//        String respString = HttpUtils.doPost(cjmbscUrl, reqString);
//        log.info("{}，结束调用场景模板删除接口， 耗时: {}", System.currentTimeMillis() - begin);
//        log.info("{}，调用场景模板删除接口出参: {}", LOGGER, respString);
//        R res = JsonUtils.getInstance().parseObject(respString, R.class);
//        if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
//            // 1 清理关联表
//            templateAdditionRelationDao.deleteBySceneTemplateIdList(idsDTO.getIds());
//            // 2 逻辑删除
//            sceneTemplateDao.deleteByIdList(idsDTO.getIds());
//            // 3 更新引用状态
//            additionElementService.refreshYyzt();
//            log.info("{}，场景模板删除成功", LOGGER);
//            return R.ok();
//        }
//        log.info("{}，场景模板删除失败", LOGGER);
//        return R.error(res.get(OrderManagementConstant.CODE).toString(), res.get(OrderManagementConstant.MESSAGE).toString());
    }

    /**
     * 查询场景模板并更新到数据库
     * @return R
     */
    @Override
    public R getSceneTemplateTask(String baseNsrsbh) {
        log.info("{}，调用场景模板查询接口入参: {}", LOGGER, baseNsrsbh);
        // 查询次数计算
        int times = 0;
        R res = sendCjmbQuery("10", "1", "", baseNsrsbh);
        if ("0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
            SceneTemplateQueryRes sceneTemplateQueryRes = JsonUtils.getInstance().parseObject(JsonUtils.getInstance().toJsonString(res.get("data")), SceneTemplateQueryRes.class);
            times = Integer.valueOf(sceneTemplateQueryRes.getPages());
        }
        log.info("{}，调用场景模板查询接口次数: {}", LOGGER, times);
        // 查询剩余数据
        for (int i = 2; i <= times; i++) {
            log.info("{}，调用场景模板查询第{}次查询", LOGGER, i);
            sendCjmbQuery("10", String.valueOf(i), "", baseNsrsbh);
        }
        log.info("{}，调用场景模板查询接口执行完成", LOGGER);
        return R.ok();
    }

    /**
     * 调用场景模板查询接口
     * @param size
     * @param current
     * @param fjxxmc
     * @return
     */
    public R sendCjmbQuery(String size, String current, String fjxxmc, String baseNsrsbh) {
        AdditionElementSaveDTO additionElementSaveDTO = new AdditionElementSaveDTO();
        additionElementSaveDTO.setCurrent(current);
        additionElementSaveDTO.setSize(size);
        additionElementSaveDTO.setFjxxmc(fjxxmc);
        additionElementSaveDTO.setBaseNsrsbh(baseNsrsbh);
        long begin = System.currentTimeMillis();
        String reqString = JsonUtils.getInstance().toJsonString(additionElementSaveDTO);
        log.info("{}，调用场景模板查询接口入参: {}", LOGGER, reqString);
        String respString = HttpUtils.doPost(cjmbcxUrl, reqString);
        log.info("{}，结束调用场景模板查询接口， 耗时: {}", System.currentTimeMillis() - begin);
        log.info("{}，调用场景模板查询接口出参: {}", LOGGER, respString);
        R res = JsonUtils.getInstance().parseObject(respString, R.class);
        SceneTemplateQueryRes sceneTemplateQueryRes = new SceneTemplateQueryRes();
        if (!ObjectUtils.isEmpty(res) && "0000".equals(res.get(OrderManagementConstant.CODE).toString())) {
            sceneTemplateQueryRes = JsonUtils.getInstance().parseObject(res.get("data").toString(), SceneTemplateQueryRes.class);
            if (ObjectUtils.isNotEmpty(sceneTemplateQueryRes) && ObjectUtils.isNotEmpty(sceneTemplateQueryRes.getRecords())) {
                for (SceneTemplateEntityRes element : sceneTemplateQueryRes.getRecords()) {
                    SceneTemplateEntity sceneTemplateEntity = sceneTemplateDao.queryDataByUUID(element.getUuid());
                    SceneTemplateEntity sceneTemplateEntit1 = new SceneTemplateEntity();
                    sceneTemplateEntit1.setUuid(element.getUuid());
                    sceneTemplateEntit1.setBaseNsrsbh(element.getNsrsbh());
                    sceneTemplateEntit1.setName(element.getCjmbmc());
                    sceneTemplateEntit1.setCjmbmc(element.getCjmbmc());
                    sceneTemplateEntit1.setYxbz(element.getYxbz());
                    try {
                        sceneTemplateEntit1.setCreateTime(new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M_DH_M_S).parse(element.getLrrq()));
                        sceneTemplateEntit1.setUpdateTime(new SimpleDateFormat(OrderInfoContentEnum.DATE_FORMAT_DATE_Y_M_DH_M_S).parse(element.getXgrq()));
                    }  catch (Exception e) {
                        log.error("{}，调用场景模板查询日期处理失败", LOGGER);
                    }
                    sceneTemplateEntit1.setCreateBy(element.getLrrmc());
                    sceneTemplateEntit1.setUpdateBy(element.getXgrmc());

                    // 更新
                    if (ObjectUtils.isNotEmpty(sceneTemplateEntity)) {
                        log.info("{}，调用场景模板查询结果更新", LOGGER);
                        sceneTemplateEntit1.setId(sceneTemplateEntity.getId());
                        int cnt = sceneTemplateDao.updateById(sceneTemplateEntit1);
                        if (cnt < 1) {
                            log.info("{}，调用场景模板查询结果更新失败", LOGGER);
                        } else {
                            log.info("{}，调用场景模板查询结果更新成功", LOGGER);
                        }

                    } else {
                        log.info("{}，调用场景模板查询结果新增", LOGGER);
                        // 新增
                        sceneTemplateEntit1.setId(DistributedKeyMaker.generateShotKey());
                        sceneTemplateEntit1.setIsDelete("0");
                        int cnt = sceneTemplateDao.insert(sceneTemplateEntit1);
                        if (cnt < 1) {
                            log.info("{}，调用场景模板查询结果新增失败", LOGGER);
                        } else {
                            log.info("{}，调用场景模板查询结果新增成功", LOGGER);
                        }
                    }
                    // 附加要素表更新
                    if (ObjectUtils.isNotEmpty(element.getJcxxFpfjysxxList())) {
                        log.info("{}，调用场景模板查询更新附加要素表", LOGGER);
                        for (SceneTemplateEntityFjusRes fjys : element.getJcxxFpfjysxxList()) {
                            AdditionElementEntity additionElementEntity = additionElementDao.queryDataByUUID(fjys.getUuid());
                            if (ObjectUtils.isEmpty(additionElementEntity)) {
                                if("string".equals(fjys.getSjlx1())){
                                    additionElementEntity.setSjlx("1");
                                }
                                if("number".equals(fjys.getSjlx1())){
                                    additionElementEntity.setSjlx("2");
                                }
                                if("date".equals(fjys.getSjlx1())){
                                    additionElementEntity.setSjlx("3");
                                }
                                additionElementEntity.setFjxxmc(fjys.getFjysxmmc());
                                additionElementEntity.setUuid(fjys.getUuid());
                                additionElementEntity.setBaseNsrsbh(baseNsrsbh);
                                additionElementEntity.setId(DistributedKeyMaker.generateShotKey());
                                additionElementEntity.setIsDelete("0");
                                additionElementEntity.setCreateTime(new Date());

                                int cnt = additionElementDao.insert(additionElementEntity);
                                if (cnt < 1) {
                                    log.info("{}，调用场景模板查询新增附加要素表失败", LOGGER);
                                } else {
                                    log.info("{}，调用场景模板查询新增附加要素表成功", LOGGER);
                                }
                            }

                            // 更新场景模板和福建要素关系表
                            log.info("{}，调用场景模板查询新增关系表", LOGGER);
                            TemplateAdditionRelationEntity templateAdditionRelationEntity = new TemplateAdditionRelationEntity();
                            templateAdditionRelationEntity.setSceneTemplateId(sceneTemplateEntit1.getId());
                            templateAdditionRelationEntity.setAdditionEleId(additionElementEntity.getId());
                            List<TemplateAdditionRelationEntity> list = templateAdditionRelationDao.queryByAllParam(templateAdditionRelationEntity);
                            if (list.size() < 1) {
                                templateAdditionRelationEntity.setId(DistributedKeyMaker.generateShotKey());
                                int cnt = templateAdditionRelationDao.insert(templateAdditionRelationEntity);
                                if (cnt < 1) {
                                    log.info("{}，调用场景模板查询新增关系表失败", LOGGER);
                                } else {
                                    log.info("{}，调用场景模板查询新增关系表成功", LOGGER);
                                }
                            } else {
                                log.info("{}，调用场景模板查询关系表无需新增", LOGGER);
                            }
                        }
                        log.info("{}，调用场景模板查询更新附加要素表", LOGGER);
                    }
                }
            }
            log.info("{}，调用附加要素查询方法成功: {}", LOGGER, res);
            return R.ok().put("data", sceneTemplateQueryRes);
        } else {
            log.info("{}，调用附加要素查询方法失败", LOGGER);
            return R.error();
        }
    }

}
