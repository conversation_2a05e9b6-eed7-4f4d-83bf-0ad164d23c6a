package com.dxhy.order.modules.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 发票特定业务-不动产销售信息
 * 
 * <AUTHOR> @email 
 * @date 2022-06-22 19:47:06
 */
@TableName("invoice_bdcxsxx")
@Data
public class InvoiceBdcxsxxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 不动产销售主键
	 */
	@ApiModelProperty(value = "不动产销售主键")
	@TableId(type = IdType.INPUT)
	private String id;
	/**
	 * 发票主表主键
	 */
	@ApiModelProperty(value = "发票主表主键")
	private String orderInvoiceInfoId;
	/**
	 * 特定业务主键
	 */
	@ApiModelProperty(value = "特定业务主键")
	private String invoiceTdywId;

	@ApiModelProperty("序号")
	private String xh;

	@ApiModelProperty("网签合同标志")
	private String wqhtbz;

	@ApiModelProperty("不动产单元代码/网签合同备案编号")
	private String bdcwqhtbh;

	@ApiModelProperty("不动产地区")
	private String bdcdq;

	@ApiModelProperty("不动产地址")
	private String bdcdz;

	@ApiModelProperty("跨地(市)标志")
	private String kdsbz;

	@ApiModelProperty("土地增值税项目编号")
	private String tdzzsxmbh;

	@ApiModelProperty("核定计税价格")
	private String hdjsjg;

	@ApiModelProperty("实际成交含税金额")
	private String sjcjhsje;

	@ApiModelProperty("房屋产权证书号/不动产权证号")
	private String cqzsh;

	@ApiModelProperty("面积单位")
	private String mjdw;

	/**
	 *逻辑删除
	 */
	@ApiModelProperty(value = "逻辑删除",hidden = true)
	private String isDelete;
}
