package com.dxhy.order.modules.service.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dxhy.order.modules.dao.InvoiceEscxxDao;
import com.dxhy.order.modules.entity.InvoiceEscxxEntity;
import com.dxhy.order.modules.service.InvoiceEscxxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 二手车信息的数据库操作Service实现
 * @createDate 2024-12-30 12:14:23
 */
@Service("invoiceEscxxService")
@Slf4j
@RefreshScope
public class InvoiceEscxxServiceImpl extends ServiceImpl<InvoiceEscxxDao, InvoiceEscxxEntity>
        implements InvoiceEscxxService {

    @Override
    public void saveBatch(List<InvoiceEscxxEntity> list) {
        this.saveBatch(list,1000);
    }

    @Override
    public void deleteByInvoiceId(String invoiceId,String tdywId) {
        LambdaUpdateWrapper<InvoiceEscxxEntity> escxxWrapper = Wrappers.lambdaUpdate();
        escxxWrapper.set(InvoiceEscxxEntity::getIsDelete, "1");
        escxxWrapper.eq(InvoiceEscxxEntity::getOrderInvoiceInfoId, invoiceId);
        escxxWrapper.eq(InvoiceEscxxEntity::getInvoiceTdywId, tdywId);
        this.update(escxxWrapper);
    }
}




