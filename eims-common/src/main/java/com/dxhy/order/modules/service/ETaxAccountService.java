package com.dxhy.order.modules.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dxhy.order.modules.entity.ETaxAccountEntity;
import com.dxhy.order.utils.R;

/**
 * 电子税务局账号表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-23 10:56:08
 */
public interface ETaxAccountService extends IService<ETaxAccountEntity> {


    R activeEtaxAccount(ETaxAccountEntity eTaxAccountEntity);

    R login(ETaxAccountEntity eTaxAccountEntity);

    R getAuthQrcode(ETaxAccountEntity eTaxAccountEntity);

    R getAuthStatus(ETaxAccountEntity eTaxAccountEntity);

    R selectByNsrsbh(ETaxAccountEntity eTaxAccountEntity);

    R setSms(ETaxAccountEntity eTaxAccountEntity);

}

