package com.dxhy.order.modules.dao;


import com.dxhy.order.model.EwmConfigInfo;

import java.util.Map;

/**
 * 二维码配置信息
 *
 * <AUTHOR>
 * @date 创建时间: 2022-06-29 09:42
 */
public interface EwmConfigInfoMapper {
    
    /**
     * 插入数据
     *
     * @param record
     * @return
     */
    int insertSelective(EwmConfigInfo record);
    
    /**
     * 更新数据
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(EwmConfigInfo record);
    
    /**
     * 查询信息
     *
     * @param paramMap
     * @return
     */
    EwmConfigInfo queryEwmConfigInfo(Map<String, Object> paramMap);
    
}
