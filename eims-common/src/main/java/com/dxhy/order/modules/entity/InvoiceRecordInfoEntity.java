package com.dxhy.order.modules.entity;

import com.dxhy.order.utils.BasePage;
import lombok.Data;

import java.io.Serializable;

/**
 * 开票记录 - 查询返回实体
 *
 * <AUTHOR>
 * @email
 * @date 2022-06-29 12:54:24
 */
@Data
public class InvoiceRecordInfoEntity extends BasePage implements Serializable {

    /**
     * 开票票种
     */
    private String fpzldm;

    /**
     * 购方名称
     */
    private String ghfmc;

    /**
     * 购方税号
     */
    private String ghfNsrsbh;

    /**
     * 全电发票号码
     */
    private String qdfphm;

    /**
     * 开票日期 yyyy-MM-dd
     */
    private String kprq;

    /**
     * 发票金额（元）
     */
    private String kpje;

    /**
     * 发票税额（元）
     */
    private String kpse;

    /**
     * 开票类型
     */
    private String kplx;

    /**
     * 开票人
     */
    private String kpr;

    /**
     * order_invice_info 主键ID
     */
    private String ID;

    /**
     * 开票状态  (0:待开;1:开票中;2:开票成功;3:开票失败;)
     */
    private String kpzt;

    /**
     * 下载标识 ofd下载链接是否已保存到数据库，已保存则显示下载按钮，未保存则不显示下载按钮
     * true|显示下载 false|不显示下载
     */
    private String downLoadFlag;

    /** ofd地址 **/
    private String ofdUrl;
    /** pdf地址 **/
    private String pdfUrl;
    /** xml地址 **/
    private String xmlUrl;
    /** 价税合计 **/
    private String jshj;
    /** 销货方名称 **/
    private String xhfMc;
    /** 销货方纳税人识别号 **/
    private String xhfNsrsbh;
    /** 基本纳税人识别号 **/
    private String baseNsrsbh;
    /** 订单来源 **/
    private String ddly;

}
