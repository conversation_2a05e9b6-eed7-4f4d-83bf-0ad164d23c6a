package com.dxhy.order.modules.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 当前销方税号DTO
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("当前销方税号DTO")
public class BaseNsrsbhDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "当前销方税号", required = true)
    @NotBlank
    private String baseNsrsbh;
}
