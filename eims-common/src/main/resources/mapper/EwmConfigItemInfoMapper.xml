<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dxhy.order.modules.dao.EwmConfigItemInfoMapper" >
  <resultMap id="BaseResultMap" type="com.dxhy.order.model.EwmConfigItemInfo" >

    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="ewm_coinfg_id" property="ewmCoinfgId" jdbcType="VARCHAR" />
    <result column="fpzldm" property="fpzldm" jdbcType="VARCHAR" />
    <result column="sld" property="sld" jdbcType="VARCHAR" />
    <result column="sld_mc" property="sldMc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, ewm_coinfg_id, fpzldm, sld, sld_mc, create_time
  </sql>
<!--  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >-->

<!--    select -->
<!--    <include refid="Base_Column_List" />-->
<!--    from ewm_config_item-->
<!--    where id = #{id,jdbcType=VARCHAR}-->
<!--  </select>-->
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >-->
<!--    delete from ewm_config_item-->
<!--    where id = #{id,jdbcType=VARCHAR}-->
<!--  </delete>-->
<!--  <insert id="insert" parameterType="com.dxhy.qrorder.model.EwmConfigItemInfo" >-->
<!--    insert into ewm_config_item (id, ewm_coinfg_id, fpzldm,-->
<!--      sld, sld_mc, create_time-->
<!--      )-->
<!--    values (#{id,jdbcType=VARCHAR}, #{ewmCoinfgId,jdbcType=VARCHAR}, #{fpzldm,jdbcType=VARCHAR},-->
<!--      #{sld,jdbcType=VARCHAR}, #{sldMc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}-->
<!--      )-->
<!--  </insert>-->
  <insert id="insertEwmConfigItem" parameterType="com.dxhy.order.model.EwmConfigItemInfo" >
    insert into ewm_config_item
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="ewmCoinfgId != null" >
        ewm_coinfg_id,
      </if>
      <if test="fpzldm != null" >
        fpzldm,
      </if>
      <if test="sld != null" >
        sld,
      </if>
      <if test="sldMc != null" >
        sld_mc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="ewmCoinfgId != null" >
        #{ewmCoinfgId,jdbcType=VARCHAR},
      </if>
      <if test="fpzldm != null" >
        #{fpzldm,jdbcType=VARCHAR},
      </if>
      <if test="sld != null" >
        #{sld,jdbcType=VARCHAR},
      </if>
      <if test="sldMc != null" >
        #{sldMc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="queryEwmItemInfoByEwmConfigId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from ewm_config_item
    where ewm_coinfg_id = #{ewmCoinfgId,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByEwmConfigId" parameterType="java.lang.String" >
    delete from ewm_config_item
    where ewm_coinfg_id = #{id,jdbcType=VARCHAR}
  </delete>


</mapper>
