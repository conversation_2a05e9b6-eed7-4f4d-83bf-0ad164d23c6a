<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.SceneTemplateDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.SceneTemplateEntity" id="sceneTemplateEntityMap">
        <id property="id" column="id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="name" column="name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="uuid" column="uuid"/>
        <result property="cjmbmc" column="cjmbmc"/>
        <result property="yxbz" column="yxbz"/>
    </resultMap>

    <select id="selectList" resultType="com.dxhy.order.modules.pojo.vo.SceneTemplateListVO">
        select
            t1.id, t1.name, t1.create_time createTime, t1.create_by createBy, t1.update_time updateTime, t1.update_by updateBy
        from scene_template t1
        <where>
            t1.base_nsrsbh = #{sceneTemplateListDTO.baseNsrsbh}
            <if test="sceneTemplateListDTO.name != null and sceneTemplateListDTO.name != ''">
                <bind name="param" value="'%' + sceneTemplateListDTO.name + '%'"/>
                and t1.name like #{param}
            </if>
            and t1.is_delete = '0'
        </where>
        order by t1.create_time desc
    </select>

    <select id="listWithoutPage" resultMap="sceneTemplateEntityMap">
        select `id`, `name` from scene_template t1
        <where>
            t1.base_nsrsbh = #{baseNsrsbh}
            <if test="name != null and name != ''">
                <bind name="param" value="'%' + name + '%'"/>
                and t1.name like #{param}
            </if>
            and t1.is_delete = '0'
        </where>
        order by t1.create_time desc
    </select>

    <select id="selectListByNsrsbhAndName" resultMap="sceneTemplateEntityMap">
        select * from scene_template where base_nsrsbh = #{baseNsrsbh} and `name` = #{name} and is_delete = '0'
    </select>

    <select id="queryDataById" resultMap="sceneTemplateEntityMap">
        select * from scene_template where id = #{id} and is_delete = '0' limit 1
    </select>

    <select id="queryDataByUUID" resultType="com.dxhy.order.modules.entity.SceneTemplateEntity">
        select * from scene_template where uuid = #{uuid} and is_delete = '0' limit 1
    </select>

    <update id="deleteByIdList">
        update scene_template set is_delete = '1' where id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>