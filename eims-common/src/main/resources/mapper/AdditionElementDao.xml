<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.AdditionElementDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.AdditionElementEntity" id="additionElementEntityMap">
        <result property="id" column="id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="fjxxmc" column="fjxxmc"/>
        <result property="sjlx" column="sjlx"/>
        <result property="srfs" column="srfs"/>
        <result property="yyzt" column="yyzt"/>
        <result property="isDelete" column="is_delete"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="uuid" column="uuid"/>
        <result property="nsrmc" column="nsrmc"/>
        <result property="yxbz" column="yxbz"/>
    </resultMap>

    <select id="selectList" resultMap="additionElementEntityMap">
        SELECT * FROM addition_element
        <where>
            base_nsrsbh = #{additionElementListDTO.baseNsrsbh}
            <if test="additionElementListDTO.fjxxmc != null and additionElementListDTO.fjxxmc != ''">
                <bind name="param" value="'%' + additionElementListDTO.fjxxmc + '%'"/>
                and fjxxmc like #{param}
            </if>
            and is_delete = '0'
        </where>
        order by create_time desc
    </select>

    <select id="selectListByNsrsbh" resultType="com.dxhy.order.modules.entity.AdditionElementEntity">
        SELECT * FROM addition_element where base_nsrsbh = #{baseNsrsbh} and is_delete = '0' order by create_time desc
    </select>

    <select id="selectListByNsrsbhAndName" resultMap="additionElementEntityMap">
        SELECT * FROM addition_element where base_nsrsbh = #{baseNsrsbh} and fjxxmc = #{fjxxmc} and is_delete = '0'
    </select>

    <select id="listAdditionElementBySceneTemplateId" resultType="com.dxhy.order.modules.pojo.vo.AdditionElementVO">
        select t2.id, t2.fjxxmc, t2.sjlx from template_addition_relation t1
        left join addition_element t2 on t1.addition_ele_id = t2.id
        where t1.scene_template_id = #{id}
          and t2.is_delete = '0'
        order by t2.create_time desc
    </select>

    <select id="listNameBySceneTemplateId" resultType="java.lang.String">
        select t2.fjxxmc from template_addition_relation t1
        left join addition_element t2 on t1.addition_ele_id = t2.id
        where t1.scene_template_id = #{id}
          and t2.is_delete = '0'
        order by t2.create_time desc
    </select>

    <select id="queryDataById" resultMap="additionElementEntityMap">
        SELECT * FROM addition_element where id = #{id} and is_delete = '0' limit 1
    </select>

    <select id="countYyztTureDataByIdList" resultType="java.lang.Integer">
        SELECT count(1) FROM addition_element
        where  yyzt = '1' and id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryDataByUUID" resultType="com.dxhy.order.modules.entity.AdditionElementEntity">
        SELECT * FROM addition_element where uuid = #{uuid} and is_delete = '0' limit 1
    </select>

    <delete id="deleteByIdList">
        update addition_element set is_delete = '1'
        where id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="refreshYyzt_0">
        update addition_element set yyzt = 0 where id not in (select distinct addition_ele_id from template_addition_relation)
    </update>

    <update id="refreshYyzt_1">
        update addition_element set yyzt = 1 where id in (select distinct addition_ele_id from template_addition_relation)
    </update>

</mapper>