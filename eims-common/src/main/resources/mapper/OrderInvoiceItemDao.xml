<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.OrderInvoiceItemDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.OrderInvoiceItemEntity" id="orderInvoiceItemMap">
        <result property="id" column="id"/>
        <result property="orderInvoiceId" column="order_invoice_id"/>
        <result property="xh" column="xh"/>
        <result property="xmmc" column="xmmc"/>
        <result property="ggxh" column="ggxh"/>
        <result property="dw" column="dw"/>
        <result property="xmsl" column="xmsl"/>
        <result property="hsbz" column="hsbz"/>
        <result property="dj" column="dj"/>
        <result property="je" column="je"/>
        <result property="sl" column="sl"/>
        <result property="se" column="se"/>
        <result property="fphxz" column="fphxz"/>
        <result property="spbm" column="spbm"/>
        <result property="zxbm" column="zxbm"/>
        <result property="yhzcbs" column="yhzcbs"/>
        <result property="lslbs" column="lslbs"/>
        <result property="zzstsgl" column="zzstsgl"/>
        <result property="kce" column="kce"/>
        <result property="qdfphm" column="qdfphm"/>
        <result property="slArray" column="sl_array"/>
        <result property="isDelete" column="is_delete"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="byzd7" column="byzd7"/>
        <result property="byzd8" column="byzd8"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectList" parameterType="com.dxhy.order.modules.entity.OrderInvoiceItemEntity" resultMap="orderInvoiceItemMap">
        SELECT * FROM order_invoice_item
    </select>


    <select id="selectItemListById" resultMap="orderInvoiceItemMap">
        SELECT * FROM order_invoice_item where order_invoice_id = #{id} and (is_delete = '0' or is_delete is NULL) order by xh
    </select>

    <select id="selectRecordItemListById" resultType="com.dxhy.order.modules.pojo.dto.InvoiceRecordItemInfoDTO">
        SELECT * FROM order_invoice_item where order_invoice_id = #{id} and (is_delete = '0' or is_delete is NULL)
    </select>

    <select id="selectRecordItemListByYsdh" resultType="com.dxhy.order.modules.pojo.dto.InvoiceRecordItemInfoDTO">
        SELECT * FROM order_invoice_item
        <where>
            order_invoice_id in
            <foreach collection="ids" item="id" separator="," open="(" close=")" index="index">
                #{id}
            </foreach>
        </where>
            and (is_delete = '0' or is_delete is NULL)
    </select>

</mapper>