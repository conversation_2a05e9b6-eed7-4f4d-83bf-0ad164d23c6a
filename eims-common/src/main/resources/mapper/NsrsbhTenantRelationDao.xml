<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.NsrsbhTenantRelationDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.NsrsbhTenantRelationEntity" id="baseResultMap">
        <result column="nsrsbh" property="nsrsbh" />
        <result column="tenant_code" property="tenantCode" />
        <result column="db_url" property="dbUrl" />
    </resultMap>


    <select id="listAll" resultMap="baseResultMap">
        select ntr.tenant_code,dt.db_url from nsrsbh_tenant_relation ntr left join db_tenant  dt on ntr.tenant_code = dt.tenant_code
    </select>

</mapper>