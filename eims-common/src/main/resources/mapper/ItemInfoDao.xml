<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.ItemInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.ItemInfoEntity" id="itemInfoEntityMap">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="xmmc" column="xmmc"/>
        <result property="sphssflbm" column="sphssflbm"/>
        <result property="sphssfljc" column="sphssfljc"/>
        <result property="yhzcbs" column="yhzcbs"/>
        <result property="sl" column="sl"/>
        <result property="jm" column="jm"/>
        <result property="hsbs" column="hsbs"/>
        <result property="dj" column="dj"/>
        <result property="dw" column="dw"/>
        <result property="ggxh" column="ggxh"/>
        <result property="isDelete" column="is_delete"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <update id="clearDataByNsrsbh">
        update item_info
            set is_delete = 1
        where base_nsrsbh = #{baseNsrsbh}
            and is_delete = '0'
            and parent_id != '0'
            and parent_id not in (select id from item_group where base_nsrsbh = #{baseNsrsbh})
    </update>

    <select id="selectList" resultMap="itemInfoEntityMap">
        SELECT * FROM item_info
        <where>
            is_delete = '0'
            <if test="itemInfoListDTO.baseNsrsbhList != null and itemInfoListDTO.baseNsrsbhList != ''">
                and base_nsrsbh in
                <foreach collection="itemInfoListDTO.baseNsrsbhList" index="index" item="base_nsrsbh" open="(" separator="," close=")">
                    #{base_nsrsbh}
                </foreach>
            </if>
            <if test="itemInfoListDTO.baseNsrsbhList == null or itemInfoListDTO.baseNsrsbhList == ''">
                and base_nsrsbh = #{itemInfoListDTO.baseNsrsbh}
            </if>
            <if test="itemInfoListDTO.id != null and itemInfoListDTO.id != ''">
                and parent_id = #{itemInfoListDTO.id}
            </if>
            <if test="itemInfoListDTO.xmmc != null and itemInfoListDTO.xmmc != ''">
                <bind name="param_xmmc" value="'%' + itemInfoListDTO.xmmc + '%'"/>
                and xmmc like #{param_xmmc}
            </if>
            <if test="itemInfoListDTO.dj != null and itemInfoListDTO.dj != ''">
                and dj = #{itemInfoListDTO.dj}
            </if>
            <if test="itemInfoListDTO.ggxh != null and itemInfoListDTO.ggxh != ''">
                <bind name="param_ggxh" value="'%' + itemInfoListDTO.ggxh + '%'"/>
                and ggxh like #{param_ggxh}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="listByNameWithoutPage" resultMap="itemInfoEntityMap">
        SELECT * FROM item_info
        <where>
            base_nsrsbh = #{baseNsrsbh}
            <if test="xmmc != null and xmmc != ''">
                <bind name="param_xmmc" value="'%' + xmmc + '%'"/>
                and xmmc like #{param_xmmc}
            </if>
            and is_delete = '0'
        </where>
        order by xmmc
    </select>

    <select id="selectListByItemInfoSaveDTO" parameterType="com.dxhy.order.modules.pojo.dto.ItemInfoSaveDTO" resultMap="itemInfoEntityMap">
        SELECT * FROM item_info
        where base_nsrsbh = #{baseNsrsbh}
            and parent_id = #{parentId}
            and xmmc = #{xmmc}
            and sphssflbm = #{sphssflbm}
            and sphssfljc = #{sphssfljc}
            and sl = #{sl}
            and is_delete = '0'
    </select>

    <select id="selectItemInfoByName" resultType="com.dxhy.order.modules.entity.ItemInfoEntity">
        SELECT * FROM item_info where xmmc = #{itemInfoEntity.xmmc}
        <if test="itemInfoEntity.baseNsrsbh != null and itemInfoEntity.baseNsrsbh != ''">
            and base_nsrsbh = #{itemInfoEntity.baseNsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="itemInfoEntity.sphssflbm != null and itemInfoEntity.sphssflbm != ''">
            and sphssflbm = #{itemInfoEntity.sphssflbm,jdbcType=VARCHAR}
        </if>
        and is_delete = '0'
        limit 1
    </select>

    <select id="listParentNameByIdList" resultType="java.util.Map">
        select t1.id id, t2.xmflmc xmflmc from item_info t1
        left join item_group t2 on t1.parent_id = t2.id
        where t1.id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and t1.is_delete = '0'
    </select>

    <insert id="insertList">
        insert into item_info
        (id,parent_id,base_nsrsbh,xmmc,sphssflbm,
         sphssfljc,yhzcbs,yhzclx,sl,jm,
         hsbs,dj,dw,ggxh,is_delete,
         create_time,create_by)
        values
        <foreach collection="itemInfoEntityList" index="index" item="itemInfoEntity" separator=",">
            (#{itemInfoEntity.id}, #{itemInfoEntity.parentId}, #{itemInfoEntity.baseNsrsbh}, #{itemInfoEntity.xmmc}, #{itemInfoEntity.sphssflbm},
            #{itemInfoEntity.sphssfljc}, #{itemInfoEntity.yhzcbs}, #{itemInfoEntity.yhzclx}, #{itemInfoEntity.sl}, #{itemInfoEntity.jm},
            #{itemInfoEntity.hsbs}, #{itemInfoEntity.dj}, #{itemInfoEntity.dw}, #{itemInfoEntity.ggxh}, #{itemInfoEntity.isDelete},
            #{itemInfoEntity.createTime}, #{itemInfoEntity.createBy})
        </foreach>
    </insert>

</mapper>