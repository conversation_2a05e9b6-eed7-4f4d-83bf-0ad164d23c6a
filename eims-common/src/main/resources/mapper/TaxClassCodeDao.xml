<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.TaxClassCodeDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.TaxClassCodeEntity" id="taxClassCodeEntityMap">
        <id column="id" property="id" />
        <result column="spbm" property="spbm" />
        <result column="spmc" property="spmc" />
        <result column="spjc" property="spjc" />
        <result column="sm" property="sm" />
        <result column="zzssl" property="zzssl" />
        <result column="gjz" property="gjz" />
        <result column="hzx" property="hzx" />
        <result column="kyzt" property="kyzt" />
        <result column="zzstsgl" property="zzstsgl" />
        <result column="zzszcyj" property="zzszcyj" />
        <result column="zzstsnrdm" property="zzstsnrdm" />
        <result column="xfsgl" property="xfsgl" />
        <result column="xfszcyj" property="xfszcyj" />
        <result column="xfstsnrdm" property="xfstsnrdm" />
        <result column="tjjbm" property="tjjbm" />
        <result column="hgjcksppm" property="hgjcksppm" />
        <result column="pid" property="pid" />
        <result column="yhzcmc" property="yhzcmc" />
        <result column="sl" property="sl" />
        <result column="create_time" property="createTime" />
        <result column="bbh" property="bbh" />
        <result column="cpy" property="cpy" />
        <result column="jdc" property="jdc" />
        <result column="enabling_time" property="enablingTime" />
        <result column="update_time" property="updateTime" />
        <result column="mslx" property="mslx" />
    </resultMap>

    <select id="listByName" resultMap="taxClassCodeEntityMap">
        select * from tax_class_code
        <where>
            <if test="spmc != null and spmc != ''">
                <bind name="param_spmc" value="'%' + spmc + '%'"/>
                and spmc like #{param_spmc}
            </if>
        </where>
        order by id
    </select>

    <select id="listById" resultMap="taxClassCodeEntityMap">
        select * from tax_class_code where pid = #{pid} order by id
    </select>
    <select id="getTaxClassCodeSpjc" resultType="com.dxhy.order.modules.entity.TaxClassCodeEntity">
        select * from tax_class_code where spbm = #{spbm}
    </select>

    <select id="getTaxClassCodeXmmc" resultType="com.dxhy.order.modules.entity.TaxClassCodeEntity">
        select * from tax_class_code where spmc = #{spmc} limit 1
    </select>

</mapper>