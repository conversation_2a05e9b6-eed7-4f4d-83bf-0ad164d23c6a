<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.OrderInvoiceConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.OrderInvoiceConfigEntity" id="orderInvoiceConfigMap">
        <result property="id" column="id"/>
        <result property="nsrsbh" column="nsrsbh"/>
        <result property="code" column="code"/>
        <result property="nsrlx" column="nsrlx"/>
        <result property="qykxsl" column="qykxsl"/>
        <result property="gfjfsj" column="gfjfsj"/>
        <result property="gfjhyx" column="gfjhyx"/>
        <result property="kpzdjf" column="kpzdjf"/>
        <result property="jezdkp" column="jezdkp"/>
        <result property="khxxzdbc" column="khxxzdbc"/>
        <result property="hzfpzdkj" column="hzfpzdkj"/>
        <result property="znppssflbm" column="znppssflbm"/>
        <result property="djcjsz" column="djcjsz"/>
        <result property="spmxhsz" column="spmxhsz"/>
        <result property="hbfsBhbtlmx" column="hbfs_bhbtlmx"/>
        <result property="hbfsHbtlmx" column="hbfs_hbtlmx"/>
        <result property="hbfsFshhb" column="hbfs_fshhb"/>
        <result property="qtgzSphpx" column="qtgz_sphpx"/>
        <result property="qtgzFpbzhgz" column="qtgz_fpbzhgz"/>
        <result property="qtgzFpbzhgzFh" column="qtgz_fpbzhgz_fh"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <select id="selectAllList" resultType="com.dxhy.order.modules.entity.OrderInvoiceConfigEntity">
        select *
        from order_invoice_config limit 1
    </select>

    <select id="selectByNsrsbh" resultType="com.dxhy.order.modules.entity.OrderInvoiceConfigEntity">
        select *
        from order_invoice_config
        where nsrsbh = #{nsrsbh} limit 1
    </select>


</mapper>