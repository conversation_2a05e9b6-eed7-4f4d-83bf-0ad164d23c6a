<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.OrderStatusOptRecordDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.OrderStatusOptRecordEntity" id="orderStatusOptRecordMap">
        <result property="id" column="id"/>
        <result property="ddh" column="ddh"/>
        <result property="orderInvoiceId" column="order_invoice_id"/>
        <result property="orderInvoiceFreshId" column="order_invoice_fresh_id"/>
        <result property="orderOptStatus" column="order_opt_status"/>
        <result property="isDelete" column="is_delete"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectList" parameterType="com.dxhy.order.modules.entity.OrderStatusOptRecordEntity" resultMap="orderStatusOptRecordMap">
        SELECT * FROM order_status_opt_record
    </select>

    <select id="selectInfoByFreshId" resultMap="orderStatusOptRecordMap">
        SELECT * FROM order_status_opt_record where order_invoice_fresh_id = #{id}
    </select>

</mapper>