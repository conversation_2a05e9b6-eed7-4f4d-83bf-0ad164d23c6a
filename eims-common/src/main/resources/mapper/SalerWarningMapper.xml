<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.order.modules.dao.SalerWarningDao">

    <resultMap id="BaseResultMap" type="com.dxhy.order.modules.entity.SalerWarningInfo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="xhfNsrsbh" column="xhf_nsrsbh" jdbcType="VARCHAR"/>
            <result property="warnEmails" column="warn_emails" jdbcType="VARCHAR"/>
            <result property="warnPhones" column="warn_phones" jdbcType="VARCHAR"/>
            <result property="warnFlag" column="warn_flag" jdbcType="VARCHAR"/>
            <result property="monthUpper" column="month_upper" jdbcType="VARCHAR"/>
            <result property="quarterUpper" column="quarter_upper" jdbcType="VARCHAR"/>
            <result property="yearUpper" column="year_upper" jdbcType="VARCHAR"/>
            <result property="yearUpper" column="year_upper" jdbcType="VARCHAR"/>
            <result property="yearUpper" column="year_upper" jdbcType="VARCHAR"/>

            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>



    </resultMap>

    <sql id="Base_Column_List">
        id,xhf_nsrsbh,warn_emails,warn_phones,
        warn_flag,month_upper,quarter_upper,year_upper,
        save_info,auto_bz
        create_time,update_time
    </sql>


    <update id="updateWarnInfoById">
        update saler_warning
        set update_Time = #{salerWarningInfo.updateTime,jdbcType=VARCHAR}
        <if test="salerWarningInfo.monthUpper != null and salerWarningInfo.monthUpper != ''" >
            , month_Upper = #{salerWarningInfo.monthUpper,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.quarterUpper != null and salerWarningInfo.quarterUpper != ''" >
            , quarter_Upper = #{salerWarningInfo.quarterUpper,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.yearUpper != null and salerWarningInfo.yearUpper != ''" >
            , year_Upper = #{salerWarningInfo.yearUpper,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.warnPhones != null and salerWarningInfo.warnPhones != ''" >
            , warn_phones = #{salerWarningInfo.warnPhones,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.warnEmails != null and salerWarningInfo.warnEmails != ''" >
            , warn_emails = #{salerWarningInfo.warnEmails,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.saveInfo != null and salerWarningInfo.saveInfo != ''" >
            , save_info = #{salerWarningInfo.saveInfo,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.autoBz != null and salerWarningInfo.autoBz != ''" >
            , auto_bz = #{salerWarningInfo.autoBz,jdbcType=VARCHAR}
        </if>
        <if test="salerWarningInfo.updateTime != null and salerWarningInfo.updateTime != ''" >
            , update_time = #{salerWarningInfo.updateTime,jdbcType=VARCHAR}
        </if>
        where id = #{salerWarningInfo.id,jdbcType=VARCHAR} or xhf_nsrsbh = #{salerWarningInfo.xhfNsrsbh,jdbcType=VARCHAR}
    </update>


    <select id="selectWarnInfoByNsrsbh" resultType="com.dxhy.order.modules.entity.SalerWarningInfo">
        select * from saler_warning where xhf_nsrsbh = #{salerWarningInfo.xhfNsrsbh,jdbcType=VARCHAR}
    </select>

    <select id="selectWarnInfoQy" resultType="com.dxhy.order.modules.entity.SalerWarningInfo">
        select * from saler_warning where warn_flag = '1'
    </select>

</mapper>
