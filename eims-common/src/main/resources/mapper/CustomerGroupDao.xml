<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.CustomerGroupDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.CustomerGroupEntity" id="customerGroupEntityMap">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="khflmc" column="khflmc"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="listByNsrsbh" resultMap="customerGroupEntityMap">
        select * from customer_group where base_nsrsbh = #{baseNsrsbh}
    </select>

    <select id="selectListByCustomerGroupSaveDTO" parameterType="com.dxhy.order.modules.pojo.dto.CustomerGroupSaveDTO" resultMap="customerGroupEntityMap">
        select * from customer_group
        where base_nsrsbh = #{baseNsrsbh}
          and (parent_id = #{parentId} or id = #{parentId})
          and khflmc = #{khflmc}
    </select>

    <select id="listIdByParentId" resultType="java.lang.String">
        select id from customer_group where parent_id = #{parentId}
    </select>

    <select id="listLevelTwoDataByNsrsbh" resultMap="customerGroupEntityMap">
        select * from customer_group
        where base_nsrsbh = #{baseNsrsbh}
        and parent_id = '0'
    </select>

    <insert id="insertList">
        insert into customer_group(id,parent_id,base_nsrsbh,khflmc,create_time,create_by)
        values
        <foreach collection="customerGroupEntityList" index="index" item="customerGroupEntity" separator=",">
            (#{customerGroupEntity.id}, #{customerGroupEntity.parentId}, #{customerGroupEntity.baseNsrsbh},
             #{customerGroupEntity.khflmc}, #{customerGroupEntity.createTime}, #{customerGroupEntity.createBy})
        </foreach>
    </insert>

</mapper>