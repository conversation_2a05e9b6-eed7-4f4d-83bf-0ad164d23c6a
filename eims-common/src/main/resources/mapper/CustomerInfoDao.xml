<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.CustomerInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.CustomerInfoEntity" id="customerInfoEntityMap">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="gsmc" column="gsmc"/>
        <result property="nsrsbh" column="nsrsbh"/>
        <result property="gsdz" column="gsdz"/>
        <result property="gsdh" column="gsdh"/>
        <result property="khyh" column="khyh"/>
        <result property="yhzh" column="yhzh"/>
        <result property="lxr" column="lxr"/>
        <result property="sjhm" column="sjhm"/>
        <result property="email" column="email"/>
        <result property="scode" column="scode"/>
        <result property="intoType" column="into_type"/>
        <result property="isDelete" column="is_delete"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd6" column="byzd6"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectList" resultMap="customerInfoEntityMap">
        SELECT * FROM customer_info
        <where>
            parent_id = #{customerInfoListDTO.id}
            <if test="customerInfoListDTO.baseNsrsbh != null and customerInfoListDTO.baseNsrsbh != ''">
                and base_nsrsbh = #{customerInfoListDTO.baseNsrsbh}
            </if>
            <if test="customerInfoListDTO.gsmc != null and customerInfoListDTO.gsmc != ''">
                <bind name="param_gsmc" value="'%' + customerInfoListDTO.gsmc + '%'"/>
                and gsmc like #{param_gsmc}
            </if>
            <if test="customerInfoListDTO.lxr != null and customerInfoListDTO.lxr != ''">
                <bind name="param_lxr" value="'%' + customerInfoListDTO.lxr + '%'"/>
                and lxr like #{param_lxr}
            </if>
            <if test="customerInfoListDTO.sjhm != null and customerInfoListDTO.sjhm != ''">
                <bind name="param_sjhm" value="'%' + customerInfoListDTO.sjhm + '%'"/>
                and sjhm like #{param_sjhm}
            </if>
            <if test="customerInfoListDTO.email != null and customerInfoListDTO.email != ''">
                <bind name="param_email" value="'%' + customerInfoListDTO.email + '%'"/>
                and email like #{param_email}
            </if>
            <if test="customerInfoListDTO.scode != null and customerInfoListDTO.scode != ''">
                <bind name="param_scode" value="'%' + customerInfoListDTO.scode + '%'"/>
                and scode like #{param_scode}
            </if>
            <if test="customerInfoListDTO.timeStart != null and customerInfoListDTO.timeStart != ''">
                and create_time &gt; #{customerInfoListDTO.timeStart}
            </if>
            <if test="customerInfoListDTO.timeEnd != null and customerInfoListDTO.timeEnd != ''">
                and create_time &lt; #{customerInfoListDTO.timeEnd}
            </if>
            and is_delete = '0'
        </where>
        order by create_time desc
    </select>

    <select id="listWithoutId" resultMap="customerInfoEntityMap">
        SELECT * FROM customer_info
        <where>
            into_type = #{customerInfoListWithoutIdDTO.intoType}
            <if test="customerInfoListWithoutIdDTO.baseNsrsbhList != null and customerInfoListWithoutIdDTO.baseNsrsbhList != ''">
                and base_nsrsbh in
                 <foreach collection="customerInfoListWithoutIdDTO.baseNsrsbhList" index="index" item="base_nsrsbh" open="(" separator="," close=")">
                    #{base_nsrsbh}
                </foreach>
            </if>
            <if test="customerInfoListWithoutIdDTO.baseNsrsbhList == null or customerInfoListWithoutIdDTO.baseNsrsbhList == ''">
                and base_nsrsbh = #{customerInfoListWithoutIdDTO.baseNsrsbh}
            </if>
            <if test="customerInfoListWithoutIdDTO.gsmc != null and customerInfoListWithoutIdDTO.gsmc != ''">
                <bind name="param_gsmc" value="'%' + customerInfoListWithoutIdDTO.gsmc + '%'"/>
                and gsmc like #{param_gsmc}
            </if>
            <if test="customerInfoListWithoutIdDTO.nsrsbh != null and customerInfoListWithoutIdDTO.nsrsbh != ''">
                <bind name="param_nsrsbh" value="'%' + customerInfoListWithoutIdDTO.nsrsbh + '%'"/>
                and nsrsbh like #{param_nsrsbh}
            </if>
            <if test="customerInfoListWithoutIdDTO.scode != null and customerInfoListWithoutIdDTO.scode != ''">
                <bind name="param_scode" value="'%' + customerInfoListWithoutIdDTO.scode + '%'"/>
                and scode like #{param_scode}
            </if>
            and is_delete = '0'
        </where>
        order by create_time desc
    </select>

    <select id="listByNameWithoutPage" parameterType="com.dxhy.order.modules.pojo.dto.CustomerInfoListByNameWithoutPageDTO" resultMap="customerInfoEntityMap">
        SELECT * FROM customer_info
        <where>
            base_nsrsbh = #{baseNsrsbh}
            <if test="gsmc != null and gsmc != ''">
                <bind name="param_gsmc" value="'%' + gsmc + '%'"/>
                and gsmc like #{param_gsmc}
            </if>
            and is_delete = '0'
        </where>
        order by gsmc
    </select>

    <select id="selectListByCustomerInfoSaveDTO" parameterType="com.dxhy.order.modules.pojo.dto.CustomerInfoSaveDTO" resultMap="customerInfoEntityMap">
        SELECT * FROM customer_info
        where base_nsrsbh = #{baseNsrsbh}
            and parent_id = #{parentId}
            and gsmc = #{gsmc}
            and nsrsbh = #{nsrsbh}
            and is_delete = '0'
    </select>


    <select id="selectGhfmcList" resultType="java.lang.String">
        select DISTINCT gsmc from customer_info
        where is_delete = 0
        <if test="baseNsrsbh != null and baseNsrsbh != ''">
            and base_nsrsbh = #{baseNsrsbh,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectGhfmcListByGhfsbh" resultType="java.lang.String">
        select DISTINCT gsmc from customer_info
        where is_delete = 0
        <if test="nsrsbh != null and nsrsbh != ''">
            and nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="baseNsrsbh != null and baseNsrsbh != ''">
            and base_nsrsbh = #{baseNsrsbh,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insertList">
        insert into customer_info
            (id,parent_id,base_nsrsbh,gsmc,nsrsbh,
             gsdz,gsdh,khyh,yhzh,lxr,
             sjhm,email,scode,into_type,is_delete,
             create_time,create_by)
        values
        <foreach collection="customerInfoEntityList" index="index" item="customerInfoEntity" separator=",">
            (#{customerInfoEntity.id}, #{customerInfoEntity.parentId}, #{customerInfoEntity.baseNsrsbh}, #{customerInfoEntity.gsmc}, #{customerInfoEntity.nsrsbh},
            #{customerInfoEntity.gsdz}, #{customerInfoEntity.gsdh}, #{customerInfoEntity.khyh}, #{customerInfoEntity.yhzh}, #{customerInfoEntity.lxr},
            #{customerInfoEntity.sjhm}, #{customerInfoEntity.email}, #{customerInfoEntity.scode}, #{customerInfoEntity.intoType}, #{customerInfoEntity.isDelete},
            #{customerInfoEntity.createTime}, #{customerInfoEntity.createBy})
        </foreach>
    </insert>

    <select id="listParentNameByIdList" resultType="java.util.Map">
        select t1.id id, t2.khflmc khflmc from customer_info t1
        left join customer_group t2 on t1.parent_id = t2.id
        where t1.id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="clearDataByNsrsbh">
        update customer_info
        set is_delete = 1
        where base_nsrsbh = #{baseNsrsbh}
          and is_delete = '0'
          and parent_id != '0'
          and parent_id not in (select id from customer_group where base_nsrsbh = #{baseNsrsbh})
    </update>

</mapper>