<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.order.modules.dao.InvoiceConfigDao">

    <resultMap id="BaseResultMap" type="com.dxhy.order.modules.entity.InvoiceConfigInfo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="xhfNsrsbh" jdbcType="VARCHAR"/>
            <result property="dz" column="dz" jdbcType="VARCHAR"/>
            <result property="dh" column="dh" jdbcType="VARCHAR"/>
            <result property="khyh" column="khyh" jdbcType="VARCHAR"/>
            <result property="yhzh" column="yhzh" jdbcType="VARCHAR"/>
            <result property="kpr" column="kpr" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,qymc,nsrsbh,dz,
        dh,khyh,yhzh,
        kpr,create_time,update_time
    </sql>

    <update id="updateZhlxByNsrsbh">
        update invoice_config set zhlx = '1' where zhlx = '0' and nsrsbh = #{invoiceConfigInfo.nsrsbh,jdbcType=VARCHAR}
    </update>

    <select id="selectList" resultType="com.dxhy.order.modules.entity.InvoiceConfigInfo">
        select * from invoice_config where nsrsbh = #{invoiceConfigInfo.nsrsbh,jdbcType=VARCHAR} order by zhlx asc
    </select>

    <select id="countForPhone" resultType="java.lang.Integer">
        select count(1) from invoice_config where dh = #{dh,jdbcType=VARCHAR}
    </select>

    <select id="checkHadConfigByNsrsbh" resultType="com.dxhy.order.modules.entity.InvoiceConfigInfo">
        select * from invoice_config where zhlx = '0'
        and nsrsbh = #{invoiceConfigInfo.nsrsbh,jdbcType=VARCHAR}
    </select>


</mapper>
