<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.OrderInvoiceInfoDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity" id="orderInvoiceInfoMap">
        <result property="id" column="id"/>
        <result property="pch" column="pch"/>
        <result property="fpqqlsh" column="fpqqlsh"/>
        <result property="ddh" column="ddh"/>
        <result property="ghfMc" column="ghf_mc"/>
        <result property="ghfNsrsbh" column="ghf_nsrsbh"/>
        <result property="ghfQylx" column="ghf_qylx"/>
        <result property="ghfSf" column="ghf_sf"/>
        <result property="ghfId" column="ghf_id"/>
        <result property="ghfDz" column="ghf_dz"/>
        <result property="ghfDh" column="ghf_dh"/>
        <result property="ghfYh" column="ghf_yh"/>
        <result property="ghfZh" column="ghf_zh"/>
        <result property="ghfSj" column="ghf_sj"/>
        <result property="ghfYx" column="ghf_yx"/>
        <result property="xhfMc" column="xhf_mc"/>
        <result property="xhfNsrsbh" column="xhf_nsrsbh"/>
        <result property="xhfDz" column="xhf_dz"/>
        <result property="xhfDh" column="xhf_dh"/>
        <result property="xhfYh" column="xhf_yh"/>
        <result property="xhfZh" column="xhf_zh"/>
        <result property="tdyw" column="tdyw"/>
        <result property="businessType" column="business_type"/>
        <result property="kplx" column="kplx"/>
        <result property="jshj" column="jshj"/>
        <result property="hjbhsje" column="hjbhsje"/>
        <result property="kpse" column="kpse"/>
        <result property="kpzt" column="kpzt"/>
        <result property="kprq" column="kprq"/>
        <result property="ddscrq" column="ddscrq"/>
        <result property="ddly" column="ddly"/>
        <result property="bz" column="bz"/>
        <result property="ddzt" column="ddzt"/>
        <result property="qdfphm" column="qdfphm"/>
        <result property="fpdm" column="fpdm"/>
        <result property="fphm" column="fphm"/>
        <result property="fpzlDm" column="fpzl_dm"/>
        <result property="jym" column="jym"/>
        <result property="kpr" column="kpr"/>
        <result property="fhr" column="fhr"/>
        <result property="skr" column="skr"/>
        <result property="gmfjbrxm" column="gmfjbrxm"/>
        <result property="jbrzjlx" column="jbrzjlx"/>
        <result property="jbrzjhm" column="jbrzjhm"/>
        <result property="fwm" column="fwm"/>
        <result property="ewm" column="ewm"/>
        <result property="jqbh" column="jqbh"/>
        <result property="ofdUrl" column="ofd_url"/>
        <result property="pdfUrl" column="pdf_url"/>
        <result property="sfcf" column="sfcf"/>
        <result property="chBz" column="ch_bz"/>
        <result property="sykchje" column="sykchje"/>
        <result property="sykchbhsje" column="sykchbhsje"/>
        <result property="sykchse" column="sykchse"/>
        <result property="chsj" column="chsj"/>
        <result property="chyy" column="chyy"/>
        <result property="zfBz" column="zf_bz"/>
        <result property="zfsj" column="zfsj"/>
        <result property="zfyy" column="zfyy"/>
        <result property="sbyy" column="sbyy"/>
        <result property="sldMc" column="sld_mc"/>
        <result property="sld" column="sld"/>
        <result property="fjh" column="fjh"/>
        <result property="qdBz" column="qd_bz"/>
        <result property="dyzt" column="dyzt"/>
        <result property="zzsytzt" column="zzsytzt"/>
        <result property="xfsytzt" column="xfsytzt"/>
        <result property="rzzt" column="rzzt"/>
        <result property="pushStatus" column="push_status"/>
        <result property="emailPushStatus" column="email_push_status"/>
        <result property="shortMsgPushStatus" column="short_msg_push_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="mongodbId" column="mongodb_id"/>
        <result property="uploadState" column="upload_state"/>
        <result property="hsbz" column="hsbz"/>
        <result property="cpy" column="cpy"/>
        <result property="gmflx" column="gmflx"/>
        <result property="yfpdm" column="yfpdm"/>
        <result property="yfphm" column="yfphm"/>
        <result property="yqdfphm" column="yqdfphm"/>
        <result property="qdxmmc" column="qdxmmc"/>
        <result property="hzxxbbh" column="hzxxbbh"/>
        <result property="gjbq" column="gjbq"/>
        <result property="kpfs" column="kpfs"/>
        <result property="fply" column="fply"/>
        <result property="fpzt" column="fpzt"/>
        <result property="sflzfp" column="sflzfp"/>
        <result property="kpfnsrsbh" column="kpfnsrsbh"/>
        <result property="fppzdm" column="fppzdm"/>
        <result property="cezslxdm" column="cezslxdm"/>
        <result property="kce" column="kce"/>
        <result property="kprqStr" column="kprq_str"/>
        <result property="hzqrxxztDm" column="hzqrxxzt_dm"/>
        <result property="spfzrrbs" column="spfzrrbs"/>
        <result property="ysdh" column="ysdh"/>
        <result property="yysdh" column="yysdh"/>
        <result property="yddh" column="yddh"/>
        <result property="ghfSj" column="gmfsjh"/>
        <result property="ghfYx" column="gmfyx"/>
        <result property="jbrgj" column="jbrgj"/>
        <result property="jbrzrrnsrsbh" column="jbrzrrnsrsbh"/>

        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="byzd5" column="byzd5"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <resultMap type="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity" id="orderInvoiceInfoMap1">
        <result property="id" column="id"/>
        <result property="pch" column="pch"/>
        <result property="fpqqlsh" column="fpqqlsh"/>
        <result property="ddh" column="ddh"/>
        <result property="ghfMc" column="ghf_mc"/>
        <result property="ghfNsrsbh" column="ghf_nsrsbh"/>
        <result property="ghfQylx" column="ghf_qylx"/>
        <result property="ghfSf" column="ghf_sf"/>
        <result property="ghfId" column="ghf_id"/>
        <result property="ghfDz" column="ghf_dz"/>
        <result property="ghfDh" column="ghf_dh"/>
        <result property="ghfYh" column="ghf_yh"/>
        <result property="ghfZh" column="ghf_zh"/>
        <result property="ghfSj" column="ghf_sj"/>
        <result property="ghfYx" column="ghf_yx"/>
        <result property="xhfMc" column="xhf_mc"/>
        <result property="xhfNsrsbh" column="xhf_nsrsbh"/>
        <result property="xhfDz" column="xhf_dz"/>
        <result property="xhfDh" column="xhf_dh"/>
        <result property="xhfYh" column="xhf_yh"/>
        <result property="xhfZh" column="xhf_zh"/>
        <result property="tdyw" column="tdyw"/>
        <result property="businessType" column="business_type"/>
        <result property="kplx" column="kplx"/>
        <result property="jshj" column="jshj"/>
        <result property="hjbhsje" column="hjbhsje"/>
        <result property="kpse" column="kpse"/>
        <result property="kpzt" column="kpzt"/>
        <result property="kprq" column="kprq"/>
        <result property="ddscrq" column="ddscrq"/>
        <result property="ddly" column="ddly"/>
        <result property="bz" column="bz"/>
        <result property="ddzt" column="zt"/>
        <result property="qdfphm" column="qdfphm"/>
        <result property="fpdm" column="fpdm"/>
        <result property="fphm" column="fphm"/>
        <result property="fpzlDm" column="fpzl_dm"/>
        <result property="jym" column="jym"/>
        <result property="kpr" column="kpr"/>
        <result property="fhr" column="fhr"/>
        <result property="skr" column="skr"/>
        <result property="gmfjbrxm" column="gmfjbrxm"/>
        <result property="jbrzjlx" column="jbrzjlx"/>
        <result property="jbrzjhm" column="jbrzjhm"/>
        <result property="fwm" column="fwm"/>
        <result property="ewm" column="ewm"/>
        <result property="jqbh" column="jqbh"/>
        <result property="ofdUrl" column="ofd_url"/>
        <result property="pdfUrl" column="pdf_url"/>
        <result property="sfcf" column="sfcf"/>
        <result property="chBz" column="ch_bz"/>
        <result property="sykchje" column="sykchje"/>
        <result property="sykchbhsje" column="sykchbhsje"/>
        <result property="sykchse" column="sykchse"/>
        <result property="chsj" column="chsj"/>
        <result property="chyy" column="chyy"/>
        <result property="zfBz" column="zf_bz"/>
        <result property="zfsj" column="zfsj"/>
        <result property="zfyy" column="zfyy"/>
        <result property="sbyy" column="sbyy"/>
        <result property="sldMc" column="sld_mc"/>
        <result property="sld" column="sld"/>
        <result property="fjh" column="fjh"/>
        <result property="qdBz" column="qd_bz"/>
        <result property="dyzt" column="dyzt"/>
        <result property="zzsytzt" column="zzsytzt"/>
        <result property="xfsytzt" column="xfsytzt"/>
        <result property="rzzt" column="rzzt"/>
        <result property="pushStatus" column="push_status"/>
        <result property="emailPushStatus" column="email_push_status"/>
        <result property="shortMsgPushStatus" column="short_msg_push_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="mongodbId" column="mongodb_id"/>
        <result property="uploadState" column="upload_state"/>
        <result property="hsbz" column="hsbz"/>
        <result property="cpy" column="cpy"/>
        <result property="gmflx" column="gmflx"/>
        <result property="yfpdm" column="yfpdm"/>
        <result property="yfphm" column="yfphm"/>
        <result property="yqdfphm" column="yqdfphm"/>
        <result property="qdxmmc" column="qdxmmc"/>
        <result property="hzxxbbh" column="hzxxbbh"/>
        <result property="gjbq" column="gjbq"/>
        <result property="kpfs" column="kpfs"/>
        <result property="fply" column="fply"/>
        <result property="fpzt" column="fpzt"/>
        <result property="sflzfp" column="sflzfp"/>
        <result property="kpfnsrsbh" column="kpfnsrsbh"/>
        <result property="fppzdm" column="fppzdm"/>
        <result property="cezslxdm" column="cezslxdm"/>
        <result property="kce" column="kce"/>
        <result property="kprqStr" column="kprq_str"/>
        <result property="hzqrxxztDm" column="hzqrxxzt_dm"/>
        <result property="spfzrrbs" column="spfzrrbs"/>
        <result property="ysdh" column="ysdh"/>
        <result property="yysdh" column="yysdh"/>
        <result property="yddh" column="yddh"/>
        <result property="ghfSj" column="gmfsjh"/>
        <result property="ghfYx" column="gmfyx"/>
        <result property="jbrgj" column="jbrgj"/>
        <result property="jbrzrrnsrsbh" column="jbrzrrnsrsbh"/>

        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>



    <select id="selectListByOrderInvoiceInfoEntity"
            parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info oii
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                oii.ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.qdfphm != null and orderInvoiceInfoEntity.qdfphm != ''">
                AND oii.qdfphm = #{orderInvoiceInfoEntity.qdfphm}
            </if>
            <if test="orderInvoiceInfoEntity.xhfNsrsbh != null and orderInvoiceInfoEntity.xhfNsrsbh != ''">
                AND oii.xhf_nsrsbh = #{orderInvoiceInfoEntity.xhfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND oii.ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND oii.ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND oii.fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND oii.ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(oii.create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(oii.create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND oii.jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND oii.jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            and oii.is_delete = '0'
            and oii.kplx = '0'
            and oii.kpzt = '2'
            and oii.ch_bz in ('0','3','4','5','6')
            and oii.qdfphm is not null
            and oii.qdfphm != ''
            and not exists (select 1 from invoice_cezs ce where ce.fphm = oii.qdfphm)
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectAllList" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                And kpzt = #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.ddzt != null and orderInvoiceInfoEntity.ddzt != ''">
                AND ddzt= #{orderInvoiceInfoEntity.ddzt}
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND ysdh= #{orderInvoiceInfoEntity.ysdh}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            and is_delete = "0" and kpzt = "0"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectAllListByDuplicate" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultType="java.lang.String">
        SELECT DISTINCT ysdh FROM order_invoice_info
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                And kpzt = #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.ddzt != null and orderInvoiceInfoEntity.ddzt != ''">
                AND ddzt= #{orderInvoiceInfoEntity.ddzt}
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND ysdh= #{orderInvoiceInfoEntity.ysdh}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            and is_delete = "0" and kpzt = "0"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectKpztIsDkList" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                And kpzt = #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.ddzt != null and orderInvoiceInfoEntity.ddzt != ''">
                AND ddzt= #{orderInvoiceInfoEntity.ddzt}
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND ysdh= #{orderInvoiceInfoEntity.ysdh}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            and is_delete = "0" and kpzt = "0"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="manualInvoiceInfo" resultType="java.lang.String">
        SELECT qdfphm FROM order_invoice_info
        <where>
            is_delete = "0" and ddly = "4" and sflzfp = 'Y' and gjbq = '1' AND ch_bz = "0"
        </where>
        ORDER BY create_time DESC
    </select>

    <update id="deleteByInvoiceId" parameterType="java.lang.String">
        update order_invoice_info
        set is_delete = "1"
        where id = #{id}
    </update>

    <update id="updateInvoiceStatusById" parameterType="java.lang.String">
        update order_invoice_info
        set kpzt = "0"
        where id = #{id}
    </update>

    <select id="selectInvoiceRecordList" parameterType="com.dxhy.order.modules.entity.InvoiceRecordQueryList"
            resultType="com.dxhy.order.modules.entity.InvoiceRecordInfoEntity">
        select t.id, t.fpzldm, t.ghfmc, t.qdfphm, t.kpje, t.kpse, t.kplx, t.downLoadFlag, t.kpzt,
        DATE_FORMAT(t.kprq,'%Y-%m-%d %H:%i:%s') kprq, t.kpr,
        t.updateTime, t.pdfUrl, t.ofdUrl, t.jshj, t.xhfMc, t.xhfNsrsbh, t.ddly
        from (
        SELECT oi.id id, oi.fpzl_dm fpzldm, oi.ghf_mc ghfmc, oi.qdfphm,oi.hjbhsje kpje, oi.kpse, oi.kplx,
        oi.kprq kprq, oi.kpr,
        (case when oi.ofd_url is null then 'false' else 'true' end) downLoadFlag, oi.kpzt kpzt,
        oi.update_time updateTime, oi.pdf_url pdfUrl, oi.ofd_url ofdUrl, oi.jshj jshj, oi.xhf_nsrsbh xhfNsrsbh,
        oi.xhf_mc xhfMc,
        case when oi.ddly = 4 then 0 when instr('0,1,2,3', oi.ddly) then 1 else oi.ddly end ddly
        FROM order_invoice_info oi
        LEFT JOIN order_invoice_item oit on oi.id = oit.order_invoice_id
        where oi.is_delete = '0' and oi.kpzt = '2' and (oi.kprq is not null or oi.kprq_str IS NOT NULL)
        <if test="invoiceRecordQueryList.ghfmc != null and invoiceRecordQueryList.ghfmc != ''">
            and oi.ghf_mc = #{invoiceRecordQueryList.ghfmc,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.kjjg != null and invoiceRecordQueryList.kjjg != ''">
            and oi.kpzt = #{invoiceRecordQueryList.kjjg,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.kpzt != null and invoiceRecordQueryList.kpzt != ''">
            and oi.kpzt = #{invoiceRecordQueryList.kpzt,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.ghfNsrsbh != null and invoiceRecordQueryList.ghfNsrsbh != ''">
            and oi.ghf_nsrsbh = #{invoiceRecordQueryList.ghfNsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.ksrq != null and invoiceRecordQueryList.ksrq != ''">
            and oi.kprq &gt;= #{invoiceRecordQueryList.ksrq}
        </if>
        <if test="invoiceRecordQueryList.jsrq != null and invoiceRecordQueryList.jsrq != ''">
            and #{invoiceRecordQueryList.jsrq} &gt;= oi.kprq
        </if>
        <if test="invoiceRecordQueryList.qdfphm != null and invoiceRecordQueryList.qdfphm != ''">
            and oi.qdfphm = #{invoiceRecordQueryList.qdfphm,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.xmmc != null and invoiceRecordQueryList.xmmc != ''">
            and oit.xmmc like concat(concat('%',#{invoiceRecordQueryList.xmmc,jdbcType=VARCHAR}),'%')
        </if>
        <if test="invoiceRecordQueryList.fpzldm != null and invoiceRecordQueryList.fpzldm != ''">
            and oi.fpzl_dm = #{invoiceRecordQueryList.fpzldm,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.kpr != null and invoiceRecordQueryList.kpr != ''">
            and oi.kpr = #{invoiceRecordQueryList.kpr,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.baseNsrsbh != null and invoiceRecordQueryList.baseNsrsbh != ''">
            and (oi.xhf_nsrsbh = #{invoiceRecordQueryList.baseNsrsbh,jdbcType=VARCHAR} OR
            oi.ghf_nsrsbh = #{invoiceRecordQueryList.baseNsrsbh,jdbcType=VARCHAR})
        </if>
        <if test="invoiceRecordQueryList.ddly != null and invoiceRecordQueryList.ddly != ''">
            and instr(#{invoiceRecordQueryList.ddly,jdbcType=VARCHAR}, oi.ddly ) > 0
        </if>

        ) t
        group by t.id
        order by t.kprq desc
    </select>

    <select id="selectInvoiceRecordAllList" parameterType="com.dxhy.order.modules.entity.InvoiceRecordQueryList"
            resultType="com.dxhy.order.modules.entity.InvoiceRecordInfoEntity">
        select t.id, t.fpzldm, t.ghfmc, t.qdfphm, t.kpje, t.kpse, t.kplx, t.downLoadFlag, t.kpzt,
        DATE_FORMAT(t.kprq,'%Y-%m-%d %H:%i:%s') kprq, t.kpr,
        t.updateTime, t.pdfUrl, t.ofdUrl, t.jshj, t.xhfMc, t.xhfNsrsbh, t.ddly
        from (
        SELECT oi.id id, oi.fpzl_dm fpzldm, oi.ghf_mc ghfmc, oi.qdfphm,oi.hjbhsje kpje, oi.kpse, oi.kplx,
        oi.kprq kprq, oi.kpr,
        (case when oi.ofd_url is null then 'false' else 'true' end) downLoadFlag, oi.kpzt kpzt,
        oi.update_time updateTime, oi.pdf_url pdfUrl, oi.ofd_url ofdUrl, oi.jshj jshj, oi.xhf_nsrsbh xhfNsrsbh,
        oi.xhf_mc xhfMc,
        case when oi.ddly = 4 then 0 when instr('0,1,2,3', oi.ddly) then 1 else oi.ddly end ddly
        FROM order_invoice_info oi
        LEFT JOIN order_invoice_item oit on oi.id = oit.order_invoice_id
        where oi.is_delete = '0' and oi.kpzt = '2' and (oi.kprq is not null or oi.kprq_str IS NOT NULL)
        <if test="invoiceRecordQueryList.ghfmc != null and invoiceRecordQueryList.ghfmc != ''">
            and oi.ghf_mc = #{invoiceRecordQueryList.ghfmc,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.kjjg != null and invoiceRecordQueryList.kjjg != ''">
            and oi.kpzt = #{invoiceRecordQueryList.kjjg,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.kpzt != null and invoiceRecordQueryList.kpzt != ''">
            and oi.kpzt = #{invoiceRecordQueryList.kpzt,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.ghfNsrsbh != null and invoiceRecordQueryList.ghfNsrsbh != ''">
            and oi.ghf_nsrsbh = #{invoiceRecordQueryList.ghfNsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.ksrq != null and invoiceRecordQueryList.ksrq != ''">
            and oi.kprq &gt;= #{invoiceRecordQueryList.ksrq}
        </if>
        <if test="invoiceRecordQueryList.jsrq != null and invoiceRecordQueryList.jsrq != ''">
            and #{invoiceRecordQueryList.jsrq} &gt;= oi.kprq
        </if>
        <if test="invoiceRecordQueryList.qdfphm != null and invoiceRecordQueryList.qdfphm != ''">
            and oi.qdfphm = #{invoiceRecordQueryList.qdfphm,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.xmmc != null and invoiceRecordQueryList.xmmc != ''">
            and oit.xmmc like concat(concat('%',#{invoiceRecordQueryList.xmmc,jdbcType=VARCHAR}),'%')
        </if>
        <if test="invoiceRecordQueryList.fpzldm != null and invoiceRecordQueryList.fpzldm != ''">
            and oi.fpzl_dm = #{invoiceRecordQueryList.fpzldm,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.kpr != null and invoiceRecordQueryList.kpr != ''">
            and oi.kpr = #{invoiceRecordQueryList.kpr,jdbcType=VARCHAR}
        </if>
        <if test="invoiceRecordQueryList.baseNsrsbh != null and invoiceRecordQueryList.baseNsrsbh != ''">
            and (oi.xhf_nsrsbh = #{invoiceRecordQueryList.baseNsrsbh,jdbcType=VARCHAR} OR
            oi.ghf_nsrsbh = #{invoiceRecordQueryList.baseNsrsbh,jdbcType=VARCHAR})
        </if>
        <if test="invoiceRecordQueryList.ddly != null and invoiceRecordQueryList.ddly != ''">
            and instr(#{invoiceRecordQueryList.ddly,jdbcType=VARCHAR}, oi.ddly ) > 0
        </if>

        ) t
        group by t.id
        order by t.kprq desc
    </select>

    <select id="selectMonthDataYkzs" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(kprq,'%Y%m%d') as dayTime,count(1) dayNum
        from order_invoice_info oi
        where kplx = '0' and kpzt = '2' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="month != null and month != ''">
            and date_format(oi.kprq,'%Y-%m') = #{month,jdbcType=VARCHAR}
        </if>
        group by dayTime
    </select>


    <select id="selectMonthDataYkje" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(kprq,'%Y%m%d') as dayTime,CONVERT(sum(jshj), CHAR) dayNum
        from order_invoice_info oi
        where oi.kpzt = '2' and oi.kplx = '0' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="month != null and month != ''">
            and date_format(oi.kprq,'%Y-%m') = #{month,jdbcType=VARCHAR}
        </if>
        group by dayTime
    </select>


    <select id="selectMonthDataHcje" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(chsj,'%Y%m%d') as dayTime,replace(CONVERT(sum(jshj), CHAR), '-', '') dayNum
        from order_invoice_info oi
        where kplx = '0' and ch_bz = '1' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="month != null and month != ''">
            and date_format(oi.chsj,'%Y-%m') = #{month,jdbcType=VARCHAR}
        </if>
        group by dayTime
    </select>


    <select id="selectMonthDataHczs" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(chsj,'%Y%m%d') as dayTime,count(1) dayNum
        from order_invoice_info oi
        where kplx = '0' and ch_bz = '1' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="month != null and month != ''">
            and date_format(oi.chsj,'%Y-%m') = #{month,jdbcType=VARCHAR}
        </if>
        group by dayTime
    </select>


    <select id="selectTotalDataLjkjje" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(kprq,'%Y%m') as monthTime,CONVERT(sum(jshj) , CHAR) monthNum
        from order_invoice_info oi
        where oi.kpzt = '2' and oi.kplx = '0' and ch_bz != '1' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="year != null and year != ''">
            and date_format(oi.kprq,'%Y') = #{year,jdbcType=VARCHAR}
        </if>
        group by monthTime
    </select>


    <select id="selectTotalDataLjkjse" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(kprq,'%Y%m') as monthTime,CONVERT(sum(kpse), CHAR) monthNum
        from order_invoice_info oi
        where oi.kpzt = '2' and oi.kplx = '0' and ch_bz != '1' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="year != null and year != ''">
            and date_format(oi.kprq,'%Y') = #{year,jdbcType=VARCHAR}
        </if>
        group by monthTime
    </select>


    <select id="selectTotalDatalLjksl" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(kprq,'%Y%m') as monthTime,sum(case when oi.ch_bz = '1' then 2 else 1 end) monthNum
        from order_invoice_info oi
        where oi.kpzt = '2' and kplx = '0' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="year != null and year != ''">
            and date_format(oi.kprq,'%Y') = #{year,jdbcType=VARCHAR}
        </if>
        group by monthTime
    </select>


    <select id="selectInvoicedList" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        <if test="orderInvoiceInfoEntity.kpzt == '0'.toString()">
            select id, fpzl_dm, kplx, ghf_mc, ddscrq, qdfphm
            from order_invoice_info
            where is_delete = '0'
            <if test="orderInvoiceInfoEntity.xhfNsrsbh != null and orderInvoiceInfoEntity.xhfNsrsbh != ''">
                AND xhf_nsrsbh= #{orderInvoiceInfoEntity.xhfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                AND instr(#{orderInvoiceInfoEntity.kpzt}, kpzt ) > 0
            </if>
            <if test="month != null and month != ''">
                and date_format(ddscrq, '%Y-%m') = #{month,jdbcType=VARCHAR}
                order by ddscrq desc
            </if>
        </if>

        <if test="orderInvoiceInfoEntity.kpzt == '2'.toString()">
            select id, fpzl_dm, kplx, ghf_mc, qdfphm, fpdm, fphm, date_format(kprq, '%Y-%m-%d') kprq
            from order_invoice_info
            where is_delete = '0'
            <if test="orderInvoiceInfoEntity.xhfNsrsbh != null and orderInvoiceInfoEntity.xhfNsrsbh != ''">
                AND xhf_nsrsbh= #{orderInvoiceInfoEntity.xhfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                AND instr(#{orderInvoiceInfoEntity.kpzt}, kpzt ) > 0
            </if>
            <if test="month != null and month != ''">
                and date_format(kprq, '%Y-%m') = #{month,jdbcType=VARCHAR}
                order by kprq desc
            </if>
        </if>

        <if test="orderInvoiceInfoEntity.kpzt == '1,3'.toString()">
            select * from (
            select id, fpzl_dm, kplx, ghf_mc,
            (case when kpzt = '1' then ddscrq else kprq end) ddscrq,
            qdfphm, fpdm, fphm, kpzt,
            case when kpzt = 3 then sbyy else '' end sbyy
            from order_invoice_info
            where is_delete = '0'
            <if test="orderInvoiceInfoEntity.xhfNsrsbh != null and orderInvoiceInfoEntity.xhfNsrsbh != ''">
                AND xhf_nsrsbh= #{orderInvoiceInfoEntity.xhfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                AND instr(#{orderInvoiceInfoEntity.kpzt}, kpzt ) > 0
            </if>
            ) t where date_format(t.ddscrq, '%Y-%m') = #{month,jdbcType=VARCHAR} order by ddscrq desc

        </if>
    </select>


    <select id="selectXMStaisticList" resultType="com.dxhy.order.modules.entity.DataStaticsQueryResultList">

        select oi.fpzl_dm fpzldm,
        case when instr(oii.sl, '%' ) > 0 then replace(oii.sl, '%', '')/100 else oii.sl end sl,
        oii.je je,
        oii.se se,
        oii.hsbz,
        oi.kplx,
        oi.kpzt,
        oi.ch_bz
        from order_invoice_info oi LEFT JOIN order_invoice_item oii on oi.id = oii.order_invoice_id
        where oi.is_delete = '0'
        and oii.sl != ''
        and oi.kplx = '0' and (oi.kpzt = '2' or oi.ch_bz = '1')
        <if test="baseNsrsbh != null and baseNsrsbh != ''">
            and oi.xhf_nsrsbh = #{baseNsrsbh, jdbcType=VARCHAR}
        </if>
        <if test="fpzldm != null and fpzldm != ''">
            and oi.fpzl_dm = #{fpzldm, jdbcType=VARCHAR}
        </if>
        <if test="beginTime != null">
            and (date_format(oi.kprq,'%Y-%m-%d') &gt;= #{beginTime,jdbcType=TIMESTAMP} or
            date_format(oi.chsj,'%Y-%m-%d') &gt;= #{beginTime,jdbcType=TIMESTAMP})
        </if>
        <if test="endTime != null">
            and (date_format(oi.kprq,'%Y-%m-%d') &lt;= #{endTime,jdbcType=TIMESTAMP} or date_format(oi.chsj,'%Y-%m-%d')
            &lt;= #{endTime,jdbcType=TIMESTAMP})
        </if>
        order by sl asc

    </select>

    <select id="queryStatusByDdqqlsh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where fpqqlsh = #{ddqqlsh}
          and is_delete = "0"
    </select>

    <select id="queryStatusByDdh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where ddh = #{ddqqlsh}
          and xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
    </select>
    <select id="queryStatusByDdhCount" resultType="int">
        select count(1)
        from order_invoice_info
        where ddh = #{ddqqlsh}
          and xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
    </select>
    <select id="queryInvoiceInfoByDdh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where ddh = #{djbh}
          and xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "1"  limit 1
    </select>

    <select id="queryStatusByDdqqlshAndNsrsbh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where fpqqlsh = #{ddqqlsh}
          and xhf_nsrsbh = #{nsrsbh}
          and is_delete = "0"
    </select>

    <select id="selectTaskInvoiceInfo" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where kpzt = '1'
          and is_delete = '0'
          and kplx = '0'
    </select>

    <select id="selectMongodbIdByFphm" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where fphm = #{fphm, jdbcType=VARCHAR}
    </select>
    <select id="selectRedInvoiceConfirm" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT t.*, sum(t.jshj) startBhsje FROM order_invoice_info t
        <where>
            <if test="orderInvoiceInfoEntity.gxfxz != null and orderInvoiceInfoEntity.gxfxz != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.gxfxz == 0">
                        AND t.xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
                    </when>
                    <when test="orderInvoiceInfoEntity.gxfxz == 1">
                        AND t.ghf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.dfnsrsbh != null and orderInvoiceInfoEntity.dfnsrsbh != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.gxfxz == 0">
                        AND t.ghf_nsrsbh = #{orderInvoiceInfoEntity.dfnsrsbh}
                    </when>
                    <when test="orderInvoiceInfoEntity.gxfxz == 1">
                        AND t.xhf_nsrsbh = #{orderInvoiceInfoEntity.dfnsrsbh}
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.dfnsrmc != null and orderInvoiceInfoEntity.dfnsrmc != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.gxfxz == 0">
                        AND t.ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.dfnsrmc}),'%')
                    </when>
                    <when test="orderInvoiceInfoEntity.gxfxz == 1">
                        AND t.xhf_mc like concat(concat('%',#{orderInvoiceInfoEntity.dfnsrmc}),'%')
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.qdfphm != null and orderInvoiceInfoEntity.qdfphm != ''">
                AND t.qdfphm = #{orderInvoiceInfoEntity.qdfphm}
            </if>
            <if test="orderInvoiceInfoEntity.fpdm != null and orderInvoiceInfoEntity.fpdm != ''">
                AND t.fpdm = #{orderInvoiceInfoEntity.fpdm}
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.gxfxz == '' or orderInvoiceInfoEntity.gxfxz == null">
                        AND (t.xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh} OR t.ghf_nsrsbh =
                        #{orderInvoiceInfoEntity.baseNsrsbh})
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.fphm != null and orderInvoiceInfoEntity.fphm != ''">
                AND t.fphm = #{orderInvoiceInfoEntity.fphm}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(t.kprq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(t.kprq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kpqd != null and orderInvoiceInfoEntity.kpqd != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpqd == 0">
                        AND t.ddly = '4'
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.kpqd != null and orderInvoiceInfoEntity.kpqd != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpqd == 1">
                        AND (t.ddly = '0' or t.ddly = '1' or t.ddly = '2' or t.ddly = '3')
                    </when>
                </choose>
            </if>
            and t.is_delete = '0' and t.kpzt = '2' and t.kplx = '0' and t.ch_bz in ('0','4') and t.qdfphm is not null
        </where>
        GROUP BY t.qdfphm
        ORDER BY t.create_time DESC
    </select>

    <select id="seleInvoiceInfoByQdfphm" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="qdfphm != null and qdfphm != ''">
                qdfphm = #{qdfphm}
            </if>
            and is_delete = "0" AND kplx = "0"
        </where>
            limit 1
    </select>

    <select id="seleInvoiceInfoByQdfphmIsDele" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            qdfphm = #{qdfphm} and is_delete = '0' limit 1
        </where>
    </select>


    <select id="getInvoiceInfoByQdfphm" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            qdfphm = #{qdfphm}
        </where>
    </select>


    <select id="seleInvoiceRedInfoByQdfphm" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="qdfphm != null and qdfphm != ''">
                qdfphm = #{qdfphm}
            </if>
            and is_delete = "0" AND kplx = "1"
        </where>
    </select>

    <select id="seleInvoiceInfoByYqdfphm" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="yqdfpdm != null and yqdfpdm != ''">
                yqdfphm = #{yqdfpdm}
            </if>
            and is_delete = "0"
        </where>
    </select>

    <select id="selectListByInfo" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.xhfNsrsbh != null and orderInvoiceInfoEntity.xhfNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.xhfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            and is_delete = "0" and kpzt = "2" and kplx = "1"
        </where>
        ORDER BY create_time DESC
    </select>


    <select id="selectInvoiceRecordListForExport"
            resultType="com.dxhy.order.modules.entity.InvoiceRecordInfoEntity">


    </select>
    <select id="selectZLStaisticList" resultType="com.dxhy.order.modules.entity.DataStaticsQueryResultList">
        select fpzl_dm,
        sum(case when kplx = '0' and kpzt = '2' then 1 else 0 end)zsfpfs, -- 正数发票份数
        sum(case when kplx = '0' and zf_bz = '1' then 1 else 0 end)zffpfs, -- 正废发票份数
        sum(case when kplx = '1' and kpzt = '2' then 1 else 0 end)fsfpfs, -- 负数发票份数
        sum(case when kplx = '1' and zf_bz = '1' then 1 else 0 end)fffpfs -- 负废发票份数
        from order_invoice_info oi
        where is_delete = '0'
        <if test="baseNsrsbh !=null and baseNsrsbh!=''">
            and xhf_nsrsbh = #{baseNsrsbh, jdbcType=VARCHAR}
        </if>
        <if test="beginTime != null and endTime != null">
            and ((date_format(oi.kprq,'%Y-%m-%d') &gt;= date_format(#{beginTime,jdbcType=TIMESTAMP},'%Y-%m-%d') and
            date_format(oi.kprq,'%Y-%m-%d') &lt;= date_format(#{endTime,jdbcType=TIMESTAMP},'%Y-%m-%d') )
            or (date_format(oi.chsj,'%Y-%m-%d') &gt;= date_format(#{beginTime,jdbcType=TIMESTAMP},'%Y-%m-%d') and
            date_format(oi.chsj,'%Y-%m-%d') &lt;= date_format(#{endTime,jdbcType=TIMESTAMP},'%Y-%m-%d')))
        </if>
        <if test="fpzldm != null and fpzldm != ''">
            AND fpzl_dm = #{fpzldm, jdbcType=VARCHAR}
        </if>
        group by fpzl_dm
    </select>

    <select id="selectTotalDataJdkjed" resultType="com.dxhy.order.modules.entity.FirstPagePortEntity">
        select date_format(kprq,'%Y%m') as monthTime,CONVERT(sum(jshj) , CHAR) monthNum
        from order_invoice_info oi
        where oi.kpzt = '2' and oi.kplx = '0' and ch_bz != '1' and is_delete = '0'
        <if test="nsrsbh != null and nsrsbh != ''">
            and oi.xhf_nsrsbh = #{nsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="begin != null and end != null">
            and oi.kprq >= #{begin,jdbcType=TIMESTAMP} and oi.kprq is not null
            and oi.kprq &lt;= #{end,jdbcType=TIMESTAMP} and oi.kprq is not null
        </if>
        group by monthTime
    </select>

    <select id="selectIHadListByNsrsbh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select * from order_invoice_info oi
        where oi.kplx = '1' and oi.kpzt = '2' and is_delete = '0' and (oi.kprq is not null or oi.kprq_str is not null)
        <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
            and oi.xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh,jdbcType=VARCHAR}
        </if>
        <if test="orderInvoiceInfoEntity.startTime != null and orderInvoiceInfoEntity.startTime != ''">
            <![CDATA[ AND DATE_FORMAT(oi.kprq, '%Y-%m-%d') >= DATE_FORMAT(#{orderInvoiceInfoEntity.startTime,jdbcType=VARCHAR}, '%Y-%m-%d') ]]>
        </if>
        <if test="orderInvoiceInfoEntity.endTime != null and orderInvoiceInfoEntity.endTime != ''">
            <![CDATA[ AND DATE_FORMAT(#{orderInvoiceInfoEntity.endTime,jdbcType=VARCHAR}, '%Y-%m-%d') >= DATE_FORMAT(oi.kprq, '%Y-%m-%d') ]]>
        </if>
        <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
            <bind name="bindGhfMc" value="'%' + orderInvoiceInfoEntity.ghfMc + '%'"/>
            and oi.ghf_mc like #{bindGhfMc}
        </if>
        order by chsj desc
    </select>

    <select id="selectReceiveRedList" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND ghf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc= #{orderInvoiceInfoEntity.ghfMc}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            and is_delete = "0" and kpzt = "2" and kplx = "1"
        </where>
        ORDER BY create_time DESC
    </select>

    <update id="updateKpztById" parameterType="java.lang.String">
        update order_invoice_info
        set kpzt = "4"
        where id = #{id}
          and xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
    </update>

    <update id="updateKpztNoNeedToWaitByids" parameterType="java.lang.String">
        update order_invoice_info
        set kpzt = '0'
        where id = #{id}
          and xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
    </update>

    <select id="selectNoNeedList" parameterType="com.dxhy.order.modules.pojo.dto.NoOrderInvoiceInfoListDTO"
            resultType="com.dxhy.order.modules.pojo.dto.NoOrderInvoiceInfoListDTO">
        SELECT * FROM order_invoice_info
        <where>
            <if test="noOrderInvoiceInfoListDTO.ddh != null and noOrderInvoiceInfoListDTO.ddh != ''">
                ddh like concat(concat('%',#{noOrderInvoiceInfoListDTO.ddh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="noOrderInvoiceInfoListDTO.baseNsrsbh != null and noOrderInvoiceInfoListDTO.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{noOrderInvoiceInfoListDTO.baseNsrsbh}
            </if>
            <if test="noOrderInvoiceInfoListDTO.ghfNsrsbh != null and noOrderInvoiceInfoListDTO.ghfNsrsbh != ''">
                AND ghf_nsrsbh like concat(concat('%', #{noOrderInvoiceInfoListDTO.ghfNsrsbh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="noOrderInvoiceInfoListDTO.ghfMc != null and noOrderInvoiceInfoListDTO.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{noOrderInvoiceInfoListDTO.ghfMc,jdbcType=VARCHAR}),'%')
            </if>
            <if test="noOrderInvoiceInfoListDTO.fpzlDm != null and noOrderInvoiceInfoListDTO.fpzlDm != ''">
                AND fpzl_dm= #{noOrderInvoiceInfoListDTO.fpzlDm}
            </if>
            <if test="noOrderInvoiceInfoListDTO.ddly != null and noOrderInvoiceInfoListDTO.ddly != ''">
                AND ddly= #{noOrderInvoiceInfoListDTO.ddly}
            </if>
            <if test="noOrderInvoiceInfoListDTO.ysdh != null and noOrderInvoiceInfoListDTO.ysdh != ''">
                AND ysdh like concat(concat('%',#{noOrderInvoiceInfoListDTO.ysdh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="noOrderInvoiceInfoListDTO.ddzt != null and noOrderInvoiceInfoListDTO.ddzt != ''">
                AND ddzt= #{noOrderInvoiceInfoListDTO.ddzt}
            </if>
            <if test="noOrderInvoiceInfoListDTO.ddscrqq!=null and noOrderInvoiceInfoListDTO.ddscrqq!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') >= #{noOrderInvoiceInfoListDTO.ddscrqq} ]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.ddscrqz!=null and noOrderInvoiceInfoListDTO.ddscrqz!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') <= #{noOrderInvoiceInfoListDTO.ddscrqz} ]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.startJehj!=null and noOrderInvoiceInfoListDTO.startJehj!=''">
                <![CDATA[ AND hjbhsje >= ${noOrderInvoiceInfoListDTO.startJehj}]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.endJehj!=null and noOrderInvoiceInfoListDTO.endJehj!=''">
                <![CDATA[ AND hjbhsje <= ${noOrderInvoiceInfoListDTO.endJehj}]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.startJshj!=null and noOrderInvoiceInfoListDTO.startJshj!=''">
                <![CDATA[ AND jshj >= ${noOrderInvoiceInfoListDTO.startJshj}]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.endJshj!=null and noOrderInvoiceInfoListDTO.endJshj!=''">
                <![CDATA[ AND jshj <= ${noOrderInvoiceInfoListDTO.endJshj}]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.startKpse!=null and noOrderInvoiceInfoListDTO.startKpse!=''">
                <![CDATA[ AND kpse >= ${noOrderInvoiceInfoListDTO.startKpse}]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.endKpse!=null and noOrderInvoiceInfoListDTO.endKpse!=''">
                <![CDATA[ AND kpse <= ${noOrderInvoiceInfoListDTO.endKpse}]]>
            </if>
            <if test="noOrderInvoiceInfoListDTO.bz != null and noOrderInvoiceInfoListDTO.bz != ''">
                AND bz like concat(concat('%',#{noOrderInvoiceInfoListDTO.bz,jdbcType=VARCHAR}),'%')
            </if>
            and is_delete = '0'
            and kpzt = '4'
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getOrderInvoiceInfoByKPZT" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT oi.*, GROUP_CONCAT(F.DYHP SEPARATOR ',') AS DYHP
        FROM
        order_invoice_info oi
        LEFT JOIN (
            SELECT DISTINCT
            qdfphm AS DYHP,
            yqdfphm AS LP
            FROM
            order_invoice_info
            WHERE
            kplx = '1'
            AND is_delete = "0"
        ) F ON F.LP = oi.qdfphm
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh like concat(concat('%',#{orderInvoiceInfoEntity.ddh}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND ysdh like concat(concat('%',#{orderInvoiceInfoEntity.ysdh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.qdfphm != null and orderInvoiceInfoEntity.qdfphm != ''">
                AND qdfphm like concat(concat('%',#{orderInvoiceInfoEntity.qdfphm,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.kprqq!=null and orderInvoiceInfoEntity.kprqq!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.kprqq} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kprqz!=null and orderInvoiceInfoEntity.kprqz!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.kprqz} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh like concat(concat('%',#{orderInvoiceInfoEntity.ghfNsrsbh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''and orderInvoiceInfoEntity.sgbj==''and orderInvoiceInfoEntity.kpzt != 2">
                AND kpzt= #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''and orderInvoiceInfoEntity.sgbj==''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpzt == 2">
                        AND (kpzt= #{orderInvoiceInfoEntity.kpzt} OR kpzt= "5")
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.jfzts!=null and orderInvoiceInfoEntity.jfzts.size &gt; 0 and orderInvoiceInfoEntity.jfzts.size &lt; 3">
                <choose>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND sjjfzt = '1'
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND yxjfzt = '1'
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND (sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((sjjfzt = '1') OR (yxjfzt = '1'))
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((sjjfzt = '1') OR ((sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)))
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((yxjfzt = '1') OR ((sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)))
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.chzt != null and orderInvoiceInfoEntity.chzt != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.chzt==0">
                        AND ch_bz != "1"
                    </when>
                    <when test="orderInvoiceInfoEntity.chzt==1">
                        AND ch_bz= "1"
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.DYHP!=null and orderInvoiceInfoEntity.DYHP!=''">
                AND F.DYHP like concat(concat('%',#{orderInvoiceInfoEntity.DYHP,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.byzd2!=null and orderInvoiceInfoEntity.byzd2!=''">
                AND byzd2 like concat(concat('%',#{orderInvoiceInfoEntity.byzd2,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.bz!=null and orderInvoiceInfoEntity.bz!=''">
                AND bz like concat(concat('%',#{orderInvoiceInfoEntity.bz,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.dsptlx != null and orderInvoiceInfoEntity.dsptlx != '' ">
                AND dsptlx = #{orderInvoiceInfoEntity.dsptlx}
            </if>
            <if test="orderInvoiceInfoEntity.dsptysddbh != null and orderInvoiceInfoEntity.dsptysddbh != '' ">
                AND dsptysddbh = #{orderInvoiceInfoEntity.dsptysddbh}
            </if>
            <if test="orderInvoiceInfoEntity.sgbj!=null and orderInvoiceInfoEntity.sgbj!=''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpzt == 2 and orderInvoiceInfoEntity.sgbj == 1">
                        AND kpzt = '5'
                    </when>
                    <when test="orderInvoiceInfoEntity.kpzt == '' and orderInvoiceInfoEntity.sgbj == 1">
                        AND kpzt = '5'
                    </when>
                    <when test="orderInvoiceInfoEntity.kpzt != 2 and orderInvoiceInfoEntity.sgbj == 1 and orderInvoiceInfoEntity.kpzt != ''">
                        AND (kpzt = '1' and kpzt = '5')
                    </when>
                    <when test="orderInvoiceInfoEntity.sgbj ==2 and orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''">
                        AND kpzt= #{orderInvoiceInfoEntity.kpzt}
                    </when>
                    <when test="orderInvoiceInfoEntity.sgbj ==2 and orderInvoiceInfoEntity.kpzt == ''">
                        AND kpzt != '5'
                    </when>
                </choose>
            </if>
            and is_delete = "0"
            and kpzt != '0'
            and kpzt != '4'
            and kplx = '0'
            and ddly != '4'
        </where>
        GROUP BY oi.qdfphm
        ORDER BY create_time DESC
    </select>

    <select id="getOrderInvoiceInfoAllByKPZT" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap">
        SELECT * FROM
        ( SELECT qdfphm AS DYHP, yqdfphm AS LP FROM order_invoice_info WHERE kplx = '1' AND is_delete = "0" ) F
        RIGHT JOIN order_invoice_info oi ON F.LP = oi.qdfphm
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh like concat(concat('%',#{orderInvoiceInfoEntity.ddh}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND ysdh like concat(concat('%',#{orderInvoiceInfoEntity.ysdh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.qdfphm != null and orderInvoiceInfoEntity.qdfphm != ''">
                AND qdfphm like concat(concat('%',#{orderInvoiceInfoEntity.qdfphm,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.kprqq!=null and orderInvoiceInfoEntity.kprqq!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.kprqq} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kprqz!=null and orderInvoiceInfoEntity.kprqz!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.kprqz} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh like concat(concat('%',#{orderInvoiceInfoEntity.ghfNsrsbh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''and orderInvoiceInfoEntity.sgbj==''and orderInvoiceInfoEntity.kpzt != 2">
                AND kpzt= #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''and orderInvoiceInfoEntity.sgbj==''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpzt == 2">
                        AND (kpzt= #{orderInvoiceInfoEntity.kpzt} OR kpzt= "5")
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.jfzts!=null and orderInvoiceInfoEntity.jfzts.size &gt; 0 and orderInvoiceInfoEntity.jfzts.size &lt; 3">
                <choose>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND sjjfzt = '1'
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND yxjfzt = '1'
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND (sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((sjjfzt = '1') OR (yxjfzt = '1'))
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((sjjfzt = '1') OR ((sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)))
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((yxjfzt = '1') OR ((sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)))
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.chzt != null and orderInvoiceInfoEntity.chzt != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.chzt==0">
                        AND ch_bz != "1"
                    </when>
                    <when test="orderInvoiceInfoEntity.chzt==1">
                        AND ch_bz= "1"
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.DYHP!=null and orderInvoiceInfoEntity.DYHP!=''">
                AND F.DYHP like concat(concat('%',#{orderInvoiceInfoEntity.DYHP,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.byzd2!=null and orderInvoiceInfoEntity.byzd2!=''">
                AND byzd2 like concat(concat('%',#{orderInvoiceInfoEntity.byzd2,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.bz!=null and orderInvoiceInfoEntity.bz!=''">
                AND bz like concat(concat('%',#{orderInvoiceInfoEntity.bz,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.sgbj!=null and orderInvoiceInfoEntity.sgbj!=''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpzt == 2 and orderInvoiceInfoEntity.sgbj == 1">
                        AND kpzt = '5'
                    </when>
                    <when test="orderInvoiceInfoEntity.kpzt == '' and orderInvoiceInfoEntity.sgbj == 1">
                        AND kpzt = '5'
                    </when>
                    <when test="orderInvoiceInfoEntity.kpzt != 2 and orderInvoiceInfoEntity.sgbj == 1 and orderInvoiceInfoEntity.kpzt != ''">
                        AND (kpzt = '1' and kpzt = '5')
                    </when>
                    <when test="orderInvoiceInfoEntity.sgbj ==2 and orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''">
                        AND kpzt= #{orderInvoiceInfoEntity.kpzt}
                    </when>
                    <when test="orderInvoiceInfoEntity.sgbj ==2 and orderInvoiceInfoEntity.kpzt == ''">
                        AND kpzt != '5'
                    </when>
                </choose>
            </if>
            and is_delete = "0"
            and kpzt != '0'
            and kpzt != '4'
            and kplx = '0'
            and ddly != '4'
        </where>
        ORDER BY create_time DESC
    </select>

    <!--    <select id="getOrderInvoiceInfoAllByKPZT" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"-->
    <!--            resultMap="orderInvoiceInfoMap">-->
    <!--        SELECT * FROM order_invoice_info-->
    <!--        <where>-->
    <!--            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">-->
    <!--                ddh like concat(concat('%',#{orderInvoiceInfoEntity.ddh}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.ids!=null and orderInvoiceInfoEntity.ids.size > 0">-->
    <!--                and id in-->
    <!--                <foreach collection="orderInvoiceInfoEntity.ids" index="index" item="id" open="(" separator=","-->
    <!--                         close=")">-->
    <!--                    #{id}-->
    <!--                </foreach>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">-->
    <!--                AND ysdh like concat(concat('%',#{orderInvoiceInfoEntity.ysdh,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">-->
    <!--                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">-->
    <!--                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.qdfphm != null and orderInvoiceInfoEntity.qdfphm != ''">-->
    <!--                AND qdfphm like concat(concat('%',#{orderInvoiceInfoEntity.qdfphm,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.kprqq!=null and orderInvoiceInfoEntity.kprqq!=''">-->
    <!--                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.kprqq} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.kprqz!=null and orderInvoiceInfoEntity.kprqz!=''">-->
    <!--                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.kprqz} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">-->
    <!--                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">-->
    <!--                AND ghf_nsrsbh like concat(concat('%',#{orderInvoiceInfoEntity.ghfNsrsbh,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">-->
    <!--                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">-->
    <!--                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">-->
    <!--                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">-->
    <!--                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">-->
    <!--                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">-->
    <!--                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">-->
    <!--                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != '' and orderInvoiceInfoEntity.sgbj=='' and orderInvoiceInfoEntity.kpzt != 2">-->
    <!--                AND kpzt= #{orderInvoiceInfoEntity.kpzt}-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''">-->
    <!--                <choose>-->
    <!--                    <when test="orderInvoiceInfoEntity.kpzt == 2 and orderInvoiceInfoEntity.sgbj==''">-->
    <!--                        AND (kpzt= #{orderInvoiceInfoEntity.kpzt} OR kpzt= "5")-->
    <!--                    </when>-->
    <!--                </choose>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">-->
    <!--                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">-->
    <!--                AND ddly= #{orderInvoiceInfoEntity.ddly}-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.jfzts!=null and orderInvoiceInfoEntity.jfzts.size &gt; 0 and orderInvoiceInfoEntity.jfzts.size &lt; 3">-->
    <!--                <choose>-->
    <!--                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">-->
    <!--                        AND (ghf_sj is not null and ghf_sj != '')-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">-->
    <!--                        AND (ghf_yx is not null and ghf_yx != '')-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">-->
    <!--                        AND ((ghf_yx is null or ghf_yx = '') and (ghf_sj is null or ghf_sj = ''))-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;)">-->
    <!--                        AND ((ghf_sj is not null and ghf_sj != '') OR (ghf_yx is not null and ghf_yx != ''))-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;)">-->
    <!--                        AND (((ghf_yx is null or ghf_yx = '') and (ghf_sj is null or ghf_sj = '')) OR (ghf_sj is not-->
    <!--                        null and ghf_sj != ''))-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;)">-->
    <!--                        AND (((ghf_yx is null or ghf_yx = '') and (ghf_sj is null or ghf_sj = '')) OR (ghf_yx is not-->
    <!--                        null and ghf_yx != ''))-->
    <!--                    </when>-->
    <!--                </choose>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.chzt != null and orderInvoiceInfoEntity.chzt != ''">-->
    <!--                <choose>-->
    <!--                    <when test="orderInvoiceInfoEntity.chzt==0">-->
    <!--                        AND ch_bz != "1"-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.chzt==1">-->
    <!--                        AND ch_bz= "1"-->
    <!--                    </when>-->
    <!--                </choose>-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.yqdfphm!=null and orderInvoiceInfoEntity.yqdfphm!=''">-->
    <!--                AND yqdfphm like concat(concat('%',#{orderInvoiceInfoEntity.yqdfphm,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.byzd2!=null and orderInvoiceInfoEntity.byzd2!=''">-->
    <!--                AND byzd2 like concat(concat('%',#{orderInvoiceInfoEntity.byzd2,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.bz!=null and orderInvoiceInfoEntity.bz!=''">-->
    <!--                AND bz like concat(concat('%',#{orderInvoiceInfoEntity.bz,jdbcType=VARCHAR}),'%')-->
    <!--            </if>-->
    <!--            <if test="orderInvoiceInfoEntity.sgbj!=null and orderInvoiceInfoEntity.sgbj!=''">-->
    <!--                <choose>-->
    <!--                    <when test="orderInvoiceInfoEntity.sgbj==1">-->
    <!--                        AND kpzt= "5"-->
    <!--                    </when>-->
    <!--                    <when test="orderInvoiceInfoEntity.sgbj==2">-->
    <!--                        AND kpzt= "2"-->
    <!--                    </when>-->
    <!--                </choose>-->
    <!--            </if>-->
    <!--            and is_delete = "0"-->
    <!--            and kpzt != '0'-->
    <!--            and kpzt != '4'-->
    <!--            and kplx = '0'-->
    <!--        </where>-->
    <!--        ORDER BY create_time DESC-->
    <!--    </select>-->


    <update id="updateToWaitByid" >
        update order_invoice_info
        set kpzt = "0",
            qdfphm = "",
            ofd_url = "",
            pdf_url = "",
            kprq = null,
            update_time = #{time}
        where id = #{id}
          and is_delete = "0"
          and kpzt = "5"
          and xhf_nsrsbh = #{baseNsrsbh}
    </update>


    <select id="countSLbyNsrsbh" resultType="com.dxhy.order.modules.pojo.dto.InvoiceRecordSumNumberDTO">
        select sum(case when kpzt = '1' or kpzt = '2' or kpzt = '3' or kpzt = '5' then 1 else 0 end) alls,
               sum(case when kpzt = '2' or kpzt = '5' then 1 else 0 end)                             kpcg,
               sum(case when kpzt = '1' then 1 else 0 end)                                           kpz,
               sum(case when kpzt = '3' then 1 else 0 end)                                           kpsb
        from order_invoice_info
        where xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
          and kplx = '0'
    </select>

    <select id="selectNoNeedInfoByid" parameterType="java.lang.String"
            resultType="com.dxhy.order.modules.pojo.dto.NoOrderInvoiceInfoListDTO">
        SELECT *
        FROM order_invoice_info
        where xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
          and id = #{id}
    </select>

    <select id="selectList1" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT *
        FROM order_invoice_info
    </select>


    <update id="updateBzById" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        update order_invoice_info
        set bz= #{orderInvoiceInfoEntity.bz}
        where id = #{orderInvoiceInfoEntity.id}
          and xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
          and is_delete = "0"
    </update>

    <select id="queryStatusByYsdh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        select *
        from order_invoice_info
        where ysdh = #{ysdh}
          and xhf_nsrsbh = #{baseNsrsbh}
          and is_delete = "0"
    </select>

    <select id="selectDdhListByYsdh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT * FROM order_invoice_info
        <where>
            is_delete = "0" and kpzt = "0" and ysdh = #{ysdh} and xhf_nsrsbh = #{xhfNsrsbh}
        </where>
    </select>
    <select id="selectDdhListByYsdhIsDelete" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT * FROM order_invoice_info
        <where>
            is_delete = "1" and kpzt = "0" and ysdh = #{ysdh} and xhf_nsrsbh = #{xhfNsrsbh}
        </where>
    </select>

    <select id="selectDdhListByYysdh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT * FROM order_invoice_info
        <where>
            is_delete = "0" and kpzt = "0" and yysdh = #{djbh} and xhf_nsrsbh = #{baseNsrsbh}
        </where>
    </select>

    <update id="updateEmailByqdhm">
        update order_invoice_info
        set ghf_yx= #{email}
        where qdfphm = #{qdfphm}
          and is_delete = "0"
    </update>

    <update id="updatePhoneByqdhm">
        update order_invoice_info
        set ghf_sj= #{phone}
        where qdfphm = #{qdfphm}
          and is_delete = "0"
    </update>

    <update id="updateYxJfzt">
        update order_invoice_info
        set yxjfzt = '1'
        where id = #{id}
          and is_delete = "0"
    </update>

    <update id="updateSjJfzt">
        update order_invoice_info
        set sjjfzt = '1'
        where id = #{id}
          and is_delete = "0"
    </update>

    <update id="updateOrderInvoiceInfo" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        update order_invoice_info
        set hjbhsje= #{orderInvoiceInfoEntity.hjbhsje},
            kpse= #{orderInvoiceInfoEntity.kpse},
            jshj= #{orderInvoiceInfoEntity.jshj}
        where id = #{orderInvoiceInfoEntity.id}
    </update>
    <select id="queryDdhListByYsdh" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT * FROM order_invoice_info
        <where>
            is_delete = "0" and ysdh = #{ysdh} and xhf_nsrsbh = #{xhfNsrsbh}
        </where>
    </select>

    <select id="selectRecordDetailByid" resultMap="orderInvoiceInfoMap">
        SELECT oi.*, GROUP_CONCAT(F.DYHP SEPARATOR ',') AS DYHP
        FROM
        order_invoice_info oi
        LEFT JOIN (
            SELECT DISTINCT
            qdfphm AS DYHP,
            yqdfphm AS LP
            FROM
            order_invoice_info
            WHERE
            kplx = '1'
            AND is_delete = "0"
        ) F ON F.LP = oi.qdfphm
        <where>
            is_delete = "0" and id = #{id}
        </where>
    </select>

    <select id="selectDdhListByYsdhAndKpzt" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT * FROM order_invoice_info
        <where>
            is_delete = "0"
            <if test="kpzt != null and kpzt != ''">
                <choose>
                    <when test="kpzt == 3 ">
                        AND (kpzt= '3' OR kpzt= '6')
                    </when>
                    <when test="kpzt != 3">
                        AND kpzt = #{kpzt}
                    </when>
                </choose>
            </if>
            and ysdh = #{ysdh}
            and xhf_nsrsbh = #{xhfNsrsbh}
        </where>
    </select>

    <select id="selectRecordDdhListByYsdhAndKpzt" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity">
        SELECT * FROM order_invoice_info
        <where>
            is_delete = "0"
            <if test="kpzt!=null and kpzt != ''">
                <choose>
                    <when test="kpzt == 2 ">
                        AND (kpzt= '2' OR kpzt= '5')
                    </when>
                    <when test="kpzt != 2">
                        AND kpzt = #{kpzt}
                    </when>
                </choose>
            </if>
            and ysdh = #{ysdh}
            and xhf_nsrsbh = #{xhfNsrsbh}
            and kpzt != '0'
            and kpzt != '4'
        </where>
    </select>

    <select id="selectRecordListByDuplicate" parameterType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultType="java.lang.String">
        SELECT DISTINCT ysdh
        FROM
        ( SELECT qdfphm AS DYHP, yqdfphm AS LP FROM order_invoice_info WHERE kplx = '1' AND is_delete = "0" ) F
        RIGHT JOIN order_invoice_info oi ON F.LP = oi.qdfphm
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                ddh like concat(concat('%',#{orderInvoiceInfoEntity.ddh}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND ysdh like concat(concat('%',#{orderInvoiceInfoEntity.ysdh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(ddscrq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.qdfphm != null and orderInvoiceInfoEntity.qdfphm != ''">
                AND qdfphm like concat(concat('%',#{orderInvoiceInfoEntity.qdfphm,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.kprqq!=null and orderInvoiceInfoEntity.kprqq!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.kprqq} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kprqz!=null and orderInvoiceInfoEntity.kprqz!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.kprqz} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND ghf_nsrsbh like concat(concat('%',#{orderInvoiceInfoEntity.ghfNsrsbh,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''and orderInvoiceInfoEntity.sgbj==''and orderInvoiceInfoEntity.kpzt != 2">
                AND kpzt= #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''and orderInvoiceInfoEntity.sgbj==''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpzt == 2">
                        AND (kpzt= #{orderInvoiceInfoEntity.kpzt} OR kpzt= "5")
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.jfzts!=null and orderInvoiceInfoEntity.jfzts.size &gt; 0 and orderInvoiceInfoEntity.jfzts.size &lt; 3">
                <choose>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND sjjfzt = '1'
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND yxjfzt = '1'
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.size == 1">
                        AND (sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((sjjfzt = '1') OR (yxjfzt = '1'))
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;1&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((sjjfzt = '1') OR ((sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)))
                    </when>
                    <when test="orderInvoiceInfoEntity.jfzts.contains(&quot;3&quot;) and orderInvoiceInfoEntity.jfzts.contains(&quot;2&quot;) and orderInvoiceInfoEntity.jfzts.size == 2">
                        AND ((yxjfzt = '1') OR ((sjjfzt != '1' OR sjjfzt is NULL) AND (yxjfzt != '1' OR yxjfzt is NULL)))
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.chzt != null and orderInvoiceInfoEntity.chzt != ''">
                <choose>
                    <when test="orderInvoiceInfoEntity.chzt==0">
                        AND ch_bz != "1"
                    </when>
                    <when test="orderInvoiceInfoEntity.chzt==1">
                        AND ch_bz= "1"
                    </when>
                </choose>
            </if>
            <if test="orderInvoiceInfoEntity.DYHP!=null and orderInvoiceInfoEntity.DYHP!=''">
                AND F.DYHP like concat(concat('%',#{orderInvoiceInfoEntity.DYHP,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.byzd2!=null and orderInvoiceInfoEntity.byzd2!=''">
                AND byzd2 like concat(concat('%',#{orderInvoiceInfoEntity.byzd2,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.bz!=null and orderInvoiceInfoEntity.bz!=''">
                AND bz like concat(concat('%',#{orderInvoiceInfoEntity.bz,jdbcType=VARCHAR}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.sgbj!=null and orderInvoiceInfoEntity.sgbj!=''">
                <choose>
                    <when test="orderInvoiceInfoEntity.kpzt == 2 and orderInvoiceInfoEntity.sgbj == 1">
                        AND kpzt = '5'
                    </when>
                    <when test="orderInvoiceInfoEntity.kpzt == '' and orderInvoiceInfoEntity.sgbj == 1">
                        AND kpzt = '5'
                    </when>
                    <when test="orderInvoiceInfoEntity.kpzt != 2 and orderInvoiceInfoEntity.sgbj == 1 and orderInvoiceInfoEntity.kpzt != ''">
                        AND (kpzt = '1' and kpzt = '5')
                    </when>
                    <when test="orderInvoiceInfoEntity.sgbj ==2 and orderInvoiceInfoEntity.kpzt!=null and orderInvoiceInfoEntity.kpzt != ''">
                        AND kpzt= #{orderInvoiceInfoEntity.kpzt}
                    </when>
                    <when test="orderInvoiceInfoEntity.sgbj ==2 and orderInvoiceInfoEntity.kpzt == ''">
                        AND kpzt != '5'
                    </when>
                </choose>
            </if>
            and is_delete = "0"
            and kpzt != '0'
            and kpzt != '4'
            and kplx = '0'
            and ddly != '4'
            and ysdh != ''
            and ysdh is not null
        </where>
        ORDER BY create_time DESC
    </select>
    <select id="selectAllListByEntity" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap1">
        SELECT
            CASE WHEN EXISTS(SELECT 1 FROM order_invoice_item WHERE order_invoice_id=a.id AND xmsl <![CDATA[<]]> 0)
                THEN '5' ELSE a.ddzt  END AS zt,
            a.* from order_invoice_item i
                LEFT JOIN order_invoice_info a on  i.order_invoice_id = a.id
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                a.ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                And a.kpzt = #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND a.xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND a.ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND a.ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND a.fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND a.ddly= #{orderInvoiceInfoEntity.ddly}
            </if>

            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND a.ysdh= #{orderInvoiceInfoEntity.ysdh}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(a.create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(a.create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND a.jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND a.jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND a.hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND a.hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND a.kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND a.kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.dsptlx != null and orderInvoiceInfoEntity.dsptlx != ''">
                a.dsptlx = #{orderInvoiceInfoEntity.dsptlx}
            </if>
            <if test="orderInvoiceInfoEntity.dsptysddbh != null and orderInvoiceInfoEntity.dsptysddbh != ''">
                a.dsptysddbh = #{orderInvoiceInfoEntity.dsptysddbh}
            </if>
            and a.is_delete = "0" and a.kpzt = "0" and  a.ysdh is not null and a.ysdh != ''
            GROUP BY a.ysdh
        </where>
        <if test="orderInvoiceInfoEntity.ddzt!=null and orderInvoiceInfoEntity.ddzt!=''">
            HAVING zt = ${orderInvoiceInfoEntity.ddzt}
        </if>
        ORDER BY a.create_time DESC
    </select>
    <select id="selectAllListDD" resultType="com.dxhy.order.modules.entity.OrderInvoiceInfoEntity"
            resultMap="orderInvoiceInfoMap1">
        SELECT
            CASE
            WHEN EXISTS
                ( SELECT 1 FROM order_invoice_item WHERE order_invoice_id = a.id AND xmsl <![CDATA[<]]> 0 ) THEN
                '5' ELSE a.ddzt
            END AS zt,
        a.*
        FROM
        order_invoice_item i left join order_invoice_info a on a.id = i.order_invoice_id
        <where>
            <if test="orderInvoiceInfoEntity.ddh != null and orderInvoiceInfoEntity.ddh != ''">
                a.ddh = #{orderInvoiceInfoEntity.ddh}
            </if>
            <if test="orderInvoiceInfoEntity.kpzt != null and orderInvoiceInfoEntity.kpzt != ''">
                And a.kpzt = #{orderInvoiceInfoEntity.kpzt}
            </if>
            <if test="orderInvoiceInfoEntity.baseNsrsbh != null and orderInvoiceInfoEntity.baseNsrsbh != ''">
                AND a.xhf_nsrsbh = #{orderInvoiceInfoEntity.baseNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfNsrsbh != null and orderInvoiceInfoEntity.ghfNsrsbh != ''">
                AND a.ghf_nsrsbh = #{orderInvoiceInfoEntity.ghfNsrsbh}
            </if>
            <if test="orderInvoiceInfoEntity.ghfMc != null and orderInvoiceInfoEntity.ghfMc != ''">
                AND a.ghf_mc like concat(concat('%',#{orderInvoiceInfoEntity.ghfMc}),'%')
            </if>
            <if test="orderInvoiceInfoEntity.fpzlDm != null and orderInvoiceInfoEntity.fpzlDm != ''">
                AND a.fpzl_dm= #{orderInvoiceInfoEntity.fpzlDm}
            </if>
            <if test="orderInvoiceInfoEntity.ddly != null and orderInvoiceInfoEntity.ddly != ''">
                AND a.ddly= #{orderInvoiceInfoEntity.ddly}
            </if>
            <if test="orderInvoiceInfoEntity.ysdh != null and orderInvoiceInfoEntity.ysdh != ''">
                AND a.ysdh= #{orderInvoiceInfoEntity.ysdh}
            </if>
            <if test="orderInvoiceInfoEntity.startTime!=null and orderInvoiceInfoEntity.startTime!=''">
                <![CDATA[ AND DATE_FORMAT(a.create_time, '%Y-%m-%d') >= #{orderInvoiceInfoEntity.startTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endTime!=null and orderInvoiceInfoEntity.endTime!=''">
                <![CDATA[ AND DATE_FORMAT(a.create_time, '%Y-%m-%d') <= #{orderInvoiceInfoEntity.endTime} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startJshj!=null and orderInvoiceInfoEntity.startJshj!=''">
                <![CDATA[ AND a.jshj >= ${orderInvoiceInfoEntity.startJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endJshj!=null and orderInvoiceInfoEntity.endJshj!=''">
                <![CDATA[ AND a.jshj <= ${orderInvoiceInfoEntity.endJshj} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startBhsje!=null and orderInvoiceInfoEntity.startBhsje!=''">
                <![CDATA[ AND a.hjbhsje >= ${orderInvoiceInfoEntity.startBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endBhsje!=null and orderInvoiceInfoEntity.endBhsje!=''">
                <![CDATA[ AND a.hjbhsje <= ${orderInvoiceInfoEntity.endBhsje} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.startSe!=null and orderInvoiceInfoEntity.startSe!=''">
                <![CDATA[ AND a.kpse >= ${orderInvoiceInfoEntity.startSe} ]]>
            </if>
            <if test="orderInvoiceInfoEntity.endSe!=null and orderInvoiceInfoEntity.endSe!=''">
                <![CDATA[ AND a.kpse <= ${orderInvoiceInfoEntity.endSe} ]]>
            </if>
            and a.is_delete = "0" and a.kpzt = "0"
            group by a.id
            <if test="orderInvoiceInfoEntity.ddzt!=null and orderInvoiceInfoEntity.ddzt!=''">
                HAVING zt = ${orderInvoiceInfoEntity.ddzt}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="seleInvoiceInfoByQdfphmIsDelete" resultMap="orderInvoiceInfoMap">
        SELECT * FROM order_invoice_info
        <where>
            <if test="qdfphm != null and qdfphm != ''">
                qdfphm = #{qdfphm}
            </if>
            and is_delete = '1'
            and ddly = '4'
            and sflzfp = 'Y'
            and gjbq = '1'
            and ch_bz = "0"
        </where>
        limit 1
    </select>
</mapper>