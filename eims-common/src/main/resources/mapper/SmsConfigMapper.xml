<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.order.modules.dao.SmsConfigDao">

    <resultMap id="BaseResultMap" type="com.dxhy.order.modules.entity.SmsConfig">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="nsrsbh" column="nsrsbh" jdbcType="VARCHAR"/>
            <result property="file" column="file" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,nsrsbh,file,
        create_time,update_time
    </sql>

    <select id="selectByNsrsbh" resultType="com.dxhy.order.modules.entity.SmsConfig">
        select * from sms_config where nsrsbh = #{nsrsbh}
    </select>


</mapper>
