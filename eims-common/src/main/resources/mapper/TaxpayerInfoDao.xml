<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.order.modules.dao.TaxpayerInfoDao">

    <resultMap id="BaseResultMap" type="com.dxhy.order.modules.entity.TaxpayerInfo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="xhfMc" column="xhf_mc" jdbcType="VARCHAR"/>
            <result property="xhfNsrsbh" column="xhf_nsrsbh" jdbcType="VARCHAR"/>
            <result property="xhfDz" column="xhf_dz" jdbcType="VARCHAR"/>
            <result property="xhfDh" column="xhf_dh" jdbcType="VARCHAR"/>
            <result property="xhfYh" column="xhf_yh" jdbcType="VARCHAR"/>
            <result property="xhfZh" column="xhf_zh" jdbcType="VARCHAR"/>
            <result property="sfbm" column="sfbm" jdbcType="VARCHAR"/>
            <result property="zsxed" column="zsxed" jdbcType="VARCHAR"/>
            <result property="secretId" column="secret_id" jdbcType="VARCHAR"/>
            <result property="secretKey" column="secret_key" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,xhf_mc,xhf_nsrsbh,
        xhf_dz,xhf_dh,xhf_yh,
        xhf_zh,sfbm,zsxed
    </sql>

    <select id="selectZsxedByNsrsbh" resultType="com.dxhy.order.modules.entity.TaxpayerInfo">
        select * from taxpayer_info where xhf_nsrsbh = #{nsrsbh} limit 1
    </select>

    <select id="selectKeyBySecretId" resultMap="BaseResultMap">
        select * from taxpayer_info where secret_id = #{secretId} limit 1
    </select>


</mapper>
