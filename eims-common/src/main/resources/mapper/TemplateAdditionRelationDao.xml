<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.TemplateAdditionRelationDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.TemplateAdditionRelationEntity" id="templateAdditionRelationEntityMap">
        <id property="id" column="id"/>
        <result property="sceneTemplateId" column="scene_template_id"/>
        <result property="additionEleId" column="addition_ele_id"/>
    </resultMap>

    <delete id="deleteBySceneTemplateIdList">
        delete from template_addition_relation where scene_template_id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="queryByAllParam" resultType="com.dxhy.order.modules.entity.TemplateAdditionRelationEntity">
        select * from template_addition_relation
        <where>
            <if test="entity.sceneTemplateId != null and entity.sceneTemplateId != ''">
                scene_template_id = #{entity.sceneTemplateId}
            </if>
            <if test="entity.additionEleId != null and entity.additionEleId != ''">
                And addition_ele_id = #{entity.additionEleId}
            </if>
            <if test="entity.id != null and entity.id != ''">
                And id = #{entity.id}
            </if>
        </where>
    </select>

</mapper>