<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.InvoiceAdditionInfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.InvoiceAdditionInfoEntity" id="invoiceAdditionInfoMap">
        <result property="id" column="id"/>
        <result property="orderInvoiceInfoId" column="order_invoice_info_id"/>
        <result property="fjxxmc" column="fjxxmc"/>
        <result property="fjxxz" column="fjxxz"/>
        <result property="sjnr" column="sjnr"/>
        <result property="additionElementId" column="addition_element_id"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>
    <delete id="deleteByOrderId" parameterType="java.lang.String">
        delete from invoice_addition_info where order_invoice_info_id = #{orderInvoiceInfoId}
    </delete>

    <select id="selectList" parameterType="com.dxhy.order.modules.entity.InvoiceAdditionInfoEntity" resultMap="invoiceAdditionInfoMap">
        SELECT * FROM invoice_addition_info
    </select>

    <select id="selectAdditionListById" resultMap="invoiceAdditionInfoMap">
        SELECT * FROM invoice_addition_info where order_invoice_info_id = #{id} and (is_delete = '0' || is_delete is NULL)
    </select>

</mapper>