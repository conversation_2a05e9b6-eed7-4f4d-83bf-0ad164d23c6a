<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.ItemGroupDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.ItemGroupEntity" id="itemGroupEntityMap">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="xmflmc" column="xmflmc"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="listByNsrsbh" resultType="com.dxhy.order.modules.entity.ItemGroupEntity">
        select * from item_group where base_nsrsbh = #{baseNsrsbh}
    </select>

    <select id="listIdByParentId" resultType="java.lang.String">
        select id from item_group where parent_id = #{parentId}
    </select>

    <select id="selectListByItemGroupSaveDTO" parameterType="com.dxhy.order.modules.pojo.dto.ItemGroupSaveDTO" resultMap="itemGroupEntityMap">
        select * from item_group
        where base_nsrsbh = #{baseNsrsbh}
          and (parent_id = #{parentId} or id = #{parentId})
          and xmflmc = #{xmflmc}
    </select>

    <select id="listLevelTwoDataByNsrsbh" resultType="com.dxhy.order.modules.entity.ItemGroupEntity">
        select * from item_group
        where base_nsrsbh = #{baseNsrsbh}
          and parent_id = '0'
    </select>

    <insert id="insertList">
        insert into item_group(id,parent_id,base_nsrsbh,xmflmc,create_time,create_by)
        values
        <foreach collection="customerGroupEntityList" index="index" item="customerGroupEntity" separator=",">
            (#{customerGroupEntity.id}, #{customerGroupEntity.parentId}, #{customerGroupEntity.baseNsrsbh},
            #{customerGroupEntity.xmflmc}, #{customerGroupEntity.createTime}, #{customerGroupEntity.createBy})
        </foreach>
    </insert>


</mapper>