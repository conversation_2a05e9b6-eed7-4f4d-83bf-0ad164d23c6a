<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.InvoiceBackpushConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity" id="invoiceBackpushConfigMap">
        <result property="id" column="id"/>
        <result property="orderConfigId" column="order_config_id"/>
        <result property="xtly" column="xtly"/>
        <result property="backpushUrl" column="backpush_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectByOrderConfigId" resultType="com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity">
        select *
        from invoice_backpush_config
        where order_config_id = #{orderConfigId}
    </select>

    <select id="selectByOrderConfigIdAndXtly" resultType="com.dxhy.order.modules.entity.InvoiceBackpushConfigEntity">
        select *
        from invoice_backpush_config
        where order_config_id = #{orderConfigId} and xtly = #{xtly} limit 1
    </select>
    
    <select id="countByOrderConfigIdAndXtly" resultType="java.lang.Integer">
        select count(1)
        from invoice_backpush_config
        where order_config_id = #{orderConfigId} and xtly = #{xtly}
    </select>

</mapper> 