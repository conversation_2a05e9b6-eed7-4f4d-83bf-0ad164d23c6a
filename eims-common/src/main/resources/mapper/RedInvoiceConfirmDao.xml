<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.RedInvoiceConfirmDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity" id="redInvoiceConfirmMap">
        <result property="id" column="id"/>
        <result property="gxsf" column="gxsf"/>
        <result property="dfnsrsbh" column="dfnsrsbh"/>
        <result property="nsrsbh" column="nsrsbh"/>
        <result property="dylpqdfphm" column="dylpqdfphm"/>
        <result property="hztzdbh" column="hztzdbh"/>
        <result property="fpje" column="fpje"/>
        <result property="fpse" column="fpse"/>
        <result property="chyy" column="chyy"/>
        <result property="zt" column="zt"/>
        <result property="dfnsrmc" column="dfnsrmc"/>
        <result property="kjzt" column="kjzt"/>
        <result property="qrdbs" column="qrdbs"/>
        <result property="lrfsf" column="lrfsf"/>
        <result property="qdfphm" column="qdfphm"/>
        <result property="sldh" column="sldh"/>
        <result property="kpjgsldh" column="kpjgsldh"/>
        <result property="kprqStr" column="kprq_str"/>
        <result property="kdsj" column="kdsj"/>
        <result property="jshj" column="jshj"/>
        <result property="xsfnsrsbh" column="xsfnsrsbh"/>
        <result property="xsfmc" column="xsfmc"/>
        <result property="gmfnsrsbh" column="gmfnsrsbh"/>
        <result property="gmfmc" column="gmfmc"/>
        <result property="slzt" column="slzt"/>
        <result property="isDelete" column="is_delete"/>
        <result property="sqrq" column="sqrq"/>
        <result property="kprq" column="kprq"/>
        <result property="lzkprq" column="lzkprq"/>
        <result property="lzhjje" column="lzhjje"/>
        <result property="lzhjse" column="lzhjse"/>
        <result property="lzfppzDm" column="lzfppz_dm"/>
        <result property="zzsytDm" column="zzsyt_dm"/>
        <result property="xfsytDm" column="xfsyt_dm"/>
        <result property="fprzztDm" column="fprzzt_dm"/>
        <result property="ykjhzfpbz" column="ykjhzfpbz"/>
        <result property="uuid" column="uuid"/>
        <result property="ztIng" column="zt_ing"/>
        <result property="lzfpdm" column="lzfpdm"/>
        <result property="lzfphm" column="lzfphm"/>
        <result property="fpfqr" column="fpfqr"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="byzd1" column="byzd1"/>
        <result property="byzd2" column="byzd2"/>
        <result property="byzd3" column="byzd3"/>
        <result property="byzd4" column="byzd4"/>
        <result property="byzd5" column="byzd5"/>
        <result property="kjztCode" column="kjzt_code"/>
        <result property="kjztMsg" column="kjzt_msg"/>

    </resultMap>


    <update id="updateKjzt1ByHzqrdbh">
        update red_invoice_confirm
        set kjzt     = #{ztdm},
            kpjgsldh = #{kpjgsldh}
        where hztzdbh = #{hztzdbh}
    </update>

    <update id="updateKjztByHzqrdbh">
        update red_invoice_confirm
        set kjzt   = 'Y',
            qdfphm = #{qdfphm}
        where hztzdbh = #{hztzdbh}
    </update>

    <select id="selectList" parameterType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity"
            resultMap="redInvoiceConfirmMap">
        SELECT * FROM red_invoice_confirm
        <where>
            <if test="redInvoiceConfirmEntity.gxsf != null and redInvoiceConfirmEntity.gxsf != ''">
                gxsf = #{redInvoiceConfirmEntity.gxsf}
            </if>
            <if test="redInvoiceConfirmEntity.zt != null and redInvoiceConfirmEntity.zt != ''">
                AND zt = #{redInvoiceConfirmEntity.zt}
            </if>
            <if test="redInvoiceConfirmEntity.kjzt != null and redInvoiceConfirmEntity.kjzt != ''">
                AND kjzt = #{redInvoiceConfirmEntity.kjzt}
            </if>
            <if test="redInvoiceConfirmEntity.dfnsrmc != null and redInvoiceConfirmEntity.dfnsrmc != ''">
                AND dfnsrmc = #{redInvoiceConfirmEntity.dfnsrmc}
            </if>
            <if test="redInvoiceConfirmEntity.chyy != null and redInvoiceConfirmEntity.chyy != ''">
                AND chyy= #{redInvoiceConfirmEntity.chyy}
            </if>
            <if test="redInvoiceConfirmEntity.lrfsf != null and redInvoiceConfirmEntity.lrfsf != ''">
                AND lrfsf= #{redInvoiceConfirmEntity.lrfsf}
            </if>
            <if test="redInvoiceConfirmEntity.kprqStartTime!=null and redInvoiceConfirmEntity.kprqStartTime!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{redInvoiceConfirmEntity.kprqStartTime} ]]>
            </if>
            <if test="redInvoiceConfirmEntity.kprqEndTime!=null and redInvoiceConfirmEntity.kprqEndTime!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{redInvoiceConfirmEntity.kprqEndTime} ]]>
            </if>
            and is_delete = "0"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="listRedInvoiceConfirm" parameterType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity"
            resultMap="redInvoiceConfirmMap">
        SELECT * FROM red_invoice_confirm
        <where>
            <if test="redInvoiceConfirmEntity.gxsf != null and redInvoiceConfirmEntity.gxsf != ''">
                gxsf = #{redInvoiceConfirmEntity.gxsf}
            </if>
            <if test="redInvoiceConfirmEntity.zt != null and redInvoiceConfirmEntity.zt != ''">
                AND zt = #{redInvoiceConfirmEntity.zt}
            </if>
            <if test="redInvoiceConfirmEntity.kjzt != null and redInvoiceConfirmEntity.kjzt != ''">
                AND kjzt = #{redInvoiceConfirmEntity.kjzt}
            </if>
            <if test="redInvoiceConfirmEntity.dfnsrmc != null and redInvoiceConfirmEntity.dfnsrmc != ''">
                AND dfnsrmc = #{redInvoiceConfirmEntity.dfnsrmc}
            </if>
            <if test="redInvoiceConfirmEntity.chyy != null and redInvoiceConfirmEntity.chyy != ''">
                AND chyy= #{redInvoiceConfirmEntity.chyy}
            </if>
            <if test="redInvoiceConfirmEntity.lrfsf != null and redInvoiceConfirmEntity.lrfsf != ''">
                AND lrfsf= #{redInvoiceConfirmEntity.lrfsf}
            </if>
            <if test="redInvoiceConfirmEntity.kprqStartTime!=null and redInvoiceConfirmEntity.kprqStartTime!=''">
                AND sqrq &gt;= #{redInvoiceConfirmEntity.kprqStartTime}
            </if>
            <if test="redInvoiceConfirmEntity.kprqEndTime!=null and redInvoiceConfirmEntity.kprqEndTime!=''">
                AND #{redInvoiceConfirmEntity.kprqEndTime} &gt;= sqrq
            </if>
            and is_delete = "0" and nsrsbh = #{redInvoiceConfirmEntity.nsrsbh} and fpfqr = "0"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getListRedInvoiceConfirm" parameterType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity"
            resultMap="redInvoiceConfirmMap">
        SELECT * FROM red_invoice_confirm
        <where>
            <if test="redInvoiceConfirmEntity.gxsf != null and redInvoiceConfirmEntity.gxsf != ''">
                gxsf = #{redInvoiceConfirmEntity.gxsf}
            </if>
            <if test="redInvoiceConfirmEntity.baseNsrsbh != null and redInvoiceConfirmEntity.baseNsrsbh != ''">
                AND nsrsbh = #{redInvoiceConfirmEntity.baseNsrsbh}
            </if>
            <if test="redInvoiceConfirmEntity.zt != null and redInvoiceConfirmEntity.zt != ''">
                AND zt = #{redInvoiceConfirmEntity.zt}
            </if>
            <if test="redInvoiceConfirmEntity.kjzt != null and redInvoiceConfirmEntity.kjzt != ''">
                AND kjzt = #{redInvoiceConfirmEntity.kjzt}
            </if>
            <if test="redInvoiceConfirmEntity.dfnsrmc != null and redInvoiceConfirmEntity.dfnsrmc != ''">
                AND dfnsrmc = #{redInvoiceConfirmEntity.dfnsrmc}
            </if>
            <if test="redInvoiceConfirmEntity.chyy != null and redInvoiceConfirmEntity.chyy != ''">
                AND chyy= #{redInvoiceConfirmEntity.chyy}
            </if>
            <if test="redInvoiceConfirmEntity.lrfsf != null and redInvoiceConfirmEntity.lrfsf != ''">
                AND lrfsf= #{redInvoiceConfirmEntity.lrfsf}
            </if>
            <if test="redInvoiceConfirmEntity.kprqStartTime!=null and redInvoiceConfirmEntity.kprqStartTime!=''">
                <![CDATA[ AND DATE_FORMAT(sqrq, '%Y-%m-%d') >= #{redInvoiceConfirmEntity.kprqStartTime} ]]>
            </if>
            <if test="redInvoiceConfirmEntity.kprqEndTime!=null and redInvoiceConfirmEntity.kprqEndTime!=''">
                <![CDATA[ AND DATE_FORMAT(sqrq, '%Y-%m-%d') <= #{redInvoiceConfirmEntity.kprqEndTime} ]]>
            </if>
            and is_delete = "0" and fpfqr = "1"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectRedInvoiceRecord" parameterType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity"
            resultMap="redInvoiceConfirmMap">
        SELECT * FROM red_invoice_confirm
        <where>
            <if test="redInvoiceConfirmEntity.gxsf != null and redInvoiceConfirmEntity.gxsf != ''">
                gxsf = #{redInvoiceConfirmEntity.gxsf}
            </if>
            <if test="redInvoiceConfirmEntity.zt != null and redInvoiceConfirmEntity.zt != ''">
                AND zt = #{redInvoiceConfirmEntity.zt}
            </if>
            <if test="redInvoiceConfirmEntity.kjzt != null and redInvoiceConfirmEntity.kjzt != ''">
                AND kjzt = #{redInvoiceConfirmEntity.kjzt}
            </if>
            <if test="redInvoiceConfirmEntity.dfnsrmc != null and redInvoiceConfirmEntity.dfnsrmc != ''">
                AND dfnsrmc = #{redInvoiceConfirmEntity.dfnsrmc}
            </if>
            <if test="redInvoiceConfirmEntity.chyy != null and redInvoiceConfirmEntity.chyy != ''">
                AND chyy= #{redInvoiceConfirmEntity.chyy}
            </if>
            <if test="redInvoiceConfirmEntity.lrfsf != null and redInvoiceConfirmEntity.lrfsf != ''">
                AND lrfsf= #{redInvoiceConfirmEntity.lrfsf}
            </if>
            <if test="redInvoiceConfirmEntity.kprqStartTime!=null and redInvoiceConfirmEntity.kprqStartTime!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') >= #{redInvoiceConfirmEntity.kprqStartTime} ]]>
            </if>
            <if test="redInvoiceConfirmEntity.kprqEndTime!=null and redInvoiceConfirmEntity.kprqEndTime!=''">
                <![CDATA[ AND DATE_FORMAT(kprq, '%Y-%m-%d') <= #{redInvoiceConfirmEntity.kprqEndTime} ]]>
            </if>
            and is_delete = "0" and kjzt = "1"
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectRedConfirmDaoByHztzdbh" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where hztzdbh = #{hzxxbbh}
          and is_delete = "0" limit 1
    </select>

    <select id="selectRedConfirmDaoListByHztzdbh" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where hztzdbh = #{hzxxbbh}
          and is_delete = "0"
    </select>

    <select id="selectRedConfirmDaoByQdfphm" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where dylpqdfphm = #{qdfphm}
          and is_delete = "0"
    </select>

    <select id="selectRedConfirmIsNotFailByQdfphm" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where dylpqdfphm = #{qdfphm}
          and kjzt != '6'
          and is_delete = "0"
    </select>

    <select id="taxGenerateRedTableResult" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where kjzt = 0
          and is_delete = "0"
    </select>

    <select id="selectRedInvoiceConfirmEntityList" resultMap="redInvoiceConfirmMap">
        SELECT * FROM red_invoice_confirm
        <where>
            <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') >= #{thisMonthFirstTime} ]]>
            <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') <= #{thisMonthEndTime} ]]>
            and is_delete = "0"
        </where>
    </select>

    <select id="selectAll" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where is_delete = "0"
    </select>

    <select id="selectRedInsvoiceConfirmSlzt" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where (zt = "02" or zt = "03" or zt = "10")
          and is_delete = "0"
          and fpfqr = "0"
    </select>

    <select id="selectByQdfphm" resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where qdfphm = #{qdfphm}
          and is_delete = "0"
    </select>

    <select id="selectRedInsvoiceConfirmKjzt" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where kjzt = "1"
          and is_delete = "0"
    </select>

    <select id="queryHzqrxxStatus" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where (zt = "02" or zt = "03")
          and is_delete = "0"
          and fpfqr = "1"
    </select>

    <select id="queryHzqrxxStatusByUuid" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where uuid = #{uuid}
          and is_delete = "0"
    </select>

    <select id="queryHzqrxxStatusByUuidAndNsrsbh" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where uuid = #{uuid}
          and nsrsbh = #{nsrsbh}
          and is_delete = "0"
    </select>


    <select id="getComfirmJgcxUUid" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where uuid is null
          and fpfqr = "0"
          and is_delete = "0"
    </select>
    <select id="getComfirmSldStatus" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where fpfqr = "0"
          and is_delete = "0"
          and zt_ing = "0"
    </select>
    <select id="getComfirmJgcxUUidByHzqrdbh" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where fpfqr = "1"
          and hztzdbh = #{hztzdbh}
          and is_delete = "0"
    </select>
    <select id="selectRedInvoiceConfirmNsrsbh" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where fpfqr = "0"
          and dylpqdfphm is not null
          and is_delete = "0"
    </select>

    <select id="selectBySldh" resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where is_delete = "0"
          and sldh = #{sldh}
          and nsrsbh = #{nsrsbh} limit 1
    </select>

    <select id="selectListBySldh" resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where is_delete = "0"
          and sldh = #{sldh}
    </select>

    <select id="selectByKpjgsldh" resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where is_delete = "0"
          and kpjgsldh = #{kpjgsldh} limit 1
    </select>
    <select id="selectRedConfirmDaoByQrdbh" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where hztzdbh = #{qrdbh}
          and fpfqr = #{fpfqr}
          and is_delete = "0"
    </select>
    <select id="selectRedConfirmsDaoByQdfphm"
            resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where dylpqdfphm = #{qdfphm}
          and is_delete = "0"
    </select>

    <select id="selectRedConfirmsDaoByQdfphmSpe"
            resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where dylpqdfphm = #{qdfphm}
          and zt not in ('05', '06', '07', '08', '09')
          and is_delete = "0"
    </select>
    <select id="selectRedConfirmDaoOneByQdfphm"
            resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where dylpqdfphm = #{qdfphm}
          and gxsf = #{gxsf}
          and zt not in ('05', '06', '07', '08', '09')
          and is_delete = "0" limit 1
    </select>

    <select id="queryHzqrxxStatusListByUuid" resultMap="redInvoiceConfirmMap">
        SELECT *
        FROM red_invoice_confirm
        where uuid = #{uuid}
          and is_delete = "0"
    </select>
    <select id="queryHzqrxxStatusListByHzqrdbh"
            resultType="com.dxhy.order.modules.entity.RedInvoiceConfirmEntity">
        SELECT *
        FROM red_invoice_confirm
        where hztzdbh = #{hztzdbh}
          and kpjgsldh is not null
          and is_delete = "0"
    </select>
</mapper>