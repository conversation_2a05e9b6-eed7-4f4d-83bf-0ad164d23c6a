<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.order.modules.dao.CustomerInfoQrcodeDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.order.modules.entity.CustomerInfoQrcodeEntity" id="baseResultMap">
        <id property="id" column="id"/>
        <result property="baseNsrsbh" column="base_nsrsbh"/>
        <result property="gsmc" column="gsmc"/>
        <result property="nsrsbh" column="nsrsbh"/>
        <result property="gsdz" column="gsdz"/>
        <result property="gsdh" column="gsdh"/>
        <result property="khyh" column="khyh"/>
        <result property="yhzh" column="yhzh"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="listData" resultMap="baseResultMap">
        select * from customer_info_qrcode where base_nsrsbh = #{baseNsrsbh} order by create_time desc
    </select>

</mapper>