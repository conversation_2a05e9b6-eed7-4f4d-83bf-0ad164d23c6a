#发送方的邮箱账号
from=大象慧云<<EMAIL>>

#发送方的邮箱密码 授权密码
pass=OSIVEYRACGUZZTVZ

#邮箱前缀
user=wangyang_727105

#发件人的邮箱的 SMTP 服务器地址
host=smtp.163.com

#需要请求认证
auth=true

startttlsEnable=true

port=465

charset=UTF-8

debug=false

# SMTP超时时长，单位毫秒，缺省值不超时
timeout = 0

# Socket连接超时值，单位毫秒，缺省值不超时
connectionTimeout = 0

# 使用SSL安全连接
sslEnable = true
# 指定实现javax.net.SocketFactory接口的类的名称,这个类将被用于创建SMTP的套接字
socketFactoryClass = javax.net.ssl.SSLSocketFactory
# 如果设置为true,未能创建一个套接字使用指定的套接字工厂类将导致使用java.net.Socket创建的套接字类, 默认值为true
socketFactoryFallback = true
# 指定的端口连接到在使用指定的套接字工厂。如果没有设置,将使用默认端口465
socketFactoryPort = 465